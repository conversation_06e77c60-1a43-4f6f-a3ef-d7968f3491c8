/*
 * @Author: shigl
 * @Date: 2022-11-10 15:32:41
 * @LastEditTime: 2023-12-01 12:06:15
 * @Description:
 */
module.exports = {
  presets: [
    '@vue/app',
    [
      '@babel/preset-env',
      {
        targets: {
          chrome: 49,
          firefox: 64,
          safari: 10,
          edge: 10,
          ie: 12
        }
      }
    ]
  ],
  plugins: [
    [
      'import',
      {
        libraryName: 'vant',
        libraryDirectory: 'es',
        style: true
      },
      'vant'
    ],
    [
      'import',
      {
        libraryName: '@ky/mobile-data-visualization',
        // 自定义名称
        customStyleName: (name, file) => {
          return `@ky/mobile-data-visualization/lib/css/${name}.css`
        },
        // 将引入的组件名转化为"-"连接的文件名
        camel2DashComponentName: false
      },
      '@ky/mobile-data-visualization'
    ]
  ]
}
