const path = require('path')
// 压缩
const compressing = require('compressing')
const WebpackBar = require('webpackbar')
const webpack = require('webpack')
const fs = require('fs')

function resolve(dir) {
  return path.join(__dirname, './', dir)
}
const sfimIgnoreFile = ['pc-web.html', 'android-device-skin.png']

function getDesPath() {
  if (process.env.VUE_APP_CHANNEL === 'web') {
    if (process.env.VUE_APP_SENSORS_ENV === 'sit') {
      return path.resolve(__dirname, './www/sit')
    }
    return path.resolve(__dirname, './www/prd')
  }
  return path.resolve(__dirname, './www')
}

function removeUselessFiles(sourcePath) {
  if (process.env.VUE_APP_CHANNEL === 'sfim') {
    if (fs.existsSync(sourcePath)) {
      sfimIgnoreFile.forEach(item => {
        if (fs.existsSync(sourcePath + '/' + item)) {
          fs.unlinkSync(sourcePath + '/' + item)
        }
      })
    }
  }
}

class Zip {
  apply(compiler) {
    compiler.hooks.done.tapAsync('zipDist', (stats, callback) => {
      const sourcePath = resolve('./www')
      const destPath = resolve('./www.zip')
      removeUselessFiles(sourcePath)
      compressing.zip.compressDir(sourcePath, destPath).then(callback)
    })
  }
}

const targetLink = {
  // target: 'http://scs-api-gw.intsit.sfdc.com.cn:1080',
  // target: 'https://scs-gw.sf-express.com',
  // target: 'http://kydata.sit.sf-express.com',
  // target: 'https://**************',
  target: process.env.VUE_APP_SERVE === 'serve-prod' ? 'https://fop.sf-express.com' : 'https://fop.sit.sf-express.com',
  changeOrigin: true,
  secure: false
}
const cssConfig = () => {
  if (process.env.NODE_ENV !== 'development') {
    return {
      extract: { ignoreOrder: true } // css 引入顺序,避免警告 (开发环境导致热更新失效)
    }
  }
  return {}
}
module.exports = {
  pages: {
    index: {
      entry: './src/main.js',
      template: 'public/index.html',
      filename: 'index.html'
    }
  },
  outputDir: getDesPath(),
  assetsDir: 'static',
  publicPath: './',
  css: {
    ...cssConfig()
  },
  pluginOptions: {},
  configureWebpack: config => {
    config.devtool = process.env.NODE_ENV === 'development' ? 'cheap-module-source-map' : false
    config.resolve = {
      extensions: ['.js', '.vue', '.json'],
      modules: [resolve('src'), resolve('node_modules')],
      alias: {
        src: resolve('src'),
        modules: resolve('src/modules'),
        common: resolve('src/common')
      }
    }
    if (process.env.NODE_ENV !== 'development') {
      config.plugins.push(
        new WebpackBar(),
        new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn/),
        new webpack.optimize.LimitChunkCountPlugin({
          maxChunks: 100
        }),
        new webpack.optimize.MinChunkSizePlugin({
          minChunkSize: 30000
        })
      )
    }
  },
  chainWebpack: config => {
    // 删除预加载插件
    config.plugins.delete('prefetch')
    config.when(process.env.NODE_ENV !== 'development', config => {
      config.devtool('none')
      config.plugin('ZipDist').use(Zip)
      if (process.env.VUE_APP_ANALYZE) {
        const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
        config.plugin('BundleAnalyzer').use(BundleAnalyzerPlugin)
      }
    })
  },
  devServer: {
    open: false,
    proxy: {
      '/api/cockpit': {
        ...targetLink,
        pathRewrite: {
          '^/api/cockpit': '/cockpit'
        }
      },
      '/api/resourceServices': {
        ...targetLink,
        pathRewrite: {
          '^/api/resourceServices': '/resourceServices'
        }
      },
      '/api/cockpit': {
        ...targetLink,
        pathRewrite: {
          '^/api/cockpit': '/cockpit'
        }
      },
      '/restApi/cockpit': {
        ...targetLink,
        pathRewrite: {
          '^/restApi/cockpit': '/restApi/cockpit'
        }
      },
      '/api/sx-security': {
        ...targetLink,
        pathRewrite: {
          '^/api/sx-security': '/sx-security'
        }
      },
      '/restApi/sx-security': {
        ...targetLink,
        pathRewrite: {
          '^/restApi/sx-security': '/restApi/sx-security'
        }
      },
      // 代理登录
      '/gateway': {
        ...targetLink,
        pathRewrite: {
          '^/gateway': '/gateway'
        }
      },
      '/': {
        ...targetLink,
        pathRewrite: {
          '^/': '/'
        }
      }
    },
    host: '0.0.0.0',
    https: false // 更改默认协议
  }
}
