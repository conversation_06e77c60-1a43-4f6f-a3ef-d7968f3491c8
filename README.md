<!--
 * @Author: shigl
 * @Date: 2023-12-04 18:44:46
 * @LastEditTime: 2023-12-15 13:43:53
 * @Description: 
-->
# 管理平台
https://open-funsionwork.sf-express.com/new-micro/#/home/<USER>

由于测试环境后台没有数据，直接使用线上数据进行开发
npm run serve:prd  运行生产环境

npm run build  丰声微服务打包(生产)

npm run build:sit  丰声微服务打包(测试)

npm install --package-lock-only

### 顺心战况微服务

统编码FOP-WEB-ICA

项目git地址：<http://git.sf-express.com/projects/FOP-WEB/repos/fop-web-ica-sxmobile/browse>

发版方式：丰声开放平台 <http://open-funsionwork.sf-express.com/new-micro/#/home/<USER>

部署方式：本地打包上传www.zip压缩包

发版方式：流水线

部署方式：oss部署 sfim-sxmobile

在线地址: <http://kydata.sf-express.com/sfim-sxmobile-online/pc-web.html>

负责产品：王伟,刘文瑜

负责后台

负责后台：

### 目录结构

```bash

├── src      
    ├── common              # 应用系统公共代码块（常量、验证）
    ├── components          # 业务通用组件
    ├── modules             # 页面模块
        ├── platform        # 权限验证页面
        ├── entrance        # 页面入口
    ├── router              # 路由 (如有子路由,在对应modules页面模块中配置router.js)
    ├── store               # vuex (如需局部命名空间,在对应modules页面模块中配置               stroe.js)
   

```

vue2+vuex+vue-router

*@ky/mobile-data-visualization*  自维护组件 (目前用在大件战况,顺心战况,健康度,航空工作台),

git地址:<http://git.sf-express.com/projects/FOP-WEB/repos/fop-web-eca-kyutils/browse>;

文档地址:<https://confluence.sf-express.com/pages/viewpage.action?pageId=136157534>

```tsx
// 加setTimeout  resize() 解决 van-swipe 路由返回报错问题 (Uncaught TypeError: Cannot read property 'width' of null)
// $nextTick 有时候不行
setTimeout(() => {
      if (this.$refs.realtimeSwipe) {
        this.$refs.realtimeSwipe.resize()
        this.$refs.realtimeSwipe.swipeTo(this.tabValue)
      }
}, 500)
```
