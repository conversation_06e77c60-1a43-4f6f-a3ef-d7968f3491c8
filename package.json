{"name": "sx-mobile", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --open --mode development", "serve:prd": "vue-cli-service serve --open --mode serve-prod", "build": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint", "sit": "vue-cli-service build --mode sit", "web.sit": "vue-cli-service build --mode web-sit", "web.prd": "vue-cli-service build --mode web-prd", "analyze": "vue-cli-service build --mode analyze"}, "dependencies": {"@ky/caslogin": "^3.2.8", "@ky/mobile-data-visualization": "^2.0.29", "@ky/sensors": "^1.1.4", "@sentry/tracing": "^7.47.0", "@sentry/vue": "^7.47.0", "animate.css": "^4.1.1", "axios": "^1.6.2", "better-scroll": "^2.5.1", "classnames": "^2.3.2", "clipboard": "^2.0.10", "core-js": "^3.33.1", "echarts": "^5.0.2", "install": "^0.13.0", "js-cookie": "^3.0.5", "lodash": "^4.17.15", "moment": "^2.24.0", "qs": "^6.10.1", "serve": "^11.1.0", "smoothscroll-polyfill": "^0.4.4", "solarlunar": "^2.0.7", "vant": "^2.13.2", "vconsole": "^3.3.4", "velocity-animate": "^1.5.2", "vue": "^2.7.14", "vue-i18n": "^8.14.0", "vue-router": "^3.0.3", "vuedraggable": "^2.24.3", "vuex": "^3.0.1", "wd-domsize-monitor": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-unit-jest": "^4.2.3", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "^4.5.19", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "1.0.0-beta.31", "autoprefixer": "^8.0.0", "babel-core": "^6.26.3", "babel-eslint": "^8.0.1", "babel-loader": "^8.0.6", "babel-plugin-import": "^1.13.8", "babel-preset-env": "^1.7.0", "browserslist": "^4.21.5", "caniuse-lite": "^1.0.30001450", "clean-webpack-plugin": "^3.0.0", "compressing": "^1.4.0", "cross-env": "^7.0.3", "css-loader": "^5.2.7", "eslint": "^5.16.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^5.0.0", "less": "^3.9.0", "less-loader": "^5.0.0", "postcss-pxtorem": "^5.1.1", "prettier": "^1.19.1", "vue-loader": "^15.9.6", "vue-template-compiler": "2.6.10", "webpack": "^4.47.0", "webpack-bundle-analyzer": "^4.4.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^3.7.2", "webpackbar": "^5.0.2"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11", "Android >= 4.0", "iOS >= 8"]}