/*
 * @Author: shigl
 * @Date: 2023-12-04 18:44:46
 * @LastEditTime: 2024-03-22 10:52:50
 * @Description:
 */
import Vue from 'vue'
import { Swipe, SwipeItem, Loading, Toast } from 'vant'

Vue.use(Swipe)
Vue.use(SwipeItem)
Vue.use(Loading)
Vue.use(Toast)
// 快运npm包及样式
// import '@ky/mobile-data-visualization/lib/main.css'
// import mobileDataVisualization from '@ky/mobile-data-visualization'
// Vue.use(mobileDataVisualization)
import {
  Stylecss,
  CardList,
  BtnFourTab,
  TargetIndex,
  MultiDataList,
  ChartLegend,
  NormalTable,
  BtnShowMore,
  MDiaLog,
  HScroll,
  Tabs,
  TitleTabs,
  RadiusButton,
  MAlertWarning,
  Kyloading,
  DatePickerSimple,
  KyDataDrawer,
  KyDataCommentsModel,
  ShowTipIcon,
  ExplainContent,
  BtnTabs,
  CommonLegend,
  ScrollTabBar,
  PageContent,
  KyDatePickerSimple,
  KydOverviewText,
  KyDatePicker,
  KydSummaryDataList,
  KydEmtyData,
  KydScrollButton,
  KydSearch,
  KydClipboard,
  KydSelect,
  KydChartModel,
  KydHorizonChartModel
} from '@ky/mobile-data-visualization'
Vue.use(Stylecss)
Vue.use(Tabs)
Vue.use(Kyloading)
Vue.use(KyDatePicker)
Vue.use(TitleTabs)
Vue.use(PageContent)
Vue.use(CardList)
Vue.use(BtnTabs)
Vue.use(ChartLegend)
Vue.use(KydChartModel)
Vue.use(NormalTable)
Vue.use(KyDataDrawer)
Vue.use(KydSearch)
Vue.use(ShowTipIcon)
Vue.use(KydHorizonChartModel)
Vue.use(KydSummaryDataList)
Vue.use(MAlertWarning)
Vue.use(KyDataCommentsModel)
Vue.use(TargetIndex)
Vue.use(HScroll)
Vue.use(BtnShowMore)
Vue.use(BtnFourTab)
Vue.use(MultiDataList)
Vue.use(MDiaLog)
Vue.use(RadiusButton)
Vue.use(DatePickerSimple)
Vue.use(ExplainContent)
Vue.use(CommonLegend)
Vue.use(ScrollTabBar)
Vue.use(KyDatePickerSimple)
Vue.use(KydOverviewText)
Vue.use(KydEmtyData)
Vue.use(KydScrollButton)
Vue.use(KydClipboard)
Vue.use(KydSelect)
