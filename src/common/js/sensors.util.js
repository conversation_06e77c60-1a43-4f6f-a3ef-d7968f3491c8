/*
 * @Descripttion: 神策工具
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-13 20:21:29
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-12-04 16:41:04
 */

import kySensors from '@ky/sensors/web-sensors'
import store from 'src/store/index'
const isDev = process.env.NODE_ENV === 'development'
// 开发环境上报灰度空间，其他上报生产空间
// const projectName = process.env.VUE_APP_SENSORS_ENV === 'production' ? 'expressTech' : 'expressTechgray'
const projectName = process.env.VUE_APP_SENSORS_ENV === 'production' ? 'snfl' : 'expressTechgray'

// 项目常量配置
const SENSORS_CONFIG = {
  SERVER_URL: `https://ubs.sf-express.com/sa?project=${projectName}`,
  SYSTEM_CODE: 'fop-web-ica-sxmobile',
  PLATFORM_TYPE: 'H5',
  PLATFORM_NAME: '顺心战况丰声端',
  CHARGE_SYS_CODE: 'FOP-DMP-DSP'
}
// 初始化神策SDK
kySensors.init({
  server_url: SENSORS_CONFIG.SERVER_URL, // 上报地址
  is_track_single_page: false,
  use_client_time: true,
  send_type: 'ajax',
  show_log: true, // 日志
  heatmap: {
    clickmap: 'not_collect',
    scroll_notice_map: 'not_collect'
  }
})
if (!isDev) {
  // 注册公共属性
  kySensors.registerPage({
    platform_type: SENSORS_CONFIG.PLATFORM_TYPE,
    platform_name: SENSORS_CONFIG.PLATFORM_NAME,
    system_code: SENSORS_CONFIG.SYSTEM_CODE,
    charge_sys_code: SENSORS_CONFIG.CHARGE_SYS_CODE,
    $app_name: '顺心战况'
  })
}

const sensors = {
  getSensorInstance: function () {
    if (isDev) return
    return kySensors.getSensorInstance()
  },
  restLastTime: function () {
    if (isDev) return
    kySensors.restLastTime()
  },
  login: function (id) {
    if (isDev) return
    kySensors.login(id)
  },
  setProfile: function (config) {
    if (isDev) return
    kySensors.setProfile(config)
  },
  track: function (eventName, config = {}) {
    if (isDev) return
    const state = store.state || {}
    kySensors.track(eventName, {
      ...config,
      zone_code: (state.zoneParams || {}).zoneCode || state.userData.zoneCode || ''
    })
  },
  webClick: function (elContent) {
    if (isDev) return
    const state = store.state || {}
    kySensors.webClick({
      $element_content: 'sxzk-' + elContent,
      zone_code: (state.zoneParams || {}).zoneCode || state.userData.zoneCode || ''
    })
  },
  functionClick: function (elContent) {
    if (isDev) return
    const state = store.state || {}
    kySensors.functionClick({
      $title: '顺心战况',
      $element_content: 'sxzk-' + elContent,
      zone_code: (state.zoneParams || {}).zoneCode || state.userData.zoneCode || ''
    })
  },
  pageview: function (title) {
    if (isDev) return
    const state = store.state || {}
    kySensors.pageview({
      $title: 'sxzk-' + title,
      zone_code: (state.zoneParams || {}).zoneCode || state.userData.zoneCode || ''
    })
  },
  pageStay: function (title) {
    if (isDev) return
    const state = store.state || {}
    kySensors.pageStay({
      $title: 'sxzk-' + title,
      zone_code: (state.zoneParams || {}).zoneCode || state.userData.zoneCode || ''
    })
  }
}

export default sensors
