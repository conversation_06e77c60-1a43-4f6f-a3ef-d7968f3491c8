const drawGaugeByCanvas = (id, progress, progress2) => {
  const value = progress
  progress = progress > 100 ? 100 : progress
  progress = progress < 0 ? 0 : progress
  progress2 = progress2 > 100 ? 100 : progress2
  let canvas
  if (typeof id === 'string') {
    canvas = document.getElementById(id)
  } else {
    canvas = id
  }
  if (!canvas) {
    return
  }
  const ctx = canvas.getContext('2d')
  let percent = progress
  const percent2 = progress2
  const circleX = canvas.width / 2
  const circleY = canvas.height / 2 + 60
  const radius = 120
  const lineWidth = 18
  const color1 = progress >= progress2 ? '#3AD4BC' : '#FF914C'
  const color2 = progress >= progress2 ? '#09BCA0' : '#F54F1E'
  const shodowColor = progress >= progress2 ? 'rgba(9,188,160,0.8)' : 'rgba(255,203,0,0.8)'
  const fontColor = progress >= progress2 ? '#09BCA0' : '#F54F1E'

  // 画圆
  function circle(cx, cy, r) {
    ctx.beginPath()
    ctx.lineWidth = 8
    ctx.setLineDash([])
    ctx.shadowOffsetX = 0 // 阴影横向位移
    ctx.shadowOffsetY = 0 // 阴影纵向位移
    ctx.shadowBlur = 0
    ctx.strokeStyle = '#eee'
    ctx.arc(cx, cy, r, Math.PI, Math.PI * 2)
    ctx.stroke()
  }

  function circle2(cx, cy, r, endAngle) {
    ctx.beginPath()
    ctx.lineWidth = 2
    ctx.setLineDash([1, 10])
    ctx.shadowOffsetX = 0 // 阴影横向位移
    ctx.shadowOffsetY = 0 // 阴影纵向位移
    ctx.shadowBlur = 0
    ctx.strokeStyle = '#eee'
    ctx.arc(cx, cy, (r * 5) / 7, Math.PI, Math.PI * 2)
    ctx.stroke()
  }

  // 画弧线
  function sector(cx, cy, r, startAngle, endAngle, anti) {
    ctx.beginPath()
    ctx.lineWidth = lineWidth
    ctx.setLineDash([])
    ctx.shadowBlur = 10
    ctx.shadowColor = shodowColor // 阴影颜色
    // 渐变色 - 可自定义
    var linGrad = ctx.createLinearGradient(circleX - radius - lineWidth, circleY, circleX + radius + lineWidth, circleY)
    linGrad.addColorStop(0.0, color1)
    linGrad.addColorStop(1.0, color2)
    ctx.strokeStyle = linGrad
    // 圆弧两端的样式
    ctx.lineCap = 'round'
    ctx.arc(cx, cy, r, Math.PI, Math.PI * (1 + endAngle / 100))
    ctx.stroke()
  }

  // 画弧线02
  function sector2(cx, cy, r, startAngle, endAngle, anti) {
    ctx.beginPath()
    ctx.lineWidth = 3
    ctx.setLineDash([1, 10])
    ctx.shadowOffsetX = 0 // 阴影横向位移
    ctx.shadowOffsetY = 0 // 阴影纵向位移
    ctx.shadowBlur = 0
    // 渐变色 - 可自定义
    ctx.lineCap = 'round'
    var linGrad = ctx.createLinearGradient(circleX - radius - lineWidth, circleY, circleX + radius + lineWidth, circleY)
    linGrad.addColorStop(0.0, '#AFB7C0')
    linGrad.addColorStop(1.0, '#AFB7C0')
    ctx.stroke()
    ctx.beginPath()
    ctx.strokeStyle = linGrad
    // 圆弧两端的样式
    ctx.lineCap = 'round'
    ctx.arc(cx, cy, (r * 5) / 7, Math.PI, Math.PI * 2)
    ctx.stroke()
    ctx.beginPath()
    ctx.lineCap = 'round'
    ctx.arc(
      cx + ((r * 5) / 7) * Math.cos(Math.PI * (1 + endAngle / 100)),
      cy + ((r * 5) / 7) * Math.sin(Math.PI * (1 + endAngle / 100)),
      2,
      0,
      Math.PI * 2
    )
    ctx.fill()
  }

  // 画弧线02
  function sector3(cx, cy, r, startAngle, endAngle, anti) {
    // ctx.beginPath()
    // 开始绘制路径
    ctx.beginPath()
    ctx.lineWidth = 1
    // 绘制圆的路径**
    ctx.arc(
      cx + r * Math.cos(Math.PI * (1 + endAngle / 100.5)),
      cy + r * Math.sin(Math.PI * (1 + endAngle / 100.5)),
      5,
      0,
      Math.PI * 2,
      false
    )
    ctx.fillStyle = '#fff'
    ctx.strokeStyle = '#fff'
    ctx.fill()
    ctx.stroke()
  }

  // 刷新
  function loading() {
    if (percent < percent2) percent = percent2
    if (process >= percent) clearInterval(circleLoading)
    if (process2 >= percent) clearInterval(circleLoading)
    // 清除canvas内容
    ctx.clearRect(0, 0, circleX * 2, circleY * 2)

    // 中间的字
    ctx.font = 42 + 'px Roboto-Medium'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillStyle = fontColor
    const filterData = value || value === 0 ? parseFloat(value).toFixed(1) : '--'
    ctx.fillText(filterData + '%', circleX, (circleY * 9) / 10)

    // 圆形
    circle(circleX, circleY, radius)
    circle2(circleX, circleY, radius, (Math.PI * 2) / 3)
    // 圆弧
    sector(circleX, circleY, radius, (Math.PI * 2) / 3, process)
    sector3(circleX, circleY, radius, (Math.PI * 2) / 3, process)
    sector2(circleX, circleY, radius, (Math.PI * 2) / 3, process2)
  }
  var process = progress
  var process2 = progress2
  var circleLoading = window.setInterval(function() {
    loading()
  }, 20)
}

export { drawGaugeByCanvas }

