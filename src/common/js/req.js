import store from '../../store'
import HttpClass from './httpPlugin'
const http = HttpClass.getInstance()
import { formatTd, checkNumber } from 'common/js/utils'

// 无权限&&当前层级未开发 不触发接口
function isHttp() {
  const { isAuth, levelthemeCurrent } = store.getters
  if (!isAuth || !levelthemeCurrent['isLevel']) {
    return false
  }
  return true
}

//  请求二维数据  (isTrue 是否启用isHttp判断条件)
// 增加分页传参
function reqHttp(
  tableName,
  obj = {
    data: {}
  },
  isLoding = true,
  isTrue = true
) {
  if (!isHttp() && isTrue) {
    return {
      data: [],
      pagingInfo: []
    }
  }
  const header = obj?.headers ? { headers: obj.headers } : {}
  const data = obj?.data
  const pagingInfo = obj?.pagingInfo || {} // 分页
  const setArrayData = []
  if (data) {
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const obj = {
          key,
          value: data[key]
        }
        setArrayData.push(obj)
      }
    }
  }
  isLoding &&
    store.commit('setRequestQueues', {
      type: 'push'
    })
  return new Promise((resolve, reject) => {
    http
      .http({
        // url: `/resourceServices/report/twoDimen/${tableName}`,
        url: `/cockpit/reportrest/report/twoDimen/${tableName}`,
        method: 'POST',
        data: {
          conditionList: setArrayData,
          pagingInfo: pagingInfo
        },
        ...header
      })
      .then(res => {
        store.commit('setRequestQueues', {
          type: 'delete'
        })
        let data = []
        let pagingInfo = {}
        if (res.success) {
          const value = res?.obj
          if (value) {
            data = value?.data || formatTd(value)
            checkNumber(data)
            pagingInfo = value?.pagingInfo || {}
          }
        }
        resolve({
          data,
          pagingInfo,
          ...res
        })
      })
      .catch(err => {
        console.log(err)
        store.commit('setRequestQueues', {
          type: 'clear'
        })
        resolve({
          data: [],
          pagingInfo: []
        })
        reject(err)
      })
  })
}
// 请求java接口
function reqJava(url, data, isLoding = true, isTrue = true) {
  if (!isHttp() && isTrue) return []
  isLoding &&
    store.commit('setRequestQueues', {
      type: 'push'
    })
  return new Promise((resolve, reject) => {
    http
      .http({
        url: url,
        method: 'POST',
        ...data
      })
      .then(res => {
        store.commit('setRequestQueues', {
          type: 'delete'
        })
        if (res.success) {
          if (res.hasOwnProperty('data')) {
            const value = res.data
            checkNumber(value)
            return resolve(value)
          }
          if (res.hasOwnProperty('obj')) {
            const value = res.obj
            if (Array.isArray(value)) {
              checkNumber(value)
            }
            return resolve(value)
          }
          return resolve([])
        }
        return resolve([])
      })
      .catch(err => {
        console.log(err)
        store.commit('setRequestQueues', {
          type: 'clear'
        })
        reject(err)
      })
  })
}

export { reqHttp, reqJava }
