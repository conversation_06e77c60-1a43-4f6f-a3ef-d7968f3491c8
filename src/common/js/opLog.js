import HttpClass from 'common/js/httpPlugin'

export default class {
  constructor() {
    this.isPage = 0
    this.endTime = 0
    this.stayTime = 0
    this.startTime = 0
    this.zoneCode = ''
    this.quotaCode = ''
    this.mergeCode = ''
    this.curPageCode = ''
    this.drillNetwork = ''
    this.lastPostTime = ''
    this.lastMergeCode = ''
    this.http = HttpClass.getInstance()
  }
  log(p) {
    console.log(p.mergeCode)
    this.setIsPage(p.isPage)
    this.setQuotaCode(p.quotaCode)
    this.setDrillNetwork(p.drillNetwork)
    if (!this.setZoneCode(p.zoneCode)) return
    if (!this.setMergeCode(p.mergeCode)) return
    if (this.checkMisoperation(p.mergeCode)) return
    this.lastPostTime = new Date().getTime()
    this.lastMergeCode = this.getMergeCode
    this.setStayTime(p.mergeCode)
    this.postLog()
  }
  postLog() {
    const url = 'resourceServices/auth/saveLog'
    const params = {
      isPage: this.isPage,
      zoneCode: this.zoneCode,
      stayTime: this.stayTime,
      quotaCode: this.quotaCode,
      mergeCode: this.mergeCode,
      drillNetwork: this.drillNetwork
    }
    this.http.http.post(url, params)
  }
  setIsPage(i) {
    if (!/^0|1$/.test(i) || !i) {
      i = '0'
    }
    this.isPage = i + ''
  }
  setZoneCode(z) {
    if (!z) {
      console.error('网点编码为空或不可识别，检查一下！zongCode : ' + z + this.mergeCode)
      return false
    }
    this.zoneCode = z + ''
    return true
  }
  setStayTime(m) {
    this.stayTime = 0
    const flag = m.substring(m.length - 2)
    if (flag === '00' && m.substring(m.length - 4) !== '0000') {
      this.startTime = new Date().getTime()
      this.curPageCode = m.substring((m.length - 4), (m.length - 2))
    }
    if (flag === '99') {
      if (this.curPageCode !== m.substring((m.length - 4), (m.length - 2))) {
        console.log('页面编码在进入页面与离开页面编码不同，请检查你的行为日志逻辑，startPageCode:' + this.curPageCode + ',endPageCode:' + m.substring((m.length - 4), (m.length - 2)))
        // return false
      }
      this.endTime = new Date().getTime()
      this.stayTime = this.endTime - this.startTime
    }
    if (this.stayTime < 0) {
      this.stayTime = 0
      console.log('页面停留时间为负值，请检查你的行为日志逻辑，stayTime：' + this.stayTime)
      // return false
    }
    return true
  }
  setMergeCode(m) {
    if (!m) {
      console.error('页面/按钮编码为空或不可识别，检查一下！mergeCode : ' + m)
      return false
    } else if (!(/^JYP(\d{8})$/.test(m))) {
      console.error('编码格式错误（编码11位），检查一下！mergeCode : ' + m)
      return false
    }
    this.mergeCode = m + ''
    return true
  }
  setQuotaCode(q) {
    this.quotaCode = (q || '') + ''
  }
  setDrillNetwork(d) {
    this.drillNetwork = (d || '') + ''
  }
  checkMisoperation(curCode) {
    if (curCode === this.lastMergeCode && Math.abs(new Date().getTime() - this.lastPostTime) < 200) {
      return true
    }
    return false
  }
}

