// 数据格式处理
import store from 'src/store'
// import vuex from 'src/store/getters'

const numToPercent = (val, fixnum = 1, abs = false) => {
  const isAuth = store.getters.isAuth
  if (!isAuth) {
    return '***'
  }
  if (!val && val !== 0) {
    return '-'
  }
  if (!abs) {
    return !parseFloat((val * 100).toFixed(fixnum)) ? '0%' : `${(val * 100).toFixed(fixnum)}%`
  } else {
    return !parseFloat((val * 100).toFixed(fixnum)) ? '0%' : `${Math.abs(val * 100).toFixed(fixnum)}%`
  }
}
const numToThousands = (val, divisor = 1, fixnum = 1) => {
  const isAuth = store.getters.isAuth
  if (!isAuth) {
    return '***'
  }
  if (!val && val !== 0) {
    return '-'
  } else {
    if (Math.abs(val / divisor) < 1) {
      return `${(val / divisor).toFixed(2)}`
    }
    return `${(+(val / divisor).toFixed(fixnum)).toLocaleString()}`
  }
}
// 取整不带分隔符,不会省略为0的小数点
const numToInFloat = (val, fixnum = 1, divisor = 1, defaultNum = 2, abs = false) => {
  const isAuth = store.getters.isAuth
  if (!isAuth) {
    return '***'
  }
  if (!val && val !== 0) {
    return '-'
  }

  if (!abs) {
    if (Math.abs(val / divisor) < 1) {
      return !parseFloat(`${(val / divisor).toFixed(defaultNum)}`)
        ? `${Number((val / divisor).toFixed(defaultNum))}`
        : `${(val / divisor).toFixed(defaultNum)}`
    }
    return `${(val / divisor).toFixed(fixnum)}`
  } else {
    if (Math.abs(val / divisor) < 1) {
      return !parseFloat(`${(val / divisor).toFixed(defaultNum)}`)
        ? `${Number((val / divisor).toFixed(defaultNum))}`
        : `${Math.abs(val / divisor).toFixed(defaultNum)}`
    }
    return `${Math.abs(val / divisor).toFixed(fixnum)}`
  }
}

// 取整带分隔符
const numToInteger = (val, fixnum = 1, divisor = 1, defaultNum = 2, abs = false) => {
  const isAuth = store.getters.isAuth
  if (!isAuth) {
    return '***'
  }
  if (!val && val !== 0) {
    return '-'
  }
  if (!abs) {
    if (Math.abs(val / divisor) < 1) {
      // 等于0的不保留小数点
      return !parseFloat(`${(val / divisor).toFixed(defaultNum)}`)
        ? `${Number((val / divisor).toFixed(defaultNum))}`
        : `${(val / divisor).toFixed(defaultNum)}`
    }
    return formatNumberString((val / divisor).toFixed(fixnum))
  } else {
    if (Math.abs(val / divisor) < 1) {
      // 等于0的不保留小数点
      return !parseFloat(`${(val / divisor).toFixed(defaultNum)}`)
        ? `${Number((val / divisor).toFixed(defaultNum))}`
        : `${Math.abs(val / divisor).toFixed(defaultNum)}`
    }
    return formatNumberString(Math.abs(val / divisor).toFixed(fixnum))
  }
}

// 格式化数字类型 (相比toLocaleString(),不会省略小数点后的0)
const formatNumberString = num => {
  const value = String(num).split('.')
  if (value[1]) {
    return value[0].replace(/(?=(\B\d{3})+$)/g, ',') + '.' + value[1]
  } else {
    return value[0].replace(/(?=(\B\d{3})+$)/g, ',')
  }
}

const noChange = val => {
  const isAuth = store.getters.isAuth
  if (!isAuth) {
    return '***'
  }
  if (!val && val !== 0) {
    return '-'
  }
  return val
}

const install = Vue => {
  return (
    (Vue.prototype.$numToPercent = numToPercent),
    (Vue.prototype.$numToInteger = numToInteger),
    (Vue.prototype.$numToInFloat = numToInFloat),
    (Vue.prototype.$numToThousands = numToThousands),
    (Vue.prototype.$noChange = noChange)
  )
}
export default {
  install
}

export { numToPercent, numToInFloat, numToInteger, noChange }
