import axios from 'axios'

axios.defaults.withCredentials = true

export default class HttpClass {
  static getInstance() {
    if (!this.instance) {
      this.instance = {}
      const baseURL = process.env.VUE_APP_REQUEST
      this.instance.http = axios.create({
        baseURL
      })
      this.instance._instance = new HttpClass()
    }
    return this.instance
  }

  /**
   * 实例请求拦截器
   *
   * @param {*} resolve
   * @param {*} reject
   * @memberof HTTP_PROXY
   */
  requestInterceptors(instance, resolve, reject) {
    // debugger
    instance.interceptors.request.use(resolve, reject)
  }
  /**
   * 实例响应拦截器
   *
   * @param {*} resolve
   * @param {*} reject
   * @memberof HTTP_PROXY
   */
  responseInterceptors(instance, resolve, reject) {
    instance.interceptors.response.use(resolve, reject)
  }
  /**
   * 全局请求拦截器
   *
   * @param {*} resolve
   * @param {*} reject
   * @memberof HTTP_PROXY
   */
  globalRequestInterceptors(resolve, reject) {
    axios.interceptors.request.use(resolve, reject)
  }
  /**
   * 全局响应拦截器
   *
   * @param {*} resolve
   * @param {*} reject
   * @memberof HTTP_PROXY
   */
  globalResponseInterceptors(resolve, reject) {
    axios.interceptors.response.use(resolve, reject)
  }
}

