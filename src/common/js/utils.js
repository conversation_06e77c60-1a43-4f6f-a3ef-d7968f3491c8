import { dateFormat } from 'common/charts/chartUtils'
import moment from 'moment'

const formatTd = data => {
  if (!data) return []
  const fieldMap = {}
  for (const index in data.colDefList) {
    fieldMap[data.colDefList[index].name] = index
  }
  const newData = []
  for (const item of data.rows) {
    const obj = {}
    // for(let param of params){
    // 	obj[param]=item[fieldMap[param]]
    // }
    for (const index in data.colDefList) {
      obj[data.colDefList[index].name] = item[index]
    }
    newData.push(obj)
  }
  return newData
}
/**
 *
 * @param {*} _this  当前组件实例
 * @param {*} moduleName  当前模块名字
 * @result 在缓存中找到该组件，并删除该组件---实现手动删除组件缓存
 */
export function handleComponentCache(_this, moduleName) {
  _this.$store.commit('setLastComponentPath', '')
  if (_this.$store.state[moduleName]) {
    // 存在则卸载
    _this.$store.unregisterModule(moduleName)
  }
  _this.$store.commit('setRemoveRouteCache', moduleName)
}

// 过滤不可用数据,空值转为''
const checkNumber = result => {
  result.map((item, index) => {
    for (const key in item) {
      if (
        (item[key] === 'N' || item[key] === '-' || item[key] === 'null' || item[key] === null) &&
        key.indexOf('desc') === -1
      ) {
        item[key] = ''
      }
    }
  })
  return result
}
// 一维过滤不可用数据,空值转为''
const checkOneNumber = result => {
  for (const key in result) {
    if (
      (result[key] === 'N' || result[key] === '-' || result[key] === 'null' || result[key] === null) &&
      key.indexOf('desc') === -1
    ) {
      result[key] = ''
    }
  }
  return result
}

// 小>大排序(空值放最后) sortBy(根据另一组数据排序)
const objectSortUp = (data, key, sortBy = []) => {
  if (!data.length) return
  if (sortBy.length) {
    data.sort((a, b) => {
      let index1 = sortBy.indexOf(a[key])
      let index2 = sortBy.indexOf(b[key])
      if (index1 === -1) {
        index1 = data.length
      }
      if (index2 === -1) {
        index2 = data.length + 1
      }
      return index1 - index2
    })
    return data
  }
  const obj1 = data.filter(item => item[key] || item[key] === 0)
  const obj2 = data.filter(item => !item[key] && item[key] !== 0)
  obj1.sort((a, b) => {
    return a[key] - b[key]
  })
  data.splice(0, data.length)
  data.push(...obj1, ...obj2)
  return data
}
// 大>小排序(空值放最后) sortBy(根据另一组数据排序)
const objectSortDown = (data, key, sortBy = []) => {
  if (!data.length) return
  if (sortBy.length) {
    data.sort((a, b) => {
      let index1 = sortBy.indexOf(a[key])
      let index2 = sortBy.indexOf(b[key])
      if (index1 === -1) {
        index1 = data.length
      }
      if (index2 === -1) {
        index2 = data.length + 1
      }
      return index1 - index2
    })
    return data
  }
  const obj1 = data.filter(item => item[key] || item[key] === 0)
  const obj2 = data.filter(item => !item[key] && item[key] !== 0)
  obj1.sort((a, b) => {
    return b[key] - a[key]
  })
  data.splice(0, data.length)
  data.push(...obj1, ...obj2)
  return data
}

const setTable = (result, columns, dateField, formatType) => {
  const dateFormatRes = (val, formatType) => {
    switch (formatType) {
      case 'day':
        return dateFormat(val, 0)
      case 'week':
        return moment(val, 'GGGG-WW').format('WW周')
      case 'month':
        return dateFormat(val, 1)
      case 'year':
        return dateFormat(val, 2)
      default:
        return dateFormat(val, 0)
    }
  }
  let resTableDataSource = []
  let resTableColumns = []
  let resTableWidth = '100%'
  if (result.length) {
    const keyValue = columns.map(item => {
      if (item.icon) {
        return {
          label: item.label,
          key: item.dataIndex
        }
      }
    })
    const newValue = changeTableData(result, keyValue)
    const columns1 = [
      {
        label: '日期',
        fixed: 'left',
        dataIndex: 'value0',
        width: '1.4rem'
      }
    ]
    const columns2 = []

    // 转成两列数据(日月字段一样)
    result.map((item, index) => {
      columns2.push({
        label: dateFormatRes(item[dateField], formatType),
        dataIndex: 'value' + (index + 1),
        render: (h, data, x, i) => columns[i].render(data)
      })
    })
    resTableDataSource = newValue
    resTableColumns = [...columns1, ...columns2]
    resTableWidth = result.length < 5 ? '100%' : (7.1 / 5) * result.length - 1 + 1.4 + 'rem'
  }
  return [resTableColumns, resTableDataSource, resTableWidth]
}

// 数组对象属性去重
const arrUnique = (arr, prop) => {
  const res = new Map()
  return arr.filter(a => !res.has(a[prop]) && res.set(a[prop], 1))
}

/**
 * 转换成横向table数据,图表展开数据用
 * result:array 正常的table数据
 * keyValue:{ label:string, key:string }
 */
const changeTableData = (result, keyValue) => {
  if (!result.length || !keyValue.length) return
  const newData = []
  keyValue.forEach((item, index) => {
    newData.push({ value0: item.label })
  })
  result.forEach((item, index) => {
    keyValue.forEach((data, index2) => {
      newData[index2]['value' + (index + 1)] = item[data.key]
    })
  })
  return newData
}
const install = Vue => {
  return (
    (Vue.prototype.$objectSortUp = objectSortUp), // 小>大排序
    (Vue.prototype.$objectSortDown = objectSortDown), // 大>小 排序
    (Vue.prototype.$checkNumber = checkNumber), // 二维过滤不可用数据转为''
    (Vue.prototype.$checkOneNumber = checkOneNumber), // 一维过滤不可用数据转为''
    (Vue.prototype.$changeTableData = changeTableData), // table数据转换
    (Vue.prototype.$setTable = setTable) // 图表展开数据table函数
  )
}
export default {
  install
}
export { formatTd, checkNumber, checkOneNumber, objectSortUp, objectSortDown, arrUnique, changeTableData }
