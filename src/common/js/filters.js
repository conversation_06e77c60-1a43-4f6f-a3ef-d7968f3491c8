/*
 * @Author: shigl
 * @Date: 2021-04-27 11:31:59
 * @LastEditTime: 2022-05-18 16:51:14
 * @Description:
 */
const numToPercent = (val, fixnum = 1, abs = false) => {
  if (!val && val !== 0) {
    return '-'
  }
  if (!abs) {
    return !parseFloat((val * 100).toFixed(fixnum)) ? '0%' : `${(val * 100).toFixed(fixnum)}%`
  }
  return !parseFloat((val * 100).toFixed(fixnum)) ? '0%' : `${Math.abs(val * 100).toFixed(fixnum)}%`
}

// 取整带分隔符
const numToInteger = (val, fixnum = 1, divisor = 1, defaultNum = 2, abs = false) => {
  if (!val && val !== 0) {
    return '-'
  }
  if (!abs) {
    if (Math.abs(val / divisor) < 1) {
      // 等于0的不保留小数点
      return !parseFloat(`${(val / divisor).toFixed(defaultNum)}`)
        ? `${Number((val / divisor).toFixed(defaultNum))}`
        : `${(val / divisor).toFixed(defaultNum)}`
    }
    return formatNumberString((val / divisor).toFixed(fixnum))
  }
  if (Math.abs(val / divisor) < 1) {
    // 等于0的不保留小数点
    return !parseFloat(`${(val / divisor).toFixed(defaultNum)}`)
      ? `${Number((val / divisor).toFixed(defaultNum))}`
      : `${Math.abs(val / divisor).toFixed(defaultNum)}`
  }
  return formatNumberString(Math.abs(val / divisor).toFixed(fixnum))
}

// 格式化数字类型 (相比toLocaleString(),不会省略小数点后的0)
const formatNumberString = num => {
  const value = String(num).split('.')
  if (value[1]) {
    return `${value[0].replace(/(?=(\B\d{3})+$)/g, ',')}.${value[1]}`
  }
  return value[0].replace(/(?=(\B\d{3})+$)/g, ',')
}
const numFormat = (val, type, data) => {
  if (type === 'int') {
    return numToInteger(val, ...data)
  }
  if (type === 'per') {
    return numToPercent(val, ...data)
  }
  return val
}

// const install = Vue => {
//   return (Vue.prototype.$numToPercent = numToPercent), (Vue.prototype.$numToInteger = numToInteger)
// }
// export default {
//   install
// }

export { numToPercent, numToInteger, numFormat }
