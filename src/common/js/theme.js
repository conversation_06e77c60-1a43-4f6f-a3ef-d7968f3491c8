/*
 * @Author: shigl
 * @Date: 2023-09-19 17:52:25
 * @LastEditTime: 2023-10-16 14:35:56
 * @Description:
 */
// 主题色,设置手机状态栏颜色用
export function setWebViewBackgroundColor(color = '#fff') {
  if (typeof SFNativeWebView !== 'undefined') {
    var params = {
      callback: res => {
        if (res.errcode === 0) {
          // 成功
          console.log(res.data)
        } else {
          // 失败
          console.log(res.errmsg)
        }
      },
      data: { statusBarColor: color, backgroundColor: color }
    }
    SFNativeWebView.setPageColor(params)
  }
}

const install = Vue => {
  return (Vue.prototype.$setWebViewBackgroundColor = setWebViewBackgroundColor)
}
export default {
  install
}
