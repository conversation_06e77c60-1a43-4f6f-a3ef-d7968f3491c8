/*
 * @Descripttion: <PERSON><PERSON>工具
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-14 10:03:24
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-05-14 10:20:00
 */

/*
 *获取cookie数据
 */
export function getCookie(key) {
  const value = localStorage.getItem(key)
  const expires = localStorage.getItem('expries')
  const time = new Date().getTime()
  if (!expires && !value) {
    return ''
  } else if (time > expires) {
    localStorage.removeItem(key)
    localStorage.removeItem('expries')
    return ''
  } else {
    return value
  }
}

/*
 *设置cookie数据
 */
export function setCookie(key, value) {
  const maxTime = new Date().setHours(23, 59, 59, 59)
  localStorage.setItem('expries', maxTime)
  localStorage.setItem(key, value)
}
