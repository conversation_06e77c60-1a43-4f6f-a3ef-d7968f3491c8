/*
 * @Author: shigl
 * @Date: 2023-04-06 15:34:53
 * @LastEditTime: 2023-10-16 11:55:54
 * @Description:
 */
/*
 * @Author: shigl
 * @Date: 2023-04-06 14:49:09
 * @LastEditTime: 2023-04-06 15:12:25
 * @Description:
 */
import Vue from 'vue'
import * as Sentry from '@sentry/vue'
import { BrowserTracing } from '@sentry/tracing'
const initSentry = (router, userId) => {
  try {
    const sentryConfig = {
      enable: process.env.NODE_ENV !== 'development', // 是否开启sentry接入
      // enable: true, // 是否开启sentry接入
      isPerformance: true, // 是否启用性能监控
      dsn: 'https://<EMAIL>/40' // 上报地址
    }
    const integrations = []
    if (sentryConfig && sentryConfig.enable) {
      // 是否接入 Perfermonce
      sentryConfig.isPerformance &&
        integrations.push(
          new BrowserTracing({
            routingInstrumentation: Sentry.vueRouterInstrumentation(router),
            tracePropagationTargets: ['localhost', 'my-site-url.com', /^\//]
          })
        )
      Sentry.init({
        Vue,
        dsn: `${sentryConfig.dsn}`,
        integrations: integrations,
        tracesSampleRate: 0.5,
        ignoreErrors: [
          /ResizeObserver/,
          /this.ctx.dpr/,
          'ResizeObserver loop limit exceeded',
          'ResizeObserver is not defined',
          'ResizeObserver loop completed with undelivered notifications',
          'Request failed with status code 401',
          /Avoided redundant navigation to current location/
        ]
      })
      Sentry.setUser({ id: userId })
    }
  } catch (error) {
    console.log(error)
  }
}
export default initSentry
