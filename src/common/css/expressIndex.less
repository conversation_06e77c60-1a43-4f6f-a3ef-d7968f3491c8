@red: #dc1e32;
@grey333: #333;
@grey3D3D3D: #3d3d3d;
@grey999: #999;
@grey666: #666;
@greyDDD: #ddd;
@green: #09bca0;
@orange: #ee7335;
@grey414042: #414042;
@font-color-blue: #175adc;
@font-color-sea-green: #08bca0;
@font-color-yellow-green: #b4ec51;
@font-color-l-green: #11cb80;
@font-color-orange: #ff6b17;
@font-color-yellow: #ffcb00;
@chart-bar-l-blue: #27aeff;
@chart-bar-blue: #2e55ec;
@chart-color-green: #09bca0;

@font-face {
  font-family: Roboto-Medium;
  src: url('../fonts/font/Roboto-Medium.ttf');
}
@import '../fonts/iconfont/iconfont.css';
// @import '//at.alicdn.com/t/font_1928874_4bc35hqbf44.css';

&::-webkit-scrollbar {
  // display: none;
  width: 4px;
  height: 2px;
}
&::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 4px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  //   background: #535353;
}
&::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  //   box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  //   border-radius: 10px;
  background: transparent;
}

.ky-feedback-h5 {
  z-index: 9999 !important;
}

.over_touch {
  -webkit-overflow-scrolling: touch;
}

// 页面背景色
.page_bgc {
  background-color: #f5f5f5;
}
// 页面滚动的内容(需指定高度)
.content-wrapper {
  overflow-y: scroll;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}
.red {
  color: @red;
}
.white {
  color: #fff;
}
.grey333 {
  color: @grey333;
}
.grey666 {
  color: @grey666;
}
.grey999 {
  color: @grey999;
}
.grey3D3D3D {
  color: @grey3D3D3D;
}
.greyDDD {
  color: @greyDDD;
}
.grey414042 {
  color: @grey414042;
}
.green {
  color: @green;
}
.orange {
  color: @orange;
}
.chart_green {
  color: @chart-color-green;
}
.chart_blue {
  color: @chart-bar-blue;
}
.chart_orange {
  color: @font-color-orange;
}
.bgc_white {
  background-color: #fff;
}
.bgc_blue {
  background-color: @chart-bar-blue;
}
.bgc_yellow {
  background-color: @font-color-yellow;
}
.bgc_orange {
  background-color: @font-color-orange;
}
.bgc_green {
  background-color: @green;
}
.bgc_data_list {
  background-color: #f8f9fc;
}
.mt4 {
  margin-top: 0.04rem;
}
.ml4 {
  margin-left: 0.04rem;
}
.mr4 {
  margin-right: 0.04rem;
}
.mt6 {
  margin-top: 0.06rem;
}
.ml5 {
  margin-left: 0.05rem;
}
.ml8 {
  margin-left: 0.08rem;
}
.mr8 {
  margin-right: 0.08rem;
}
.mr16 {
  margin-right: 0.16rem;
}
.mt8 {
  margin-top: 0.08rem;
}
.mt10 {
  margin-top: 0.1rem;
}
.ml10 {
  margin-left: 0.1rem;
}
.mr10 {
  margin-right: 0.1rem;
}
.ml12 {
  margin-left: 0.12rem;
}
.mt12 {
  margin-top: 0.12rem;
}
.mt14 {
  margin-top: 0.14rem;
}
.mb4 {
  margin-bottom: 0.04rem;
}
.mb12 {
  margin-bottom: 0.12rem;
}
.mt16 {
  margin-top: 0.16rem;
}
.mb16 {
  margin-bottom: 0.16rem;
}

.ml16 {
  margin-left: 0.16rem;
}
.mr12 {
  margin-right: 0.12rem
}

.mt18 {
  margin-top: 0.18rem;
}

.mt20 {
  margin-top: 0.2rem;
}
.ml20 {
  margin-left: 0.2rem;
}
.mr20 {
  margin-right: 0.2rem;
}
.mb20 {
  margin-bottom: 0.2rem;
}
.ml22 {
  margin-left: 0.22rem;
}
.mt24 {
  margin-top: 0.24rem;
}
.ml24 {
  margin-left: 0.24rem;
}
.mr24 {
  margin-right: 0.24rem;
}
.mb24 {
  margin-bottom: 0.24rem;
}
.mt28 {
  margin-top: 0.28rem;
}
.mt30 {
  margin-top: 0.3rem;
}
.mt32 {
  margin-top: 0.32rem;
}
.mb32 {
  margin-bottom: 0.32rem;
}
.ml32 {
  margin-left: 0.32rem;
}
.mr32 {
  margin-right: 0.32rem;
}
.mt40 {
  margin-top: 0.4rem;
}
.mt48 {
  margin-top: 0.48rem;
}
.ml40 {
  margin-left: 0.4rem;
}
.mt64 {
  margin-top: 0.64rem;
}
.mt68 {
  margin-top: 0.68rem;
}

.pd20 {
  padding: 0.2rem;
  box-sizing: border-box;
}
.pdt24 {
  padding-top: 0.24rem;
  box-sizing: border-box;
}
.pd24 {
  padding: 0.24rem;
  box-sizing: border-box;
}
.pd30 {
  padding: 0.3rem;
  box-sizing: border-box;
}
.pd_l20 {
  padding-left: 0.2rem;
  box-sizing: border-box;
}
.pd_r20 {
  padding-right: 0.2rem;
  box-sizing: border-box;
}
.pd_lr10 {
  padding: 0 0.1rem;
  box-sizing: border-box;
}
.pd_lr20 {
  padding: 0 0.2rem;
  box-sizing: border-box;
}
.pd_lr40 {
  padding: 0 0.4rem;
  box-sizing: border-box;
}
.pd_b20 {
  padding-bottom: 0 0.2rem;
  box-sizing: border-box;
}
.fs16 {
  font-size: 0.16rem;
}
.fs20 {
  font-size: 0.2rem;
}
.fs24 {
  font-size: 0.24rem;
}
.fs26 {
  font-size: 0.26rem;
}
.fs28 {
  font-size: 0.28rem;
}
.fs32 {
  font-size: 0.32rem;
}
.fs36 {
  font-size: 0.38rem;
}
.fs38 {
  font-size: 0.38rem;
}
.fs40 {
  font-size: 0.4rem;
}
.fs48 {
  font-size: 0.48rem;
}
.fs68 {
  font-size: 0.68rem;
}
.fw300 {
  font-weight: 300;
}
.fw500 {
  font-weight: 500;
}
.fw700 {
  font-weight: 700;
}

.ff_rbt {
  font-family: Roboto-Medium;
}
.fr {
  float: right;
}
.tc {
  text-align: center;
}
.tl {
  text-align: left;
}
.tr {
  text-align: right;
}
.fr {
  float: right;
}
.flex0,
.flex_between,
.flex_column_center,
.flex_start,
.flex_end,
.flex_around,
.flex_center,
.flex_col_between,
.flex_column_around,
.flex_column,
.flex_col_around,
.flex_column_between,
.flex_column_end,
.flex_row {
  display: flex;
}

.flex1 {
  flex: 1;
}

.flex_row {
  flex-direction: row;
}
.flex_column {
  flex-direction: column;
}
.flex_center {
  align-items: center;
  justify-content: center;
}
.flex_start {
  align-items: center;
  justify-content: flex-start;
}
.flex_end {
  align-items: center;
  justify-content: flex-end;
}
.flex_around {
  align-items: center;
  justify-content: space-around;
}

.flex_between {
  align-items: center;
  justify-content: space-between;
}

.flex_col_between {
  flex-direction: column;
  justify-content: space-between;
}

.flex_column_center {
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.flex_col_around {
  flex-direction: column;
  justify-content: space-around;
}
.flex_column_end {
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
}

.flex_column_around {
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
}

.flex_column_between {
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.grid2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 0;
}
.grid3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 0;
}
.grid4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 0;
}
.nav_unit {
  font-size: 0.2rem;
  font-weight: 700;
  color: #666;
  line-height: 0.24rem;
}
.ht_chart {
  height: 3.3rem;
  width: 100%;
}
.ky-feedback--icon {
  width: 1rem;
  height: 1rem;
  font-size: 0.2rem;
  bottom: 1.2rem;
  right: 0.3rem;
  z-index: 8888;
  // background: url(./img/feedbacks.png) no-repeat;
  background-size: cover;
  border: 0;
  box-shadow: 0 0.02rem 0.06rem 0.04rem rgba(153, 153, 153, 0.1);
  // pointer-events: none;
  display: none;

  img {
    width: 0.3rem;
    position: relative;
    z-index: 9999;
    opacity: 0;
  }
  p {
    font-size: 0.2rem;
    position: relative;
    opacity: 0;
    z-index: 9999;
  }
}

.fontgreen {
  color: #52cca3;
}

.fontorange {
  color: #ff8066;
}

.fontblue {
  color: #4c83f9;
}

.fontyellow {
  color: #ffbb33;
}

.bggreen {
  background: #52cca3;
}

.bggreen2 {
  background: #a1e6ce;
}

.bgorange {
  background: #ff8066;
}

.bgorange2 {
  background: #febfb3;
}

.bgblue {
  background: #4c83f9;
}

.bgblue2 {
  background: #b3ccff;
}

.bgyellow {
  background: #ffbb33;
}

.bgyellow2 {
  background: #ffe6b3;
}

.bgpurple {
  background: #a07ff3;
}

.bgpurple2 {
  background: #f0b3ff;
}

.fontgrey {
  color: #999;
}

.fontsuccess {
  color: #08bca0 !important;
}
.fontfail {
  color: #ff6a17 !important;
}

.rect {
  width: 0.12rem;
  height: 0.12rem;
  border-radius: 50%;
}

.header-tip {
  font-family: PingFangSC-Regular;
  font-size: 0.2rem;
  color: #999;
  font-weight: 700;
}
// 查看更多
.check-trend {
  font-family: PingFangSC-Medium;
  font-size: 0.24rem;
  color: #dc1e32 !important;
  font-weight: 700;
  display: flex;
  align-items: center;
  div,
  span {
    font-weight: 700;
  }
  i {
    color: #333 !important;
    font-size: 0.2rem;
    margin-left: 0.04rem;
    font-weight: normal;
  }
}

// 排序样式
.table-sort {
  width: 0.28rem;
  height: 0.28rem;
  &.up {
    background: url('../img/srotImg/sortAsc.png') no-repeat;
    background-size: 100%;
  }
  &.down {
    background: url('../img/srotImg/sortDesc.png') no-repeat;
    background-size: 100%;
  }
  &.normal {
    background: url('../img/srotImg/sortNormal.png') no-repeat;
    background-size: 100%;
  }
}
.table-tag-ying {
  background: #effbf7;
  border-radius: 0.04rem;
  color: #08bca0;
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.06rem;
  height: 0.32rem;
}
.table-tag-que {
  background: #fff7ed;
  border-radius: 0.04rem;
  color: #ff6a17;
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.06rem;
  height: 0.32rem;
}
// 排名样式
.table-wrap {
  .normal-rank {
    width: 0.33rem;
    height: 0.33rem;
  }
  .warning-rank {
    width: 0.33rem;
    height: 0.33rem;
  }
  .default-rank {
    width: 0.33rem;
    height: 0.33rem;
  }
  tbody tr {
    td {
      // 表单有拓展时排名样式
      .normal-table-rank-1 {
        background: url('../img/ranking/gold.png') no-repeat;
      }
      .normal-table-rank-2 {
        background: url('../img/ranking/sliver.png') no-repeat;
      }
      .normal-table-rank-3 {
        background: url('../img/ranking/brown.png') no-repeat;
      }
      .normal-table-rank-1,
      .normal-table-rank-2,
      .normal-table-rank-3 {
        background-size: 100%;
        width: 0.38rem;
        height: 0.38rem;
      }
      // 警告前三只有一个样式
      .warning-table-rank {
        background: url('../img/ranking/warning-rank.png') no-repeat;
        background-size: 100%;
        width: 0.38rem;
        height: 0.38rem;
        color: #fff;
        font-size: 0.2rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 0;
      }
      .default-table-rank {
        background: rgba(221, 221, 221, 0.47);
        border: 1px solid rgba(221, 221, 221, 0.47);
        border-radius: 50%;
        font-size: 0.2rem;
        color: #afb7c0;
        width: 0.38rem;
        height: 0.38rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 700;
      }
    }
    td {
      &:first-child {
        .normal-rank,
        .default-rank,
        .warning-rank,
        .normal-rank-next {
          background: rgba(221, 221, 221, 0.47);
          border: 1px solid rgba(221, 221, 221, 0.47);
          border-radius: 50%;
          font-family: PingFangSC-Medium;
          font-size: 0.2rem;
          color: #afb7c0;
          text-align: center;
          line-height: 0.33rem;
          width: 0.33rem;
          height: 0.33rem;
          box-sizing: content-box;
        }
      }
    }
    &:first-child > td {
      &:first-child .normal-rank {
        background: url('../img/ranking/gold.png') no-repeat center;
        background-size: contain;
        border: none;
        width: 0.38rem;
        height: 0.38rem;
        border-radius: 0;
      }
      &:first-child .warning-rank {
        background: url('../img/ranking/warning-rank.png') no-repeat center;
        background-size: 100%;
        border: none;
        width: 0.38rem;
        height: 0.38rem;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 0;
      }
    }
    &:nth-of-type(2) > td {
      &:first-child .normal-rank {
        background: url('../img/ranking/sliver.png') no-repeat center;
        background-size: contain;
        border: none;
        width: 0.38rem;
        height: 0.38rem;
        border-radius: 0;
      }
      &:first-child .normal-rank-next {
        background: url('../img/ranking/gold.png') no-repeat center;
        background-size: contain;
        border: none;
        width: 0.38rem;
        height: 0.38rem;
        border-radius: 0;
      }
      &:first-child .warning-rank {
        background: url('../img/ranking/warning-rank.png') no-repeat center;
        background-size: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border: none;
        width: 0.38rem;
        height: 0.38rem;
        color: #fff;
      }
    }
    &:nth-of-type(3) > td {
      &:first-child .normal-rank {
        background: url('../img/ranking/brown.png') no-repeat center;
        border: none;
        background-size: contain;
        width: 0.38rem;
        height: 0.38rem;
        border-radius: 0;
      }
      &:first-child .normal-rank-next {
        background: url('../img/ranking/sliver.png') no-repeat center;
        border: none;
        background-size: contain;
        width: 0.38rem;
        height: 0.38rem;
        border-radius: 0;
      }
      &:first-child .warning-rank {
        background: url('../img/ranking/warning-rank.png') no-repeat center;
        background-size: 100%;
        border: none;
        width: 0.38rem;
        height: 0.38rem;
        color: #fff;
        border-radius: 0;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    &:nth-child(4) > td {
      &:first-child .normal-rank-next {
        background: url('../img/ranking/brown.png') no-repeat center;
        border: none;
        background-size: contain;
        width: 0.38rem;
        height: 0.38rem;
        border-radius: 0;
      }
    }
  }
  .custom-rank-1 {
    background: url('../img/ranking/gold.png') no-repeat center;
    background-size: contain;
    border: none;
    width: 0.38rem;
    height: 0.38rem;
    border-radius: 0;
  }
  .custom-rank-2 {
    background: url('../img/ranking/sliver.png') no-repeat center;
    border: none;
    background-size: contain;
    width: 0.38rem;
    height: 0.38rem;
    border-radius: 0;
  }
  .custom-rank-3 {
    background: url('../img/ranking/brown.png') no-repeat center;
    border: none;
    background-size: contain;
    width: 0.38rem;
    height: 0.38rem;
    border-radius: 0;
  }
}
.table-default-rank {
  width: 0.34rem;
  height: 0.34rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  background-color: rgba(221, 221, 221, 0.47);
  &.default-color {
    color: #afb7c0;
  }
  &.green-color {
    color: #08bca0;
    font-weight: 700;
  }
  &.red-color {
    color: #dc1e32;
    font-weight: 700;
  }
}

.line_h67 {
  line-height: 0.67rem;
}
.line_h48 {
  line-height: 0.48rem;
}
.line_h45 {
  line-height: 0.45rem;
}
.line_h40 {
  line-height: 0.4rem;
}
.line_h33 {
  line-height: 0.33rem;
}
.line_h30 {
  line-height: 0.3rem;
}
.line_h28 {
  line-height: 0.28rem;
}

.customer-icon-banner {
  > div:first-child {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0.13rem 0;
    > div {
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-family: PingFangSC-Medium;
        font-size: 0.2rem;
        color: #ffffff;
      }
    }
  }
  .customer-icon {
    width: 0.64rem;
    height: 0.64rem;
    margin-bottom: 0.12rem;
  }
  .multi_data_list_wrapper {
    background: none;
  }
}
.preview-container {
  .arrow {
    display: none;
  }
}

.legend_center {
  .common-legend-inline-wrap {
    justify-content: center;
  }
  .common-legend-inline__item {
    justify-content: center;
  }
}
.calendar-select-box {
  height: 0.8rem;
  width: 100%;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f5f5;
}

.tabs-text {
  /deep/.tab_conent {
    border: none !important;
    color: #666 !important;
    min-width: 0.4rem !important;
  }
  /deep/.active {
    background-color: inherit !important;
    color: #dc1e23 !important;
    font-weight: bold;
  }
}

// 头条卡片
.common-index-box {
  width: 100%;
  background: #ffffff;
  padding: 0.08rem 0 0 0;
}
// 按钮底部三角箭头
.btn_sanjiao_tag_indicate {
  position: relative;
  &::after {
    content: '';
    width: 0.32rem;
    height: 0.32rem;
    clip-path: polygon(50% 38%, 0% 100%, 100% 100%);
    background-color: #f5f5f5;
    position: absolute;
    bottom: -0.4rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    transition: all 2s;
  }
}
.line_h33 {
  line-height: 0.33rem;
}

.round_btn_warp {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
}
