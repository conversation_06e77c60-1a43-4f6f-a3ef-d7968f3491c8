import echarts from './echartsUI'
import moment from 'moment'
import DomSize from 'wd-domsize-monitor'

import { numToInteger, numToPercent } from 'common/js/numFormat'
import defaultsdeep from 'lodash.defaultsdeep'
import handleImage from '../img/echarts/handleImage.png'
import { dateFormat, intFormat, perFormat } from './chartUtils'

const defaultPictorial = `image://${require('../img/user_icon.svg')}`
const defaultPictorial2 = `image://${require('../img/user_icon2.svg')}`
const defaultColor = [
  '#4C83F9',
  '#FF8066',
  '#52CCA3',
  '#FFBB33',
  '#A07FF3',
  '#B3CCFF',
  '#FEBFB3',
  '#A1E6CE',
  '#FFE6B3',
  '#F0B3FF'
]
// const defaultRgb = ['76,131,249', '255,128,102', '82,204,163', '255,187,51', '160,127,243']
// const defaultShadowColor = [
//   'rgba(45,85,236,0.50)',
//   'rgba(255,106,23,0.50)',
//   'rgba(82,204,163,0.5)',
//   'rgba(255,187,51,0.50)',
//   '#A07FF3',
//   '#d2544e',
//   '#63bc47',
//   '#f4f5fe',
//   '#71abf6',
//   '#d96e8e'
// ]
// 渐变色
const defaultBarColor = [
  ['#4C83F9', 'rgba(76,131,249,0.1)'],
  ['#FF8066', 'rgba(255,107,23,0.10)'],
  ['#DC1E32', '#FF874F'],
  ['#DC1E32', '#FF874F'],
  ['#DC1E32', '#FF874F']
]
// 背景grid分隔线
const splitLine = {
  show: true,
  lineStyle: {
    color: '#ddd',
    type: 'dashed'
  }
}
const legend = {
  show: false,
  bottom: '5',
  itemWidth: 16,
  itemHeight: 16,
  data: []
}
const grid = {
  top: '5%',
  left: '0',
  right: '0',
  bottom: '2%',
  containLabel: true
}
const nameTextStyle = {
  color: '#999999',
  fontSize: 10
}
// x坐标轴线
const xAxisLine = {
  show: true,
  lineStyle: {
    color: '#F2F2F2'
  }
}
// y坐标轴线
const yAxisLine = {
  show: false
}
// 刻度线
const AxisTick = {
  show: false
}
// x轴文字
const xAxisLabel = {
  show: true,
  interval: 0,
  fontSize: 10,
  color: '#666'
}
// y轴文字
const yAxisLabel = {
  show: true,
  fontSize: 10,
  color: '#666',
  formatter: value => {
    return numToInteger(value, 1)
  }
}
// x轴带箭头的样式rich样式,可结合$valueFormat()换行
const xDataRich = {
  a: {
    fontSize: 10,
    height: 12,
    lineHeight: 12
  },
  b: {
    backgroundColor: {
      image: require('common/img/intoIcon.svg')
    },
    height: 10,
    width: 10
  }
}

const createBaseLineSeries = (options, item, index) => {
  const color = item?.lineStyle?.color || (options?.color && options?.color[index]) || defaultColor[index]
  return {
    type: 'line',
    showSymbol: false,
    symbol: 'emptyCircle',
    symbolSize: 0.1,
    smooth: true, // 平滑
    itemStyle: {},
    emphasis: {
      symbolSize: 7,
      itemStyle: {
        color: '#fff',
        borderWidth: 2,
        borderColor: color
      }
    },
    lineStyle: {
      type: 'solid',
      width: 2,
      color
    },
    data: [],
    zlevel: 15
  }
}
const barSeries = item => {
  const itemStyleFunc = item => {
    if (item?.stack) {
      return {
        borderRadius: 0
      }
    }
    return {
      borderRadius: [2, 2, 2, 2]
    }
  }
  return {
    type: 'bar',
    barWidth: 10,
    data: [],
    // stack: '总量', // 堆叠(需传入)
    label: {
      show: false,
      position: 'inside',
      color: '#fff'
    },
    barGap: '10%',
    showBackground: false,
    backgroundStyle: {
      color: '#f8f9fc'
    },
    itemStyle: {
      ...itemStyleFunc(item)
    },
    zlevel: 5
  }
}

const barShadowStyle = {
  color: {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(221, 221, 221,0)'
      },
      {
        offset: 0.2,
        color: 'rgba(221, 221, 221,0)'
      },
      {
        offset: 0.2,
        color: 'rgba(221, 221, 221,.5)'
      },
      {
        offset: 0.8,
        color: 'rgba(221, 221, 221,.5)'
      },
      {
        offset: 0.8,
        color: 'rgba(221, 221, 221,0)'
      },
      {
        offset: 1,
        color: 'rgba(221, 221, 221,0)'
      }
    ],
    global: false // 缺省为 true
  }
}

// bar多图(有Y轴,可带折线)
const drawMoreBarChart = (options, chartDom, lastBool = false, isNewline = false) => {
  const seriesOptions = []
  if (options.series.length) {
    options.series.forEach((item, index) => {
      if (item.type === 'line') {
        const defaultLineSeries = createBaseLineSeries(options, item, index)
        defaultLineSeries.zlevel -= index
        seriesOptions.push(defaultLineSeries)
      } else {
        seriesOptions.push(barSeries(item))
      }
    })
  }

  const defaultOption = {
    color: defaultColor,
    tooltip: {
      show: true,
      trigger: 'axis',
      tirggerOn: 'click|mousemove',
      axisPointer: {
        type: 'shadow',
        shadowStyle: barShadowStyle,
        zlevel: 0
      },
      formatter: () => {}
    },
    legend,
    grid: {
      top: '5%',
      left: '0',
      right: '0',
      bottom: '0',
      containLabel: true
    },
    xAxis: [
      {
        data: [],
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: {
          show: true,
          interval: 0,
          fontSize: 10,
          color: '#666',
          height: isNewline ? 50 : '',
          lineHeight: 12,
          formatter: value => {
            // 换行
            return isNewline ? newline(value) : value
          },
          rich: xDataRich
        }
      },
      {
        data: [],
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: xAxisLabel
      }
    ],
    yAxis: [
      {
        nameTextStyle,
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: yAxisLabel,
        splitNumber: 2,
        splitLine
      },
      {
        nameTextStyle,
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: yAxisLabel,
        splitNumber: 2,
        splitLine: {
          show: false
        }
      }
    ],
    series: seriesOptions
  }
  const option = defaultsdeep(options, defaultOption)
  return drawChartMain(chartDom, option, lastBool)
}
const customGoalBarSeries = color => {
  return {
    type: 'bar',
    barWidth: 10,
    data: [],
    color: color,
    label: {
      show: false,
      position: 'inside',
      color: '#fff'
    },
    barGap: '50%',
    // barCategoryGap: '50%',
    showBackground: true,
    backgroundStyle: {
      color: '#f8f9fc'
    },
    zlevel: 5
  }
}
const customGoalLineSeries = (color, align) => {
  return {
    type: 'custom',
    name: 'Average',
    renderItem: (params, api) => {
      const bandWidth = 14
      const point = api.coord([api.value(0), api.value(1)])
      const alignObj = {
        left: {
          x1: point[0] - bandWidth - 1,
          x2: point[0],
          y1: point[1],
          y2: point[1]
        },
        right: {
          x1: point[0],
          x2: point[0] + bandWidth + 1,
          y1: point[1],
          y2: point[1]
        }
      }
      return {
        type: 'line',
        transition: ['shape'],
        shape: alignObj[align],
        style: api.style({
          fill: null,
          stroke: color,
          lineWidth: 2
        })
      }
    },
    encode: {
      x: 0,
      y: 1
    },
    data: [],
    zlevel: 5
  }
}

// 带目标值的柱状图(铆钉)
const drawCustomGoalBarChart = (options, chartDom, lastBool = false, isNewline = false) => {
  const colorList = ['#4C83F9', '#2B56B1', '#FF8066', '#C5442A']
  const seriesOptions = []
  if (options.series.length) {
    options.series.forEach((item, index) => {
      switch (index) {
        case 1:
          seriesOptions.push(customGoalLineSeries(item.color || colorList[index], 'left'))
          break
        case 3:
          seriesOptions.push(customGoalLineSeries(item.color || colorList[index], 'right'))
          break
        default:
          seriesOptions.push(customGoalBarSeries(item.color || colorList[index]))
          break
      }
    })
  }
  const defaultOption = {
    color: defaultColor,
    tooltip: {
      show: true,
      trigger: 'axis',
      tirggerOn: 'click|mousemove',
      axisPointer: {
        type: 'shadow',
        shadowStyle: barShadowStyle,
        zlevel: 0
      },
      formatter: () => {}
    },
    legend,
    grid: {
      top: '5%',
      left: '0',
      right: '0',
      bottom: '0',
      containLabel: true
    },
    xAxis: [
      {
        data: [],
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: {
          show: true,
          interval: 0,
          fontSize: 10,
          color: '#666',
          height: isNewline ? 50 : '',
          lineHeight: isNewline ? 12 : '',
          formatter: value => {
            // 换行
            return isNewline ? newline(value) : value
          },
          rich: xDataRich
        }
      },
      {
        data: [],
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: xAxisLabel
      }
    ],
    yAxis: [
      {
        nameTextStyle,
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: yAxisLabel,
        splitNumber: 2,
        splitLine
      },
      {
        nameTextStyle,
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: yAxisLabel,
        splitNumber: 2,
        splitLine: {
          show: false
        }
      }
    ],
    series: seriesOptions
  }
  const option = defaultsdeep(options, defaultOption)
  return drawChartMain(chartDom, option, lastBool)
}
const onebarSeries = item => {
  const itemStyleFunc = item => {
    if (item?.stack) {
      return {
        borderRadius: 0
      }
    }
    return {
      borderRadius: [2, 2, 2, 2]
    }
  }
  return {
    type: 'bar',
    barWidth: 10,
    data: [],
    // stack: '总量', // 堆叠(需传入)
    label: {
      show: false,
      position: 'inside',
      color: '#fff'
    },
    barGap: '10%',
    showBackground: true,
    backgroundStyle: {
      color: '#f8f9fc'
    },
    itemStyle: {
      ...itemStyleFunc(item)
    },
    zlevel: 5
  }
}

// 单独的bar图
const drawBarChart = (options, chartDom, colorList = [], isNewline = false, lastBool = false) => {
  const seriesOptions = []
  if (options.series.length) {
    options.series.forEach((item, index) => {
      seriesOptions.push(onebarSeries(item))
    })
  }
  const defaultOption = {
    color: defaultColor,
    tooltip: {
      show: false,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(221, 221, 221,0)'
              },
              {
                offset: 0.2,
                color: 'rgba(221, 221, 221,0)'
              },
              {
                offset: 0.2,
                color: 'rgba(221, 221, 221,.5)'
              },
              {
                offset: 0.8,
                color: 'rgba(221, 221, 221,.5)'
              },
              {
                offset: 0.8,
                color: 'rgba(221, 221, 221,0)'
              },
              {
                offset: 1,
                color: 'rgba(221, 221, 221,0)'
              }
            ],
            global: false // 缺省为 true
          }
        },
        zlevel: 0
      }
    },
    legend,
    grid: {
      top: '15%',
      left: '0',
      right: '0',
      bottom: isNewline ? 32 : '15%',
      containLabel: false
    },
    xAxis: [
      {
        data: [],
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: {
          show: true,
          interval: 0,
          fontSize: 10,
          color: '#666',
          height: isNewline ? 50 : '',
          lineHeight: 12,
          formatter: value => {
            // 换行
            return isNewline ? newline(value) : value
          },
          rich: xDataRich
        }
      },
      {
        data: [],
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: {
          show: true,
          color: '#333',
          // fontWeight: '700',
          fontSize: 10,
          fontFamily: 'Roboto-Medium',
          interval: 0,
          formatter: value => {
            return numToInteger(value, 0)
          }
        }
      }
    ],
    yAxis: [
      {
        axisLine: yAxisLine,
        axisTick: AxisTick,
        axisLabel: yAxisLabel,
        splitNumber: 3,
        splitLine
      },
      {
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: yAxisLabel,
        splitNumber: 3,
        splitLine: {
          show: false
        }
      }
    ],
    series: seriesOptions
  }
  const option = defaultsdeep(options, defaultOption)
  return drawChartMain(chartDom, option, lastBool)
}
// line折线图
const drawLineChart = (options, chartDom, obj = {}) => {
  const { lastBool = true, dateIndex = 'null', isWeek = false } = obj
  const seriesOptions = []

  if (options.series.length) {
    options.series.forEach((item, index) => {
      const defaultLineSeries = createBaseLineSeries(options, item, index)
      // 线的层级展示
      defaultLineSeries.zlevel -= index
      seriesOptions.push(defaultLineSeries)
    })
  }
  const defaultOption = {
    color: options.color || defaultColor,
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#d71c2c',
          type: 'dotted',
          width: 1.5,
          opacity: 1
        },
        zlevel: 0
      }
    },
    legend,
    grid,
    xAxis: [
      {
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: {
          ...xAxisLabel,
          ...(dateIndex !== 'null'
            ? {
                formatter: value => dateFormat(value, dateIndex, isWeek)
              }
            : {})
        }
      },
      {
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: {
          ...xAxisLabel,
          formatter: value => numToInteger(value, 1)
        }
      },
      {
        axisTick: AxisTick,
        axisLine: {
          show: false
        },
        axisLabel: xAxisLabel
      }
    ],
    yAxis: [
      {
        axisLine: yAxisLine,
        axisTick: AxisTick,
        axisLabel: {
          ...yAxisLabel,
          formatter: value => intFormat(value, 1)
        },
        splitNumber: 3,
        splitLine
      },
      {
        axisLine: yAxisLine,
        axisTick: AxisTick,
        axisLabel: {
          ...yAxisLabel,
          formatter: value => perFormat(value)
        },
        splitNumber: 3,
        splitLine: {
          show: false
        }
      }
    ],
    series: seriesOptions
  }

  const option = defaultsdeep(options, defaultOption)
  drawChartMain(chartDom, option, lastBool)
}

const oneLineSeries = color => {
  return {
    type: 'line',
    showSymbol: false,
    symbol: 'emptyCircle',
    symbolSize: 0.1,
    smooth: true,
    itemStyle: {
      color: color[0],
      borderWidth: 2
    },
    emphasis: {
      symbolSize: 7,
      itemStyle: {
        color: '#fff',
        borderWidth: 2
      }
    },
    lineStyle: {
      type: 'solid',
      width: 2
    },
    areaStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: color[0] },
        { offset: 1, color: color[1] }
      ])
    },
    data: [],
    zlevel: 10
  }
}

// 一条折线图
const drawOneLineChart = (options, chartDom, lastBool = true, colorIndex = 0) => {
  const seriesOptions = []
  if (options.series.length) {
    options.series.forEach((item, index) => {
      seriesOptions.push(oneLineSeries(defaultBarColor[colorIndex]))
    })
  }
  const defaultOption = {
    color: options.color || defaultColor,
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#d71c2c',
          type: 'dotted',
          width: 1.5,
          opacity: 1
        },
        zlevel: 0
      }
    },
    legend,
    grid,
    xAxis: [
      {
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: xAxisLabel
      },
      {
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: xAxisLabel
      }
    ],
    yAxis: [
      {
        axisLine: yAxisLine,
        axisTick: AxisTick,
        axisLabel: yAxisLabel,
        splitNumber: 3,
        splitLine
      },
      {
        axisLine: yAxisLine,
        axisTick: AxisTick,
        axisLabel: yAxisLabel,
        splitNumber: 3,
        splitLine: {
          show: false
        }
      }
    ],
    series: seriesOptions
  }
  const option = defaultsdeep(options, defaultOption)
  drawChartMain(chartDom, option, lastBool)
}

// 饼图
const drawPieChart = (options, chartDom) => {
  const defaultOption = {
    color: defaultColor,
    tooltip: {
      show: false
    },
    grid: {
      containLabel: true
    },
    series: [
      {
        type: 'pie',
        radius: ['45%', '70%'],
        minAngle: 10, // 设置每块扇形的最小占比
        itemStyle: {
          shadowColor: 'rgba(255, 255, 255,0.1)',
          shadowBlur: 5
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [],
        emptyCircleStyle: {
          color: '#fff',
          opacity: 0
        }
      },
      {
        type: 'pie',
        radius: ['45%', '75%'],
        minAngle: 10, // 设置每块扇形的最小占比
        itemStyle: {
          opacity: 0
        },
        label: {
          show: true,
          alignTo: 'right',
          position: 'outside',
          fontWeight: 'bold',
          color: '#414042'
        },
        labelLine: {
          show: true
        },
        data: [],
        emptyCircleStyle: {
          color: '#fff',
          opacity: 0
        }
      }
    ]
  }
  const option = defaultsdeep(options, defaultOption)
  drawChartMain(chartDom, option)
}

const horizonSeries = () => {
  return {
    barWidth: 16,
    itemStyle: {
      borderRadius: 0
      //   color: color
    },
    showBackground: false
  }
}

// y轴为类目轴的柱状图(横向bar图)
const drawHorizonBarChart = (options, chartDom, zoom = false, num = 8) => {
  const seriesOptions = []
  if (options.series.length) {
    options.series.forEach((item, index) => {
      seriesOptions.push(JSON.parse(JSON.stringify(horizonSeries())))
    })
  }
  const len = options?.yAxis?.[0]?.data?.length
  const dataZoom = zoom
    ? {
        dataZoom: [
          {
            filterMode: 'none',
            type: 'inside',
            id: 'insideY',
            yAxisIndex: 0,
            startValue: len - 1,
            endValue: len - 8,
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true,
            throttle: 100,
            zoomLock: true
          },
          {
            filterMode: 'none',
            type: 'inside',
            id: 'insideY2',
            yAxisIndex: 1,
            startValue: len - 1,
            endValue: len - 8,
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true,
            throttle: 100,
            zoomLock: true
          }
        ]
      }
    : {}
  const defaultOption = {
    color: defaultColor,
    ...dataZoom,
    xAxis: [
      {
        type: 'value',
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        },
        splitNumber: 3
      },
      {
        show: false,
        splitLine: {
          type: 'dashed'
        }
      }
    ],
    grid: {
      containLabel: true,
      bottom: 0,
      top: 10
    },
    yAxis: [
      {
        type: 'category',
        axisLabel: {
          formatter: val => val,
          fontSize: 12
        },
        splitLine: {
          show: false
        }
      },
      {
        axisLine: {
          lineStyle: {
            type: 'dashed'
          }
        },
        axisLabel: {
          fontSize: 12,
          color: '#333',
          fontFamily: 'PingFangSC-Medium'
        }
      }
    ],
    series: seriesOptions
  }
  const option = defaultsdeep(options, defaultOption)
  drawBarChart(option, chartDom)
}

// 圆环ring,单数
const drawRingChart = (options, chartDom, color) => {
  const defaultOption = {
    title: {
      text: '目标达成率', // 标题
      textStyle: {
        color: ' #999999',
        fontSize: 12,
        fontWeight: 300
      },
      subtext: '-', // 副标题
      subtextStyle: {
        color: '#3D3D3D',
        fontWeight: 'bold',
        fontSize: 16
      },
      itemGap: 5, // 主副标题距离
      left: 'center',
      top: '30%'
    },
    angleAxis: {
      max: 100, // 满分
      clockwise: true, // 顺时针
      // 隐藏刻度线
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    radiusAxis: {
      type: 'category',
      // 隐藏刻度线
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    polar: {
      center: ['50%', '50%'],
      radius: '160%' // 图形大小
    },
    series: [
      {
        type: 'bar',
        data: [
          {
            value: '',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: color ? color[0] : '#FF954F'
                },
                {
                  offset: 1,
                  color: color ? color[1] : '#F44618 '
                }
              ])
            }
          }
        ],
        coordinateSystem: 'polar',
        roundCap: true,
        barWidth: 9,
        barGap: '-100%', // 两环重叠
        zlevel: 2
      },
      {
        // 灰色环
        type: 'bar',
        data: [
          {
            value: 100,
            itemStyle: {
              color: '#e2e2e2'
            }
          }
        ],
        coordinateSystem: 'polar',
        roundCap: true,
        barWidth: 9,
        barGap: '-100%', // 两环重叠
        zlevel: 1
      }
    ]
  }
  const option = defaultsdeep(options, defaultOption)
  drawChartMain(chartDom, option)
}

// 半圆进度条(默认绿色 colorIndex=1为橙色)
const drawGaugeChart = (options, chartDom, colorIndex = 0) => {
  const itemColorList = [
    ['rgba(82,204,163,0.5)', 'rgba(82,204,163,1)'],
    ['rgba(255,128,102,0.5)', 'rgba(255,128,102,1)']
  ][colorIndex]
  const defaultSeries = {
    center: ['50%', '50%'],
    type: 'gauge',
    name: '项目名称',
    radius: '100%',
    splitNumber: 4,
    startAngle: 180,
    endAngle: 0,
    min: 0,
    max: 100,
    // 仪表盘之间的分割线
    splitLine: {
      show: false,
      length: 7,
      lineStyle: {
        color: 'auto',
        width: 1
      }
    },
    // 仪表盘之间的刻度
    axisTick: {
      show: false,
      length: 0,
      lineStyle: {
        color: 'auto'
      }
    },
    axisLabel: {
      show: false,
      color: '#454a55',
      distance: 2,
      fontSize: 12,
      fontFamily: 'PingFangSC-Regular'
    },
    pointer: { show: false, length: 20, width: 2 },
    axisLine: {
      lineStyle: {
        width: 6,
        color: [
          [0, itemColorList[0]],
          [1, itemColorList[1]]
        ]
      }
    },
    data: [
      {
        value: 0
      }
    ],
    detail: {
      show: false,
      offsetCenter: [0, -6],
      rich: {
        name: {
          color: '#666'
        },
        value: {
          fontFamily: 'Roboto-Medium',
          fontWeight: 400,
          fontSize: 16,
          lineHeight: 24,
          color: '#3D3D3D'
        }
      }
    }
  }
  const series = options.series.map(item => defaultsdeep(item, defaultSeries))
  const option = {
    series
  }

  drawChartMain(chartDom, option)
}
// 带指针的仪表盘图(默认绿色 colorIndex=1为橙色)
const drawGaugeRateChart = (options, chartDom, colorIndex = 0) => {
  const itemColor = ['#FF8066', '#52CCA3'][colorIndex]
  const itemColorList = [
    ['rgba(255,128,102,0.5)', 'rgba(255,128,102,1)'],
    ['rgba(82,204,163,0.5)', 'rgba(82,204,163,1)']
  ][colorIndex]
  const defaultSeries = [
    {
      type: 'gauge',
      center: ['50%', '75%'],
      radius: '90%',
      startAngle: 160,
      endAngle: 20,
      min: 0,
      max: 1,
      splitNumber: 5,
      itemStyle: {
        color: itemColor
      },
      progress: {
        show: false,
        width: 10
      },
      pointer: {
        show: true,
        itemStyle: {
          color: '#646C7D'
        },
        length: '90%',
        width: 3,
        icon:
          'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z'
      },
      anchor: {
        show: true,
        size: 6,
        itemStyle: {
          color: '#2E3F63;',
          opacity: 0.5
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          width: 30
        }
      },
      axisTick: {
        distance: -38,
        splitNumber: 3,
        lineStyle: {
          width: 1,
          color: itemColor
        },
        length: 4
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      title: {
        show: false
      },
      detail: {
        show: false
      },
      data: [
        {
          value: 0
        }
      ]
    },
    {
      type: 'gauge',
      center: ['50%', '75%'],
      radius: '90%',
      startAngle: 160,
      endAngle: 20,
      min: 0,
      max: 1,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 1,
          colorStops: [
            {
              offset: 0,
              color: itemColorList[0] // 0% 处的颜色
            },
            {
              offset: 1,
              color: itemColorList[1] // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      },
      progress: {
        show: true,
        width: 10
      },
      pointer: {
        show: false
      },
      anchor: {
        show: true,
        showAbove: true,
        size: 2
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      detail: {
        show: false
      },
      data: [
        {
          value: 1
        }
      ],
      zlevel: 0
    },
    {
      type: 'gauge',
      center: ['50%', '75%'],
      radius: '90%',
      startAngle: 180,
      endAngle: -0,
      min: 0,
      max: 1,
      progress: {
        show: false,
        width: 10
      },
      pointer: {
        show: false
      },
      anchor: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: true,
        fontSize: 10,
        distance: -32,
        padding: [0, 0, 0, 10],
        color: '#999',
        formatter: value => {
          if (!value || value === 1) {
            return numToPercent(value)
          }
          return ''
        }
      },
      detail: {
        show: false
      },
      data: [
        {
          value: 1
        }
      ],
      zlevel: 0
    }
  ]

  const series = defaultsdeep(options.series, defaultSeries)

  const option = {
    series
  }
  drawChartMain(chartDom, option)
}
const scrollBarSeries = item => {
  const itemStyleFunc = item => {
    if (item?.stack) {
      return {
        borderRadius: 0
      }
    }
    return {
      borderRadius: [2, 2, 2, 2]
    }
  }
  return {
    type: 'bar',
    barWidth: 'auto',
    barMaxWidth: 10,
    itemStyle: {
      ...itemStyleFunc(item)
    },
    // emphasis: {
    //   itemStyle: {
    //     color: `rgb(${color})`
    //   }
    // },
    zlevel: 5
  }
}

// 绘制带有滚动逻辑的图表
const drawScrollChart = (options, chartDom, obj = {}) => {
  const {
    lastBool = true,
    dateType = 'day',
    extraxAxis = [],
    dateIndex = 'null',
    isWeek = false,
    weekType = 'GGGGWW',
    mounts,
    isOneLine = false, // 是否只有一条折线
    colorIndex = 0 // 只有一条折线的样式(0: blue, 1: orange)
  } = obj
  const startValueMap = {
    day: mounts || 7,
    month: mounts || 12
  }
  const maxValueMap = {
    day: 34,
    month: 23
  }
  const seriesOptions = []
  const xAxisLength = ((options.xAxis[0] || {}).data || []).length
  if (options.series.length) {
    options.series.forEach((item, index) => {
      if (item.type === 'line') {
        if (isOneLine) {
          seriesOptions.push(oneLineSeries(defaultBarColor[colorIndex]))
        } else {
          const defaultLineSeries = createBaseLineSeries(options, item, index)
          defaultLineSeries.zlevel -= index
          seriesOptions.push(defaultLineSeries)
        }
      } else {
        seriesOptions.push(scrollBarSeries(item))
      }
    })
  }

  const hasJanuary = dateType === 'month' && options.xAxis[0].data.some(item => moment(item).format('MM') === '01')
  const defaultOption = {
    color: defaultColor,
    tooltip: {
      show: true,
      trigger: 'axis',
      confine: true,
      transitionDuration: 0,
      // triggerOn: 'none',
      formatter: () => {}
    },
    xAxis: [
      {
        data: [],
        axisPointer: {
          triggerEvent: true,
          snap: true,
          type: 'line',
          lineStyle: {
            color: '#d71c2c',
            type: 'dotted',
            width: 1.5
          },
          label: {
            show: true,
            color: '#333',
            backgroundColor: 'none',
            fontSize: 10,
            formatter: params =>
              Object.prototype.toString.call(options.xAxis[0].axisLabel.formatter) === '[object Function]'
                ? options.xAxis[0].axisLabel.formatter.call(this, params.value)
                : params.value
          },
          handle: {
            show: true,
            icon: `image://${handleImage}`,
            color: '#fff',
            size: [28, 30],
            margin: hasJanuary ? 50 : 34,
            shadowColor: 'rgba(184,186,190,0.50)',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowBlur: 0
          },
          zlevel: 2
        },
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: {
          ...xAxisLabel,
          interval: 'auto',
          color: 'rgba(102,102,102,0.7)',
          ...(dateIndex !== 'null'
            ? {
                formatter: value => dateFormat(value, dateIndex, isWeek, weekType)
              }
            : {})
        }
      },
      {
        data: [],
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: xAxisLabel
      }
    ],
    legend,
    yAxis: [
      {
        nameTextStyle,
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: {
          ...yAxisLabel,
          formatter: value => intFormat(value, 1)
        },
        splitNumber: 2,
        splitLine
      },
      {
        nameTextStyle,
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: {
          ...yAxisLabel,
          formatter: value => perFormat(value)
        },
        splitNumber: 2,
        splitLine: {
          show: false
        }
      }
    ],
    grid: {
      bottom: 34,
      containLabel: true,
      top: '5%',
      left: 0,
      right: 10
    },
    dataZoom: [
      {
        type: 'slider',
        startValue: lastBool ? xAxisLength - startValueMap[dateType] : 0,
        endValue: lastBool ? xAxisLength : startValueMap[dateType],
        handleSize: 0,
        height: 0,
        backgroundColor: 'rgba(221, 221, 221, 0.9)',
        fillerColor: 'rgba(221, 221, 221, 0.6)',
        borderColor: 'rgba(221, 221, 221, 0.6)',
        dataBackground: {
          areaStyle: 'rgba(221, 221, 221, 0.9)'
        },
        xAxisIndex: 1,
        moveHandleSize: 0,
        shadowColor: 'rgba(221, 221, 221, 0.9)',
        shadowOffsetX: 10,
        shadowBlur: 10,
        bottom: hasJanuary ? 28 : 24,
        left: 0,
        right: 0,
        zlevel: 0
      },
      {
        type: 'inside',
        startValue: lastBool ? xAxisLength - startValueMap[dateType] : 0,
        endValue: lastBool ? xAxisLength : startValueMap[dateType],
        minValueSpan: startValueMap[dateType],
        maxValueSpan: maxValueMap[dateType],
        preventDefaultMouseMove: false, // 默认移动事件，防止页面不能上下滑动
        throttle: 50
      }
    ],
    series: seriesOptions
  }
  if (hasJanuary) {
    const extraData = extraxAxis.length ? extraxAxis : options.xAxis[0].data
    const xAxis3 = {
      position: 'bottom',
      data: extraData,
      axisPointer: { show: false },
      offset: 14,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        interval: 0,
        formatter: val => {
          if (moment(val).format('YYYYMM') === moment(val).format('YYYY01')) {
            return `{a|${moment(val).format('YYYY')}}`
          } else {
            return ''
          }
        },
        rich: {
          a: {
            fontSize: 8,
            // height: 10,
            // width: 20,
            // lineHeight: 10,
            backgroundColor: '#4C83F9',
            color: '#fff',
            fontWeight: 500,
            borderRadius: 2,
            padding: 2
          }
        }
      }
    }
    defaultOption.xAxis.push(xAxis3)
  }
  const option = defaultsdeep(options, defaultOption)
  return drawChartMain(chartDom, option, lastBool)
}

const drawPictorialChart = (options, chartDom, imgSrc = [defaultPictorial, defaultPictorial2]) => {
  const defaultOption = {
    tooltip: {
      show: false
    },
    xAxis: {
      type: 'value',
      show: false
    },
    yAxis: {
      type: 'category',
      show: false
    },
    grid: {
      top: 'center',
      left: 0,
      right: 0
    },
    series: [
      {
        // current data
        type: 'pictorialBar',
        symbol: imgSrc[0],
        symbolRepeat: 8,
        symbolMargin: '50%',
        symbolClip: true,
        symbolSize: 16,
        symbolBoundingData: 1,
        data: [0.94],
        zlevel: 10
      },
      {
        // full data
        type: 'pictorialBar',
        itemStyle: {
          opacity: 0.1
        },
        animationDuration: 0,
        symbolRepeat: 8,
        symbolMargin: '50%',
        symbol: imgSrc[1],
        symbolSize: 16,
        symbolBoundingData: 1,
        data: [1],
        zlevel: 5
      }
    ]
  }
  const option = defaultsdeep(options, defaultOption)
  drawChartMain(chartDom, option)
}

// 绘制新版仪表盘图表
const drawNewGaugeChart = (options, chartDom, obj = {}) => {
  const {
    center = ['50%', '98%'],
    radius = '183%',
    value = 0,
    name = '目标达成率',
    pointerRadius = '-70%',
    showDetail = false
  } = obj
  let radiusNum = 100
  if (/%/.test(radius)) {
    radiusNum = radius.replace('%', '')
  }
  let pointerRadiusNum = -60
  if (/%/.test(pointerRadius)) {
    pointerRadiusNum = pointerRadius.replace('%', '')
  }

  const defaultOption = {
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        center: center,
        radius: radiusNum + '%',
        progress: {
          show: true,
          width: 20
        },
        pointer: {
          show: false
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 20,
            color: [[1, '#f9f9f9']],
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 2
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        title: {
          show: true
        },
        itemStyle: {
          opacity: 0
        },
        detail: {
          show: showDetail,
          offsetCenter: [0, '-20%'],
          formatter: [`{name|${name}}`, `{value|${value ? value.toFixed(1) : value}}{rate|%}`].join('\n'),
          rich: {
            name: {
              color: '#999'
            },
            value: {
              fontWeight: 700,
              fontSize: 16,
              padding: [-20, 0, 0, 0],
              color: value >= 100 ? '#52CCA3' : '#FF8066'
            },
            rate: {
              fontSize: 12,
              fontWeight: 700,
              padding: [-20, 0, 0, 0],
              color: value >= 100 ? '#52CCA3' : '#FF8066'
            }
          }
        },
        data: [
          {
            value: value
          }
        ],
        zlevel: 0
      },
      {
        type: 'gauge',
        center: center,
        radius: radiusNum - 10 + '%',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 11,
        itemStyle: {
          color: value >= 100 ? '#52CCA3' : '#FF8066'
        },
        progress: {
          show: true,
          width: 12
        },
        pointer: {
          show: false
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 12,
            color: [[1, '#becbe1']]
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          show: false
        },
        data: [
          {
            value: value
          }
        ],
        zlevel: 1
      },
      {
        type: 'gauge',
        center: center,
        radius: radiusNum + '%',
        startAngle: 170,
        endAngle: 10,
        min: 0,
        max: 100,
        progress: {
          show: false
        },
        pointer: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisTick: {
          splitNumber: 1,
          distance: -4,
          length: 6,
          lineStyle: {
            width: 1.5,
            color: '#fff'
          }
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        title: {
          show: false
        },
        zlevel: 2
      },
      {
        type: 'gauge',
        center: center,
        radius: radiusNum - 60 + '%',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            color: [[1, '#f9f9f9']],
            width: 1
            // shadowColor: 'rgba(0,0,0,0.15)',
            // shadowBlur: 2,
            // shadowOffsetY: -1
          }
        },

        progress: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        pointer: {
          show: false
        },
        title: {
          show: false
        },
        anchor: {
          show: false
        },

        detail: {
          show: false
        },
        zlevel: 4
      },
      {
        type: 'gauge',
        center: center,
        radius: radiusNum - 5 + '%',
        min: 0,
        max: 100,
        startAngle: 180,
        endAngle: 0,
        progress: {
          show: false
        },

        pointer: {
          icon: 'roundRect',
          length: '20%',
          width: 5,
          showAbove: true,
          offsetCenter: [0, `${pointerRadiusNum}%`],
          itemStyle: {
            color: '#fff',
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 5
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          show: false
        },
        data: [
          {
            value: value
          }
        ],
        zlevel: 5
      }
    ]
  }
  const option = defaultsdeep(options, defaultOption)
  drawChartMain(chartDom, option, false)
}
// x轴标签换行(大于4个字换行,两行) provideNumber默认4个字
const newline = (params, provideNumber = 4) => {
  // 显示结果
  let newParamsName = ''
  // 文字字数
  const paramsNameNumber = params.length

  // 行数
  const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
  let paramValue = ''
  if (paramsNameNumber > provideNumber * 2) {
    paramValue = `${params.substring(0, provideNumber * 2 - 1)}…`
  } else {
    paramValue = params
  }
  for (let row = 0; row < rowNumber; row++) {
    // 小于等于4个字不做换行处理
    if (paramsNameNumber <= provideNumber || row) {
      newParamsName += `${paramValue.substring(row * provideNumber, (row + 1) * provideNumber)}`
    } else {
      newParamsName += `${paramValue.substring(row * provideNumber, (row + 1) * provideNumber)}\n`
    }
  }
  return newParamsName
}
// y双轴的0点位置相等,在传入的option自行配置
const yAixsOption = {
  max: value => {
    if (Math.abs(value.max) > Math.abs(value.min)) {
      return (Math.abs(value.max) * 1.2).toFixed(2)
    } else {
      return (Math.abs(value.min) * 1.2).toFixed(2)
    }
  },
  min: value => {
    if (Math.abs(value.max) > Math.abs(value.min)) {
      return (-Math.abs(value.max) * 1.2).toFixed(2)
    } else {
      return (-Math.abs(value.min) * 1.2).toFixed(2)
    }
  }
}

// 绘制图表
const drawChartMain = (chartDom, option, lastBool) => {
  if (!chartDom || !option) return
  if (chartDom.hasAttributes('_echarts_instance_')) {
    chartDom.removeAttribute('_echarts_instance_')
  }
  if (!chartDom?.offsetWidth) return
  const chartInstance = echarts.init(chartDom, null, {
    // devicePixelRatio: 2,
    renderer: 'svg'
  })
  chartInstance.setOption(option, { notMerge: true })
  if (option?.series?.length) {
    option?.series.forEach((item, index) => {
      if (item?.data) {
        const dataIndex = item.data.length - 1
        chartInstance.dispatchAction({
          type: 'showTip',
          seriesIndex: index,
          dataIndex: lastBool ? dataIndex : 0
        })
        return
      }
    })
  }

  DomSize.bind(chartDom, callback => {
    chartInstance.resize()
  })
  // DomSize.remove(chartDom)

  return chartInstance
}
const drawScrollChartNew = (options, chartDom, obj = {}) => {
  const {
    lastBool = true,
    // dateType = 'day',
    // extraxAxis = [],
    dateIndex = 'null',
    isWeek = false,
    weekType = 'GGGGWW',
    // mounts,
    isOneLine = false, // 是否只有一条折线
    colorIndex = 0 // 只有一条折线的样式(0: blue, 1: orange)
  } = obj

  // 配置默认选中第几个点
  const targetIndex = options.xAxis[0].data.length - 2 // 这里默认选中倒数第二个点

  const seriesOptions = []
  const xAxisLength = ((options.xAxis[0] || {}).data || []).length
  if (options.series.length) {
    options.series.forEach((item, index) => {
      if (item.type === 'line') {
        if (isOneLine) {
          seriesOptions.push(oneLineSeries(defaultBarColor[colorIndex]))
        } else {
          const defaultLineSeries = createBaseLineSeries(options, item, index)
          defaultLineSeries.zlevel -= index
          seriesOptions.push(defaultLineSeries)
        }
      } else {
        seriesOptions.push(scrollBarSeries(item))
      }
    })
  }

  const defaultOption = {
    color: defaultColor,
    tooltip: {
      show: true,
      trigger: 'axis',
      confine: true,
      transitionDuration: 0,
      formatter: () => {}
    },
    xAxis: [
      {
        data: [],
        axisPointer: {
          triggerEvent: true,
          snap: true,
          type: 'line',
          lineStyle: {
            color: '#d71c2c',
            type: 'dotted',
            width: 1.5
          },
          label: {
            show: true,
            color: '#333',
            backgroundColor: 'none',
            fontSize: 10,
            formatter: params =>
              Object.prototype.toString.call(options.xAxis[0].axisLabel.formatter) === '[object Function]'
                ? options.xAxis[0].axisLabel.formatter.call(this, params.value)
                : params.value
          },
          handle: {
            show: true,
            icon: `image://${handleImage}`,
            color: '#fff',
            size: [28, 30],
            margin: 34,
            shadowColor: 'rgba(184,186,190,0.50)',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowBlur: 0
          },
          zlevel: 2
        },
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: {
          ...xAxisLabel,
          interval: 'auto',
          color: 'rgba(102,102,102,0.7)',
          ...(dateIndex !== 'null'
            ? {
                formatter: value => dateFormat(value, dateIndex, isWeek, weekType)
              }
            : {})
        }
      },
      {
        data: [],
        axisTick: AxisTick,
        axisLine: xAxisLine,
        axisLabel: xAxisLabel
      }
    ],
    legend,
    yAxis: [
      {
        nameTextStyle,
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: {
          ...yAxisLabel,
          formatter: value => intFormat(value, 1)
        },
        splitNumber: 2,
        splitLine
      },
      {
        nameTextStyle,
        axisLine: xAxisLine,
        axisTick: AxisTick,
        axisLabel: {
          ...yAxisLabel,
          formatter: value => perFormat(value)
        },
        splitNumber: 2,
        splitLine: {
          show: false
        }
      }
    ],
    grid: {
      bottom: 34,
      containLabel: true,
      top: '5%',
      left: 0,
      right: 10
    },
    dataZoom: [
      {
        type: 'slider',
        startValue: lastBool ? xAxisLength - 7 : 0,
        endValue: lastBool ? xAxisLength : 7,
        handleSize: 0,
        height: 0,
        backgroundColor: 'rgba(221, 221, 221, 0.9)',
        fillerColor: 'rgba(221, 221, 221, 0.6)',
        borderColor: 'rgba(221, 221, 221, 0.6)',
        dataBackground: {
          areaStyle: 'rgba(221, 221, 221, 0.9)'
        },
        xAxisIndex: 1,
        moveHandleSize: 0,
        shadowColor: 'rgba(221, 221, 221, 0.9)',
        shadowOffsetX: 10,
        shadowBlur: 10,
        bottom: 24,
        left: 0,
        right: 0,
        zlevel: 0
      },
      {
        type: 'inside',
        startValue: lastBool ? xAxisLength - 7 : 0,
        endValue: lastBool ? xAxisLength : 7,
        minValueSpan: 7,
        maxValueSpan: 34,
        preventDefaultMouseMove: false, // 默认移动事件，防止页面不能上下滑动
        throttle: 50
      }
    ],
    series: seriesOptions
  }

  const option = defaultsdeep(options, defaultOption)

  // 绘制图表
  const chartInstance = drawChartMain(chartDom, option, lastBool)

  // 在图表绘制完成后，手动触发 tooltip 显示倒数第二个数据点
  chartInstance.dispatchAction({
    type: 'showTip',
    seriesIndex: 0, // 假设只需要触发第一个折线图的 tooltip
    dataIndex: targetIndex // 设置选中的数据点
  })

  return chartInstance
}

export {
  drawMoreBarChart,
  drawBarChart,
  drawLineChart,
  drawPieChart,
  drawRingChart,
  drawChartMain,
  newline,
  drawOneLineChart,
  drawHorizonBarChart,
  drawGaugeChart,
  drawScrollChart,
  drawGaugeRateChart,
  defaultColor,
  yAixsOption,
  drawPictorialChart,
  drawCustomGoalBarChart,
  drawNewGaugeChart,
  drawScrollChartNew
}
