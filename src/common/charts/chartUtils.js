import { numToPercent, numToInteger } from 'common/js/numFormat'
import moment from 'moment'
import { newline } from './chartOption'
// 格式化日期 index:number  (type是周的格式,根据数据格式自定填写)
const dateFormat = (val, index, isWeek = false, type = 'GGGGWW') => {
  if (isWeek) {
    return moment(val, ['YYYYMMDD', type, 'YYYYMM', 'YYYY'][index]).format(['MM.DD', 'WW周', 'M月', 'Y年'][index])
  }
  return moment(val, ['YYYYMMDD', 'YYYYMM', 'YYYY'][index]).format(['MM.DD', 'M月', 'Y年'][index])
}
// 坐标轴格式化(整数) fixnum:单位换算的数值(万10000,吨1000)
const intFormat = (val, fixnum = 1) => {
  return Math.abs(val) >= 10 * fixnum ? numToInteger(val, 0, fixnum) : numToInteger(val, 1, fixnum)
}
// 坐标轴格式化(百分比)
const perFormat = val => {
  return Math.abs(val) >= 0.1 ? numToPercent(val, 0) : numToPercent(val, 1)
}

const valueFormat = (val, isNewline = false, isShowIcon = false, provideNumber = 4) => {
  let value = val
  if (/战区/.test(val)) {
    value = val.replace('战区', '')
  }
  if (!isShowIcon) {
    if (isNewline) {
      return newline(value, provideNumber)
    }
    return value
  } else {
    if (isNewline) {
      return `{a|${newline(value, provideNumber)}}{b|}`
    }
    return `{a|${value}}`
  }
}

// Hsroll宽度 (lengthNum:number数据总数,defaultNum:number默认展示几条数据)
const hScrollWidth = (lengthNum, defaultNum) => {
  return lengthNum <= defaultNum ? '100%' : (100 / defaultNum) * lengthNum + '%'
}

// echart x轴日期数据合并(去重并排序)(多条数据不对等时使用)
const mergexAxisData = (result, key) => {
  const xList = []
  const xData = []
  if (result.length) {
    result.forEach((item, index) => {
      item.forEach((data, index2) => {
        xList.push(data[key])
      })
    })
    xData.push(...new Set(xList))
    xData.sort((a, b) => a - b)
  }
  return xData
}
/**
 * @description: 根据日期对数据进行重构,在原数据添加新的自定义字段
 * @param {array} result 要转换的数据
 * @param {array} dateList 所有的日期
 * @param {string} key_date 原数据的日期字段
 * @param {string} key_value 要转换的数值的字段
 * @param {string} key 新的字段命名
 * @return {array} 转换新增自定义字段(没有当前日期下的数据转为'')
 */
const changeSeriesData = (result, dateList, key_date, key_value, key) => {
  if (!result.length) return []
  const tmp = []
  dateList.forEach((item, index) => {
    const value = result.find(data => data[key_date] === item)
    if (value) {
      tmp.push({
        [key_date]: item,
        [key]: value[key_value]
      })
    } else {
      tmp.push({
        [key_date]: item,
        [key]: ''
      })
    }
  })
  tmp.sort((a, b) => a.key_date - b.key_date)
  return tmp
}
const install = Vue => {
  return (
    (Vue.prototype.$dateFormat = dateFormat),
    (Vue.prototype.$intFormat = intFormat),
    (Vue.prototype.$perFormat = perFormat),
    (Vue.prototype.$hScrollWidth = hScrollWidth),
    (Vue.prototype.$mergexAxisData = mergexAxisData),
    (Vue.prototype.$changeSeriesData = changeSeriesData),
    (Vue.prototype.$valueFormat = valueFormat)
  )
}
export default {
  install
}

export { dateFormat, intFormat, perFormat, hScrollWidth, valueFormat }
