import * as echarts from 'echarts/core'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Treemap<PERSON>hart
} from 'echarts/charts'

import {
  <PERSON><PERSON>omponent,
  TooltipComponent,
  GridComponent,
  TitleComponent,
  AxisPointerComponent,
  DataZoomComponent,
  PolarComponent,
  MarkPointComponent,
  MarkLineComponent
} from 'echarts/components'

import { CanvasRenderer } from 'echarts/renderers'

echarts.use([
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  Radar<PERSON>hart,
  MapChart,
  FunnelChart,
  GaugeChart,
  Treemap<PERSON>hart,
  LegendComponent,
  PolarComponent,
  TooltipComponent,
  <PERSON>ridComponent,
  TitleComponent,
  AxisPointerComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent,
  CanvasRenderer
])

export default echarts
