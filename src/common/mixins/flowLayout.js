// import BScroll from 'better-scroll'
export default {
  data() {
    return {
      scrollTop: 0
    }
  },
  methods: {
    initYBS(ref = 'contentWrapper', xMove = false, height = 1.8, height2 = 2.6) {
    //   if (this.$store.state.isPeak && this.$route.path === '/heavyCargo') {
    //     height += (this.$store.state.bannarHeight * 2) / 100
    //     height2 += (this.$store.state.bannarHeight * 2) / 100
    //   }
      const domTop = this.$refs['contentWrapper']
      if (!domTop) return
      domTop.style.cssText = `height: calc(100vh - ${height2}rem); overflow-y: scroll; -webkit-overflow-scrolling: touch; `
      this.topDistance = true

      domTop.onscroll = () => {
        // scrollTop就是触发滚轮事件时滚轮的高度
        this.scrollTop = domTop.scrollTop
        if (+this.scrollTop > 10) {
          domTop.style.height = `calc(100vh - ${height}rem)`
          document.querySelector('.first-level') && document.querySelector('.first-level').classList.add('dispear')
          document.querySelector('.second-level') && document.querySelector('.second-level').classList.add('dispear')
          this.topDistance = false
        } else if (+this.scrollTop <= 10) {
          domTop.style.height = `calc(100vh - ${height2}rem)`
          document.querySelector('.first-level') && document.querySelector('.first-level').classList.remove('dispear')
          document.querySelector('.second-level') && document.querySelector('.second-level').classList.remove('dispear')
          this.topDistance = true
        }
      }
    }
  }
}
