import { checkNumber, checkOneNumber, formatTd } from 'common/js/utils'
const oneDimen = '/resourceServices/report/oneDimen'
const twoDimen = '/resourceServices/report/twoDimen'
import { mapGetters, mapState, mapMutations } from 'vuex'
export default {
  data() {
    return {
      isFeedback: false, // 名词解释反馈按钮判断条件
      explainData: [], // 名词解释data
      explaindDetail: []
    }
  },
  computed: {
    ...mapGetters({
      isAuth: 'isAuth'
    }),
    ...mapState({
      requestQueues: state => state.requestQueues,
      zoneParams: state => state.zoneParams
    })
  },
  methods: {
    ...mapMutations(['setRequestQueues']),
    // 添加请求队列
    async addRequestQueue(isLoading = true) {
      if (isLoading) {
        this.setRequestQueues({
          type: 'push'
        })
      }
    },
    /**
     * 发送网络请求(多棱镜的接口)
     * @param {object} config
     */
    sendRequest(config = {}, isLoading = true) {
      const orgCode = this.zoneParams.zoneCode || ''
      const orgLevel = ['30', '31', '32', '33', '34'][this.zoneParams.zoneLevel]
      let isJmq = false
      if (this.zoneParams.zoneName) {
        isJmq = this.zoneParams.zoneName.includes('加盟区')
      }
      const headers = {
        'orgCode': orgCode,
        'orgLevel': orgLevel
      }
      const configs = isJmq && !config.notMatchJm ? { headers } : {}
      const baseConfig = {
        url: '',
        method: 'GET',
        params: null,
        ...config,
        ...configs
      }

      const _this = this
      return new Promise(async function(resolve, reject) {
        const queue = _this.$http(baseConfig)
        _this.addRequestQueue(isLoading)
        try {
          queue
            .then(res => {
              _this.setRequestQueues({
                type: 'delete'
              })
              resolve(res)
            })
            .catch(err => {
              _this.setRequestQueues({
                type: 'clear'
              })
              console.log(err)
            })
            .finally(() => {})
        } catch (e) {
          console.log('error')
          _this.setRequestQueues({
            type: 'clear'
          })

          reject(e)
        }
      })
    },

    // 多维数据请求（快运中横接口）
    _dimen(dimen = 1, tableName, conditionList, isLoading = true) {
      const _this = this
      return new Promise(async function(resolve, reject) {
        try {
          const res = await _this.sendRequest(
            {
              url: dimen === 1 ? `${oneDimen}/${tableName}` : `${twoDimen}/${tableName}`,
              method: 'POST',
              data: {
                conditionList
              }
            },
            isLoading
          )
          //   if (!res.success) return
          if (res.success) {
            // eslint-disable-next-line prefer-const
            let { obj = {}, ...surplus } = res || {}
            if (dimen === 2 && obj && !Object.keys(obj).length) {
              obj = {
                colDefList: [],
                rows: []
              }
            }
            const result = dimen === 2 ? formatTd(obj) : obj
            // 过滤无用数据为''
            dimen === 1 ? checkOneNumber(result) : checkNumber(result)
            resolve({ ...surplus, obj: result })
          } else {
            resolve(dimen === 1 ? { obj: {} } : { obj: [] })
          }
        } catch (err) {
          reject(err)
        }
      })
    },

    // 一维数据
    sendOneDimenRequest(tableName, conditionList, isLoading = true, useAuth = true) {
      if (useAuth && !this.isAuth) {
        return new Promise((resolve, reject) => {
          resolve({ obj: {} })
        })
      }
      return this._dimen(1, tableName, conditionList, isLoading)
    },

    // 二维数据
    sendTwoDimenRequest(tableName, conditionList, isLoading = true, useAuth = true) {
      if (useAuth && !this.isAuth) {
        return new Promise((resolve, reject) => {
          resolve({ obj: [] })
        })
      }
      return this._dimen(2, tableName, conditionList, isLoading)
    },

    // 多维数据请求（快运中横接口）
    // cockpit后端服务迁移，url变更，并且兼容其他接口，故新增了方法_dimenNew，使用时直接传入完整的url即可
    _dimenNew(dimen = 1, url, conditionList, isLoading = true) {
      const _this = this
      return new Promise(async function(resolve, reject) {
        try {
          const res = await _this.sendRequest(
            {
              url,
              method: 'POST',
              data: {
                conditionList
              }
            },
            isLoading
          )
          if (res.success) {
            const { obj = {}, ...surplus } = res || {}
            if (dimen === 2 && obj && !Object.keys(obj).length) {
              obj.colDefList = []
              obj.rows = []
              // obj = {
              //   colDefList: [],
              //   rows: []
              // }
            }
            const result = dimen === 2 ? formatTd(obj) : obj
            // 过滤无用数据为''
            dimen === 1 ? checkOneNumber(result) : checkNumber(result)
            resolve({ ...surplus, obj: result })
          } else {
            resolve(dimen === 1 ? { obj: {} } : { obj: [] })
          }
        } catch (err) {
          reject(err)
        }
      })
    },

    // 一维数据-(新增)
    sendOneDimenRequestNew(url, conditionList, isLoading = true, useAuth = true) {
      if (useAuth && !this.isAuth) {
        return new Promise((resolve, reject) => {
          resolve({ obj: {} })
        })
      }
      return this._dimenNew(1, url, conditionList, isLoading)
    },

    // 二维数据-（新增）
    sendTwoDimenRequestNew(url, conditionList, isLoading = true, useAuth = true) {
      if (useAuth && !this.isAuth) {
        return new Promise((resolve, reject) => {
          resolve({ obj: [] })
        })
      }
      return this._dimenNew(2, url, conditionList, isLoading)
    },

    // 请求java接口
    sendJavaRequest(config, isLoading = true, useAuth = true) {
      if (useAuth && !this.isAuth) {
        return new Promise((resolve, reject) => {
          resolve({ obj: [] })
        })
      }
      const sendR = this.sendRequest
      return new Promise(async function(resolve, reject) {
        try {
          const res = await sendR(
            {
              url: '/resourceServices/businessMonitor/product/query',
              method: 'POST',
              data: {},
              ...config
            },
            isLoading
          )
          if (res.success) {
            if (res.data) {
              resolve({ obj: res.data || [] })
            }
            if (res.obj) {
              resolve({ obj: res.obj || [] })
            }
            resolve(res)
          } else {
            resolve({ obj: [] })
          }
        } catch (err) {
          reject(err)
        }
      })
    },
    // 一维二维接口传参数据格式化
    forMapData(data) {
      const newData = []
      for (const key in data) {
        if (Object.hasOwnProperty.call(data, key)) {
          const obj = {
            key,
            value: data[key]
          }
          newData.push(obj)
        }
      }
      return newData
    },
    // 名词解释的接口,全局用到(org_code:平台编码,写死sxzk, glossary_code:传入位置编码传参)
    async getExplainData(glossary_code) {
      const { obj } = await this.sendTwoDimenRequest('explain_glossary_zhankuang', [
        { key: 'org_code', value: 'sxzk' },
        { key: 'glossary_code', value: glossary_code }
      ])
      console.log(JSON.parse(JSON.stringify(obj)), '名册解释')
      if (obj.length) {
        this.explainData = obj[0]
        // 有工号,显示反馈按钮
        if (obj[0].managerNo) {
          this.isFeedback = true
        }
        this.explaindDetail = JSON.parse(obj[0].detail)
        console.log(this.explaindDetail, 'detail')
      }
    },
    // 名词解释跳转
    btnFeedback() {
      // 打开个人详情 (account:员工工号)
      if (process.env.NODE_ENV !== 'development') {
        SFNativeIM.openPersonalInfo({ data: { account: this.explainData.managerNo } })
        this.$sensors.pageview('名词解释-反馈跳转-' + this.explainData.managerNo)
      }
    },
    // 打开名词解释弹窗调用接口
    btnShowTip(isShow, key) {
      if (isShow) {
        // 名词解释反馈接口
        this.getExplainData(key)
      }
    },
    // 节假日接口
    getHoliday(startDay, endDay) {
      return this.sendTwoDimenRequest('dim_calendar', [
        { key: 'start_day', value: startDay },
        { key: 'end_day', value: endDay }
      ])
    },
    postRequest(url, params) {
      const baseURL = process.env.VUE_APP_REQUEST
      return this.sendRequest({
        url,
        data: params,
        method: 'POST',
        baseURL
      })
    }
  }
}
