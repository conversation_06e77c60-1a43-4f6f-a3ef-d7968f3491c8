/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2024-05-08 18:23:12
 * @Description:
 */
import moment from 'moment'
import _ from 'lodash'
import filters from './filters'
import HttpClass from 'common/js/httpPlugin'
import OpLog from 'common/js/opLog'
import * as components from './components/index.js'
import { handleComponentCache } from './js/utils'
import handlerExitAPP from 'common/components/ExitApp/index.js'

import { Toast } from 'vant'

export default {
  install(Vue) {
    const commonComponents = components.default
    Object.keys(commonComponents).forEach(key => {
      Vue.component(commonComponents[key].name, commonComponents[key])
    })
    // 全局过滤器
    Object.keys(filters).forEach(key => {
      Vue.filter(key, filters[key])
    })
    // 时间处理插件
    Object.defineProperty(Vue.prototype, '$moment', { value: moment })
    Object.defineProperty(Vue.prototype, '_', { value: _ })
    // 行为日志
    const opLog = new OpLog()
    Object.defineProperty(Vue.prototype, 'opLog', { value: opLog })
    // xhr插件
    const http = HttpClass.getInstance()
    http.http._instance = http._instance
    http.http._instance.responseInterceptors(
      http.http,
      ({ data }) => {
        return data
      },
      error => {
        // if (error.response.status === 401) {
        //   window.g_cas.timeoutLogout()
        // }
        if (error.response.status === 403) {
          window.g_cas.logout()
        }
        Toast({
          duration: 5000,
          type: 'loading',
          message: '网络异常，请稍后重试！'
        })
        return error
      }
    )
    // http请求相关
    Object.defineProperty(Vue.prototype, '$http', { value: http.http })
    // 全局指令
    // Object.keys(directives).forEach(key => {
    //   Vue.directive(key, directives[key])
    // })
    Vue.mixin({
      beforeRouteLeave(to, from, next) {
        // 针对安卓机返回
        if ((to.name === 'advertisement' || to.name === 'versionUpdate') && from.fullPath !== '/') {
          handlerExitAPP()
          next(false)
        } else {
          const componentName = this.$options.name
          if (from.meta.keepAlive) {
            // 下钻重新请求下级页面，返回走缓存----通过配置路由参数pageLevel+keepAlive
            if (!from.meta.pageLevel || from.meta.pageLevel >= (to.meta.pageLevel ? to.meta.pageLevel : 0)) {
              // 如果模块路由noCleanCacheList设置了跳转目标路由不请缓存
              if (
                ((!from.meta.noCleanCacheList || from.meta.noCleanCacheList.indexOf(to.name) < 0) &&
                  !from.meta.isAlwaysCache) ||
                to.meta.nextRouterCleanCache
              ) {
                handleComponentCache(this, componentName)
              }
            }
          }
          if (to.meta.pageLevel === '1' && from.meta.pageLevel === '1') {
            if (from.meta.noCleanCacheList && from.meta.noCleanCacheList.indexOf(to.name) > -1) {
              this.$store.commit('setRemoveRouteCache', ['organization'])
            } else {
              this.$store.commit('setRemoveRouteCache', ['organization', componentName])
            }
          }
          next()
        }
      },
      beforeRouteEnter(to, from, next) {
        // ...
        if (to.meta.keepAlive && to.meta.pageLevel !== 1) {
          next(vm => {
            vm.$store.commit('setRemoveRouteCache', 'nonHomePage')
          })
        } else {
          next()
        }
      }
    })
  }
}
