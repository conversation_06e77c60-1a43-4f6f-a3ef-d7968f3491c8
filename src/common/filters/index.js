// 待开发
// 处理时间
// 处理数据相关
import moment from 'moment'
function validDate(date, type) {
  let tempDate = date
  if (!tempDate) {
    return false
  }
  if (typeof tempDate === 'string' && tempDate.indexOf('-') < 0) {
    if (!(tempDate - 0)) return
    if (!type || type === 'day') {
      tempDate =
        tempDate.substr(0, 4) +
        '/' +
        tempDate.substr(4, 2) +
        '/' +
        tempDate.substr(6, 2)
    } else if (type === 'month') {
      tempDate = tempDate.substr(0, 4) + '/' + tempDate.substr(4, 2) + '/01'
    }
  }
  if (typeof tempDate === 'string') {
    tempDate = tempDate.replace(/-/g, '/')
  }
  tempDate = new Date(tempDate)
  if (tempDate.toString() === 'Invalid Date') {
    return false
  } else {
    return tempDate
  }
}
function dealFmtDate(tempDate, tempFmt) {
  // const o = {
  //   'M+': tempDate.getMonth() + 1, // 月份
  //   'd+': tempDate.getDate(), // 日
  //   'h+': tempDate.getHours(), // 小时
  //   'm+': tempDate.getMinutes(), // 分
  //   's+': tempDate.getSeconds(), // 秒
  //   'q+': Math.floor((tempDate.getMonth() + 3) / 3), // 季度
  //   'S': tempDate.getMilliseconds() // 毫秒
  // }
  // if (/(y+)/.test(tempFmt)) {
  //   tempFmt = tempFmt.replace(RegExp.$1, (tempDate.getFullYear() + '').substr(4 - RegExp.$1.length))
  // }
  // for (const k in o) {
  //   if (new RegExp('(' + k + ')').test(tempFmt)) {
  //     tempFmt = tempFmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
  //   }
  // }
  // return tempFmt
  return moment(tempDate).format(tempFmt.replace(/y/g, 'Y').replace(/d/g, 'D'))
}

export default {
  // value：传入的值，fixNum：需要保留的小数位，可不传
  thousands: function(val, fixNum) {
    const reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
    if (val === 0 || val === '0') {
      return 0
    } else if (reg.test(val)) {
      return val
    } else if (!val || isNaN(val) || (fixNum !== undefined && isNaN(fixNum))) {
      return '-'
    }
    const value = Number(val).toString()
    let intNum = value - 0 > 0 ? Math.floor(Number(value)) : Math.ceil(Number(value))
    // let intNum = value - 0 > 0 ? Math.floor(value) : Math.ceil(value)
    let dotStr = ''
    if (fixNum === undefined && value.indexOf('.') > 0) {
      if (value.split('.')[1] - 0 > 0) {
        dotStr = '.' + value.split('.')[1]
      }
    } else if (Number(fixNum) > 0) {
      intNum = Number(value)
        .toFixed(fixNum)
        .split('.')[0]
      dotStr =
        '.' +
        Number(value)
          .toFixed(fixNum)
          .split('.')[1]
    }
    return intNum.toLocaleString() + dotStr
  },
  hasVal: function(value) {
    if (value === '' || value === undefined || value === null) {
      return '-'
    }
    return value
  },
  /**
   * @param {*} date 日期，Date类型，字符串类型都可以
   * @param {*} fmt 需要返回的格式
   * @param {*} type 如果传过来的是201806这样的月格式，则必传 'month'，如果是日类型的，则可不传
   */
  // HTML 中调用：<span>{{'20181012'|formatDate('yy年MM月')}}</span> => 18年10月
  // js 中调用：Vue.filter('formatDate')('201901','yy年MM月','month') => 19年01月
  formatDate: function(date, fmt, type) {
    let tempDate = null
    if (!fmt) {
      return
    }
    tempDate = validDate(date, type)
    if (!tempDate) return
    return dealFmtDate(tempDate, fmt)
  },
  // 调用方法参考formatDate，num为往前倒推多少天或月
  preDate: function(date, fmt, type, num) {
    let tempDate = null
    tempDate = validDate(date, type)
    if (!tempDate) return
    if (type === 'day') {
      tempDate.setDate(tempDate.getDate() + num)
    } else if (type === 'month') {
      tempDate.setMonth(tempDate.getMonth() + num)
    }
    return dealFmtDate(tempDate, fmt)
  },
  // 时间格式化（去除.0）---后台数据返回的时间格式 eg：2019-01-28 09:45:10.0
  timeFormat(val) {
    let returnVal = '-'
    if (val !== '' && typeof val !== 'undefined') {
      const index = val.lastIndexOf('.')
      returnVal = val.substring(0, index)
    }
    return returnVal
  },
  orderByDesc(arr, key) {
    const newArr = arr.sort(function(a, b) {
      return b[key] - a[key]
    })
    return newArr
  },
  // 传入20181131,
  dayFormat(val) {
    if (val) {
      if (val.length === 8) {
        return (
          val.substring(0, 4) +
          '年' +
          val.substring(4, 6) * 1 +
          '月' +
          val.substring(6, 8) +
          '日'
        )
      } else if (val.length === 6) {
        return val.substring(0, 4) + '年' + val.substring(4, 6) * 1
      }
    } else {
      return '-'
    }
  },
  numberFormatUS(input) {
    if (input === undefined) {
      return
    }
    if (isNaN(input)) {
      return '-'
    }
    var number = Number(input)
    return number.toLocaleString('en-US')
  },
  // 周月格式  传入201811或201842,
  // dateType为Boolen
  MWFormat(val, dateType) {
    if (val) {
      if (val.length === 6) {
        return val.substring(4, 6) * 1 + (dateType ? '月' : '周')
      }
    } else {
      return '-'
    }
  },
  // 保留多少位小数
  toFixed(val, num) {
    if (val === undefined || isNaN(val)) {
      return '-'
    }
    return Number(val).toFixed(num || 0)
  }

}

