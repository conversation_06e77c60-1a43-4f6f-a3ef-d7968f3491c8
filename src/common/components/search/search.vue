<template>
  <div>
    <div class="serach_block">
      <div
        :style="{
          backgroundColor: isFocus || singleval !== '' ? '#F8F9FC' : '#F8F9FC',
          boxShadow: isFocus || singleval ? 'none' : ''
        }"
      >
        <div class="input">
          <input
            ref="search"
            type="search"
            :placeholder="placeholder"
            v-model="singleval"
            style="line-height: 0.6rem; width: 100%"
            :style="{
              backgroundColor: isFocus || singleval !== '' ? '#F8F9FC' : '#F8F9FC'
            }"
          />
          <label class="input-label">{{ singleval !== '' || this.isFocus ? '' : placeholder }}</label>
        </div>
      </div>
      <div class="cancel" v-if="isFocus || singleval !== ''" @click="clear">取消</div>
    </div>
  </div>
</template>

<script>
let timeOut = null
export default {
  name: 'SearchBlock',
  props: {
    placeholder: {
      type: String,
      default: '请输入名称'
    }
  },
  data() {
    return {
      singleval: '',
      isFocus: false,
      inputSign: false
    }
  },
  methods: {
    clear() {
      this.singleval = ''
      this.inputSign = false
      this.$emit('cancel', '')
    },
    inputFocus() {
      this.isFocus = true
    },
    inputBlur() {
      this.isFocus = false
    },
    refresh() {
      this.isFocus = false
      this.singleval = ''
    }
    // onInput(val) {
    //   this.inputSign = true
    // }
  },
  mounted() {
    this.$refs.search.addEventListener('focus', this.inputFocus)
    this.$refs.search.addEventListener('blur', this.inputBlur)
  },
  watch: {
    singleval(newVal, oldVal) {
      // if (newVal !== '') {
      if (timeOut !== null) {
        clearTimeout(timeOut)
      }
      timeOut = setTimeout(() => {
        this.$emit('search', newVal)
      }, 1000)
    }
    // }
  }
}
</script>

<style lang="less" scoped>
input:placeholder-shown::placeholder {
  color: transparent;
}
.serach_block {
  padding: 0.2rem 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;

  & > div:first-child {
    flex: 1;
    display: flex;
    align-items: center;
    background: #ffffff;
    border-radius: 0.4rem;
    // box-shadow: 0 0 12px 0 rgba(240, 157, 168, 0.23);
    height: 0.6rem;

    .search_icon {
      width: 0.7rem;
      height: 0.4rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-right: 0.14rem;
    }

    .input {
      height: 100%;
      flex: 1;
    }

    input {
      height: 100%;
    }
  }

  .cancel {
    line-height: 0.37rem;
    font-size: 0.26rem;
    padding-left: 0.3rem;
    color: #333;
  }
}

.input-label {
  position: absolute;
  left: 50%;
  top: 50%;
  pointer-events: none;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  color: #999;
  line-height: 0.33rem;

  &::before {
    content: '';
    display: inline-block;
    background: url('./search_icon.png') no-repeat;
    background-size: contain;
    height: 0.4rem;
    width: 0.4rem;
  }
}

.input {
  position: relative;
  padding: 0 0.2rem 0 0.6rem;
}

input:not(:placeholder-shown) ~ .input-label,
input:focus ~ .input-label {
  color: #999;
  left: 0.2rem;
  transform: translate(-0%, -50%);
  transition: all 0.2s;
}

input {
  color: #999;
}

input::-webkit-search-cancel-button {
  display: none;
}

input[type='search']::-ms-clear {
  display: none;
}

input::-webkit-input-placeholder {
  color: #888;
  line-height: 0.4rem;
  font-size: 0.28rem;
}

input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #888;
  line-height: 0.4rem;
  font-size: 0.28rem;
}

input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #888;
  line-height: 0.4rem;
  font-size: 0.28rem;
}

input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #888;
  line-height: 0.4rem;
  font-size: 0.28rem;
}
</style>
