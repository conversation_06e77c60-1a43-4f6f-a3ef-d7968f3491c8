/*
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-14 21:28:36
 * @LastEditors: LiuZ<PERSON><PERSON>
 * @LastEditTime: 2021-04-28 15:30:59
 */
import './index.less'

export default {
  name: 'progressPie',
  props: {
    columns: Object,
    dataSource: Array,
    processDivisor: {
      type: Number,
      default: 1
    }
  },
  render(h) {
    const getLen = (val = '') => {
      let len = 0
      for (let i = 0; i < val.length; i++) {
        const length = val.charCodeAt(i)
        if (length >= 0 && length <= 128) {
          len += 0.6
        } else {
          len += 1
        }
      }
      return len
    }
    const { columns, dataSource } = this
    const maxLabelLength = Math.max(...dataSource.map(item => getLen(item[columns.labelDataIndex])))
    const maxLabelWidth = `${(maxLabelLength || 0) * 0.24 + 0.16}rem`
    return (
      <div class='progress-pie-wrapper'>
        {dataSource.map(item => {
          const zbDom = columns.zbRender ? (
            <span class='zb-span'>
              {columns.zbLabel && <span class='span-label'>{columns.zbLabel}</span>}
              {columns.zbRender(item[columns.zbDataIndex])}
            </span>
          ) : (
            <span class='zb-span'>
              {columns.zbLabel && <span class='span-label'>{columns.zbLabel}</span>}
              {item[columns.zbDataIndex]}
            </span>
          )

          const valueDom = columns.valueRender ? (
            <span class='value-span'>
              {columns.valueLabel && <span class='span-label'>{columns.valueLabel}</span>}
              {columns.valueRender(item[columns.valueDataIndex])}
            </span>
          ) : (
            <span class='value-span'>
              {columns.valueLabel && <span class='span-label'>{columns.valueLabel}</span>}
              {item[columns.valueDataIndex]}
            </span>
          )

          const hbDom = columns.hbRender ? (
            <span class='hb-span'>
              {columns.hbLabel && <span class='span-label'>{columns.hbLabel}</span>}
              {columns.hbRender(item[columns.hbDataIndex])}
            </span>
          ) : (
            <span class='hb-span'>
              {columns.hbLabel && <span class='span-label'>{columns.hbLabel}</span>}
              {item[columns.hbDataIndex]}
            </span>
          )

          const tbDom = columns.tbRender ? (
            <span class='tb-span'>
              {columns.tbLabel && <span class='span-label'>{columns.tbLabel}</span>}
              {columns.tbRender(item[columns.tbDataIndex])}
            </span>
          ) : (
            <span class='tb-span'>
              {columns.tbLabel && <span class='span-label'>{columns.tbLabel}</span>}
              {item[columns.tbDataIndex]}
            </span>
          )
          // xx
          return (
            <div class='progress-pie--item'>
              <div class='progress-pie--item--label' style={{ minWidth: maxLabelWidth }}>
                {columns.labelRender ? columns.labelRender(item[columns.labelDataIndex]) : item[columns.labelDataIndex]}
              </div>
              <div class='progress-pie--item--content' style={{ minWidth: `calc(100% - ${maxLabelWidth})` }}>
                <div class='flex_between progress-pie--item--top'>
                  <div>{columns.topLeft || zbDom}</div>
                  <div>{columns.topRight || valueDom}</div>
                </div>
                <div class='progress-pie--item--bg'>
                  <div style={{ width: `${this.$numToPercent(item[columns.zbDataIndex] / this.processDivisor, 1)}` }}></div>
                </div>
                <div class='flex_between'>
                  <div style='margin-left:auto'>{columns.bottomLeft || hbDom}</div>
                  <div style='margin-left:24px'>{columns.bottomRight || tbDom}</div>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }
}
