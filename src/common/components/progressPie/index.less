.progress-pie-wrapper {
  display: flex;
  flex-direction: column;

  .progress-pie--item {
    display: flex;
    align-items: center;
    min-height: 0.88rem;
    margin-bottom: 0.24rem;
    &:last-child {
      margin-bottom: 0;
    }
    &--label {
      font-family: PingFangSC-Medium;
      font-size: 0.24rem;
      color: #333333;
      font-weight: 500;
    }

    &--content {
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    &--bg {
      height: 0.1rem;
      width: 100%;
      position: relative;
      background: #F5F5F5;
      margin: 0.08rem 0;

      > div {
        position: absolute;
        height: 100%;
        background: #4c83f9;
        top: 0;
        left: 0;
      }
    }
  }

  .zb-span, .value-span {
    font-family: PingFangSC-Medium;
    font-size: 0.24rem;
    line-height: 0.33rem;
    color: #333;

    .span-label {
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      line-height: 0.33rem;
      margin-right: 0.08rem;
      color: #999;
    }
  }
  
  .hb-span, .tb-span {
    font-family: PingFangSC-Medium;
    line-height: 0.28rem;
    font-size: 0.20rem;
    color: #333;

    .span-label {
      font-family: PingFangSC-Regular;
      font-size: 0.20rem;
      line-height: 0.28rem;
      color: #999;
      margin-right: 0.08rem;
    }
  }
}