<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-16 17:45:42
 * @LastEditors: JieJw
 * @LastEditTime: 2021-07-21 17:58:45
 * @Description:
-->
<template>
  <div class="product-tab-wrapper">
    <div
      :class="{
        'product-tab--item': true,
        'product-tab--item__active': index === activeIndex
      }"
      @click="tabChange(index, item)"
      v-for="(item, index) in tabTitle"
      :key="item"
    >
      <span>{{ item }}</span>
      <div class="parent_value" v-if="columns.parent">
        {{typeof columns.parent.render === 'function' ? columns.parent.render(formatDataSource[columns.parent.dataIndex[index]]) : formatDataSource[columns.parent.dataIndex[index]]}}
      </div>
      <div class="content" v-if="columns.child">
        <span v-for="(item, _index) in columns.child" :key="_index">
          {{ item.label }}
          <span>{{
            typeof item.render === 'function'
              ? item.render(formatDataSource[item.dataIndex[index]])
              : formatDataSource[item.dataIndex[index]]
          }}</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    activeIndex: Number,
    dataSource: [Object, Array],
    tabTitle: Array,
    columns: Object
  },
  computed: {
    formatDataSource() {
      return Array.isArray(this.dataSource) ? this.dataSource[this.acticeIndex] : this.dataSource
    }
  },
  methods: {
    tabChange(index, item) {
      if (index !== this.activeIndex) {
        this.$emit('change', index, item)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.product-tab-wrapper {
  display: flex;
  .parent_value{
    font-family: Roboto-Medium;
    font-size: 0.28rem;
    line-height: 0.33rem;
  }
  .product-tab--item {
    padding: 0.12rem 0.24rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fc;
    position: relative;
    border-radius: 0.08rem;
    &:first-child {
      margin-right: 0.24rem;
    }
    > span {
      font-family: PingFangSC-Medium;
      font-size: 0.2rem;
      line-height: 0.28rem;
      color: #333333;
      font-weight: bold;
      margin-bottom: 0.12rem;
    }
    .content {
      display: flex;
      flex-direction: column;
      > span {
        font-family: PingFangSC-Regular;
        line-height: 0.28rem;
        font-size: 0.2rem;
        color: #999999;
        margin-top: 0.08rem;
        &:first-child {
          margin-top: 0rem;
        }
        > span {
          color: #333;
          margin-left: 0.08rem;
          font-weight: bold;
        }
      }
    }
  }
  .product-tab--item__active {
    background: #dc1e23;
    border: 1px solid #fff;
    filter: drop-shadow(0.1rem 0.12rem 0.06rem #f0f0f0);
    span, div {
      color: #fff !important;
    }
  }
}
</style>
