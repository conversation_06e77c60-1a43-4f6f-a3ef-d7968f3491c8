<template>
  <div>
    <div class="btn_box flex_center">
      <div v-if="isShow" class="" @click="btnChange">
        <i class="iconfont icon-table fs24 mr4 red"></i>
        <span class="grey666">{{ options[0] || '切换数据' }}</span>
      </div>
      <div v-if="!isShow" class="" @click="btnChange">
        <i class="iconfont icon-chart_icon fs24 mr4 red"></i>
        <span class="grey666">{{ options[1] || '切换图表' }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    defaultIndex: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      isShow: true
    }
  },
  methods: {
    btnChange() {
      this.isShow = !this.isShow
      this.$emit('btnChange', this.isShow)
    }
  },
  mounted() {
    this.isShow = !this.defaultIndex
  }
}
</script>
<style lang="less" scoped>
.btn_box {
  width: 1.24rem;
  height: 0.4rem;
  font-size: 0.2rem;
  border-radius: 0.04rem;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.2);
}
</style>
