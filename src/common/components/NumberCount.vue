<template>
	<div>
		<ul :class="['num-card-block', $store.state.baseThemeName]">
			<li
				v-for="(item) in displayCards"
				:class="item.value == ',' ? 'dot' : 'num'"
				:key="item.uuid"
			>
				<div v-if="item.value == ','">
                ,
                </div>
                <div v-else  ref="num">
                    <div v-for="i in rows" :key="i">{{i}}</div>
                </div>
			</li>
		</ul>
	</div>
</template>

<script>
const generateUUID = function() {
  let d = new Date().getTime()
  if (window.performance && typeof window.performance.now === 'function') {
    d += performance.now()
  }
  var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (d + Math.random() * 16) % 16 | 0
    d = Math.floor(d / 16)
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
  return uuid
}
export default {
  name: 'NumberCount',
  props: {
    startVal: {
      type: Number,
      default: 0
    },
    endVal: {
      type: Number,
      default: 0
    },
    duration: {
      type: Number,
      required: false,
      default: 1000
    },
    separator: {
      type: String,
      required: false,
      default: ','
    }
  },
  data() {
    return {
      displayValue: this.formatNumber(this.endVal),
      rows: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
    }
  },
  computed: {
    displayCards() {
      const disVal = this.displayValue.split('')
      const displayCards = []
      for (let i = 0; i < disVal.length; i++) {
        displayCards.push({
          value: disVal[i],
          uuid: generateUUID()
        })
      }

      return displayCards
    }
  },
  created() {
  },
  mounted() {
    this.rowHeight = document.getElementsByClassName('num')[0].offsetHeight
    this.runNumber()
  },
  methods: {
    runNumber() {
      const startVal = this.startVal + ''
      const endVal = this.endVal + ''
      let x = endVal.length - 1
      let y = startVal.length - 1
      while (x >= 0) {
        let t = 0
        const sNum = Number(startVal[y] ? startVal[y] : '0')
        const eNum = Number(endVal[x])
        const dom = this.$refs.num[x]
        let startTime = Date.now()
        const step = () => {
          const flushData = Date.now()
          const distance = flushData - startTime
          startTime = flushData
          const value = this.linearFn(t, sNum, eNum, this.duration) * this.rowHeight
          dom.style.transform = `translateY(${-value}px)`
          t += distance
          if (t <= this.duration) {
            requestAnimationFrame(step)
          } else {
            dom.style.transform = `translateY(${-eNum * this.rowHeight}px)`
          }
        }
        step()
        x--
        y--
      }
    },
    isNumber(val) {
      return !isNaN(parseFloat(val))
    },
    formatNumber(num) {
      // let x1 = num + ''
      // const rgx = /(\d+)(\d{3})/
      // if (this.separator && !this.isNumber(this.separator)) {
      //   while (rgx.test(x1)) {
      //     x1 = x1.replace(rgx, '$1' + this.separator + '$2')
      //   }
      // }
      // return x1
      const x1 = num + ''
      const regex = /(?!^)(?=(\d{3})+$)/g
      if (this.separator && !this.isNumber(this.separator)) {
        return x1.replace(regex, this.separator)
      }
      return x1
    },
    linearFn(t, b, c, d) {
      if (c >= b) {
        // 一圈内
        return ((c - b) * t) / d + b
      } else {
        // 需要转两圈
        if (t <= ((9 - b) * d) / (9 + c - b)) {
        //   console.log('aaa')
          return this.linearFn(t, b, 9, ((9 - b) * d) / (9 + c - b))
        } else {
          return this.linearFn(t, 0, c, d)
        }
      }
    }
  },
  watch: {
    endVal(newValue, oldValue) {
      this.displayValue = this.formatNumber(newValue)
      this.$nextTick(() => {
        this.runNumber()
      })
    }
  }
}
</script>

<style lang="less" scoped>
.num-card-block{
  display: flex;
  align-items: center;
  &>li{
      height: 30px;
      width: 0.3rem;
      margin-left: 0.04rem;
      color: #FFFFFF;
      font-size: 20px;
      text-align: center;
      overflow: hidden;
      &.num{
      font-weight: bold;
      background: #1e3dd9;
      line-height: 1.5;
      }
      &.dot{
      width: 0.15rem;
      font-weight: normal;
      line-height: 1.5;
    }
      &>div{
          height: 30px;
          font-weight: bold;
          line-height: 1.5;
          div{
              font-weight: bold;
              height: 100%;
              line-height: 1.5;
          }
      }
  }
}
</style>
