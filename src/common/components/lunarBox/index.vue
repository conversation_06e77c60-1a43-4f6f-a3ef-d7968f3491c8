<!--
 * @Author: your name
 * @Date: 2021-03-30 18:29:27
 * @LastEditTime: 2024-01-19 23:45:45
 * @LastEditors: Please set LastEditors
 * @Description: 去年农历,阳历 (农历-lunar,阳历-solar)
-->
<template>
  <div v-if="isShow" class="lunar-box-wrap">
    <div class="lunar flex_start mt10">
      <div :class="{ lunar_img: type === 'lunar', solar_img: type === 'solar' }"></div>
      <div class="content">
        <div class="title-date">{{ lunarDate }}</div>
        <div v-for="(item, index) in columns" :key="index">
          <span class="fs20">{{ item.label }}</span>
          <span class="fs20 ml8">{{
            item['render'] ? item['render'](dataSource[item.dataIndex], index) : dataSource[item.dataIndex]
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import solarLunar from 'solarlunar'
// import moment from 'moment'
export default {
  props: {
    // date: {
    //   type: String,
    //   default: moment().format('YYYYMMDD')
    // },
    isShow: {
      type: Boolean
    },
    type: {
      type: String,
      default: 'lunar'
    },
    lunarDate: String,
    dataSource: {
      type: Object,
      default: () => {
        return {
          // shouru: 10,
          // tb: 0.2
        }
      }
    },
    columns: {
      type: Array,
      default: () => {
        return [
          // {
          //   label: '收入',
          //   dataIndex: 'shouru',
          //   render: val => 123
          // },
          // {
          //   label: '同比',
          //   dataIndex: 'tb'
          // }
        ]
      }
    }
  },
  computed: {
    // dateLunar() {
    //   const year = this.$moment(this.date).year()
    //   const month = this.$moment(this.date).month() + 1
    //   const day = this.$moment(this.date).date()
    //   const solar2lunarData = solarLunar.solar2lunar(year, month, day)
    //   return solar2lunarData.dayCn
    // },
    // dateSolar() {
    //   return this.$moment(this.date).add(-1, 'year').format('YYYY.M.D')
    // }
  }
}
</script>

<style lang="less" scoped>
.lunar-box-wrap {
  padding: 0.12rem 0.12rem;
  // background-color: #233252;
  border-radius: 0.12rem;
  // background: url('./lunar.png') no-repeat right bottom;
  // background-size: 0.8rem 0.8rem;
  background-color: #233252;
  // border: 1px solid red;

  .lunar {
    .lunar_img {
      height: 0.6rem;
      width: 0.6rem;
      background: url('./lunar.png');
      background-size: cover;
    }
    .solar_img {
      height: 0.6rem;
      width: 0.6rem;
      background: url('./solar.png');
      background-size: cover;
    }
    .content {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      font-size: 0.2rem;
      color: #fff;
      padding-left: 0.2rem;
      .title-date {
        font-size: 0.2rem;
        font-weight: 700;
        color: #ee7335;
      }
      > div {
        margin-top: 0.08rem;
        &:first-child {
          margin-top: 0;
        }
      }
      // &::before {
      //   content: '';
      //   height: 0.31rem;
      //   background: #233252;
      //   margin: 0 0.15rem 0 0rem;
      // }
      // > div {
      //   display: flex;
      //   flex-direction: column;
      //   // > div:first-child {
      //   //   margin-bottom: 0.08rem;
      //   // }
      //   span {
      //     margin-left: 0.08rem;
      //     font-family: PingFangSC-Medium;
      //     font-size: 0.24rem;
      //     color: #fff;
      //   }
      // }
      // .tbBox {
      //   margin-top: 0.08rem;
      // }
    }
  }
  .solar {
    color: #fff;
  }
}
</style>
