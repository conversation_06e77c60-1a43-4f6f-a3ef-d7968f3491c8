<template>
  <div class="datePicker-wrap">
    <ul>
      <li v-for="(item, index) in dayList"
          :key="index"
          :class="{'active':selected == index}"
          @click="switchDay(item,index)">
        <span>{{item.week}}</span>
        <p :class="[selected == index?'active':'']">{{item.day}}</p>
      </li>
    </ul>
  </div>
</template>
<script>
const daysOfWeek = ['日', '一', '二', '三', '四', '五', '六']
export default {
  name: 'DatePickerSimple',
  props: {
    // 需要显示多少个日期，默认是7天
    preNum: {
      type: Number,
      default: 7
    },
    // 默认时间格式，采用momentJs的格式
    format: {
      type: String,
      default: 'YYYYMMDD'
    },
    // 最后一天显示的日期
    endDay: {
      default: ''
    },
    // 默认点亮哪一个，以最后一个日期索引为0，往前一天-1，依次类推
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      selected: '',
      dayList: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    init() {
      const endDay = this.$moment(this.endDay || new Date(), this.format)
      this.dayList = []
      let tempDay
      for (let i = this.preNum - 1; i >= 0; i--) {
        tempDay = this.$moment(endDay).subtract(i, 'days')
        this.dayList.push({
          date: tempDay.format(this.format),
          week: daysOfWeek[tempDay.format('d')],
          day: tempDay.format('DD')
        })
      }
      this.upDateIndex()
    },
    upDateIndex() {
      this.selected = this.activeIndex - 1 + this.preNum
    },
    switchDay(item, index) {
      if (index === this.selected) return
      this.selected = index

      // 返回参数,选择的日期和索引
      this.$emit('change', {
        date: item.date,
        week: item.week,
        index: index - this.preNum + 1
      })
    }
  },
  watch: {
    activeIndex() {
      this.upDateIndex()
    },
    endDay() {
      this.init()
    }
  }
}
</script>
<style lang="less" scoped>
.datePicker-wrap {
  position: relative;
  height: 1.1rem;
  background-color: white;

  ul {
    display: flex;
    li {
      display: flex;
      flex: 1;
      line-height: 0.5rem;
      font-size: 0.22rem;
      flex-direction: column;
      align-items: center;
      padding-top: 0.02rem;

      p {
        height: 0.5rem;
        width: 0.5rem;
        font-size: 0.3rem;
        text-align: center;
      }

      &.active {
        p {
          border-radius: 50%;
          color: white;
          background-color: var(--brightColor);
        }
      }
    }
  }
}
</style>

