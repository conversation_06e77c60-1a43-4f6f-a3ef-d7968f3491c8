<!--
 * @Descripttion: 标注引导
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-14 16:21:54
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-05-14 19:20:22
-->
<template>
  <div class="overlay" v-if="isShowBiaoZhuGuide">
    <!-- 遮罩蒙板 -->
    <transition name="overlay-mask">
      <div v-if="isShowBiaoZhuGuide" class="overlay-mask"></div>
    </transition>
    <!-- 引导内容 -->
    <div class="overlay-body">
      <div class="pagination-wrapper" v-if="stepNum !== 6">
        <div class="pagination">{{stepNum}}/{{stepSum}}</div>
        <div class="skip-btn" @click="handleSkipClick">跳过</div>
      </div>
      <div class="step step-1" v-if="stepNum === 1">
        <img class="step-bg" :src="step1IMG">
        <div class="step-1-nextbtn" @click="handleNextClick(2)"></div>
      </div>
      <div class="step step-2" v-if="stepNum === 2">
        <img class="step-bg" :src="step2IMG">
        <div class="step-2-btns">
          <div class="btn" @click="handleNextClick(1)"></div>
          <div class="btn" @click="handleNextClick(3)"></div>
        </div>
      </div>
      <div class="step step-3" v-if="stepNum === 3">
        <img class="step-bg" :src="step3IMG">
        <div class="step-3-btns">
          <div class="btn" @click="handleNextClick(2)"></div>
          <div class="btn" @click="handleNextClick(4)"></div>
        </div>
      </div>
      <div class="step step-4" v-if="stepNum === 4">
        <img class="step-bg" :src="step4IMG">
        <div class="step-4-btns">
          <div class="btn" @click="handleNextClick(3)"></div>
          <div class="btn" @click="handleNextClick(5)"></div>
        </div>
      </div>
      <div class="step step-5" v-if="stepNum === 5">
        <img class="step-bg" :src="step5IMG">
        <div class="step-5-btns">
          <div class="btn" @click="handleNextClick(4)"></div>
          <div class="btn" @click="handleNextClick(6)"></div>
        </div>
      </div>
      <div class="step step-6" v-if="stepNum === 6">
        <img class="step-bg" :src="step6IMG">
        <div class="step-6-btns" @click="handleSkipClick"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isShowBiaoZhuGuide: false,
      stepNum: 1,
      stepSum: 6,
      step1IMG: require('./imgs/step1_bg.png'),
      step2IMG: require('./imgs/step2_bg.png'),
      step3IMG: require('./imgs/step3_bg.png'),
      step4IMG: require('./imgs/step4_bg.png'),
      step5IMG: require('./imgs/step5_bg.png'),
      step6IMG: require('./imgs/step6_bg.png')
    }
  },
  mounted() {
    const isShowBiaoZhuGuide = localStorage.getItem('isShowBiaoZhuGuide')
    if (!isShowBiaoZhuGuide) {
      localStorage.setItem('isShowBiaoZhuGuide', true)
      this.isShowBiaoZhuGuide = true
    }
  },
  methods: {
    handleNextClick(stepNum) {
      this.stepNum = stepNum
    },
    handleSkipClick() {
      this.isShowBiaoZhuGuide = false
    }
  }
}
</script>

<style lang="less" scoped>
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9990;
  .overlay-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9990;
  }
  .overlay-mask-enter-active,
  .overlay-mask-leave-active {
    transition: background-color 0.4s ease-out;
  }
  .overlay-mask-enter,
  .overlay-mask-leave-to {
    background-color: transparent;
  }
  .overlay-body {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    .pagination-wrapper {
      position: absolute;
      top: .48rem;
      right: .48rem;
      display: flex;
      align-items: center;
      justify-content: center;

      .pagination{
        font-size: .4rem;
        margin-right: .24rem;
        color: #fff;
      }
      .skip-btn {
        width: 1.32rem;
        line-height: .66rem;
        font-size: .3rem;
        color: #fff;
        text-align: center;
        border: 1px solid #FFFFFF;
        border-radius: 4px;
      }
    }
    .step {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        .step-bg {
          width: 100%;
        }
    }
    .step-1 {
      .step-1-nextbtn {
        width: 3.78rem;
        height: 1.63rem;
        background:transparent;
        position:absolute;
        bottom: 3.65rem;
        left: 1rem;
      }
    }
    .step-2 {
      .step-bg {
        position: absolute;
        left: .7rem !important;
        bottom: 0;
        width: 5.82rem !important;
      }
      .step-2-btns {
        position: absolute;
        right: 1.22rem;
        bottom: 3rem;
        background: transparent;
        width: 3rem;
        height: .6rem;
        display: flex;
        .btn {
          flex: 1;
          height: 100%;
        }
      }
    }
    .step-3 {
      .step-3-btns {
        position: absolute;
        right: 3.8rem;
        bottom: 2.3rem;
        background: transparent;
        width: 3rem;
        height: .6rem;
        display: flex;
        .btn {
          flex: 1;
          height: 100%;
        }
      }
    }
    .step-4 {
      .step-4-btns {
        position: absolute;
        right: 2.53rem;
        bottom: 4.80rem;
        background: transparent;
        width: 3rem;
        height: .6rem;
        display: flex;
        .btn {
          flex: 1;
          height: 100%;
        }
      }
    }
    .step-5 {
      .step-5-btns {
        position: absolute;
        right: 3.3rem;
        bottom: 0.97rem;
        background: transparent;
        width: 3rem;
        height: .6rem;
        display: flex;
        .btn {
          flex: 1;
          height: 100%;
        }
      }
    }
    .step-6 {
      .step-6-btns {
        position: absolute;
        right: 3.04rem;
        bottom: 8.71rem;
        background: transparent;
        width: 1.5rem;
        height: .6rem;
      }
    }
  }
}
</style>
