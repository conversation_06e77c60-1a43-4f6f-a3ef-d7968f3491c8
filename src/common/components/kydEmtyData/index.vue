<!--
 * @Author: shigl
 * @Date: 2022-10-27 17:23:42
 * @LastEditTime: 2022-10-31 11:25:02
 * @Description: 空数据
-->
<template>
  <div class="emty_data_wrap" :style="{ height: height }">
    <img class="emty_img" :src="emtyImg" alt="" :style="imgStyle" />
    <div class="grey999 fs20">{{ text }}</div>
  </div>
</template>
<script>
import emtyImg from './yqtz_img.png'
export default {
  name: 'KydEmtyData',
  props: {
    text: {
      type: String,
      default: '暂无数据'
    },
    imgStyle: {
      type: Object,
      default: () => {}
    },
    height: {
      type: String,
      default: '3rem'
    }
  },
  data() {
    return {
      emtyImg
    }
  }
}
</script>
<style lang='less' scoped>
.emty_data_wrap {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .emty_img {
    width: 2.27rem;
    height: 2.27rem;
  }
}
</style>
