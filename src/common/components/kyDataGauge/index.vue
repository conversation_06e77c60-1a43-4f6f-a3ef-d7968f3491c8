<!--
 * @Author: JieJw
 * @Date: 2021-11-26 10:37:15
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-03-20 15:46:19
 * @Description:
-->
<template>
  <div class="ky-data-gauge-wrap">
    <div class="gauge-chart-box" ref="gaugeChartBox"></div>
    <div class="content" :style="{ bottom: type === 'day' ? 0 : '-0.32rem' }">
      <div class="text">
        <div :style="{ color: this.textColor }">
          <!-- {{ $numToInteger(this.value, 1, 0.01) }} -->
          <VueCountUp :endVal="value ? value * 100 : 0" :decimals="1" :duration="1500" />
        </div>
        <span :style="{ color: this.textColor }">%</span>
      </div>
      <div class="left" v-if="type === 'month'">
        <template v-if="+leftMonthNum === 0"> {{ $moment(date).add(1, 'months').format('M月') }}火力全开! </template>
        <template v-else>
          当月剩余
          <span :style="{ background: this.bgColor }">
            {{ leftMonthNum }}
            <span :style="{ background: this.bgColor }"></span>
          </span>
          天
        </template>
      </div>
      <div class="left" v-if="type === 'year'">
        <template v-if="+leftYearNum === 0"> {{ $moment(date).add(1, 'year').format('YYYY年') }}火力全开! </template>
        <template v-else>
          当年剩余
          <span :style="{ background: this.bgColor }">
            {{ leftYearNum }}
            <span :style="{ background: this.bgColor }"></span>
          </span>
          天
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { drawNewGaugeChart } from 'common/charts/chartOption'
import VueCountUp from '../vue-count-to/vue-countTo'
export default {
  components: {
    VueCountUp
  },
  props: {
    type: String,
    value: [Number, String],
    date: String
  },
  data() {
    return {}
  },
  computed: {
    leftMonthNum() {
      const latestDate = this.$moment(this.date)
      const lastMonthDate = this.$moment(this.date).endOf('month')
      return lastMonthDate.diff(latestDate, 'days')
    },
    leftYearNum() {
      const latestDate = this.$moment(this.date)
      const lastYearDate = this.$moment(this.date).endOf('year')
      return lastYearDate.diff(latestDate, 'days')
    },
    leftRate() {
      const lastYearDate = this.$moment(this.date).endOf('year')
      const lastMonthDate = this.$moment(this.date).endOf('month')
      const startYearDate = this.$moment(this.date).startOf('year')
      const startMonthDate = this.$moment(this.date).startOf('month')
      const yearNum = lastYearDate.diff(startYearDate, 'days') + 1
      const monthNum = lastMonthDate.diff(startMonthDate, 'days') + 1
      return this.type === 'month' ? (monthNum - this.leftMonthNum) / monthNum : (yearNum - this.leftYearNum) / yearNum
    },
    textColor() {
      // let value = this.value || 0
      // if (value > 1) {
      //   value = 1
      // } else if (value < 0) {
      //   value = 0
      // }
      const isFinish = this.type === 'day' ? this.value >= 1 : this.value >= this.leftRate
      const isUrget =
        this.type === 'day' ? false : this.type === 'month' ? this.leftMonthNum <= 10 : this.leftYearNum <= 30
      return isFinish ? '#06D7AD' : isUrget ? '#cb363a' : '#ee7547'
    },
    bgColor() {
      const isUrget = this.type === 'month' ? this.leftMonthNum <= 10 : this.leftYearNum <= 30
      return isUrget ? '#cb363a' : '#435163'
    }
  },
  watch: {
    value() {
      this.initChart()
    }
  },
  methods: {
    initChart() {
      let value = this.value || 0
      if (value > 100) {
        value = 100
      } else if (value < 0) {
        value = 0
      }
      const option = {
        series: [
          {},
          {
            itemStyle: {
              color: this.textColor
            },
            data: [
              {
                value: value * 100
              }
            ]
          },
          {},
          {},
          {
            data: [
              {
                value: value * 100
              }
            ]
          }
        ]
      }
      drawNewGaugeChart(option, this.$refs.gaugeChartBox)
    }
  }
}
</script>

<style lang="less" scoped>
.ky-data-gauge-wrap {
  position: relative;
  z-index: 0;
  .gauge-chart-box {
    height: 100%;
    width: 100%;
  }
  .content {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    z-index: 1;
    .text {
      display: flex;
      text-align: center;
      > div {
        /deep/.num-card {
          display: flex;
          font-family: Roboto-Medium;
          font-size: 0.48rem;
          line-height: 0.48rem;
          font-weight: 700;
        }
      }
      > span {
        font-family: Roboto-Medium;
        font-size: 0.48rem;
        line-height: 0.48rem;
        font-weight: 700;
        &:last-child {
          font-size: 0.24rem;
          line-height: 0.3rem;
          font-weight: 700;
          align-self: flex-end;
        }
      }
    }
    .left {
      height: 0.32rem;
      line-height: 0.28rem;
      font-size: 0.2rem;
      color: #333;
      text-align: center;
      font-weight: 700;
      background: #f8f9fc;
      border: 0.02rem solid rgba(187, 204, 227, 0.2);
      border-radius: 0.16rem;
      padding: 0 0.2rem;
      margin-top: 0.12rem;
      > span {
        display: inline-block;
        margin: 0 0.1rem;
        width: 0.32rem;
        height: 100%;
        font-size: 0.24rem;
        color: #fff;
        text-align: center;
        font-weight: 700;
        position: relative;
        > span {
          display: inline-block;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          height: 0.02rem;
          width: 100%;
        }
      }
    }
  }
}
</style>
