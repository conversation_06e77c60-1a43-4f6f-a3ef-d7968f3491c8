<template>
    <!-- 上下箭头 -->
    <span>
        <i v-if="num>0" class="iconfont icon-up" :class="[ perType==='up' ? 'green' : 'orange']"
           :style="{fontSize:fontSize+'rem'}"></i>
        <i v-if="num<0" class="iconfont icon-down" :class="[ perType==='up' ? 'orange' : 'green']"
           :style="{fontSize:fontSize+'rem'}"></i>
    </span>
</template>
<script>
export default {
  // num:数字 , perType: 正负向指标(up正 down负) fontSize:像素大小
  props: {
    num: [Number, String],
    perType: String,
    fontSize: String
  }
}
</script>
<style lang='less' scoped>
</style>
