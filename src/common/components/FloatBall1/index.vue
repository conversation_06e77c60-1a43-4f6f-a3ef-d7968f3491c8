<!--
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-11 16:27:26
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-12-01 13:55:00
-->
<template>
  <div class="overlay" v-if="auth && auth.length>0">
    <div
      :class="{ 'overlay-menu-container': true, 'overlay-menu__open': isOpened, 'overlay-menu__back': isBack }"
      ref="overlayMenuDom"
      :style="{ top: menutop + 'px' }"
    >
      <div
        class="overlay-menu-open-btn"
        @click="open"
        @touchstart="touchstart($event)"
        @touchmove="touchMove($event)"
        @touchend="touchEnd($event)"
      >
        <!-- AI问数 -->
        <!-- <div class="open-btn-img"></div>
        <div class="open-btn-title">AI问数</div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    auth: {
      type: Array,
      default: () => []
    }
  },
  components: {
  },
  computed: {
    ...mapState({
      zoneCode: state => state.userMsg.deptCode
    })
  },
  watch: {
    // 监听路由变化
    '$route'(to, from) {
      // console.log('路由发生变化了！')
      // console.log('从:', from)
      // console.log('到:', to)
      if (to.name === 'assistant') {
        this.isOpened = true
      } else {
        this.isOpened = false
      }
    }
  },
  data() {
    return {
      menutop: 0,
      isOpened: false,
      isBack: false,
      timeOutEvent: null,
      // 手指原始位置
      oldMousePos: {},
      // 元素原始位置
      oldNodePos: {},
      btnType: 'right'
    }
  },
  methods: {
    open() {
      // this.isBack = false
      // this.isOpened = true
      this.$router.push({
        name: 'assistant'
      })
    },
    close() {
      setTimeout(() => {
        this.isOpened = false
      })
      this.isBack = true
    },
    touchstart(ev) {
      const selectDom = ev.target
      const { pageX, pageY } = ev.touches[0] // 手指位置
      const { offsetLeft, offsetTop } = selectDom // 元素位置
      // 手指原始位置
      this.oldMousePos = {
        x: pageX,
        y: pageY
      }
      // 元素原始位置
      this.oldNodePos = {
        x: offsetLeft,
        y: offsetTop
      }
    },
    touchMove(ev) {
      const selectDom = ev.target
      // x轴偏移量
      const lefts = this.oldMousePos.x - this.oldNodePos.x
      // y轴偏移量
      const tops = this.oldMousePos.y - this.oldNodePos.y
      const { pageX, pageY } = ev.touches[0] // 手指位置
      selectDom.style.left = `${pageX - lefts}px`
      selectDom.style.top = `${pageY - tops}px`
    },
    touchEnd(ev) {
      const selectDom = ev.target
      const { clientHeight, clientWidth } = document.body
      const { offsetTop, offsetLeft, offsetWidth } = selectDom
      // 注意点(offsetTop相对父元素定位)
      this.menutop = offsetTop + this.menutop

      if (this.menutop < 120) {
        this.menutop = 120
      } else if (clientHeight - this.menutop < 120) {
        this.menutop = clientHeight - 120
      }

      if (offsetLeft > -16) {
        selectDom.style.left = -0.20 + 'rem'
      } else if (clientWidth + offsetLeft < offsetWidth + 16) {
        const leftWidth = clientWidth - offsetWidth
        selectDom.style.left = -(leftWidth - 16) + 'px'
      }
      // selectDom.style.left = 0
      selectDom.style.top = 0
      localStorage.setItem('floatBallTop1', this.menutop)
    }
  },
  mounted() {
    this.$nextTick(() => {
      const floatBallTop = localStorage.getItem('floatBallTop1')
      if (floatBallTop) {
        this.menutop = Number(floatBallTop)
      } else {
        const { clientHeight } = document.body
        this.menutop = clientHeight - 260
      }
    })
  }
}
</script>

<style lang="less" scoped>
@animation-step-1-time: 0.2s;
@animation-step-2-time: 0.2s;

.overlay {
  z-index: 999900;

  .overlay-menu-container {
    position: fixed;
    right: 0;
    width: 0.96rem;
    height: 0.96rem;
    z-index: 999909;
    .overlay-menu-open-btn {
      position: absolute;
      left: -0.16rem;
      top: 0;
      width: 0.96rem;
      height: 0.96rem;
      border-radius: 48rem;
      opacity: 1;
      z-index: 999920;
      background: #FFFFFF;
      box-shadow: 0px 6px 24px 0px rgba(22, 67, 135, 0.12);
      background-image: url('./img/assistance.gif'), url('./img/assistant_text.png');
      background-size: 0.48rem 0.45rem, 0.53rem 0.18rem;
      background-repeat: no-repeat, no-repeat;
      background-position: 50% 0.12rem, 50% 0.58rem;
      box-sizing: border-box;
      padding-top: 0.62rem;
      text-align: center;
    }
  }

  .overlay-menu__open {
    animation: menu_open @animation-step-1-time ease-out forwards;
    .overlay-menu-open-btn {
      animation: menu_open_openbtn @animation-step-1-time ease forwards;
    }
  }

  .overlay-menu__back {
    animation: memu_back @animation-step-1-time ease-out @animation-step-2-time both;
    .overlay-menu-open-btn {
      animation: menu_back_openbtn @animation-step-1-time ease @animation-step-2-time both;
    }
  }
}

@keyframes menu_open {
  0% {
    right: -0.28rem;
  }
  100% {
    right: 0.4rem;
  }
}
@keyframes memu_back {
  0% {
    right: 0.4rem;
  }
  100% {
    right: -0.28rem;
  }
}

@keyframes menu_open_openbtn {
  0% {
    opacity: 1;
    z-index: 999920;
  }
  100% {
    opacity: 0;
    z-index: 999910;
  }
}

@keyframes menu_back_openbtn {
  0% {
    opacity: 0;
    z-index: 999910;
  }
  100% {
    opacity: 1;
    z-index: 999920;
  }
}
</style>
