.kyd_summary_data_list_container {
  &.grid_1 {
    width: 100%;
  }
  &.grid_2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 0.24rem;
  }
  &.grid_3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 0.1rem;
  }
  &.grid_4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 0.1rem;
  }
  .card_wrap {
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    background-position: right bottom;
    background-repeat: no-repeat;
    background-size: 1.1rem;

    &.border {
      border: 1px solid rgba(221, 221, 221, 0.7);
    }

    // 点击选中样式
    &.select {
      background: #f8f9fc;
      border: 1px solid #dc1e32;
      position: relative;
      background-position: right bottom;
      background-repeat: no-repeat;
      background-size: 1.1rem;
      > img {
        width: 0.32rem;
        height: 0.32rem;
        position: absolute;
        top: -1px;
        right: -1px;
      }
      // &::before {
      // content: '\e68a';
      // font-family: iconfont;
      // font-size: 0.32rem;
      // color: #dc1e32;
      // position: absolute;
      // top: -0.5px;
      // right: 0;
      // }
    }
    .card_title {
      border-bottom: 1px solid rgba(221, 221, 221, 0.7);
      padding: 0.12rem 0;
      line-height: 0.33rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
      }
      .label {
        color: #333;
        font-weight: 700;
        margin-left: 0.08rem;
      }
      .text {
        margin-left: 0.08rem;
        color: #666;
        font-weight: 700;
      }
      .unit {
        font-weight: 700;
        color: #666;
        margin-left: 0.04rem;
      }
    }
    .card_header {
      background: #f5f5f5;
      border-bottom: 1px dashed #c6c7cc;
      padding: 0.12rem 0;
      line-height: 0.33rem;
      border-radius: 4px 4px 0 0;
      img {
        width: 0.32rem;
        height: 0.32rem;
      }
      .label {
        color: #999;
        margin-left: 0.08rem;
      }
      .text {
        margin-left: 0.08rem;
        color: #666;
        font-weight: 700;
      }
      .unit {
        font-weight: 700;
        color: #666;
        margin-left: 0.04rem;
      }
    }
    .card_content {
      &.normal {
        padding: 0.2rem;
      }
      &.default {
        padding: 0.2rem;
      }
      &.small {
        padding: 0.12rem;
      }
      &.medium {
        padding: 0.16rem 0.12rem;
      }
      .card_sub_title {
        line-height: 0.4rem;
        display: flex;
        img {
          width: 0.32rem;
          height: 0.32rem;
        }
        .label {
          color: #333;
          font-weight: 700;
        }
        .text {
          margin-left: 0.08rem;
          color: #666;
          font-weight: 700;
        }
        .unit {
          font-weight: 700;
          color: #666;
          margin-left: 0.04rem;
        }
        &.medium {
          .label {
            font-size: 0.2rem;
          }
          .text_content {
            .text {
              font-size: 0.24rem;
            }
          }
        }
      }
      .parent_box {
        display: flex;
        .parent_wrap {
          .label {
            color: #666;
            line-height: 0.32rem;
          }
          .text_content {
            margin-top: 0.12rem;
            display: flex;
            align-items: center;
            .text {
              font-size: 0.32rem;
              font-weight: 700;
            }
            .unit {
              font-weight: 700;
              margin-left: 0.04rem;
            }
          }
        }
        &.normal {
          .parent_wrap {
            .text_content {
              .text {
                font-size: 0.32rem;
              }
            }
          }
        }
        &.default {
          .parent_wrap {
            .text_content {
              .text {
                font-size: 0.32rem;
              }
            }
          }
        }
        &.small {
          .parent_wrap {
            .label {
              color: #666;
              font-size: 0.2rem;
              font-weight: 700;
            }
            .text_content {
              .text {
                font-size: 0.24rem;
              }
            }
          }
        }
        &.medium {
          .parent_wrap {
            .label {
              color: #666;
              font-size: 0.2rem;
            }
            .text_content {
              margin-top: 0.12rem;
              .text {
                font-size: 0.24rem;
              }
            }
          }
        }
      }
      .child_box {
        display: flex;
        > div {
          &:first-child {
            margin-left: 0rem;
          }
        }
        .child_wrap {
          margin-top: 0.16rem;
          .label {
            font-size: 0.24rem;
            color: #999999;
          }
          .text {
            font-size: 0.24rem;
            font-weight: 700;
            margin-left: 0.04rem;
          }
          .unit {
            font-weight: 700;
            margin-left: 0.04rem;
            font-size: 0.2rem;
          }
        }
        &.normal {
          > div {
            margin-left: 0.16rem;
            &:first-child {
              margin-left: 0rem;
            }
          }
        }

        &.default {
          flex-direction: column;
          justify-content: center;
        }
        &.small {
          flex-direction: column;
          justify-content: center;
          .label {
            font-size: 0.2rem;
          }
          .text {
            font-size: 0.2rem;
          }

          > div {
            margin-left: 0rem;
          }
        }
        &.medium {
          // flex-direction: column;
          // justify-content: center;
          .label {
            font-size: 0.2rem;
          }
          .text {
            font-size: 0.2rem;
          }

          > div {
            margin-left: 0rem;
          }
        }
      }
      .complete_box {
        display: flex;
        height: 0.4rem;
        line-height: 0.4rem;
        border-radius: 0.1rem;
        background: #F8F9FC;
        width: fit-content;
        padding: 0rem 0.1rem;
        margin-top: 0.1rem;
        color: #666666;
        .completeRate_wrap {
          .text_content {
            // margin-top: 0.12rem;
            display: flex;
            align-items: center;
            .label {
              color: #666;
              font-size: 0.24rem;
            }
            .text {
              font-size: 0.24rem;
              font-weight: 500;
              color: #CC1B23;
              margin-left: 0.12rem;
            }
            .unit {
              font-weight: 500;
              margin-left: 0.04rem;
            }
          }
        }
        &.normal {
          .completeRate_wrap {
            .text_content {
              .text {
                font-size: 0.24rem;
                font-weight: 500;
                color: #CC1B23;
                margin-left: 0.12rem;
              }
              .label {
                color: #666;
              }
            }
          }
        }
        &.default {
          .completeRate_wrap {
            .text_content {
              .text {
                font-size: 0.24rem;
                font-weight: 500;
                color: #CC1B23;
                margin-left: 0.12rem;
              }
              .label {
                color: #666;
              }
            }
          }
        }
        &.small {
          .completeRate_wrap {
            .text_content {
              .text {
                font-size: 0.2rem;
                font-weight: 500;
                color: #CC1B23;
                margin-left: 0.12rem;
              }
              .label {
                color: #666;
                font-size: 0.2rem;
              }
            }
          }
        }
        &.medium {
          .completeRate_wrap {
            .text_content {
              .label {
                color: #666;
                font-size: 0.24rem;
              }
              .text {
                font-size: 0.24rem;
                margin-left: 0.12rem;
              }
            }
          }
        }
      }
      .child_box_header {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        .child_box_detail {
          color: #666666;
          background: #F8F9FC;
          font-size: 0.23rem;
          height: 0.42rem;
          line-height: 0.42rem;
          border-radius: 0.24rem;
          padding: 0rem 0.16rem;
          // font-size: 22px;
          // height: 40px;
          // line-height: 40px;
          // border-radius: 24px;
          // padding: 0px 12px;
        }
      }
    }
    .card_rate {
      .rate_wrap {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        padding: 0.16rem 0.2rem 0.16rem 0.2rem;
        &.green {
          background: #effbf7;
          border-top: 1px dashed #08bca0;
          .bar {
            background: #08bca0;
          }
        }
        &.orange {
          background: #fff6f1;
          border-top: 1px dashed #ff6a17;
          .bar {
            background-color: #ff4902;
          }
        }
        .label {
          font-size: 0.2rem;
          color: #666;
          font-weight: 700;
        }
        .chart_bar {
          background: #eaecf0;
          border-radius: 0.08rem;
          height: 0.16rem;
          //   width: 44%;
          flex: 1;
          margin: 0 0.04rem;
          .bar {
            border-radius: 0.08rem;
            height: 0.16rem;
          }
        }
        .text {
          font-size: 0.2rem;
          color: #333;
          font-weight: 700;
        }
        &.normal {
          .label {
            font-weight: 700;
          }
        }
        &.default {
          .label {
            font-weight: 700;
          }
        }
        &.small {
          padding: 0.08rem 0.1rem;
          .label {
            font-weight: normal;
          }
          .chart_bar {
            background: #eaecf0;
            border-radius: 0.04rem;
            height: 0.08rem;
            .bar {
              border-radius: 0.04rem;
              height: 0.08rem;
            }
          }
        }
      }
    }
    .card_footer {
      &.normal {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background: #f8f9fc;
        padding: 0.12rem 0;
        border-radius: 0 0 4px 4px;
      }
      &.default {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background: #f8f9fc;
        padding: 0.12rem 0;
        border-radius: 0 0 4px 4px;
      }
      &.small {
        // background-image: linear-gradient(
        //   270deg,
        //   rgba(32, 113, 213, 0.164) 0%,
        //   rgba(210, 221, 235, 0.048) 0%,
        //   rgba(165, 195, 242, 0.37) 100%
        // );
        background: #f1f3fb;
        padding: 0.12rem;
        line-height: 0.28rem;
        border-radius: 0 0 4px 4px;
      }
      .footer_wrap {
        .label {
          font-size: 0.20rem;
          color: #999999;
        }
        .text {
          font-size: 0.20rem;
          font-weight: 700;
          margin-left: 0.04rem;
        }
        .unit {
          font-weight: 700;
          margin-left: 0.00rem;
          font-size: 0.24rem;
        }
      }
    }
  }
}
