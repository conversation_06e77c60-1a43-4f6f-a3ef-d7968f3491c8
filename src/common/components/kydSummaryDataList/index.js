import targetIcon from './img/target_icon.svg'
import { numFormat } from '../../js/filters'
import check12Icon from '../../img/check12_icon.svg'
import './index.less'
export default {
  name: 'KydSummaryDataList',
  props: {
    // columns 示例
    //    header: [
    //       {
    //         label: '目标',
    //         dataIndex: 'target',
    //         int: [0, 10000],
    //         unit: '万元'
    //       }
    //     ],
    //     parent: [
    //       {
    //         label: '产品收入(税前)',
    //         dataIndex: 'profit',
    //         int: [0, 10000],
    //         unit: '万元'
    //       }
    //     ],
    //     child: [
    //       {
    //         label: '环比',
    //         dataIndex: 'hb',
    //         per: [1],
    //         indexType: 'up'
    //       },
    //       {
    //         label: '同比',
    //         dataIndex: 'tb',
    //         per: [1],
    //         indexType: 'up'
    //       }
    //     ],
    //     footer: [
    //       {
    //         label: '收入达成',
    //         dataIndex: 'rate',
    //         per: [1]
    //       }
    //     ]
    // 权限判断,默认为true,空数据时显示'-',false时所有数据为'***'
    isAuth: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array,
      default: () => {
        return []
      }
    },
    dataSource: {
      type: [Array, Object],
      default: () => {
        return []
      }
    },
    // 每行展示数量 1,2,3
    colNum: {
      type: [String, Number],
      default: 2
    },
    isBorder: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'normal'
    },
    onSelect: {
      type: Function
    },
    selectIndex: {
      type: [String, Number],
      default: 0
    },
    bgcImg: {
      type: Array,
      default: () => []
    },
    bgColor: {
      type: String,
      default: '#fff'
    },
    toDetail: {
      type: Function
    }
    // showDetailBtn: {
    //   type: Boolean,
    //   default: false
    // }
  },
  data() {
    return {}
  },

  render(h) {
    const { isAuth, columns, dataSource, colNum, size, onSelect, selectIndex, isBorder, bgcImg, bgColor, toDetail } = this
    // dataSource处理
    const dataFilter = index => {
      if (Array.isArray(dataSource)) {
        return dataSource.length && dataSource[index]
      }
      return dataSource
    }
    // label处理
    const labelFilter = (data, index) => {
      const { label } = data
      if (label && typeof label === 'function') {
        return label(h, index, dataFilter(index))
      }
      return label
    }
    // 单位处理
    const unitFilter = (data, index) => {
      const { unit } = data
      if (unit && typeof unit === 'function') {
        return unit(h, index, dataFilter(index))
      }
      return unit
    }
    // 数据处理
    const numFilter = (data, index) => {
      const {
        dataIndex,
        int,
        per,
        render,
        indexType,
        isFontColor = false,
        targetNum = 0,
        isShowIcon = true,
        fontStyle = { fontSize: '0.2rem', fontWeight: 700 },
        iconStyle = { fontSize: '0.2rem' }
      } = data
      let type = ''
      let content = []
      if (int) {
        type = 'int'
        content = int
      }
      if (per) {
        type = 'per'
        content = per
      }
      if (render && typeof render === 'function') {
        return isAuth
          ? numFormat(render(h, dataFilter(index)[dataIndex], dataFilter(index), index), type, content)
          : '***'
      }
      // 文字有颜色时
      if (indexType && isFontColor) {
        return (
          <TargetIndex
            num={dataFilter(index)[dataIndex]}
            showNum={isAuth ? numFormat(dataFilter(index)[dataIndex], type, content) : '***'}
            targetNum={targetNum}
            isFontColor={isFontColor}
            fontStyle={fontStyle}
            iconStyle={iconStyle}
            indexType={indexType}
            isShowIcon={isShowIcon}
          />
        )
      }
      if (dataIndex) {
        return isAuth ? numFormat(dataFilter(index)[dataIndex], type, content) : '***'
      }
    }
    // 箭头处理
    const arrowsFilter = (data, index) => {
      const {
        dataIndex,
        indexType,
        isFontColor = false,
        targetNum = 0,
        isShowIcon = true,
        fontStyle = { fontSize: '0.24rem', fontWeight: 700 },
        iconStyle = { fontSize: '0.20rem' }
      } = data
      // 保留箭头
      if (indexType && !isFontColor) {
        return (
          <TargetIndex
            num={dataFilter(index)[dataIndex]}
            targetNum={targetNum}
            indexType={indexType}
            isFontColor={isFontColor}
            fontStyle={fontStyle}
            iconStyle={iconStyle}
            isShowIcon={isShowIcon}
          />
        )
      }
      return <span></span>
    }
    // 卡片样式
    const CardBody = () => {
      const headerFun = (item, index) => {
        const { header } = item
        if (!header) return
        const HeaderDom = ({ props }) => {
          const { data } = props
          const { icon = '', isShowIcon = true } = data
          return (
            <div class={'card_header flex_center'}>
              {isShowIcon ? <img src={icon || targetIcon} alt="" /> : ''}
              <div class={'label'}>{labelFilter(data, index)}</div>
              <div class={'text flex_start'}>
                {numFilter(data, index)}
                {arrowsFilter(data, index)}
              </div>
              <div class={'unit'}>{unitFilter(data, index)}</div>
            </div>
          )
        }
        // header是数组
        if (Array.isArray(header)) {
          return header.map((data, i) => {
            return <HeaderDom data={data} />
          })
        }
        // header是对象
        return <HeaderDom data={header} />
      }
      const titleFun = (item, index) => {
        const { title } = item
        if (!title) return
        const TitleDom = ({ props }) => {
          const { data } = props
          const { icon = '', isShowIcon = false } = data
          return (
            <div class={'card_title flex0'}>
              {isShowIcon ? <img src={icon || targetIcon} alt="" /> : ''}
              <div class={'label'}>{labelFilter(data, index)}</div>
              <div class={'text flex_start'}>
                {numFilter(data, index)}
                {arrowsFilter(data, index)}
              </div>
              <div class={'unit'}>{unitFilter(data, index)}</div>
            </div>
          )
        }
        if (Array.isArray(title)) {
          return title.map((data, i) => {
            return <TitleDom data={data} />
          })
        }
        return <TitleDom data={title} />
      }
      const contentFun = (item, index) => {
        const { subTitle, parent, child, completeRate, detailBtn } = item
        const subTitleFun = () => {
          if (!subTitle) return
          const SubtitleDom = ({ props }) => {
            const { data } = props
            const { icon = '', isShowIcon = false } = data
            return (
              <div>
                {isShowIcon ? <img src={icon || targetIcon} alt="" /> : ''}
                <div class={'label'}>{labelFilter(data, index)}</div>
                <div class={'text flex_start'}>
                  {numFilter(data, index)}
                  {arrowsFilter(data, index)}
                </div>
                <div class={'unit'}>{unitFilter(data, index)}</div>
              </div>
            )
          }
          if (Array.isArray(subTitle)) {
            return subTitle.map((data, i) => {
              return <SubtitleDom data={data} />
            })
          }
          return <SubtitleDom data={subTitle} />
        }
        const parentFun = () => {
          if (!parent) return
          const ParentDom = ({ props }) => {
            const { data } = props
            const { width = '100%' } = data
            return (
              <div class={`parent_wrap `} style={{ width: width }}>
                <div class={'label'}>{labelFilter(data, index)}</div>
                <div class={'text_content'}>
                  <div class={'text flex_start'}>
                    {numFilter(data, index)}
                    {arrowsFilter(data, index)}
                  </div>
                  <div class={'unit'}>{unitFilter(data, index)}</div>
                </div>
              </div>
            )
          }
          if (Array.isArray(parent)) {
            return parent.map((data, i) => {
              return <ParentDom data={data} />
            })
          }
          return <ParentDom data={parent} />
        }
        const childFun = () => {
          if (!child) return
          const ChildDom = ({ props }) => {
            const { data } = props
            const { type = 'line', width = '100%' } = data
            const classType = {
              line: 'flex_start',
              column: 'line_h33 '
            }[type]
            return (
              <div class={'child_wrap'} style={{ width: width }}>
                <div class={classType}>
                  <div class={'label'}>{labelFilter(data, index)}</div>
                  <div class={'text flex_start'}>
                    {numFilter(data, index)}
                    {arrowsFilter(data, index)}
                  </div>
                  <div class={'unit'}>{unitFilter(data, index)}</div>
                </div>
              </div>
            )
          }
          if (Array.isArray(child)) {
            return child.map((data, i) => {
              return <ChildDom data={data} />
            })
          }
          return <ChildDom data={child} />
        }
        // 查看详情
        const detailFun = () => {
          if (!detailBtn) return
          const DetailBtnDom = ({ props }) => {
            const { data } = props
            return (
              <div class={`child_box_detail ${size}`} onclick={() => toDetail && toDetail(item, index)}>
                <span>查看{data.name}</span>
                <i class="iconfont icon-dayuhao fs20"></i>
              </div>
            )
          }
          if (Array.isArray(detailBtn)) {
            return detailBtn.map((data, i) => {
              return <DetailBtnDom data={data} />
            })
          }
          return <DetailBtnDom data={detailBtn} />
        }
        // 达成率
        const subCompleteFun = () => {
          if (!completeRate) return
          const SubCompleteFun = ({ props }) => {
            const { data } = props
            const { width = '100%' } = data
            return (
              <div class={'completeRate_wrap'} style={{ width: width }}>
                <div class={'text_content'}>
                  <div class={'label'}>{labelFilter(data, index)}</div>
                  <div class={'text flex_start'}>
                    {numFilter(data, index)}
                    {arrowsFilter(data, index)}
                  </div>
                  <div class={'unit'}>{unitFilter(data, index)}</div>
                </div>
              </div>
            )
          }
          if (Array.isArray(completeRate)) {
            return completeRate.map((data, i) => {
              return <SubCompleteFun data={data} />
            })
          }
          return <SubCompleteFun data={completeRate} />
        }
        return (
          <div>
            <div class={`child_box_header ${size}`}>
              <div class={`child_box_left ${size}`}>
                <div class={`card_sub_title ${size}`}>{subTitleFun()}</div>
                <div class={`parent_box ${size}`}>{parentFun()}</div>
              </div>
              <div class={`child_box_right ${size}`}>{detailFun()}</div>
              {/* {this.showDetailBtn ? <div class={`child_box_detail ${size}`} onclick={() => toDetail && toDetail(item, index)}>查看省区<i class="iconfont icon-dayuhao fs20"></i></div> : null} */}
            </div>
            <div class={`complete_box ${size}`}>{subCompleteFun()}</div>
            <div class={`child_box ${size}`}>{childFun()}</div>
          </div>
        )
      }
      const fateFun = (item, index) => {
        const { rate } = item
        if (!rate) return
        const RateDom = ({ props }) => {
          const { data } = props
          const { dataIndex } = data
          const rate = dataFilter(index)[dataIndex]
          let barWidth = 0
          if (!rate) {
            barWidth = '0%'
          } else if (rate < 1) {
            barWidth = rate * 100 + '%'
          } else {
            barWidth = '100%'
          }

          return (
            <div class={`rate_wrap ${rate >= 1 ? 'green' : 'orange'} ${size}`}>
              <div class={'label'}>{labelFilter(data, index)}</div>
              <div class={'chart_bar'}>
                <div class={'bar'} style={{ width: barWidth }}></div>
              </div>
              <div class={'text'}> {numFilter(data, index)}</div>
            </div>
          )
        }
        if (Array.isArray(rate)) {
          return rate.map((data, i) => {
            return <RateDom data={data} />
          })
        }
        return <RateDom data={rate} />
      }
      const footerFun = (item, index) => {
        const { footer } = item
        if (!footer) return
        const FooterDom = ({ props }) => {
          const { data } = props
          return (
            <div class={'footer_wrap'}>
              <div class={'flex_start'}>
                <div class={'label'}>{labelFilter(data, index)}</div>
                <div class={'text flex_start'}>
                  {numFilter(data, index)}
                  {arrowsFilter(data, index)}
                </div>
                <div class={'unit'}>{unitFilter(data, index)}</div>
              </div>
            </div>
          )
        }

        if (Array.isArray(footer)) {
          return footer.map((data, i) => {
            return <FooterDom data={data} />
          })
        }
        return <FooterDom data={footer} />
      }
      return columns.map((item, index) => {
        return (
          <div
            class={`card_wrap  ${isBorder ? 'border' : ''} ${onSelect && selectIndex === index ? 'select' : ''}`}
            style={{ backgroundImage: `url(${bgcImg[index]})`, backgroundColor: bgColor }}
            onclick={() => onSelect && onSelect(dataFilter(index), index)}
          >
            {onSelect && selectIndex === index ? <img src={check12Icon} alt="" /> : ''}
            {headerFun(item, index)}
            {titleFun(item, index)}
            <div class={`card_content ${size}`}>{contentFun(item, index)}</div>
            <div class={'card_rate'}>{fateFun(item, index)}</div>
            <div class={footerFun(item, index) ? `card_footer ${size}` : ''}>{footerFun(item, index)}</div>
          </div>
        )
      })
    }
    return (
      <div class={`kyd_summary_data_list_container  grid_${colNum} `}>
        <CardBody />
      </div>
    )
  }
}
