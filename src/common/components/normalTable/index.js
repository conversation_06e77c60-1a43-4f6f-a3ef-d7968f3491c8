import './index.less'
import KydEmtyData from '../kydEmtyData'
import _ from 'lodash'

export default {
  name: 'NormalTable',
  props: {
    // 是否有行背景色
    isRowBgc: {
      type: Boolean,
      default: true
    },
    // 是否有边框。默认有
    border: {
      type: Boolean,
      default: true
    },
    // 头部是否有边框。默认没有(头部分组时可用)
    headBorder: {
      type: Boolean,
      default: false
    },
    // 尺寸 emun('medium', 'small' ,'little') 尺寸
    size: {
      type: String,
      default: 'small'
    },
    /**
     * @description: 数据列表
     * @param {boolean} [expand] 自定义添加,为true时列表可点击展开
     * @return {*}
     */
    dataSource: {
      type: Array,
      default: () => []
    },
    /**
     * @typedef columnsItemInterface columns元素接口
     * @property {string|jsx|function} label 如果是个方法，第一个参数为createElement
     * @property {string} [dataIndex] 取值key
     * @property {number} [width] 配置单元格宽度(rem)
     * @property {number} [align] 配置单元格文字位置(left, right, center)
     * @property {number} [fixed] 固定列表(left, right),目前只支持第一,二列(left),最后一列(right)
     * @property {string} [thClassName] 表头单元格classname
     * @property {string} [tdClassName] 表体单元格classname
     * @property {function} [render] 同上 参数为（h, value, rowData）类似antd design table
     *isMoreIcon：是否显示更多图标，如果为true，则可以通过点击该单元格右上角的更多图标展开更多内容；
     *isExpandIcon：是否显示展开图标，如果为true，则可以通过点击该单元格左侧的展开图标展开详细内容；
     *expandType：当isExpandIcon为true时，展开类型，可选值为'button'和'icon'，分别表示展开按钮和展开图标；
     */
    columns: {
      type: Array,
      default: () => []
    },
    childColumns: {
      type: Array,
      default: () => []
    },
    // 大于100%会提供滚动效果，且默认第一列固定
    width: {
      type: String,
      default: 'max-content'
    },
    height: {
      type: String,
      default: ''
    },
    maxHeight: {
      type: String,
      default: '9rem'
    },
    minHeight: {
      type: String,
      default: '2rem'
    },
    // 整行点击
    onSelect: {
      type: Function,
      default: () => {}
    },
    isAnimate: {
      type: Boolean,
      default: false
    },
    animateKey: {
      type: String,
      default: new Date().getTime().toString()
    },
    loading: {
      // 传入loading , 表单加载时不显示'暂无数据'
      type: Boolean,
      default: false
    },
    // 滚动指示器背景颜色
    scrollBarColor: {
      type: String,
      default: '#f4f4f4'
    },
    // 滚动指示器颜色
    sliderColor: {
      type: String,
      default: '#DC1E32'
    },
    // 是否显示滚动指示器
    isIndicator: {
      type: Boolean,
      default: true
    },
    // 是否固定表头
    isFixedHeader: {
      type: Boolean,
      default: true
    },
    // 表格出现滚动时的默认位置
    position: {
      type: String,
      default: 'left'
    },
    rowClassName: {
      type: Function,
      default: () => {
        return ''
      }
    },
    // 行下标,对这行进行居中展示
    centerIndex: {
      type: [Number, String],
      default: ''
    },
    loadMore: {
      type: Function,
      default: () => {}
    },
    hasMore: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      observer: null,
      // 是否向左偏移了距离
      skewingLeft: false,
      skewingRight: false,
      // 列是否固定
      isFixed: [false, false],
      isFixedLast: false,
      thead: [],
      colgroup: [],
      leftRatio: 0,
      wrapWidth: 0,
      showBar: false,
      hasLoadedMore: true
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        this.$nextTick(() => {
          this.setScrollBar()
          this.tableFixed()
        })
      },
      deep: true
    },
    columns: {
      handler(val) {
        this.$nextTick(() => {
          this.setScrollBar('columns')
          this.tableFixed()
        })
      },
      deep: true
    },
    hasMore: {
      handler(val) {
        this.hasLoadedMore = val
      },
      immediate: true
    },
    width() {
      this.$nextTick(() => {
        this.setScrollBar()
        this.tableFixed()
      })
    },
    centerIndex: {
      handler(val) {
        if (val || val === 0) {
          this.$nextTick(() => {
            this.trshow()
          })
        }
      }
    }
  },
  mounted() {
    // dom元素变化重新获取宽高
    this.observer = new ResizeObserver(
      _.throttle(() => {
        this.$nextTick(() => {
          this.setScrollBar()
          this.tableFixed()
          this.trshow()
        })
      }, 300)
    )
    this.observer.observe(this.$refs['tableOuterWrap'])
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
  },
  methods: {
    // 监听滚动
    handleScroll(e) {
      const target = e.target
      const { scrollLeft, scrollWidth, scrollTop, scrollHeight, clientHeight } = target
      if (this.isIndicator && this.showBar) {
        this.skewingLeft = scrollLeft > 2
        this.skewingRight = scrollWidth - scrollLeft > this.wrapWidth
        const ratio = Math.abs(scrollLeft / (scrollWidth - this.wrapWidth))
        this.leftRatio = `${ratio * 100}%`
      }
      if (scrollTop + clientHeight >= scrollHeight - 1 && this.hasLoadedMore) {
        this.loadMore && this.loadMore()
        this.hasLoadedMore = false
        return
      }
    },
    expandClick(e, item, index) {
      this.$emit('onExpandClick', item, index)
      // 阻止冒泡
      e.stopPropagation()
    },
    // 隐藏滚动条
    setScrollBar(type = '') {
      if (!this.$refs['tableOuterWrap'] || !this.$refs['tableWrap']) {
        return
      }
      this.$refs['tableWrap'].style.width = this.width
      // table可视区宽高
      const { width, height } = this.$refs['tableOuterWrap'].getBoundingClientRect()
      // table 宽高
      const { width: tableWidth } = this.$refs['tableWrap'].getBoundingClientRect()
      this.wrapWidth = width
      // ios遮挡底部滚动条(maxHeight设为%高度时不可遮挡,因为不是明确高度)
      this.$refs.tableOuterShow.style.height = /%/.test(this.maxHeight) ? '' : height - 10 + 'px'
      // 如不用滚动就隐藏
      this.showBar = tableWidth > width
      const scrollLeft = this.$refs['tableOuterWrap'].scrollLeft
      if (!this.showBar && this.width === 'max-content') {
        this.$refs['tableWrap'].style.width = '100%'
      }
      const resetLeft = () => {
        this.leftRatio = 0
        this.skewingLeft = 0
        this.skewingRight = 0
        if (this.position === 'right') {
          this.$refs['tableOuterWrap'].scrollLeft = tableWidth
        }
        if (this.position === 'left') {
          this.$refs['tableOuterWrap'].scrollLeft = 0
        }
        // 默认表格size为little是在右边
        if (this.size === 'little') {
          this.$refs['tableOuterWrap'].scrollLeft = tableWidth
        }
      }

      // columns 配置变化,将表格重置
      if (type === 'columns') {
        resetLeft()
        return
      }
      if (scrollLeft !== 0 && scrollLeft !== tableWidth) return
      if (!type) {
        resetLeft()
      }
    },
    // 设置列表固定
    tableFixed() {
      // 设置表头是否固定
      const headerDom = this.$refs.sheader
      const sbodyDom = this.$refs.sbody
      if (!headerDom || !sbodyDom) return
      const trDomList = headerDom.querySelectorAll('tr')
      // 第一行表头
      const thDomList1 = trDomList[0].querySelectorAll('tr>th')
      // 第二行表头(不一定有th)
      const thDomList2 = trDomList[1].querySelectorAll('tr>th')
      if (!thDomList1.length) return
      const numList = []
      if (this.isFixedHeader) {
        for (let index = 0; index < thDomList1.length; index++) {
          thDomList1[index].style.position = 'sticky'
          thDomList1[index].style.top = 0
          thDomList1[index].style.zIndex = 1
          thDomList1[0].style.zIndex = 2
          const { height } = thDomList1[index].getBoundingClientRect()
          numList.push(height)
        }
        if (thDomList2.length) {
          // 取第一行高的最小值(最小值为分组之后的行高)
          const minNum = Math.min(...numList)
          if (this.isFixedHeader) {
            for (let index = 0; index < thDomList2.length; index++) {
              thDomList2[index].style.position = 'sticky'
              thDomList2[index].style.top = minNum + 'px'
              thDomList2[index].style.zIndex = 1
            }
          }
        }
      }

      // 列表固定
      const sbodyDomList = sbodyDom.querySelectorAll('tr')
      const { width } = thDomList1[0].getBoundingClientRect()
      this.columns.forEach((item, index) => {
        const { fixed = '' } = item

        if (index <= 1 && fixed === 'left') {
          this.isFixed = [true, index === 1]
          thDomList1[index].style.position = 'sticky'
          thDomList1[index].style.left = !index ? 0 : width + 'px'
          thDomList1[index].style.zIndex = 2
          sbodyDomList.forEach(item => {
            item.children[index].style.position = 'sticky'
            item.children[index].style.left = !index ? 0 : width + 'px'
          })
        }
        if (index === this.columns.length - 1 && fixed === 'right') {
          this.isFixedLast = true
          const lastThDom = thDomList1[thDomList1.length - 1]
          lastThDom.style.position = 'sticky'
          lastThDom.style.right = 0
          lastThDom.style.zIndex = 2
          sbodyDomList.forEach(item => {
            item.lastChild.style.position = 'sticky'
            item.lastChild.style.right = 0
          })
        }
        if (!fixed) {
          this.isFixedLast = false
        }
      })
    },
    /**
     * 生成表头
     * @param {function} h createElment
     * @return {object} type{{thead, colgroup}} 表头项目
     */
    generateHeadRow(h) {
      const { columns, headBorder } = this
      const colgroup = []
      const thCell1 = []
      const thCell2 = []
      // 目前只做两行表头
      columns.forEach((th, index) => {
        const { width = '', dataIndex, label, thClassName = '', align = 'left', colSpan = 1, children = [] } = th
        let thColSpan = colSpan
        if (children.length) {
          thColSpan = children.length
        }
        const thRowSpan = children.length ? '1' : '2'
        if (thColSpan && thRowSpan) {
          thCell1.push(
            <th
              key={dataIndex}
              class={`${thClassName} ${headBorder ? 'head-border' : ''}`}
              style={{ textAlign: align, width: width }}
              colspan={thColSpan}
              rowspan={thRowSpan}
            >
              {typeof label === 'function' ? label(h) : label}
            </th>
          )
        } else {
          thCell1.push(<span style={{ display: 'none', width: '0px' }}></span>)
        }
        if (children.length) {
          children.forEach(th => {
            const { width = '', dataIndex, label, thClassName = '', align = 'left' } = th
            thCell2.push(
              <th
                key={dataIndex}
                class={`${thClassName} ${headBorder ? 'head-border' : ''}`}
                style={{ textAlign: align, width: width }}
                rowspan={children.length ? '1' : '0'}
              >
                {typeof label === 'function' ? label(h) : label}
              </th>
            )
            const col = <col width={width}></col>
            colgroup.push(col)
          })
        } else {
          const col = <col width={width}></col>
          colgroup.push(col)
        }
      })
      const thead = (
        <thead class={`sheader`} ref="sheader">
          <tr>{thCell1}</tr>
          <tr>{thCell2}</tr>
        </thead>
      )
      return { thead, colgroup: <colgroup> {colgroup}</colgroup> }
    },
    trshow() {
      const sheaderDom = this.$refs['sheader']
      const tableOuterWrapDom = this.$refs['tableOuterWrap']
      const sbodyDom = this.$refs['sbody']
      if (sbodyDom) {
        const childDom = sbodyDom.childNodes
        const trDom = childDom[this.centerIndex]
        if (trDom) {
          const trScrollTop = trDom.offsetTop
          const height = sheaderDom.offsetHeight
          const showHeight = this.height || this.maxHeight
          if (/rem/.test(showHeight)) {
            // 行居中
            tableOuterWrapDom.scrollTop = trScrollTop - (showHeight.replace('rem', '') * 50) / 2 + height
          }
        }
      }
    }
  },
  render(h) {
    const {
      width,
      isRowBgc,
      columns,
      childColumns,
      dataSource,
      handleScroll,
      size,
      border,
      height,
      maxHeight,
      // minHeight,
      onSelect,
      expandClick,
      isAnimate,
      animateKey,
      loading,
      leftRatio,
      scrollBarColor,
      sliderColor,
      isIndicator,
      showBar,
      skewingLeft,
      skewingRight,
      isFixed,
      isFixedLast,
      rowClassName,
      centerIndex
      // loadMore
      // $event
    } = this
    const expandable = this.$slots.expandable
    const { colgroup, thead } = this.generateHeadRow(h)

    const BodyDom = () => {
      const res = []
      // td结构处理结构
      const tdDom = (columnsCol, rowData, rowIndex) => {
        const tdCell = []
        const mapFun = (tdCol, i) => {
          const {
            dataIndex,
            render,
            tdClassName = '',
            align = 'left',
            width = '',
            maxWidth = '',
            isExpandIcon = false,
            expandType = 'icon',
            isMoreIcon = false
            // isMerge = false
            // children = []
          } = tdCol

          // 滚动时第一列添加阴影...求阴影面积
          // 如果传入了render
          let value = rowData[dataIndex]
          // const childData = {}
          let propsObj = {}
          if (render && typeof render === 'function') {
            const renderData = render(h, dataIndex ? value : rowIndex, rowData, rowIndex)
            if (renderData && renderData.hasOwnProperty('component')) {
              value = renderData.component
            } else {
              value = renderData
            }
            if (renderData && renderData.hasOwnProperty('props')) {
              propsObj = renderData.props
            }
          }
          const MoreIcon = () => {
            if (isMoreIcon) {
              return <i class={`iconfont icon-kyd-dayuhao grey666 fs20 ml8`}></i>
            }
            return ''
          }
          const ExpandIcon = () => {
            if ((rowData.child && isExpandIcon) || (expandable && isExpandIcon)) {
              if (expandType === 'icon') {
                return (
                  <i
                    class={`iconfont icon-kyd-dayuhao grey666 fs20 ml8 ${
                      rowData.expand ? 'expand-open' : 'expand-close'
                    }`}
                  ></i>
                )
              }
              if (expandType === 'button') {
                return (
                  <div class={'expand-button flex_center'} onClick={event => expandClick(event, rowData, rowIndex)}>
                    {rowData.expand ? '收起' : '展开'}
                    <i
                      class={`iconfont icon-kyd-dayuhao grey666 fs20 ml4 ${
                        rowData.expand ? 'expand-open' : 'expand-close'
                      }`}
                    ></i>
                  </div>
                )
              }
              return ''
            }
          }
          const tmp = {
            left: 'start',
            center: 'center',
            right: 'end'
          }
          const { colSpan = 1, rowSpan = 1 } = propsObj
          // 有合并的数列
          if (colSpan && rowSpan) {
            tdCell.push(
              <td
                key={`${tdCol.dataIndex}_${rowIndex}_${i}`}
                class={`${tdClassName} ${colSpan > 1 || rowSpan > 1 ? 'merge_row' : ''} `}
                style={{ textAlign: align, width: width, maxWidth: maxWidth }}
                colspan={colSpan}
                rowspan={rowSpan}
              >
                <div class={`flex_${tmp[align]}  ${rowData.isWeight ? 'table-tr-weight' : ''}`}>
                  {value}
                  <MoreIcon />
                  <ExpandIcon />
                </div>
              </td>
            )
          } else {
            tdCell.push(<span style={{ display: 'none', width: '0px' }}></span>)
          }
        }
        columnsCol.map((tdCol, i) => {
          const { children = [] } = tdCol
          if (children.length) {
            children.map((tdCol, i) => {
              mapFun(tdCol, i)
            })
          } else {
            mapFun(tdCol, i)
          }
        })
        return tdCell
      }
      const defaultRowClassName = (rowData, index) => {
        if (!rowData || !isRowBgc) return
        const classList = []
        if (index % 2 !== 0) {
          classList.push('row_bgc')
        }
        // if (rowData.isMerge) {
        //   classList.push('merge_row_1')
        // }
        if (rowData.isBgc) {
          classList.push('default_row_bgc')
        }
        return classList.join(' ')
      }
      dataSource.forEach((rowData, index) => {
        const { isBgc, child, expand } = rowData
        res.push(
          <tr
            class={`${border ? 'border' : ''} ${isAnimate ? `animate__animated ${animateKey}` : ''}  ${
              isBgc && !rowClassName(rowData, index) ? 'row_bgc' : ''
            } ${rowClassName(rowData, index) || defaultRowClassName(rowData, index)} ${
              centerIndex === index ? 'center_row_bgc' : ''
            }`}
            onClick={() => onSelect(rowData, index)}
          >
            {tdDom(columns, rowData, index)}
          </tr>
        )
        // 如果有child拓展
        if (child && expand) {
          child.forEach((childRowData, childRowIndex) => {
            res.push(
              <tr
                class={` ${border ? 'border' : ''}`}
                // style={{
                //   display: expand ? 'table-row' : 'none'
                // }}
                onClick={() => onSelect(childRowData, childRowIndex)}
              >
                {tdDom(childColumns, childRowData, childRowIndex)}
              </tr>
            )
          })
        }
        // 如果有expandable插槽拓展
        if (expandable && expand) {
          res.push(
            <div
              class="expandable-tr "
              style={{
                // display: expand ? 'table-row' : 'none',
                height: 'auto',
                width: '100%',
                background: '#f8f9fc'
              }}
            >
              <td colspan={columns.length}>{expandable}</td>
            </div>
          )
        }
      })
      return res
    }
    const TbodyDom = () => {
      if (dataSource.length) {
        return (
          <tbody class="sbody" ref="sbody">
            <BodyDom />
          </tbody>
        )
      }
      if (size !== 'little') {
        if (!dataSource.length && !loading) {
          return (
            <div class="table-outer-empty">
              <KydEmtyData imgStyle={{ width: '1.6rem', height: '1.6rem' }} text={''} height={'2rem'}></KydEmtyData>
            </div>
          )
        }
      }
    }
    // 滚动指示器
    const MtableIndicator = () => {
      if (isIndicator && showBar && columns.length) {
        return (
          <div class={`table-scroll-container ${size}`}>
            <div class="table-scroll-bar" style={{ background: scrollBarColor }}>
              <div class="limit">
                <div style={{ left: leftRatio, background: sliderColor }}></div>
              </div>
            </div>
          </div>
        )
      } else {
        return ''
      }
    }
    const minHeightFun = () => {
      if (size !== 'little') {
        if (!dataSource.length) {
          return '2.4rem'
        }
      }
      if (size === 'little') {
        return '1rem'
      }
      return '2rem'
    }
    return (
      <div class="kyd-table-outer-wrap-container" ref="tableOuterWrapContainer">
        <div class="table-outer-show" ref="tableOuterShow">
          <div
            class={`table-outer-wrap fixed-left 
                    ${skewingLeft && isFixed[0] ? 'left1' : ''} 
                    ${skewingLeft && isFixed[1] ? 'left2' : ''} 
                    ${skewingRight && isFixedLast ? 'fixed-right' : ''}`}
            ref="tableOuterWrap"
            style={{ maxHeight, minHeight: minHeightFun(), height }}
            // onScroll={isIndicator && showBar ? handleScroll : () => false}
            onScroll={ handleScroll }
          >
            <table class={`table-wrap ${size} `} style={{ width: width, minWidth: '100%' }} ref="tableWrap">
              {colgroup}
              {thead}
              <TbodyDom />
            </table>
          </div>
        </div>
        <MtableIndicator />
      </div>
    )
  }
}
