.kyd-table-outer-wrap-container {
  position: relative;
}
.table-outer-show {
  overflow: hidden;
  background-color: white;
}
.table-outer-wrap {
  position: relative;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  table-layout: fixed;
  padding-bottom: 10px;

  &::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    display: none;
  }
  // 向左移动的时候-增加阴影
  &.fixed-left {
    &.left1 {
      tr {
        > *:first-child {
          position: relative;
          // background-color: #fff;
          &::after {
            content: '';
            position: absolute;
            right: -0.3rem;
            top: 0;
            width: 0.3rem;
            height: 100%;
            box-shadow: inset 3px 0px 3px -3px fade(black, 20%);
            pointer-events: none;
          }
        }
      }
    }

    &.left2 {
      tr {
        > *:nth-child(2) {
          // background-color: #fff;
          position: relative;
          &::after {
            content: '';
            position: absolute;
            right: -0.3rem;
            top: 0;
            width: 0.3rem;
            height: 100%;
            box-shadow: inset 3px 0px 3px -3px fade(black, 20%);
            pointer-events: none;
          }
        }
      }
    }
  }
  &.fixed-right {
    tr {
      > *:last-child {
        // background-color: #fff;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          left: -0.3rem;
          top: 0;
          width: 0.3rem;
          height: 100%;
          box-shadow: inset -3px 0px 3px -3px fade(black, 20%);
          pointer-events: none;
        }
      }
    }
  }
  .table-wrap {
    font-size: 0.24rem;
    border-collapse: collapse;
    // 正常尺寸
    @medium: 0.4rem;
    @small: 0.18rem;
    // th td padding(size)设置
    .cellPadding(@ud, @lr: 0.1rem) {
      padding: @ud @lr;
    }
    .default-rank {
      width: 0.33rem;
      height: 0.33rem;
    }
    tr {
      > * {
        vertical-align: middle;
        line-height: 0.32rem;
        background-color: #fff;
        &:first-child {
          padding-left: 0.2rem !important;
        }
        &:last-child {
          padding-right: 0.3rem !important;
        }
      }
    }

    &.medium {
      th,
      td {
        font-family: PingFangSC-Regular;
        box-sizing: border-box;
        .expand-open {
          transform: rotate(-90deg);
          transition: 200ms linear;
        }
        .expand-close {
          transform: rotate(90deg);
          transition: 200ms linear;
        }
      }
      .sheader {
        tr {
          th {
            .cellPadding(@medium);
            color: #888;
            background: #f8f9fc;
          }
        }
      }
      .sbody {
        position: relative;
        tr {
          &.border {
            border-bottom: 1px solid #f2f2f2;
          }
          td {
            .cellPadding(@medium);
            color: #000;
          }
        }
      }
    }
    // 小尺寸表格
    &.small {
      th,
      td {
        font-family: PingFangSC-Regular;
        box-sizing: border-box;
        .expand-button {
          margin-left: 0.08rem;
          height: 0.4rem;
          width: 0.8rem;
          border: 1px solid #f2f2f2;
          font-size: 0.2rem;
          color: #666;
          font-weight: 700;
          border-radius: 2px;
          background-color: #f8f9fc;
        }
        .expand-open {
          transform: rotate(-90deg);
          transition: 200ms linear;
        }
        .expand-close {
          transform: rotate(90deg);
          transition: 200ms linear;
        }
      }
      .sheader {
        tr {
          th {
            // background: #f8f9fc;
            background-color: #f1f3fb;
            .cellPadding(@small);
            font-weight: 700;
            color: #6e7b91;
            position: sticky;
            &.head-border {
              // border: 1px solid #f2f2f2;
              border-left: 1px solid rgba(110, 123, 145, 0.3);
              border-top: 1px solid rgba(110, 123, 145, 0.3);
              padding: 0.1rem 0.1rem;
            }
          }
          &:first-child {
            .head-border {
              border-top: none;
              &:first-child {
                border-left: none;
              }
            }
          }
        }
      }
      .sbody {
        position: relative;
        tr {
          &.border {
            border-bottom: 1px solid #f2f2f2;
          }
          &.row_bgc {
            > td {
              background: #fafafa !important;
            }
          }
          &.default_row_bgc {
            > td {
              background: #e9f6fe !important;
            }
          }
          &.center_row_bgc {
            > td {
              background-color: #ebf6fd !important;
              > div {
                font-weight: 700 !important;
              }
            }
          }
          .merge_row {
            border-right: 1px solid #f2f2f2;
            background-color: #fff !important;
          }

          td {
            .cellPadding(@small);
            color: #666;
            &:first-child {
              .default-rank {
                background: rgba(221, 221, 221, 0.47);
                border: 1px solid rgba(221, 221, 221, 0.47);
                border-radius: 50%;
                font-family: PingFangSC-Medium;
                font-size: 0.2rem;
                color: #afb7c0;
                text-align: center;
                line-height: 0.33rem;
                width: 0.33rem;
                box-sizing: content-box;
              }
            }
          }
        }
        .expandable-tr > td {
          background: #f8f9fc;
          padding: 0 !important;
        }
      }
    }
    &.little {
      th,
      td {
        background: #f5f5f5;
        font-size: 0.2rem;
        color: #666;
        .expand-open {
          transform: rotate(-90deg);
          transition: 200ms linear;
        }
        .expand-close {
          transform: rotate(90deg);
          transition: 200ms linear;
        }
      }
      th {
        padding: 0.24rem 0.16rem 0.14rem 0.16rem;
        font-weight: 700;
      }
      td {
        padding: 0.14rem 0.16rem;
      }
    }
  }
}
.table-outer-empty {
  position: absolute;
  left: 50%;
  bottom: 40%;
  transform: translate(-50%, 50%);
  color: #999;
  width: 100%;
  //   height: 2rem;
  text-align: center;
  //   line-height: 2rem;
}

.table-scroll-container {
  height: 0.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
  &.little {
    background-color: #f5f5f5;
  }
  .table-scroll-bar {
    width: 10%;
    height: 0.06rem;
    border-radius: 0.28rem;
    @barWidht: 56.25%;
    .limit {
      position: relative;
      width: 100% - @barWidht;
      height: 100%;
      > div {
        position: absolute;
        top: 0;
        left: 0;
        width: @barWidht / (100% - @barWidht) * 100;
        height: 100%;
        border-radius: 0.28rem;
      }
    }
  }
}

.table-tr-weight {
  font-weight: 700 !important;
  color: #333;
  div {
    font-weight: 700 !important;
  }
  span {
    font-weight: 700 !important;
  }
}
// 排名样式
.table-wrap {
  .default-rank {
    width: 0.33rem;
    height: 0.33rem;
  }
}
