<template>
  <div>
    <div class="ky-modal-content" v-if="visible" :style="{ width: width, height: height }">
      <div class="title">
        <span>{{ title }}</span>
        <i class="iconfont icon-guanbi" @click="closeModal"></i>
      </div>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'kymodal',
  props: {
    visible: Boolean,
    height: {
      type: String
      //   default: '60%'
    },
    width: {
      type: String,
      default: '85.3%'
    },
    title: {
      type: String
    }
  },
  watch: {
    visible(newVal, oldVal) {
      const targetEle = document.querySelector('.ky-modal-wrapper')
      if (newVal) {
        const pageDom1 = document.querySelector('.content-wrapper')
        const pageDom2 = document.querySelector('.contentWrapper')
        const bodyDom = document.body
        const pageDom = pageDom1 || pageDom2 || bodyDom
        const bgDiv = document.createElement('div')
        bgDiv.setAttribute('class', 'ky-modal-wrapper')
        bgDiv.appendChild(this.$el)
        pageDom.appendChild(bgDiv)
      } else if (!newVal && targetEle) {
        const pElement = targetEle.parentNode
        if (pElement) {
          pElement.removeChild(targetEle)
        }
      }
    }
  },
  methods: {
    closeModal() {
      this.$emit('closeModal')
    }
  }
}
</script>

<style lang="less" scoped>
.ky-modal-content {
  position: absolute;
  background: #fff;
  border-radius: 0.08rem;
  left: 50%;
  top: 45%;
  transform: translate(-50%, -50%);
  padding-bottom: 0.5rem;
  padding-top: 0.24rem;
}
.title {
  position: relative;
  line-height: 0.45rem;
  font-size: 0.32rem;
  text-align: center;
  span {
    font-weight: 700;
  }
  i {
    position: absolute;
    top: -0.1rem;
    right: 0.2rem;
    font-size: 12px;
  }
}
</style>

<style lang="less">
.ky-modal-wrapper {
  position: absolute;
  top: 0;
  z-index: 9999;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
}
</style>
