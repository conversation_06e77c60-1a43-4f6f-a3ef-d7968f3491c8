<!--
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-11 16:27:26
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-12-01 13:55:00
-->
<template>
  <div class="overlay">
    <!-- 遮罩蒙板 -->
    <transition name="overlay-mask">
      <div v-if="isOpened" class="overlay-mask" @click="close"></div>
    </transition>
    <!-- 悬浮菜单 -->
    <div
      :class="{ 'overlay-menu-container': true, 'overlay-menu__open': isOpened, 'overlay-menu__back': isBack }"
      ref="overlayMenuDom"
      :style="{ top: menutop + 'px' }"
    >
      <div class="overlay-menu-fan">
        <fan-item
          v-for="item in fanItemData"
          :key="item.type"
          class="overlay-menu-fan-item"
          :class="item.type"
          v-bind="item"
          @item-click="handleItemClick"
        />
        <div class="overlay-menu-close-btn" @click="close"></div>
      </div>
      <div
        class="overlay-menu-open-btn"
        @click="open"
        @touchstart="touchstart($event)"
        @touchmove="touchMove($event)"
        @touchend="touchEnd($event)"
      ></div>
    </div>
  </div>
</template>

<script>
import FanItem from './FanItem'
import { mapState } from 'vuex'
import feedback from './feedback.util'
export default {
  props: {},
  components: {
    FanItem
  },
  computed: {
    ...mapState({
      zoneCode: state => state.userMsg.deptCode
    })
  },
  data() {
    return {
      menutop: 0,
      fanItemData: [
        {
          imgUrl: require('./img/tool_tagging_icon.svg'),
          label: '标注',
          type: 'tag'
        },
        {
          imgUrl: require('./img/tool_task_icon.svg'),
          label: '任务',
          type: 'task'
        },
        {
          imgUrl: require('./img/tool_feedback_icon.svg'),
          label: '反馈',
          type: 'feedback'
        }
      ],
      isOpened: false,
      isBack: false,
      timeOutEvent: null,
      // 手指原始位置
      oldMousePos: {},
      // 元素原始位置
      oldNodePos: {},
      btnType: 'right'
    }
  },
  methods: {
    open() {
      this.isBack = false
      this.isOpened = true
      this.$sensors.pageview('悬浮小工具')
    },
    close() {
      setTimeout(() => {
        this.isOpened = false
      })
      this.isBack = true
    },
    handleItemClick(type) {
      if (!this.isOpened) return
      switch (type) {
        // 截图标注
        case 'tag':
          if (process.env.NODE_ENV !== 'development') {
            try {
              const params = {
                callback: res => {
                  if (res.errcode === 0) {
                    // console.log(res.data)
                    const { appVersion = '' } = res.data
                    if ((appVersion.split('.') || [0])[0] >= 6) {
                      const shareparams = {
                        data: { type: 1 },
                        callback: res => {
                          console.log(res)
                        }
                      }
                      // 收起动画再截屏
                      this.close()
                      setTimeout(() => {
                        SFNativeIM.shareScreenShot(shareparams)
                      }, 800)
                    } else {
                      this.$toast({
                        duration: 2000,
                        message: '请更新丰声版本V6以上！'
                      })
                    }
                  } else {
                    console.log('截图错误')
                  }
                }
              }
              SFNativeApp.getAppInfo(params)
            } catch (error) {
              console.log(error)
              this.$toast({
                duration: 2000,
                message: '请更新丰声版本V6以上！'
              })
            }
          }

          break
        // 任务
        case 'task':
          if (process.env.NODE_ENV !== 'development') {
            try {
              SFNativeTask.openCreateTask({
                callback: res => {
                  if (res.errcode === 0) {
                    // 成功
                    this.close()
                  } else {
                    // 失败

                    this.$toast({
                      duration: 2000,
                      message: `${res.errcode} ${res.errmsg}` || `${res.errcode}:SFNativeTask error callback`
                    })
                  }
                }
              })
            } catch (error) {
              console.log(error)
              this.$toast({
                duration: 2000,
                message: '请更新丰声版本V6以上！'
              })
            }
          }
          break
        case 'feedback':
          feedback().open()
          this.close()
          break
        default:
          break
      }
      const obj = {
        tag: '标注',
        task: '任务',
        feedback: '反馈'
      }
      this.$sensors.pageview('悬浮小工具-' + obj[type])
    },

    touchstart(ev) {
      const selectDom = ev.target
      const { pageX, pageY } = ev.touches[0] // 手指位置
      const { offsetLeft, offsetTop } = selectDom // 元素位置
      // 手指原始位置
      this.oldMousePos = {
        x: pageX,
        y: pageY
      }
      // 元素原始位置
      this.oldNodePos = {
        x: offsetLeft,
        y: offsetTop
      }
    },
    touchMove(ev) {
      const selectDom = ev.target
      // x轴偏移量
      const lefts = this.oldMousePos.x - this.oldNodePos.x
      // y轴偏移量
      const tops = this.oldMousePos.y - this.oldNodePos.y
      const { pageX, pageY } = ev.touches[0] // 手指位置
      selectDom.style.left = `${pageX - lefts}px`
      selectDom.style.top = `${pageY - tops}px`
    },
    touchEnd(ev) {
      const selectDom = ev.target
      const { clientHeight } = document.body
      const { offsetTop } = selectDom
      // 注意点(offsetTop相对父元素定位)
      this.menutop = offsetTop + this.menutop

      if (this.menutop < 120) {
        this.menutop = 120
      } else if (clientHeight - this.menutop < 120) {
        this.menutop = clientHeight - 120
      }
      selectDom.style.left = 0
      selectDom.style.top = 0
      localStorage.setItem('floatBallTop', this.menutop)
    }
  },
  mounted() {
    this.$nextTick(() => {
      const floatBallTop = localStorage.getItem('floatBallTop')
      if (floatBallTop) {
        this.menutop = Number(floatBallTop)
      } else {
        const { clientHeight } = document.body
        this.menutop = clientHeight - 160
      }
    })
  }
}
</script>

<style lang="less" scoped>
@animation-step-1-time: 0.2s;
@animation-step-2-time: 0.2s;

.overlay {
  z-index: 999900;
  .overlay-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999900;
  }
  .overlay-mask-enter-active,
  .overlay-mask-leave-active {
    transition: background-color 0.4s ease-out;
  }
  .overlay-mask-enter,
  .overlay-mask-leave-to {
    background-color: transparent;
  }

  .overlay-menu-container {
    position: fixed;
    right: -0.28rem;
    width: 0.88rem;
    height: 0.88rem;
    z-index: 999909;
    .overlay-menu-fan {
      position: absolute;
      width: 0.88rem;
      height: 0.88rem;
      opacity: 0;
      z-index: 999910;
      .overlay-menu-fan-item {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
      }
      .overlay-menu-close-btn {
        position: absolute;
        width: 0.88rem;
        height: 0.88rem;
        background: url('./img/tool_retract_icon.svg') no-repeat;
        background-size: cover;
      }
    }
    .overlay-menu-open-btn {
      position: absolute;
      left: 0;
      top: 0;
      width: 0.88rem;
      height: 0.88rem;
      border-radius: 4px;
      // box-shadow: 0 0.02rem 0.06rem 0.04rem rgba(153, 153, 153, 0.1);
      overflow: hidden;
      // background: url('./img/k_icon.svg') no-repeat;
      background: url('./img/tool_open_icon.png') no-repeat;
      background-size: cover;
      opacity: 1;
      z-index: 999920;
    }
  }

  .overlay-menu__open {
    animation: menu_open @animation-step-1-time ease-out forwards;
    .overlay-menu-fan {
      animation: menu_open_fan @animation-step-1-time ease forwards;
      .tag {
        animation: menu_open_fanitem_tag @animation-step-2-time ease-out @animation-step-1-time forwards;
      }
      .task {
        animation: menu_open_fanitem_task @animation-step-2-time ease-out 0.4s forwards;
      }
      .feedback {
        animation: menu_open_fanitem_feedback @animation-step-2-time ease-out 0.5s forwards;
      }
      .overlay-menu-close-btn {
        animation: menu_open_closebtn @animation-step-1-time ease forwards;
      }
    }
    .overlay-menu-open-btn {
      animation: menu_open_openbtn @animation-step-1-time ease forwards;
    }
  }

  .overlay-menu__back {
    animation: memu_back @animation-step-1-time ease-out @animation-step-2-time both;
    .overlay-menu-fan {
      animation: menu_back_fan @animation-step-1-time ease @animation-step-2-time both;
      .tag {
        animation: menu_back_fanitem_tag 0.2s linear 0.2s both;
      }
      .task {
        animation: menu_back_fanitem_task 0.3s linear 0.1s both;
      }
      .feedback {
        animation: menu_back_fanitem_feedback @animation-step-2-time linear both;
      }
      .overlay-menu-close-btn {
        animation: menu_back_closebtn @animation-step-1-time linear @animation-step-2-time both;
      }
    }
    .overlay-menu-open-btn {
      animation: menu_back_openbtn @animation-step-1-time ease @animation-step-2-time both;
    }
  }
}

@keyframes pan {
  0% {
    right: 1.8rem;
  }
  100% {
    right: -0.28rem;
  }
}

@keyframes menu_open {
  0% {
    right: -0.28rem;
  }
  100% {
    right: 0.4rem;
  }
}
@keyframes memu_back {
  0% {
    right: 0.4rem;
  }
  100% {
    right: -0.28rem;
  }
}

@keyframes menu_open_fan {
  0% {
    opacity: 0;
    z-index: 999910;
  }
  100% {
    opacity: 1;
    z-index: 999920;
  }
}
@keyframes menu_back_fan {
  0% {
    opacity: 1;
    z-index: 999920;
  }
  100% {
    opacity: 0;
    z-index: 999910;
  }
}

@keyframes menu_open_fanitem_tag {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(0deg) scale(0.5);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
    transform: translate(-1.64rem, -1.92rem) rotate(360deg) scale(1);
  }
}

@keyframes menu_back_fanitem_tag {
  0% {
    opacity: 1;
    transform: translate(-1.64rem, -1.92rem) rotate(360deg) scale(1);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(0deg) scale(0.5);
  }
}

@keyframes menu_open_fanitem_task {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(0deg) scale(0.5);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
    transform: translate(-2.78rem, -50%) rotate(360deg) scale(1);
  }
}

@keyframes menu_back_fanitem_task {
  0% {
    opacity: 1;
    transform: translate(-2.78rem, -50%) rotate(360deg) scale(1);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(0deg) scale(0.5);
  }
}

@keyframes menu_open_fanitem_feedback {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(0deg) scale(0.5);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
    transform: translate(-1.64rem, 0.62rem) rotate(360deg) scale(1);
  }
}

@keyframes menu_back_fanitem_feedback {
  0% {
    opacity: 1;
    transform: translate(-1.64rem, 0.62rem) rotate(360deg) scale(1);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(0deg) scale(0.5);
  }
}

@keyframes menu_open_openbtn {
  0% {
    opacity: 1;
    z-index: 999920;
  }
  100% {
    opacity: 0;
    z-index: 999910;
  }
}

@keyframes menu_back_openbtn {
  0% {
    opacity: 0;
    z-index: 999910;
  }
  100% {
    opacity: 1;
    z-index: 999920;
  }
}

@keyframes menu_open_closebtn {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}

@keyframes menu_back_closebtn {
  0% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
</style>
