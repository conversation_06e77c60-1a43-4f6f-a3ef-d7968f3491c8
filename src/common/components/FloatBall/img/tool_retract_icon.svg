<?xml version="1.0" encoding="UTF-8"?>
<svg width="88px" height="88px" viewBox="0 0 88 88" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>tool_Retract_icon</title>
    <defs>
        <circle id="path-1" cx="44" cy="44" r="44"></circle>
        <filter x="-6.8%" y="-6.8%" width="113.6%" height="113.6%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="2" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0950861392   0 0 0 0 0.0950861392   0 0 0 0 0.0950861392  0 0 0 0.1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <circle id="path-3" cx="44" cy="45" r="38"></circle>
        <filter x="-21.1%" y="-18.4%" width="142.1%" height="142.1%" filterUnits="objectBoundingBox" id="filter-4">
            <feMorphology radius="2" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.600049005   0 0 0 0 0.600049005   0 0 0 0 0.600049005  0 0 0 0.1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="tool_Retract_icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g>
            <g id="椭圆形" opacity="0">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#131415" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <g id="椭圆形" opacity="0.5">
                <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                <use fill="#000000" fill-rule="evenodd" xlink:href="#path-3"></use>
            </g>
            <g id="编组" transform="translate(24.000000, 25.000000)">
                <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" fill-rule="nonzero" x="0" y="0" width="40" height="40"></rect>
                <polyline id="路径" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" transform="translate(13.500000, 20.500000) scale(-1, 1) translate(-13.500000, -20.500000) " points="20 8 7 20.5 20 33"></polyline>
                <polyline id="路径" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" points="34 8 21 20.5 34 33"></polyline>
                <line x1="31" y1="5" x2="31" y2="36" id="直线" stroke="#FFFFFF" stroke-width="4" opacity="0" stroke-linecap="round" stroke-linejoin="round"></line>
            </g>
        </g>
    </g>
</svg>