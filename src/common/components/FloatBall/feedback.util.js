/*
 * @Descripttion: 反馈工具
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-13 21:36:41
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-08-05 14:19:57
 */

const CONFIG = {
  SEND_HOST_DEV: 'https://scs-gw.sit.sf-express.com:45348',
  SEND_HOST: 'https://scs-gw.sf-express.com',
  SEND_PATH: '/eosFmsSfpsServices/userFeedback/openFeedback',
  PUBLIC_KEY_HOST_DEV: 'http://scs-api-gw.intsit.sfdc.com.cn:1080',
  PUBLIC_KEY_HOST: 'https://scs-gw.sf-express.com',
  PUBLIC_KEY_PATH: '/eosFmsSfpsServices/userFeedback/getPublicKey',
  APP_CODE: 'fop-web-ica-sxmobile'
}

const feedBack = () => {
  const sendUrl =
    process.env.NODE_ENV === 'development'
      ? `${CONFIG.SEND_HOST_DEV}${CONFIG.SEND_PATH}`
      : `${CONFIG.SEND_HOST}${CONFIG.SEND_PATH}`
  const publicKeyUrl =
    process.env.NODE_ENV === 'development'
      ? `${CONFIG.PUBLIC_KEY_HOST_DEV}${CONFIG.PUBLIC_KEY_PATH}`
      : `${CONFIG.PUBLIC_KEY_HOST}${CONFIG.PUBLIC_KEY_PATH}`
  try {
    return new KYFeedback({
      userId: sessionStorage.getItem('userId') || '', // 员工工号
      urlMatches: /\S/, // 根据路径匹配在哪些页面显示，/\S/代表所有路径都显示
      showHeader: true, // 是否显示导航头
      onOpen: () => {
        const baseFont = document.documentElement.style.fontSize
        sessionStorage.setItem('baseFont', baseFont)
        document.documentElement.style.fontSize = '14px'
      },
      onSuccess: () => {},
      onClose: () => {
        document.documentElement.style.fontSize = sessionStorage.getItem('baseFont')
      },
      showIcon: false, // 是否显示悬浮Icon,不显示可以自行调用实例方法的open方法显示反馈组件
      appCode: CONFIG.APP_CODE, // 项目系统编码，初始化时需加入
      gapWidth: 0, // 反馈图标距离左右边距
      gapTop: 0, // 反馈图标上边距
      gapBottom: 0, // 反馈图标下边距
      sendUrl,
      publicKeyUrl
    })
  } catch (err) {
    console.log(err)
  }
}

export default feedBack
