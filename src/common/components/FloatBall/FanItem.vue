<!--
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-05 18:15:10
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-03-08 15:50:20
-->

<template>
  <div class="fan-item" @click="emitClick">
    <img :src="imgUrl" />
    <p>{{ label }}</p>
  </div>
</template>

<script>
export default {
  props: {
    imgUrl: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  methods: {
    emitClick() {
      this.$emit('item-click', this.type)
    }
  }
}
</script>

<style lang="less" scoped>
.fan-item {
  width: 1.2rem;
  height: 1.2rem;
  background: #fff;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #ececec;
  box-shadow: 0 0.02rem 0.07rem 0.05rem rgba(153, 153, 153, 0.1);
  color: #666666;
  font-size: 0.2rem;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  > img {
    display: block;
    width: 0.55rem;
    height: 0.55rem;
    margin-bottom: 0.038rem;
  }
}
</style>
