
<template>
  <div class="common-head-container" :class="tabBarClassName">
    <div class="common-head-wrap" :class="{ left: isLeft, right: !isLeft }">
      <div class="common-head-left">
        <div v-if="homePage === 'getLastLevel'">
          <slot name="goLastLevel"></slot>
        </div>
        <div v-if="homePage === true" @click="exitApp">
          <i class="iconfont icon-exit"></i>
        </div>
        <div v-if="!homePage" @click="goBack">
          <i class="iconfont icon-goback"></i>
        </div>
        <div v-if="homePage" class="btn-text">退出</div>
      </div>
      <div class="common-head-center">
        <slot name="title">{{ title }}</slot>
      </div>
      <div class="common-head-right">
        <slot name="certain">
          <div class="right-btn" :class="{ left: isLeft, right: !isLeft }" @click="goPublicZones">
            <i v-if="showPublicSelect" class="iconfont icon-nav-level"></i>
            <div class="btn-text">
              {{ rightName }}
            </div>
          </div>
        </slot>
      </div>
    </div>
    <div class="common-head-placeholder"></div>
  </div>
</template>

<script>
import handlerExitAPP from 'common/components/ExitApp/index.js'
export default {
  props: {
    isLeft: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '顺心战况'
    },
    rightName: String,
    homePage: [Boolean, String],
    showPublicSelect: [Boolean, String],
    tabBarClassName: {
      type: String,
      default: 'normal-tab-bar'
    }
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    goPublicZones() {
      if (this.showPublicSelect) {
        this.$router.push({
          name: 'freightOrg'
        })
      }
    },
    goBack() {
      this.$router.back(-1)
    },
    exitApp() {
      handlerExitAPP()
    }
  }
}
</script>

<style lang="less" scoped>
.common-head-container {
  &.normal-tab-bar {
    background-color: #fff;
    color: #333;

  }
  &.peak-tab-bar {
    background: #02091b !important;
    color: #fff;
    border: 1px solid #000;
    .btn-text {
      color: #8ab5ec !important;
    }
  }
}
.common-head-wrap {
  top: 0;
  position: fixed;
  width: 100vw;
  height: 0.88rem;
  z-index: 5457;
  display: flex;
  align-items: center;
  padding: 0 0.2rem;
  &.left {
    justify-content: flex-start;
  }
  &.right {
    justify-content: space-between;
  }

  .common-head-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    margin-right: 0.3rem;
    i {
      font-size: 0.4rem;
    }
    .btn-text {
      font-size: 0.2rem;
      color: #666;
      margin-top: 0.04rem;
    }
  }

  .common-head-center {
    position: absolute;
    font-size: 0.36rem;
    font-weight: 500;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .common-head-right {
    .right-btn {
      display: flex;
      flex-direction: column;
      justify-content: center;
      &.left {
        align-items: flex-start;
      }
      &.right {
        align-items: flex-end;
      }
      i {
        font-size: 0.4rem;
      }
      .btn-text {
        font-size: 0.2rem;
        color: #666;
        margin-top: 0.04rem;
        max-width: 1.5rem;
        overflow: hidden; /*超出部分隐藏*/
        white-space: nowrap; /*不换行*/
        text-overflow: ellipsis; /*超出部分文字以...显示*/
      }
    }
  }
}
.common-head-placeholder {
  width: 100%;
  height: 0.88rem;
}
</style>
