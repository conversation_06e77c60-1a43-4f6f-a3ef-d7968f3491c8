import Vue from 'vue'
import ExitApp from './ExitApp'
let invockFlag = true
export default function handlerExitAPP() {
  if (invockFlag) {
    invockFlag = false
    setTimeout(() => {
      invockFlag = true
    }, 1000)
    const ExitAppConstructor = Vue.extend(ExitApp)
    const qc = new ExitAppConstructor().$mount()
    document.body.appendChild(qc.$el)
    return qc
  }
  return {
    switchContent() {
      return
    }
  }
}
