<script>
export default {
  name: 'ExitApp',
  render(h) {
    return (<div class='exit__app' on-click={this.cancelExit}>
      <div class='info__confirm'>
        <div class='confirm__content'>是否确定退出顺心战况平台</div>
        <div class='confirm__button'>
          <div class='button__item'>否</div>
          <div class='button__item'
            on-click={this.exitStage}>是</div>
        </div>
      </div>
    </div>)
  },
  methods: {
    cancelExit() {
      this.$nextTick(() => {
        this.$destroy()
        document.body.removeChild(this.$el)
      })
    },
    exitStage(event) {
      event.stopPropagation()
      // if (typeof ExpressPlugin !== 'undefined') {

      // }
      try {
        ExpressPlugin.backToWidget(() => {}, () => { })
      } catch (error) {

      }
    }
  }
}
</script>

<style lang="less" scoped>
.exit__app{
    position:absolute;
    top: 0;
    left:0;
    height: 100%;
    width: 100%;
    background: rgba(37, 38, 45, 0.4);
    z-index:999;
}
.info__confirm {
  position: relative;
  width: 6rem;
  top: 50%;
  left:50%;
  transform: translate(-50%,-50%);
  background: #FFFFFF;
  border-radius: 0.2rem;
  .confirm__content {
    position: relative;
    width: 100%;
    font-size: 0.34rem;
    color: #000;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .confirm__button {
    position: relative;
    width: 100%;
    height: 1rem;
    display: flex;
    border-top: 0.01rem solid #f2f2f2;
    .button__item {
      flex: 1;
      color: #2772ff;
      display: flex;
      align-items: center;
      font-size: 0.32rem;
      justify-content: center;
      border-right: 0.01rem solid #f2f2f2;
      &:last-child {
        border-right: 0;
      }
    }
  }
}
</style>
