<template>
    <div class="roller-bar">
        <div class="roller-bar-item" :class="{ 'roller-bar-item--active': activeIndex === index}"
             v-for="(item, index) in dataSource" :key="index" @click="handleRollerBarClick( item, index)">
            {{item.label}}
            <span></span>
        </div>
    </div>
</template>
<script>
export default {
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  methods: {
    handleRollerBarClick(item, index) {
      this.$emit('handleClick', { item, index })
    }
  }
}
</script>
<style lang='less' scoped>
.roller-bar {
  display: flex;
  height: 0.88rem;
  align-items: center;
  box-shadow: 0 0.1rem 0.2rem rgba(190, 190, 206, 0.2);
  position: relative;
  background-color: #ffffff;
  .roller-bar-item {
    height: 0.88rem;
    line-height: 0.88rem;
    text-align: center;
    flex: 1;
    font-size: 0.28rem;
    // color: #808285;
    color: #999;
    box-sizing: border-box;
    position: relative;
    > span {
      position: absolute;
      width: 0.48rem;
      height: 2px;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
    }
  }
  .roller-bar-item--active {
    color: #dc1e32;
    font-weight: 700;
    font-size: 0.32rem;
    > span {
      border-bottom: 2px solid #dc1e32;
      border-top-left-radius: 0.04rem;
      border-top-right-radius: 0.04rem;
    }
  }
}
</style>
