<!--实时监控tab栏-->
<template>
  <div class="tab-bar">
    <div
      class="tab-bar__item"
      :class="{ 'tab-bar__item--active': curIndex === index }"
      v-for="(item, index) in dataList"
      :key="index"
      @click="_itemClick(index)"
    >
      {{ item }}
    </div>
  </div>
</template>

<script type="text/ecmascript-6">
export default {
  name: 'tabBar',
  props: {
    // tab-bar数据
    dataList: {
      type: Array,
      default() {
        return ['TOP增幅', 'TOP热门路线']
      }
    },
    // 默认选中tab 下标
    curTabIndex: {
      type: Number,
      default: 0
    },
    // 选中颜色
    activeColor: {
      type: String,
      default: '#2772FF'
    },
    // tab-bar宽度
    width: {
      type: String,
      default: '5.16rem'
    }
  },
  data() {
    return {
      curIndex: 0
    }
  },
  created() {
    if (Number.parseInt(this.curTabIndex) <= this.dataList.length - 1) {
      this.curIndex = Number.parseInt(this.curTabIndex)
    }
    this._initRoot()
  },
  watch: {
    curTabIndex(newVal, oldVal) {
      if (Number.parseInt(newVal) <= this.dataList.length - 1) {
        this.curIndex = Number.parseInt(newVal)
      }
    }
  },
  methods: {
    // 初始化 css root
    _initRoot() {
      const element = document.documentElement
      element.style.setProperty('--width', this.width)
      element.style.setProperty('--activeColor', this.barColor)
    },
    // tab栏点击
    _itemClick(index) {
      if (this.curIndex === index) {
        return
      }
      this.curIndex = index
      this.$emit('tabBarItemClick', index)
    }
  },
  computed: {
    barColor() {
      return this.activeColor
    }
  }
}
</script>

<style lang="less" scoped>
.tab-bar {
  position: relative;
  margin: 0 auto;
  width: var(--width);
  display: flex;
  // background: #ffffff;
  .tab-bar__item {
    flex: 1;
    height: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.28rem;
    color: var(--activeColor);
    border: 1px solid var(--activeColor);
    box-sizing: border-box;
    background: #ffffff;
    &.tab-bar__item--active {
      background: var(--activeColor);
      color: #ffffff;
    }
    &:first-child {
      border-top-left-radius: 0.25rem;
      border-bottom-left-radius: 0.25rem;
      border-right: 0;
    }
    &:last-child {
      border-left: 0;
      border-top-right-radius: 0.25rem;
      border-bottom-right-radius: 0.25rem;
    }
  }
}
</style>
