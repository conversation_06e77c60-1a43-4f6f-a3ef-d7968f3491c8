/**
 * columns legend配置
 * {label: 主标题; dataIndex: 主标题字段数据; icon: 是否带任何icon; iconRender: icon计算逻辑，根据传的dataIndex数据; width: 自定义宽度}
 * --------------------
 * dataSource legend数组源 传入一个对象，字段和每一个dataIndex对应
 * --------------------
 * inline 数据是否横向排列
 * inline: true: 排序方式 icon+label+dataIndex;
 * inline: false: 排序方式 icon+ (label / dataindex)
 * num: Number: 一行有多少条数据 使用flex布局 每个布局占据相同宽度
 */

import './index.less'

export default {
  props: {
    columns: Array,
    dataSource: Object,
    inline: Boolean,
    num: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      msg: 'Hello World'
    }
  },
  methods: {
  },
  render(h) {
    const { columns, dataSource, inline, num } = this
    const haveWidth = columns.filter(item => item.width)
    const len = haveWidth.length
    let totalWidth = 0
    let unit = 'rem'
    if (len) {
      const width = haveWidth[0].width
      const unitBool = /rem/.test(width)
      unit = unitBool ? 'rem' : '%'
    }
    columns.forEach(item => {
      if (item.width) {
        totalWidth += Number(item.width.replace(/rem|%/g, ''))
      }
    })
    totalWidth = totalWidth / (num - len) + unit
    const inlineEle = (
      <div class='common-legend-inline-wrap'>
        {columns.map(item => {
          return (
            <div
              class='common-legend-inline__item'
              style={{
                minWidth: item.width || `calc(${(1 / (num - len)) * 100}% - ${totalWidth})`,
                maxWidth: item.width || `calc(${(1 / (num - len)) * 100}% - ${totalWidth})`,
                width: item.width ? item.width : 'aoto'
              }}
            >
              <div class='common-legend-inline__item__icon'>{item.icon}</div>
              <div class='common-legend-inline__item__label'>{item.label}</div>
              <div class='common-legend-inline__item__value'>
                {item.render ? item.render(dataSource[item.dataIndex]) : dataSource[item.dataIndex]}
              </div>
            </div>
          )
        })}
      </div>
    )
    const blockEle = (
      <div class='common-legend-block-wrap'>
        {columns.map(item => {
          return (
            <div
              class='common-legend-block__item'
              style={{
                minWidth: item.width || `calc(${(1 / (num - len)) * 100}% - ${totalWidth})`,
                maxWidth: item.width || `calc(${(1 / (num - len)) * 100}% - ${totalWidth})`,
                width: item.width ? item.width : 'aoto'
              }}
            >
              <div class='common-legend-block-icon'>{item.icon}</div>
              <div class='common-legend-block-content'>
                <div class='common-legend-block__item__label'>{item.label}</div>
                <div class='common-legend-block__item__value'>
                  {item.render ? item.render(dataSource[item.dataIndex]) : dataSource[item.dataIndex]}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
    return inline ? <div>{inlineEle}</div> : <div>{blockEle}</div>
  }
}
