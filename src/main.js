// The Vue build version to load with the `import` command
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import CAS from '@ky/caslogin'
import Config from './config'
import handlerExitAPP from 'common/components/ExitApp/index.js'
import initSentry from './common/js/sentry'
import 'animate.css'
require('lodash')

import './theme-ui'

// 神策
import sensors from 'common/js/sensors.util'
Vue.prototype.$sensors = sensors

import numToformat from 'common/js/numFormat'
Vue.use(numToformat)

import utils from 'common/js/utils'
Vue.use(utils)

import chartFormat from 'common/charts/chartUtils'
Vue.use(chartFormat)

// 主题色
import theme from 'common/js/theme'
Vue.use(theme)

import Vconsole from 'vconsole'

// 屏幕自适应插件
import 'common/js/jsPlugins/flexible'

import echarts from 'common/charts/echartsUI'
Vue.prototype.$echarts = echarts

import common_plugin from 'common'
Vue.use(common_plugin)

import 'common/css/index.less'

import VueI18n from 'vue-i18n'
Vue.use(VueI18n)
const i18n = new VueI18n({})

// 网关登录
const casLogin = () => {
  return new Promise((resolve, reject) => {
    const list =
      process.env.VUE_APP_SERVE === 'serve-prod'
        ? {
            ENV: 'PRD'
          }
        : {
            url: '/restApi',
            ENV: process.env.NODE_ENV !== 'production' ? 'SIT' : 'PRD'
          }
    const cas = new CAS({
      appKey: Config.appKey,
      appSecret: Config.appSecret,
      ...list
    })
    window.g_cas = cas
    window.g_cas.login().then(() => {
      const userId = sessionStorage.getItem('userid') || ''
      sessionStorage.setItem('userId', userId)
      // 神策
      sensors.login(userId)
      sensors.setProfile({
        emp_id: userId
      })
      resolve()
    })
  })
}

const vConsoleController = res => {
  const vConsoleUsers = ['01369078', '80005016', '01388729', '01389583', '01405730', '01409765', '89011417', '01405390']
  if (vConsoleUsers.includes(res)) {
    new Vconsole()
  }
}

const renderApp = () => {
  const userId = sessionStorage.getItem('userId')
  initSentry(router, userId)
  try {
    new Vue({
      router,
      store,
      i18n,
      render: h => h(App)
    }).$mount('#app')
  } catch (err) {
    console.log(err)
  }
}

const initAppWithPicPrefetch = async () => {
  if (process.env.VUE_APP_CHANNEL === 'web') {
    try {
      await casLogin()
      renderApp()
    } catch (err) {
      console.log(err)
    }
  } else {
    const success = res => {
      sessionStorage.setItem('userId', res)
      vConsoleController(res)
      // 神策
      const userId = res || ''
      sensors.login(userId)
      sensors.setProfile({
        emp_id: userId
      })
      renderApp()
    }
    const failed = () => {
      handlerExitAPP()
    }
    ExpressPlugin.getUserWorkId(success, failed)
  }
}

if (process.env.VUE_APP_CHANNEL === 'web') {
  if (process.env.NODE_ENV === 'development') {
    Vue.config.devtools = true
  }
  initAppWithPicPrefetch()
} else {
  // 生产环境需要确保cordova加载完成
  document.addEventListener('deviceready', initAppWithPicPrefetch, false)
}
