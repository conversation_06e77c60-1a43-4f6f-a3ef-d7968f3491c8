// @Author: author
// @Date: 2024-10-17 09:25:34
// @LastEditTime: 2024-10-17 09:25:34
// @Description: 经营概况总览

import baseMixins from './baseMixins'
import requestMixins from 'common/mixins/requestMixins'
export default {
  mixins: [requestMixins, baseMixins],
  data() {
    return {}
  },
  methods: {
    // 1.基础-根据工号查询所属网点
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483014
    _getByEmployeeCode(params) {
      return this.sendJavaRequest(
        {
          url: `/emt-core/userBizInfoManage/getByEmployeeCode?employeeCode=${params}`,
          method: 'GET'
        },
        false,
        false
      )
    },
    // 2.基础-获取菜单权限
    // http://yapi.sit.sf-express.com/project/1147/interface/api/183695
    // _getUserAuthorResources() {
    //   return this.sendJavaRequest(
    //     {
    //       url: `/rbac-app/sxrbac/resources/author/authorResources?type=5`,
    //       method: 'GET'
    //     },
    //     false,
    //     false
    //   )
    // }
    // _getQueryMenuAuth() {
    //   return this.sendJavaRequest(
    //     {
    //       url: `/cockpit/menuAuth/queryMenuAuth`,
    //       method: 'POST'
    //     },
    //     false,
    //     false
    //   )
    // }
    // 财务战况-菜单权限
    // http://yapi.sit.sf-express.com/project/4230/interface/api/487942
    _getQueryMenuAuth(params) {
      return this.postRequest('/cockpit/menuAuth/queryMenuAuth', params)
    }
  }
}
