 <!-- @Author: author
 @Date: 2024-10-17 09:25:34
 @LastEditTime: 2024-10-17 09:25:34
 @Description: 经营概况总览 -->
 <template>
  <div class="md24">
    <component
      v-for="item in menuAuthList"
      :is="item"
      :key="item"
    ></component>
    <!-- <div v-if="noAuthList?.length>0">
      <ApplyAuthDrawer title="经营细项权限申请" :dataSourse="noAuthList"/>
    </div> -->
    <!-- <BusinessOverview></BusinessOverview>
    <BusinessOverviewDetail></BusinessOverviewDetail> -->
  </div>
</template>
<script>
import { mapState } from 'vuex'
import BusinessOverview from "./businessOverview"
import BusinessOverviewDetail from "./businessOverviewDetail"
import ApplyAuthDrawer from "./components/applyAuthDrawer"
export default {
  name: "finance",
  components: { BusinessOverview, BusinessOverviewDetail, ApplyAuthDrawer },
  data() {
    return {
      // 页面卡片列表，新增权限卡片时，需要维护上对应的resourceCode，并且需要在管理门户菜单中添加，类型为：菜单
      menuList: [
        {
          componentName: '经营概况总览',
          componentCode: 'BusinessOverview',
          resourceCode: 'menu_sxBattle_finance_overview'
        },
        {
          componentName: '经营细项总览',
          componentCode: 'BusinessOverviewDetail',
          resourceCode: 'menu_sxBattle_finance_operation'
        }
      ]
    }
  },
  computed: {
    ...mapState('financeNew', {
      financeMenuAuth: state => state.financeMenuAuth
    }),
    menuAuthList() {
      return this.menuList.map(item => item.componentCode)
    }
    // menuAuthList() {
    //   const authInfo = this.financeMenuAuth
    //   if (authInfo?.isOpenAuthCheck) {
    //     const list = this.menuList.filter((item) => {
    //       const target = (authInfo?.dataList || []).find(item2 => item2.resourceCode === item.resourceCode)
    //       return target
    //     })
    //     return list.map((item) => item.componentCode)
    //   } else {
    //     return this.menuList.map((item) => item.componentCode)
    //   }
    // },
    // // 待开的菜单权限
    // noAuthList() {
    //   const authInfo = this.financeMenuAuth
    //   if (authInfo?.isOpenAuthCheck) {
    //     const list = this.menuList.filter((item) => {
    //       const target = (authInfo?.dataList || []).find(item2 => item2.resourceCode === item.resourceCode)
    //       return !target
    //     })
    //     return list.map((item) => {
    //       return {
    //         ...item,
    //         title: item.componentName,
    //         desc: '【卡片菜单权限】'
    //       }
    //     })
    //   }
    // }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>
<style lang="less" scoped>

</style>
