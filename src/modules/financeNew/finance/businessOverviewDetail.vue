 <!-- @Author: author
 @Date: 2024-10-17 09:25:34
 @LastEditTime: 2024-10-17 09:25:34
 @Description: 经营概况总览 -->
 <template>
  <div class="mb20">
    <CardList title="经营细项总览">
      <div class="tab_list">
        <div class="tab_list_item" v-for="(item, index) in tabList" :key="index" :class="{ 'active_tab': tabIndex === index }" @click="selectTab(index)">
          {{ item?.label }}
        </div>
      </div>
      <div v-for="(item, index) in dataLists" :key="index">
        <DataList :items="item" :index="index" @handleViewTrend="handleViewTrend" @handleExpandChild="handleExpandChild" @showCalculationTip="showCalculationTip"></DataList>
      </div>
      <div v-if="!dataLists?.length" class="no_data">
        <img :src="empty_img" class="no_data_img" alt="" srcset="">
        <div class="no_data_text" v-if="!noAuthList?.length">暂时没有数据哦~</div>
      </div>
      <div v-if="noAuthList?.length>0">
        <ApplyAuthDrawer title="经营细项权限申请" :dataSourse="noAuthList"/>
      </div>
    </CardList>
    <TrendDrawer :visible="visible" :trendConfig="trendConfig" :handleClose="closeDrawer" :request="getTrendQueryData"></TrendDrawer>
    <BottomModal :visible="tipVisible" :tipConfig="tipConfig" :handleClose="closeTopModal"></BottomModal>
  </div>
</template>
<script>
import { mapMutations } from "vuex"
import request from "./commonMixins/request"
import mixins from "./commonMixins/mixins"
import DataList from "./components/dataList"
import TrendDrawer from "./components/trendDrawer"
import BottomModal from "./components/bottomModal"
import ApplyAuthDrawer from "./components/applyAuthDrawer"
import empty_img from 'common/img/empty_img.png'
export default {
  mixins: [request, mixins],
  components: { DataList, TrendDrawer, BottomModal, ApplyAuthDrawer },
  data() {
    return {
      empty_img,
      dataLists: [],
      tabIndex: 0,
      tabList: [
       {
         value: 0,
         label: "收入"
       },
       {
         value: 1,
         label: "成本"
       }
      ],
      visible: false,
      tipConfig: {
        content: "",
        title: ''
      },
      tipVisible: false,
      trendConfig: {}
    }
  },
  computed: {
    noAuthList() {
      const dataObjs = this.detailResponseData || {}
      console.log("wef-detailResponseData", dataObjs)
      const datasList = [
        {
          ...dataObjs?.costResp,
          title: '成本',
          desc: '【包含完成比、环比、下级数据、规则说明】'
        },
        {
          ...dataObjs?.revenueResp,
          title: '收入',
          desc: '【包含完成比、环比、下级数据、规则说明】'
        }
      ]
      return (datasList || [])?.filter(item => item?.isPermission === false)
    }
  },
  watch: {
    dateIndex(val) {
      this.init()
    },
    tabIndex(val) {
      this.getPageData()
    },
    dateDay(val) {
      this.init()
    },
    zoneCode(val) {
      this.init()
    }
  },
  methods: {
    ...mapMutations('financeNew', ['setPageData']),
    // 初始化
    async init() {
      console.log('_getOperationDetailQuery--省区', this.zoneData)
      const dateParam = this.$moment(this.dateDay).format('YYYY-MM-DD')
      const params = {
       "levelCode": this.zoneLevel,
       "incDay": dateParam,
       "timeDim": ["day", 'week', 'month'][this.dateIndex]
      }
      if (this.level_next_code) {
        params[this.level_next_code] = this.zoneCode
      }
      const res = await this._getOperationDetailQuery(params)
      if (res?.success && res?.obj) {
        const obj = res.obj
        this.setPageData({
          type: 'detail',
          dataType: "detailResponseData",
          data: obj || {}
        })
        this.getPageData()
      } else {
        this.setPageData({
          type: 'detail',
          dataType: "detailResponseData",
          data: {}
        })
      }
    },
    // 获取主页面数据
    getPageData() {
      const fileds = ['revenueResp', 'costResp'][this.tabIndex]
      const dataObjs = this.detailResponseData[fileds] || {}
      const dataObj = dataObjs?.data
      const isPermission = dataObjs?.isPermission
      const newArr = []
      for (const key in dataObj) {
        if (Object.hasOwnProperty.call(dataObj, key)) {
          const data = dataObj[key] || []
          if (data && data.length > 0) {
            const obj = {
              isPermission: isPermission,
              ...dataObj[key][0],
              childrenList: data.filter((item, index) => index > 0),
              isOpen: false
              // desc: data[0]?.desc || ''
            }
            newArr.push(obj)
          }
        }
      }
      this.dataLists = newArr
    },
    // 成本、收入切换
    selectTab(index) {
      this.tabIndex = index
    },
    // 查看趋势
    async handleViewTrend({ item, index }) {
      const params = {
       levelCode: this.zoneLevel,
       incDay: this.$moment(this.dateDay).format('YYYY-MM-DD'),
       timeDim: ["day", 'week', 'month'][this.dateIndex],
       orderFieldCode: item?.rankFieldCode,
       indexLabelFieldCode: item?.labelCode
      }
      if (this.level_next_code) {
        params[this.level_next_code] = this.zoneCode
      }
      this.visible = true
      this.trendConfig = {
        ...item,
        title: item?.indicatorName,
        payLoad: params
      }
    },
    // 获取趋势数据
    async getTrendQueryData(params) {
      let res = {}
      if (this.tabIndex === 1) {
        res = await this._getCostDetailQueryTrend(params)
      } else {
        res = await this._getRevenueDetailQueryTrend(params)
      }
      if (res?.success) {
        return res?.obj || {}
      } else {
        return {}
      }
    },
    // 展开详情
    handleExpandChild({ item, index }) {
      this.$set(this.dataLists[index], "isOpen", !item?.isOpen)
    },
    // 展示计算口径
    showCalculationTip(data) {
      this.tipConfig = data
      this.tipVisible = true
    },
    // 关闭查看明细
    closeDrawer() {
      this.visible = false
    },
    // 关闭计算口径弹窗
    closeTopModal() {
      this.tipVisible = false
    }
  },
  mounted() {
    this.init()
  }
}
</script>
<style lang="less" scoped>
.tab_list {
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 0.28rem;
  font-weight: 500;
  color: #999999;
  border-bottom: 0.01rem solid #f2f2f2 ;
  margin: 0 0.2rem;
  margin-top: 0.16rem;
  padding-top: 0.12rem;
  padding-bottom: 0.18rem;
  .tab_list_item {
    cursor: pointer;
    height: 0.34rem;
    line-height: 0.34rem;
  }
  .active_tab {
    color: #DC1E32;
    font-weight: 600;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 0.64rem;
      height: 0.04rem;
      bottom: -0.18rem;
      background-color: #DC1E32;
    }
  }
}
.no_data {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.64rem;
  flex-direction: column;
  .no_data_img {
    width: 2.18rem;
    height: 1.8rem;
  }
  .no_data_text {
    font-family: PingFang SC;
    font-size: 13px;
    margin-top: 28px;
    padding-bottom: 8px;
    color: #666666;
  }
}
</style>
