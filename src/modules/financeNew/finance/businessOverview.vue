 <!-- @Author: author
 @Date: 2024-10-17 09:25:34
 @LastEditTime: 2024-10-17 09:25:34
 @Description: 经营概况总览 -->
 <template>
  <div class="mb20">
    <CardList title="经营概况总览">
      <div v-for="(item, index) in dataLists" :key="index">
        <DataList :items="item" :index="index" @handleViewTrend="handleViewTrend" @handleExpandChild="handleExpandChild" @showCalculationTip="showCalculationTip"></DataList>
      </div>
      <div v-if="!dataLists?.length" class="no_data">
        <img :src="empty_img" class="no_data_img" alt="" srcset="">
        <div class="no_data_text" v-if="!noAuthList?.length">暂时没有数据哦~</div>
      </div>
      <div v-if="noAuthList?.length>0">
        <ApplyAuthDrawer title="经营细项权限申请" :dataSourse="noAuthList"/>
      </div>
    </CardList>
    <TrendDrawer :visible="visible" :trendConfig="trendConfig" :handleClose="closeDrawer"  :request="getTrendQueryData"></TrendDrawer>
    <BottomModal :visible="tipVisible" :tipConfig="tipConfig" :handleClose="closeTopModal"></BottomModal>
  </div>
</template>
<script>
import { mapMutations } from "vuex"
import request from "./commonMixins/request"
import mixins from "./commonMixins/mixins"
import DataList from "./components/dataList"
import TrendDrawer from "./components/trendDrawer"
import BottomModal from "./components/bottomModal"
import ApplyAuthDrawer from "./components/applyAuthDrawer"
import empty_img from 'common/img/empty_img.png'
export default {
  mixins: [request, mixins],
  components: { DataList, TrendDrawer, BottomModal, ApplyAuthDrawer },
  data() {
    return {
      empty_img,
      dataLists: [],
      dataSource: [],
      tipVisible: false,
      tipConfig: {},
      visible: false,
      trendConfig: {}
    }
  },
  computed: {
    noAuthList() {
      const lists = Object.values(this.finBusinessOverviewData || {})
      return lists.filter(item => !item?.isPermission).map(items => ({
        ...items,
        title: items.bigIndicatorName,
        desc: '【包含完成比、环比、下级数据、规则说明】'
      }))
    }
  },
  watch: {
    dateIndex(val) {
      this.init()
    },
    dateDay(val) {
      this.init()
    },
    zoneCode(val) {
      this.init()
    },
    finBusinessOverviewData(val) {
      this.getPageData(val)
    }
  },
  methods: {
    ...mapMutations('financeNew', ['setPageData']),
    async init() {
      const params = {
       levelCode: this.zoneLevel,
       incDay: this.$moment(this.dateDay).format('YYYY-MM-DD'),
       timeDim: ["day", 'week', 'month'][this.dateIndex]
      }
      if (this.level_next_code) {
        params[this.level_next_code] = this.zoneCode
      }
      const res = await this._getFinBusinessOverviewQuery(params)
      if (res?.success && res?.obj) {
        const obj = res.obj
        this.setPageData({
          type: 'survey',
          dataType: "finBusinessOverviewData",
          data: obj || {}
        })
        // this.getPageData(obj)
      } else {
        this.setPageData({
          type: 'survey',
          dataType: "finBusinessOverviewData",
          data: {}
        })
      }
    },
    getPageData(dataObj) {
      const newArr = []
      for (const key in dataObj) {
        if (Object.hasOwnProperty.call(dataObj, key)) {
          const isPermission = dataObj[key]?.isPermission
          const data = dataObj[key]?.data || []
          if (isPermission) {
            const element = {
              isPermission: isPermission,
              ...(dataObj[key]?.data)[0],
              childrenList: data.filter((item, index) => index > 0),
              isOpen: false
            }
            newArr.push(element)
          }
          // if (data && data.length > 0) {
          //   const element = {
          //     isPermission: dataObj[key]?.isPermission,
          //     ...(dataObj[key]?.data)[0],
          //     childrenList: data.filter((item, index) => index > 0),
          //     isOpen: false
          //   }
          //   newArr.push(element)
          // }
        }
      }
      this.dataLists = newArr || []
      // this.dataLists = newArr.map(item => {
      //   return {
      //     ...item,
      //     indicatorCompletion: Math.random() * 2,
      //     indicatorMom: Math.random() * 2,
      //     indicatorValue: 234232.32,
      //     valueUnit: "万元"
      //   }
      // })
    },
    // 查看趋势
    async handleViewTrend({ item, index }) {
      const params = {
       levelCode: this.zoneLevel,
       incDay: this.$moment(this.dateDay).format('YYYY-MM-DD'),
       timeDim: ["day", 'week', 'month'][this.dateIndex],
       orderFieldCode: item?.orderFieldCode,
       indexLabelFieldCode: item?.indexLabelFieldCode
      }
      if (this.level_next_code) {
        params[this.level_next_code] = this.zoneCode
      }
      this.visible = true
      this.trendConfig = {
        ...item,
        title: item?.indicatorName,
        payLoad: params
      }
    },
    // 获取趋势数据
    async getTrendQueryData(params) {
      const res = await this._getQueryIndicatorTrend(params)
      if (res?.success) {
        return res?.obj || {}
      } else {
        return {}
      }
    },
    // 展开详情
    handleExpandChild({ item, index }) {
      this.$set(this.dataLists[index], "isOpen", !item?.isOpen)
    },
    // 展示计算口径
    showCalculationTip(data) {
      this.tipConfig = data
      this.tipVisible = true
    },
    // 关闭查看明细
    closeDrawer() {
      this.visible = false
    },
    // 关闭计算口径弹窗
    closeTopModal() {
      this.tipVisible = false
    },
    // 点击申请权限
    handleApplyAuth() {}
  },
  mounted() {
    this.init()
  }
}
</script>
<style lang="less" scoped>
.no_data {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.64rem;
  flex-direction: column;
  .no_data_img {
    width: 2.18rem;
    height: 1.8rem;
  }
  .no_data_text {
    font-family: PingFang SC;
    font-size: 13px;
    margin-top: 28px;
    padding-bottom: 8px;
    color: #666666;
  }
}
</style>
