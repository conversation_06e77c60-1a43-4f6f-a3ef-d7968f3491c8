<template>
  <KyDataDrawer :visible="innerVisible" :title="`${trendConfig?.title}概览情况`" height="90%" @close="diaClose" @drawerHeight="mixinsDrawerHeight">
    <div class="mt24 pd_lr20">
      <div class="flex_between">
        <div class="fs28 fw700">{{`${trendConfig?.title}`}}趋势</div>
      </div>
      <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableOption">
        <div class="mt24" style="height: 3.2rem" ref="chart-dom-trend"></div>
      </KydChartModel>
    </div>
    <div class="rank_table_box">
      <div class="rank_table_title fs28 fw700">省区排名</div>
      <NormalTable
        class="mt24"
        size="small"
        maxHeight="5rem"
        :width="moreTableWidth"
        :dataSource="dataSource"
        :columns="tableColumns"
      >
      </NormalTable>
    </div>
  </KyDataDrawer>
</template>
<script>
import mixins from "../commonMixins/mixins"
import { drawScrollChart } from 'common/charts/chartOption'
import { numToPercent, numToInFloat } from 'common/js/numFormat'
export default {
  name: 'TrendDrawer',
  mixins: [mixins],
  components: { },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    handleClose: {
      type: Function,
      default() {
        return () => { }
      }
    },
    trendConfig: {
      type: Object,
      default() {
        return () => ({})
      }
    },
    request: {
      type: Function,
      default() {
        return () => { }
      }
    }
  },
  data() {
    return {
      mixinsTableMaxHeight: '8rem',
      innerVisible: false,
      selectedTabIndex: 0, // 选中的tab索引
      tableOption: {},
      moreTableWidth: '100%',
      dataSource: [],
      legendDataSource: [],
      numForValueList: ['均重', '抛比', '率', '单价', '单票件数'] // 不需要转换成%
    }
  },
  computed: {
    legendOption() {
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: [
          { label: this.trendConfig?.title, dataIndex: 'indicatorValue', seriesIndex: 0 }
        ],
        dataSource: this.legendDataSource
      }
    },
    tableColumns() {
      const rowObj = this.trendConfig || {}
      // 个别指标没有目标值，表格就不展示这两列
      const targetColumn = [
        { label: '目标值', dataIndex: 'indicatorTarget', fixed: 'left', render: (h, val) => this.formatToThousands(val) },
        { label: '目标达成', dataIndex: 'indicatorCompletion', fixed: 'left', render: (h, val) => numToPercent(val) }
      ]
      const newList = [
        { label: '排名', dataIndex: 'indicatorRank', fixed: 'left', width: '1.0rem' },
        { label: '组织', dataIndex: 'orgName', fixed: 'left' },
        {
          label: `${rowObj?.title}${rowObj?.valueUnit ? `(${rowObj?.valueUnit})` : ''}`,
          dataIndex: 'indicatorValue',
          fixed: 'left',
          render: (h, val, record) => `${this.formatToThousands(val)}`
        },
        { label: ["日环比", '周环比', '月环比'][this.dateIndex] + this.getReventUnit(rowObj.indicatorName), dataIndex: 'indicatorMom', fixed: 'left', render: (h, val, rowData) => <div>
            {this.isIncludesfield(rowObj.indicatorName, this.numForValueList) ? numToInFloat(val, 3, 1, 3) : (rowObj.indicatorName === '总净利' ? val : numToPercent(val))}
          </div>
        }
      ]
      if (rowObj?.completionCal) {
        newList.splice(3, 0, ...targetColumn)
      }
      return newList
    }
  },
  watch: {
    visible(val) {
      this.innerVisible = val
      if (val) {
        this.init()
      }
    }
  },
  mounted() {},
  methods: {
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    init() {
      this.initChart([])
      this.dataSource = []
      this.request(this.trendConfig?.payLoad).then(res => {
        this.initChart(res?.indicatorTrendInformationList || [])
        this.dataSource = res?.subIndicatorInformationList || []
      })
    },
    initChart(result) {
      const fliterList = JSON.parse(JSON.stringify(result))
      const xData = []
      const sData = [[], []]
      if (fliterList && fliterList.length > 0) {
        fliterList.forEach((item, index) => {
          const dateField = ['incDay', 'weekNo', 'incDay'][this.dateIndex]
          xData.push(item[dateField])
          sData[0].push({
            ...item,
            value: item.indicatorValue || 0
          })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 12
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              interval: 0, // 每个标签都显示
              formatter: value => {
                if (this.dateIndex === 0) {
                  return this.$dateFormat(value, this.dateIndex)
                } else if (this.dateIndex === 1) {
                  return value
                } else {
                  return this.$moment(value).format('MM月')
                }
              }
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: (value, ...reset) => {
                return value
              }
            }
          }
        ],
        series: [
          {
            type: 'line',
            data: sData[0],
            smooth: true
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-dom-trend'], { isOneLine: true })
      this.tableOption = {
        options: option
      }
    },
    diaClose() {
      this.handleClose()
    },
    selectTab(index) {
      this.selectedTabIndex = index // 更新选中的tab索引
    },
    initOption() {},
    getReventUnit(val) {
      return val === '总净利' ? '(万元)' : ''
    }
  }
}
</script>
<style lang="less" scoped>
.rank_table_box {
  padding-bottom: 0.56rem;
}
.rank_table_title {
  margin-top: 0.32rem;
  color: #333333;
  line-height: 0.38rem;
  padding: 0 0.2rem;
  box-sizing: border-box;
}
/deep/ .rank_table_box .sheader .flex-left {
  display: flex;
  justify-content: space-around !important;
  align-items: center;
  white-space: nowrap;
}
/deep/ .rank_table_box .sbody .flex_start {
  display: flex;
  justify-content: space-around !important;
  align-items: center;
  white-space: nowrap;
}
</style>
