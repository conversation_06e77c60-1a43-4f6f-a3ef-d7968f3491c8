 <!-- @Author: author
 @Date: 2024-10-17 09:25:34
 @LastEditTime: 2024-10-17 09:25:34
 @Description: 经营概况总览 -->
 <template>
  <div>
    <div class="row_list fs24 mt32">
      <div class="row_list_item col_1">
        <div class="col_item_top fs24">
          <span>{{ items.indicatorName }}</span>
          <span v-if="items?.calAperture" class="col_item_tip" @click="showCalculationTip(items)">?</span>
        </div>
        <div class="col_item_bottom mt16 fw700">
          <div class="col_item_value">{{ formatToThousands(items?.indicatorValue) || '-'}}</div>
          <div class="col_item_unit">{{ items?.valueUnit }}</div>
        </div>
      </div>
      <div class="row_list_item col_2">
        <div class="col_item_top fs24">
          <span>完成比</span>
          <!-- <span v-if="items?.completionCal" class="col_item_tip" @click="showCalculationComTip(items)">?</span> -->
        </div>
        <div class="col_item_bottom mt16 fw700">
          <div class="col_item_value">{{ formDataNumber(items?.indicatorCompletion, 100) }}</div>
          <div class="col_item_unit">{{ items?.indicatorCompletion === null ? '' : '%' }}</div>
          <div class="col_item_chart" v-show="items?.indicatorCompletion !== null">
            <div class="inner_chart" :style="{backgroundColor:`${chartNumberRate(items?.indicatorCompletion, true) >= 1 ? '#08BCA0' : '#FF6A17'}`, width: `${chartNumberRate(items?.indicatorCompletion)}%`}"></div>
          </div>
        </div>
        <div class="divide_line"></div>
      </div>
      <div class="row_list_item col_3">
        <div class="col_item_top fs24">
          <span>{{ getMomLabel }}</span>
          <!-- <span v-if="items?.completionCal" class="col_item_tip" @click="showCalculationMomTip(items)">?</span> -->
        </div>
        <div class="col_item_bottom mt16 fw700">
          <!-- <div class="col_item_value">{{ formDataNumber(items?.indicatorMom, 100) }}</div>
          <div class="col_item_unit">%</div> -->
          <div class="col_item_value">{{ items.indicatorName === '总净利' ? items?.indicatorMom : formDataNumber(items?.indicatorMom, 100) }}</div>
          <div class="col_item_unit">{{ items.indicatorName === '总净利' ? '万元' : '%'}}</div>
          <div class="col_item_icon">
            <img class="trend_icon" :src="items?.indicatorMom > 0 ? green_up_icon : red_down_icon" alt="" srcset="">
          </div>
        </div>
        <div class="divide_line"></div>
      </div>
      <div class="row_list_item tr pd_r20 col_4">
        <div class="col_item_top fs24 color_red flex_right">
          <span @click="handleViewTrend(items, index)">趋势<i class="iconfont icon-dayuhao fs20"></i></span>
        </div>
        <div class="col_item_bottom fs24 mt16 grey999 flex_right">
          <div class="flex_right" @click="handleExpandChild(items, index)" v-show="!items.childrenList || items.childrenList.length>0">
            <div>展开</div>
            <!-- <i class="iconfont icon-dayuhao fs20 scale_down expand-open"></i> -->
            <i :class="[
              'iconfont',
              'icon-kyd-dayuhao',
              'fs20',
              'ml8',
              'fw500',
              items.isOpen ? 'expand-open' : 'expand-close'
            ]"></i>
          </div>
        </div>
      </div>
    </div>
    <div :class="items.isOpen ? 'detail_table_box' : 'table_box_hide'">
      <NormalTable
        class="mt24"
        size="small"
        minHeight="2rem"
        :width="moreTableWidth"
        :dataSource="items.childrenList || []"
        :columns="tableColumnAlls"
      >
      </NormalTable>
    </div>
  </div>
</template>
<script>
import { green_up_icon, red_down_icon, grey_help_icon } from "../commonMixins/config"
import { numToInFloat, numToPercent } from 'common/js/numFormat'
import mixins from "../commonMixins/mixins"
const helpStyle = {
  marginLeft: '0.08rem',
  display: 'inline-block',
  height: '0.24rem',
  width: '0.24rem'
}
export default {
  mixins: [mixins],
  components: {},
  props: {
    items: {
      type: Object,
      default() {
        return {}
      }
    },
    index: {
      type: Number,
      default() {
        return 0
      }
    }
  },
  data() {
    return {
      green_up_icon,
      red_down_icon,
      grey_help_icon,
      numForValueList: ['均重', '率', '单价', '单票件数']
    }
  },
  computed: {
    getMomLabel() {
      return ['日环比', '周环比', '月环比'][this.dateIndex]
    },
    tableColumnAlls() {
      return [
        {
          label: '类型名称',
          dataIndex: 'indicatorName',
          render: (h, val, record) => (
            <div>
              <span>{record.indicatorName}</span>
              {record?.calAperture && <img class="help_icon" style={helpStyle} src={grey_help_icon} onClick={() => this.showCalculationTip(record)}></img>}
            </div>
          )
        },
        // { label: '完成值', dataIndex: 'indicatorValue', render: (h, val, record) => <div>{val || '-'}{record?.valueUnit}</div> },
        { label: '完成值', dataIndex: 'indicatorValue', render: (h, val, record) => <div>{this.formatToThousands(val)}{record?.valueUnit}</div> },
        { label: '完成比', dataIndex: 'indicatorCompletion', render: (h, val) => numToPercent(val) },
        { label: ["日环比", '周环比', '月环比'][this.dateIndex], dataIndex: 'indicatorMom', render: (h, val, rowData) => <div class={val > 0 ? 'green' : 'orange'}>
          {/* { numToPercent(val) } */}
          {this.formatNumberData(val, rowData.indicatorName)}
          {/* {this.isIncludesfield(rowData.indicatorName, this.numForValueList) ? numToInFloat(val, 3, 1, 3) : numToPercent(val)} */}
        </div> },
        { label: '操作', align: 'right', render: (h, val, record) => <div class='red' onClick={() => { this.handleViewTrend(record, val) }}>查看</div> }
      ]
    }
  },
  methods: {
    handleViewTrend(record, index) {
      if (this.zoneData.zoneLevel > 33) {
        return
      }
      this.$emit('handleViewTrend', {
        item: record,
        index
      })
    },
    // 展开详情
    handleExpandChild(record, index) {
      if (this.zoneData.zoneLevel > 33) {
        return
      }
      this.$emit('handleExpandChild', {
        item: record,
        index
      })
    },
    // 显示计算口径
    showCalculationTip (record) {
      if (this.zoneData.zoneLevel > 33) {
        return
      }
      this.$emit('showCalculationTip', {
        title: record?.indicatorName ? `${record?.indicatorName}说明` : '',
        content: record?.calAperture,
        completionCal: record?.completionCal
      })
    },
    // 完成比计算公式
    showCalculationComTip(record) {
      if (this.zoneData.zoneLevel > 33) {
        return
      }
      this.$emit('showCalculationTip', {
        title: record?.indicatorName ? `${record?.indicatorName}完成比说明` : '',
        content: record?.completionCal,
        completionCal: record?.completionCal
      })
    },
    // 环比计算公式
    showCalculationMomTip(record) {
      if (this.zoneData.zoneLevel > 33) {
        return
      }
      this.$emit('showCalculationTip', {
        title: record?.indicatorName ? `${record?.indicatorName}环比比说明` : '',
        content: record?.completionCal
      })
    },
    // 格式化数字
    formDataNumber(val, type = 1) {
      const num = val ? Number(val) * type : null
      if (num || num === 0) {
        return numToInFloat(num, 1, 1, 1)
        // return numToInFloat(num)
      } else {
        return "--"
      }
    },
    // 完成比chart图标数据
    chartNumberRate(value, orgValue) {
      const num = value ? Number(value) : null
      if (orgValue) {
        return num || 0
      }
      if (num || num === 0) {
        if (num >= 1) {
          return 100
        } else {
          return num * 100
        }
      } else {
        return 0
      }
    }
  },
  mounted() {}
}
</script>
<style lang="less" scoped>
.tab_list {
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 0.28rem;
  font-weight: 500;
  color: #999999;
  border-bottom: 0.01rem solid #f2f2f2 ;
  margin: 0 0.2rem;
  margin-top: 0.16rem;
  padding-top: 0.12rem;
  padding-bottom: 0.18rem;
  .tab_list_item {
    cursor: pointer;
    height: 0.34rem;
    line-height: 0.34rem;
  }
  .active_tab {
    color: #DC1E32;
    font-weight: 600;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 0.64rem;
      height: 0.04rem;
      bottom: -0.18rem;
      background-color: #DC1E32;
    }
  }
}
.row_list {
  // display: grid;
  // grid-template-columns: 1.3fr 1fr 0.9fr 0.8fr;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}
.row_list_item {
  box-sizing: border-box;
  padding-left: 0.2rem;
  position: relative;
}
.col_item_top {
 color: #666666;
 height: 0.30rem;
 line-height: 0.30rem;
 display: flex;
 justify-content: flex-start;
//  align-items: center;
}
.col_item_tip {
  margin-left: 0.08rem;
  display: inline-block;
  height: 0.24rem;
  width: 0.24rem;
  background: #DDDDDD;
  color: #fff;
  text-align: center;
  border-radius: 0.12rem;
}
.col_item_bottom {
 display: flex;
 justify-content: flex-start;
 align-items: baseline;
 height: 0.48rem;
 line-height: 0.48rem;
 .scale_down {
   transform: rotateZ(180deg);
 }
}
.col_item_value {
  font-size: 0.36rem;
 font-weight: bold;
 color: #333333;
}
.col_item_unit {
 font-size: 0.26rem;
 font-weight: bold;
 margin-left: 0.04rem;
}
.col_item_icon {
 height: 0.16rem;
 width: 0.10rem;
 margin-left: 0.10rem;
}
.col_item_chart {
  position: relative;
  height: 0.12rem;
  width: 0.45rem;
  background: #EBEBEB;
  border-radius: 0.06rem;
  overflow: hidden;
  margin-left: 0.08rem;
  .inner_chart {
    position: absolute;
    left: 0;
    top: 0;
    width: 0.40rem;
    height: 0.12rem;
    border-radius: 0.06rem;
    background: #08BCA0;
  }
}
.trend_icon {
 height: 0.16rem;
 width: 0.10rem;
}
.help_icon {
  height: 0.12rem;
  width: 0.12rem;
}
.color_red {
 color: #DC1E32;
}
.flex_right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.detail_table_open {
  height: 100%;
  display: block;
  overflow: hidden;
}
.detail_table_hide {
  height: 0;
  display: none;
  overflow: hidden;
}
.detail_table_box {
  overflow: hidden;
  width: 100%;
}
.table_box_hide {
  height: 0px;
  overflow: hidden;
}
.col_1 {
 width: 32%;
}
.col_2 {
 width: 29%;
}
.col_3 {
 width: 24%;
}
.col_4 {
 width: 15%;
}
.pd_r20 {
  padding-right: 0.2rem;
  padding-left: 0rem;
}
.divide_line {
  position: absolute;
  height: 0.32rem;
  top: 50%;
  left: -3px;
  transform: translateY(-50%);
  border-left: 0.01rem solid #DDDDDD;
}
.expand-open {
  transform: rotate(-90deg);
  transition: 200ms linear;
}
.expand-close {
  transform: rotate(90deg);
  transition: 200ms linear;
}
</style>
