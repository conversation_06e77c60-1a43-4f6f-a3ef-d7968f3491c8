 <!-- @Author: author
 @Date: 2024-10-17 09:25:34
 @LastEditTime: 2024-10-17 09:25:34
 @Description: 经营概况总览--权限申请 -->
<template>
  <div>
    <div class="apply_auth_entry">
      <div class="apply_auth_button" @click="handleApplyAuth">经营概览权限申请<i class="iconfont icon-dayuhao fs20 scale_down"></i></div>
    </div>
    <KyDataDrawer :visible="innerVisible" :title="title" height="auto" @close="diaClose" @drawerHeight="mixinsDrawerHeight">
      <div class="modal_content_box">
        <div class="modal_content">
          <div class="list_item" v-for="(item, index) in dataSourse" :key="index">
            <span class="list_item_title">{{ item?.title }}</span>
            <span class="list_item_desc">{{ item?.desc}}</span>
            <!-- <span class="list_item_desc">【包含完成比、环比、下级数据、规则说明】</span> -->
          </div>
          <div class="bottom" @click="askFs">
            <div class="left"></div>
            <div class="middle">
              <div>王君 <span class="grey666">01393192</span></div>
              <div class="mt8 fs20 grey999 line_h28">上面数据无查看权限，如需开通，可联系业务接口人</div>
              <!-- <span class="grey333 fs24 fw700 flex_start line_h33"
                >{{ defaultPeople.employeeName
                }}<span class="grey666 fs24 fw400 ml16">{{ defaultPeople.employeeCode }}</span></span
              >
              <span class="mt8 fs20 grey999 line_h28">若有疑问，可联系业务接口人</span> -->
            </div>
            <div class="right"></div>
          </div>
        </div>
      </div>
    </KyDataDrawer>
  </div>
</template>
<script>
import mixins from "../commonMixins/mixins"
export default {
  name: 'TrendDrawer',
  mixins: [mixins],
  components: { },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    handleClose: {
      type: Function,
      default() {
        return () => { }
      }
    },
    title: {
      type: String,
      default: ''
    },
    dataSourse: {
      type: Array,
      default() {
        return () => []
      }
    }
  },
  data() {
    return {
      mixinsTableMaxHeight: '8rem',
      innerVisible: false,
      moreTableWidth: '100%',
      defaultPeople: {}
    }
  },
  computed: {},
  watch: {
    visible(val) {
      console.log("wqttt", val)
      this.innerVisible = val
    }
  },
  mounted() {
    this.initOption()
  },
  methods: {
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    handleApplyAuth() {
      this.innerVisible = true
    },
    diaClose() {
      this.innerVisible = false
    },
    getDefaultPeople() {
      this.sendTwoDimenRequest('cp_sxzkqxsprgl', [], false, false).then(res => {
        console.log(res, 'res---')
        this.defaultPeople = res.obj[0] || {}
      })
    },
    askFs() {
      try {
        ExpressPlugin.openContactDetail(this.defaultPeople.employeeCode)
      } catch (err) {
        console.log(err)
      }
    },
    initOption() {}
  }
}
</script>
<style lang="less" scoped>
.apply_auth_entry {
  padding-top: 0.48rem;
  padding-bottom: 0.08rem;
  display: flex;
  justify-content: center;
  align-items: center;
  .apply_auth_button {
    width: 2.6rem;
    height: 0.48rem;
    line-height: 0.48rem;
    border-radius: 0.24rem;
    background: #F8F9FC;
    text-align: center;
    color: #333333;
  }
}
.modal_content_box {
 overflow-y: scroll;
//  max-height: 6rem;
 margin-bottom: 0.56rem;
}
.modal_content {
  font-family: PingFang SC;
  padding: 0 0.4rem;
  padding-top: 0.48rem;
  box-sizing: border-box;
}
.list_item {
  height: 0.80rem;
  line-height: 0.80rem;
  border-radius: 0.04rem;
  background: #FAFAFA;
  margin-bottom: 0.2rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 0.24rem;
  box-sizing: border-box;
  .list_item_title {
    font-weight: 500;
    font-size: 0.26rem;
    color: #333333;
  }
  .list_item_desc {
    font-weight: 400;
    font-size: 0.24rem;
    color: #666666;
  }
}
.bottom {
  background: #f2f3f7;
  border: 1px solid #dddddd;
  border-radius: 0.04rem;
  height: 0.96rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.13rem 0.24rem;
  position: sticky;
  bottom: 0;
  .left {
    height: 0.64rem;
    width: 0.64rem;
    background: url('../../../platform/img/<EMAIL>') no-repeat;
    background-size: contain;
  }
  .middle {
    display: flex;
    flex-direction: column;
    flex: 1 0 3.84rem;
    margin-left: 0.24rem;
  }
  .right {
    height: 0.32rem;
    width: 0.32rem;
    background: rgba(0, 107, 234, 0.1);
    border-radius: 0.04rem;
    display: flex;
    align-items: center;
    justify-content: center;
    &::after {
      content: '';
      display: inline-block;
      height: 0.2rem;
      width: 0.18rem;
      background: url('../../../platform/img/nav_return_icon.svg');
      background-size: 100% 100%;
      transform: rotate(180deg);
    }
  }
}
</style>
