 <!-- @Author: author
 @Date: 2024-10-17 09:25:34
 @LastEditTime: 2024-10-17 09:25:34
 @Description: 经营概况总览--计算口径 -->
<template>
  <KyDataDrawer :visible="innerVisible" :title="tipConfig?.title" minHeight="10%" height="auto" @close="diaClose" @drawerHeight="mixinsDrawerHeight">
    <div class="modal_tip_box">
      <div class="modal_tip_content">
        <div class="modal_tip_content-item">{{ tipConfig?.content }}</div>
        <div class="modal_tip_content-item" v-if="tipConfig?.completionCal">{{ tipConfig?.completionCal }}</div>
        <div>
          <div class="modal_tip_content-item" v-if="isIncludesfields(tipConfig?.title, calculationIntList)">
            <div>计算公式： 1、(本期数-上期数)。</div>
            <div>计算逻辑：
              <div>①日环比：当日-前7日，例如：本周一数据对比上周一数据，本周三数据对比上周三数据</div>
              <div>②周环比：周累计环比上周累计同期。例如：本周一~周三累计值，对比上周一~周三累计值</div>
              <div>③月环比：当月累计-上月同期累计，例如：本月1-10号的累计数据，对比上月1-10号的累计数据</div>
            </div>
          </div>
          <div class="modal_tip_content-item" v-else>
            <div>计算公式： 1、(本期数-上期数)÷上期数×100%。</div>
            <div>计算逻辑：
              <div>①日环比：当日-前7日，例如：本周一数据对比上周一数据，本周三数据对比上周三数据。</div>
              <div>②周环比：周累计环比上周累计同期。例如：本周一~周三累计值，对比上周一~周三累计值</div>
              <div>③月环比：当月累计-上月同期累计，例如：本月1-10号的累计数据，对比上月1-10号的累计数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </KyDataDrawer>
</template>
<script>
import mixins from "../commonMixins/mixins"
export default {
  name: 'TrendDrawer',
  mixins: [mixins],
  components: { },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    handleClose: {
      type: Function,
      default() {
        return () => { }
      }
    },
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    tipConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      mixinsTableMaxHeight: '8rem',
      innerVisible: false,
      moreTableWidth: '100%',
      calculationIntList: ['抛比', '均重', '单票件数'] // 环比计算公式为直接相减的
    }
  },
  computed: {},
  watch: {
    visible(val) {
      console.log("wqttt", val)
      this.innerVisible = val
    }
  },
  mounted() {
    this.initOption()
  },
  methods: {
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    diaClose() {
      console.log("关闭---2")
      this.handleClose()
    },
    initOption() {},
    isIncludesfields(str, keywords) {
      if (!str) return false
      return keywords.some(keyword => str.includes(keyword))
    }
  }
}
</script>
<style lang="less" scoped>
.modal_tip_box {
  margin-top: 0.4rem;
  padding: 0 0.4rem;
}
.modal_tip_content {
  padding-bottom: 0.56rem;
}
.modal_tip_content-item {
  font-family: PingFang SC;
  font-size: 0.28rem;
  font-weight: normal;
  line-height: 0.40rem;
  color: #666666;
  margin-top: 12px;
}
</style>
