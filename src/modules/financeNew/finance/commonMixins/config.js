// 绿色向上箭头
export const green_up_icon = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAhCAYAAAA2/OAtAAAAAXNSR0IArs4c6QAAASlJREFUSA3tVjsOwjAMzVYjGBg5DgMjh2LgAEiIqVJjzsDAUKOMHIOBgSMwMIBMa6qUfPpho5UiK3H8bL86TpSKfWY3SQgPQPhkqcxuEjOJ6pMTbhlQBs+jRqENQNlCwCyZ63nIzq8zegqEVwusivjSiQYg1B7AgooTpv6IHJqEsmUQsIyY9znMHUvhtD8/rHR6VUZPHSj2UjTtildxoG2E2qxp2nVq/DS0S1uiFOmmoUPaAijSpqFr2n4a+qUtUYosaADCtO6x5zxVQHjvCSJRirwz6PnHoGc1Nno2KtqbAUIet5ZOeP/bdkR6w3i18lccebiRtD1R7GEAHTitruQGNWt3p68iHUrq309UjusGZSQN+Qm5XrmqyF4r7qwj0P4RBn/rj64Xygvn+RJDhZ0G+gAAAABJRU5ErkJggg=="
// 绿色向上箭头
export const red_down_icon = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAhCAYAAAA2/OAtAAAAAXNSR0IArs4c6QAAARVJREFUSA3tlrENwjAQRTMARYSEj4JhGIAxKPClYQcKBkgKCiQYAomSkjEoKDJCihQfOVCQuxA7kI5EcnGnu2f907edKBIf1nEMNmcwlWBCyyqrunUcC4QOwbRtAelNEtpoisiAzaETlM1BIHQ4QIeZtppeeHWwlD5D9cyfnSgsxzMwpbB0qRZT3u2WovytN3W8CEzXjhDhU+Xpq4MWPUMLB933DN1Hrzfp3g/Y3B2vMikSWvQCTWhRcz2sOf4EtuZYA7rgtzG8yZbkr8cgZStw1zE0yVbQ5x9KoBtaZCtwqBt8shXYP4ad7PHGHjfcwJORF9JUgNV03ujd1XTeVB+cgzVZDWxNFtz8qdDJBNPpBT6FyH4A66dD/3CrlZcAAAAASUVORK5CYII="
// 问号图标
export const grey_help_icon = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAA69JREFUaAXtWr1OFUEY3cLCwsLC0sLCB7DgASxINMFEEyx8AN9AE0xsTDSBDhILCx+AwoLCxoSCwsKCgorcwM75pqCgoKCgoLgFcJZZMvfb2b0zs3tBDSSbnZmdOd858/t9cymK27//oAd2d3fvWWufAVgG8BPACMAhgDP3MM0yfltmXba5cekAFgCsAzj1yNakp73Zhm0Xrl2ItfYlgJ0M0m2idog5cyHGmMcANgckrgVt0sZMhBhj3hhjTmZIvhJDG7Q1qAgR+Txr4hqfNgcRAWBVg19jfrWXCLct6nl6rfnskeA87NHTYxH5A+AHH5ce5+IlrwnuBJkL9tgY835vb++BHnqWAfgA4DhViFvY8btT5la5XZblQ01c51nn4vDaThVBThormDfGvMoAH6W4BqzrXIuk9URuQdJ+YcYJO7bWPvExmHZTZhHAYmhKAZjL6KgdbWciv7+//yIDdH0CpCiKC99oSflH9HuWAvXoCyWNAjlqnKu8c66SAPWwdu1eejcRkdepAsjxirCfcPMy2ascjUaPfJwpC3Tbr8u2GQJOg+vNGPM8A+zMJ7S1tXVnGoZfn+lp9UPfyVXjEIjBSNL0Yf2yLJ/WYEx3YYiIrevy7RZ6sk1y9XGqtIuWcsCOROQjHwBHXQLKsvzkG54muANrw8ep0iLCkC9HQFQbY8wvTjHfcK6jSK4+TpVWMWwUqQjBmyLyFUBj63PTJ9mtcDYPQwKGIl3hiMjbhhGvoMeUrfA9qMtkRG8mCbTW3m0YKYrC7VTf+tprYA89hRoGLrdMug+/+5In1wb+0ItYG5hywCWNbtsi5qVTElBX/YCAwbABNLdRACtdhFK/zVjAisYvcl2JDmHztREA8x31kkcm6ErkOnMdxKJP6A6MkLiwM8feynGnE42HCKWWhd1pJyAnoGkjwFsIbgx8sm8kAh3UONXrqVq9RWSIS1vGFXM1sAsfk2MNTZ7caszWd2ZQPzEKIvJdG2CZJpSa19GftnGVz7xW8UU0rgRzPU9PZNy1ClW4a/Q+N9EH1tr7dY8wDeDAI+OLjUmfWGsnQtcau/XdFZxHErEi8o4PABvZJihGXwa0ktYfjDFf+hgeoi05aF5JeQBrQxDJxFhLIttW+SZGonfPazFuTfRZ2MH5HRiZ4X9iqsX80z/y1SL45oEy0IldjQqxog8pn0jfNG8bnAOY4yawDS93u32bviRj2tMVd/EEg6INF55O/KuBK9tg4MS6wfvNGGO3df6yHjgHzfoP+BmiTRAAAAAASUVORK5CYII="
// 排名表格column配置
export const tableColumn = [
  { label: '排名', dataIndex: 'indicatorRank', fixed: 'left', maxWidth: '1.2rem' },
  { label: '组织', dataIndex: 'orgName', fixed: 'left', maxWidth: '2.2rem' },
  { label: '开单货量', dataIndex: 'indicatorValue', fixed: 'left', maxWidth: '1.2rem' },
  { label: '目标达成', dataIndex: 'indicatorCompletion', fixed: 'left', maxWidth: '1.2rem' },
  { label: '日环比', dataIndex: 'indicatorMom', fixed: 'left', maxWidth: '1.2rem' }
]
// 测试箭头
export const dataList = [
  {
    title: '开单货量',
    weight: '123',
    completeRate: '0.234',
    weightHc: '0.62',
    isOpen: false
  },
  {
    title: 'KA开单货量',
    weight: '123',
    completeRate: '0.234',
    weightHc: '0.62',
    isOpen: false
  },
  {
    title: '网单开单货量',
    weight: '123',
    completeRate: '0.234',
    weightHc: '0.62',
    isOpen: false
  },
  {
    title: '融通开单货量',
    weight: '123',
    completeRate: '0.234',
    weightHc: '0.62',
    isOpen: false
  }
]
// 概况总览
export const totalListAll = {
  "totalOrderVolumeSum": {
    "isPermission": true,
    "data": [
      {
        "indicatorName": "总开单货量",
        "indicatorValue": "223.4",
        "indicatorCompletion": "5",
        "indicatorMom": "6",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "加盟开单货量",
        "indicatorValue": "4",
        "indicatorCompletion": "-5",
        "indicatorMom": "6",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "KA开单货量",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "-6",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "网单开单货量",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "-3",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "融通开单货量",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "6",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "出货口开单货量",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "6",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "开单件均重",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "6",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "OD开单票数",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "-2",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "开单抛比",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "6",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "开单票均重",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "-1",
        "indicatorRank": "7"
      },
      {
        "indicatorName": "开单单票件数",
        "indicatorValue": "4",
        "indicatorCompletion": "5",
        "indicatorMom": "6",
        "indicatorRank": "7"
      }
    ]
  },
  "totalReceivedVolumeSum": {
    "isPermission": true,
    "data": [
      {
        "indicatorName": "总签收货量",
        "indicatorValue": 32,
        "indicatorCompletion": 1,
        "indicatorMom": 2,
        "indicatorRank": 3
      },
      {
        "indicatorName": "加盟签收货量",
        "indicatorValue": 344,
        "indicatorCompletion": 1,
        "indicatorMom": -1,
        "indicatorRank": 1
      },
      {
        "indicatorName": "KA签收货量",
        "indicatorValue": 13231,
        "indicatorCompletion": 1,
        "indicatorMom": 1,
        "indicatorRank": 1
      },
      {
        "indicatorName": "网单签收货量",
        "indicatorValue": 212,
        "indicatorCompletion": 2,
        "indicatorMom": -2,
        "indicatorRank": null
      },
      {
        "indicatorName": "融通签收货量",
        "indicatorValue": 2134,
        "indicatorCompletion": 2,
        "indicatorMom": 1,
        "indicatorRank": 1
      },
      {
        "indicatorName": "出货口签收货量",
        "indicatorValue": 2221,
        "indicatorCompletion": 1,
        "indicatorMom": -0.8,
        "indicatorRank": 1
      },
      {
        "indicatorName": "签收件均重",
        "indicatorValue": 21.3,
        "indicatorCompletion": 1,
        "indicatorMom": 2,
        "indicatorRank": null
      },
      {
        "indicatorName": "OD签收票数",
        "indicatorValue": null,
        "indicatorCompletion": null,
        "indicatorMom": null,
        "indicatorRank": null
      },
      {
        "indicatorName": "签收抛比",
        "indicatorValue": 12,
        "indicatorCompletion": 12,
        "indicatorMom": -0.7,
        "indicatorRank": null
      },
      {
        "indicatorName": "签收票均重",
        "indicatorValue": null,
        "indicatorCompletion": null,
        "indicatorMom": null,
        "indicatorRank": null
      },
      {
        "indicatorName": "签收单票件数",
        "indicatorValue": null,
        "indicatorCompletion": null,
        "indicatorMom": null,
        "indicatorRank": null
      }
    ]
  },
  "arrivalVolumeSum": {
    "isPermission": true,
    "data": [
      {
        "indicatorName": "总到达货量",
        "indicatorValue": 112,
        "indicatorCompletion": 2,
        "indicatorMom": 1,
        "indicatorRank": null
      }
    ]
  }
}

