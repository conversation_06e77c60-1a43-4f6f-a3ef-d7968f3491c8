// export const tableColumn = () => {
//   const columns = [
//     { label: '类型', dataIndex: 'this_day_value', render: (h, val) => this.$numToInteger(val, 0, 1) },
//     { label: '完成值', dataIndex: 'month_target_rate', render: (h, val) => this.$numToPercent(val) },
//     { label: '完成比', dataIndex: 'month_target_rate', render: (h, val) => this.$numToPercent(val) },
//     { label: '当日值', dataIndex: 'this_day_value', render: (h, val) => this.$numToInteger(val, 0, 1) },
//     {
//       label: '日环比',
//       dataIndex: 'this_day_value_hb',
//       render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val, 1)}</div>
//     },
//     { label: '操作', dataIndex: 'month_target_diff', render: (h, val) => '查看' }
//   ]
//   return columns
// }
// export const tableColumn = [
//   { label: '类型', dataIndex: 'title', render: (h, val) => this.$numToInteger(val, 0, 1) },
//   { label: '完成值', dataIndex: 'weight', render: (h, val) => this.$numToPercent(val) },
//   { label: '完成比', dataIndex: 'completeRate', render: (h, val) => this.$numToPercent(val) },
//   {
//     label: '日环比',
//     dataIndex: 'weightHc',
//     render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val, 1)}</div>
//   },
//   { label: '操作', render: (h, val) => '查看' }
// ]
import { numToPercent } from 'common/js/numFormat'
// 经营细项总览
export const tableColumnAll = [
  { label: '类型名称', dataIndex: 'indicatorName' },
  { label: '完成值', dataIndex: 'indicatorValue' },
  { label: '完成比', dataIndex: 'indicatorCompletion' },
  { label: '日环比', dataIndex: 'indicatorMom', render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{ numToPercent(val) }</div> },
  { label: '操作', render: (h, val) => <div class='red'>查看</div> }
]
// 经营细项总览
export const tableColumn = [
  { label: '类型名称', dataIndex: 'title' },
  { label: '完成值', dataIndex: 'weight' },
  { label: '完成比', dataIndex: 'completeRate' },
  { label: '日环比', dataIndex: 'weightHc' },
  { label: '操作', render: (h, val) => '查看' }
]
