// @Author: author
// @Date: 2024-10-17 09:25:34
// @LastEditTime: 2024-10-17 09:25:34
// @Description: 经营概况总览

import mixins from './mixins'
import baseMixins from '../../baseMixins'
export default {
  mixins: [mixins, baseMixins],
  data() {
    return {}
  },
  methods: {
    // 1.财务战况-运营成本细项查询
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483014
    _getOperationDetailQuery(params) {
      return this.postRequest('/cockpit/finaBatter/operationQuery', params)
    },
    // 2.财务战况-成本排名查询
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483006
    _getCostRankDeatilQuery(params) {
      return this.postRequest('/cockpit/finaBatter/costRankQuery', params)
    },
    // 3.财务战况-成本趋势查询
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483010
    _getCostTrendDetailQuery(params) {
      return this.postRequest('/cockpit/finaBatter/costTrendQuery', params)
    },
    // 4.财务战况-收入排名查询
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483018
    _getRevenueRankDetailQuery(params) {
      return this.postRequest('/cockpit/finaBatter/revenueRankQuery', params)
    },
    // 5.财务战况-收入趋势查询
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483022
    _getRevenueTrendDetailQuery(params) {
      return this.postRequest('/cockpit/finaBatter/revenueTrendQuery', params)
    },
    // 财务战况-总览-查询
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483022
    _getFinBusinessOverviewQuery(params) {
      return this.postRequest('/cockpit/finBusinessOverview/querySum', params)
    },
    // 财务战况-总览-趋势查询
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483022
    _getQueryIndicatorTrend(params) {
      return this.postRequest('/cockpit/finBusinessOverview/queryIndicatorTrend', params)
    },
    // 财务战况-细项-收入趋势
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483410
    _getRevenueDetailQueryTrend(params) {
      return this.postRequest('/cockpit/finaBatter/revenueDetailQuery', params)
    },
    // 财务战况-细项-成本趋势
    // http://yapi.sit.sf-express.com/project/4230/interface/api/483414
    _getCostDetailQueryTrend(params) {
      return this.postRequest('/cockpit/finaBatter/costDetailQuery', params)
    }
  }
}
