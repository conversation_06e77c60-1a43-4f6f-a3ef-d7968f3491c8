// @Author: author
// @Date: 2024-10-17 09:25:34
// @LastEditTime: 2024-10-17 09:25:34
// @Description: 经营概况总览
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
import baseMixins from '../../baseMixins'
import { numToPercent, numToInFloat } from 'common/js/numFormat'
export default {
  mixins: [requestMixins, baseMixins],
  data() {
    return {
      moreTableWidth: '100%'
    }
  },
  computed: {
    // 下级组织入参
    level_next_code() {
      const obj = {
        30: null,
        32: 'provinceCode',
        33: 'areaCode',
        34: 'deptCode'
      }
      return obj[this.zoneLevel]
    },
    mom_label() {
      return ['日环比', '周环比'][this.dateIndex]
    },
    ...mapGetters({}),
    ...mapGetters('finance', {
      dateValue: 'dateValue'
    }),
    ...mapState('financeNew', {
      dateIndex: state => state.all.dateIndex,
      dateDay: state => state.all.dateDay,
      finBusinessOverviewData: state => state.survey.finBusinessOverviewData, // 情况总览
      detailResponseData: state => state.detail.detailResponseData // 细项-成本、收入数据
    })
  },
  methods: {
    // 格式化数字类型 (相比上面的numToThousands方法，此方法不对原始值进行格式化)
    formatToThousands(num) {
      if (!num && num !== 0) {
        return '-'
      }
      const value = typeof num === 'number' ? String(num).split('.') : num.split('.')
      if (value[1]) {
        return value[0].replace(/(?=(\B\d{3})+$)/g, ',') + '.' + value[1]
      } else {
        return value[0].replace(/(?=(\B\d{3})+$)/g, ',')
      }
      // let value
      // if (typeof num === 'string') {
      //   value = Number(num)
      // } else {
      //   value = num
      // }
      // return this.formatNumberString(value)
    },
    // 格式化数字类型 (相比toLocaleString(),不会省略小数点后的0)
    formatNumberString(num) {
      const value = String(num).split('.')
      if (value[1]) {
        return value[0].replace(/(?=(\B\d{3})+$)/g, ',') + '.' + value[1]
      } else {
        return value[0].replace(/(?=(\B\d{3})+$)/g, ',')
      }
    },
    // 查找字符串中是否包含指定字符
    isIncludesfield(str, keywords) {
      return keywords.some(keyword => str.includes(keyword))
    },
    // 环比数据处理
    formatNumberData(val, str, type) {
      if (type) {
        return val
      }
      // 率值要百分比
      const flag0 = ['抛比'].some(keyword => str.includes(keyword))
      const flag1 = ['均重', '单票件数'].some(keyword => str.includes(keyword))
      const flag3 = ['单价'].some(keyword => str.includes(keyword))
      if (flag0) {
        return parseInt(val)
      } else if (flag1) {
        return numToInFloat(val, 1, 1, 1)
        // return numToInteger(val, 1)
      } else if (flag3) {
        return numToInFloat(val, 3, 1, 3)
        // return numToInteger(val, 3)
      } else {
        return numToPercent(val, 1)
      }
    }
  }
}
