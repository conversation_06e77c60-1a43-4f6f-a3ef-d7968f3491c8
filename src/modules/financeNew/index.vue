
 <!-- @Author: author
 @Date: 2024-10-17 09:25:34
 @LastEditTime: 2024-10-17 09:25:34
 @Description: 顺心战况财务模块 -->

<template>
  <div class="page_bgc">
    <MAlertWarning type="error" v-if="zoneData.zoneLevel > 33"> 财务暂未开发{{ zoneData.levelName }}层级</MAlertWarning>
    <PageContent>
      <div class="date_container">
        <KyDatePicker
          style="width: 3rem"
          isScroll
          :dateValue="dateValue"
          @onChange="dateChange"
          :holidayData="holidayData"
        />
        <Tabs :options="['日', '周']" :tabIndex="dateIndex" @tabSelect="tabSelect"></Tabs>
      </div>
      <FinanceOverview></FinanceOverview>
    </PageContent>
  </div>
</template>
<script>
import { mapMutations, mapGetters, mapActions, mapState } from 'vuex'
import baseMixins from './baseMixins'
import FinanceOverview from './finance'
import baseRequest from './baseRequest'
export default {
  mixins: [baseMixins, baseRequest],
  components: { FinanceOverview },
  data() {
    return {}
  },
  computed: {
    ...mapState({
      holidayData: 'holidayData'
    }),
    ...mapGetters('financeNew', {
      dateValue: 'dateValue'
    }),
    ...mapState('financeNew', {
      dateIndex: state => state.all.dateIndex
    }),
    userNo() {
      return sessionStorage.getItem('userId') || ''
    }
    // dateType() {
    //   console.log("www==4", this.dateValue, this.dateIndex)
    //   return ['day', 'week'][this.dateIndex]
    // }
  },
  watch: {
    zoneCode() {
      this.initOptions()
    }
  },
  methods: {
    ...mapMutations('financeNew', ['setDate', 'setDateIndex', 'setPageData', 'setFinaMenuAuth']),
    ...mapActions('financeNew', ['initDate']),
    dateChange(date) {
      console.log('日期', date)
      this.setDate({
        type: 'all',
        key: 'dateDay',
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
      this.initOptions()
    },
    tabSelect(index) {
      this.setDateIndex({
        type: 'all',
        index: index
      })
    },
    initOptions() {},
    async getQueryMenuAuth() {
      const params = {
        resourceCode: 'menu_sxBattle_finance',
        type: 1
      }
      const res = await this._getQueryMenuAuth(params)
      if (res?.success) {
        this.setFinaMenuAuth(res?.obj || {})
      }
    }
  },
  mounted() {
    this.$sensors.pageview('财务-' + '顺心财务')
  },
  created() {
    // 初始化日期
    this.initDate()
    this.getQueryMenuAuth()
   }
}
</script>
<style lang="less" scoped>
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f4f4f4;
  position: sticky;
  top: 0px;
  z-index: 10;
}
</style>
