/*
 * @Author: author
 * @Date: 2024-10-17 09:25:34
 * @LastEditTime: 2024-10-17 09:25:34
 * @Description: 顺心战况财务模块
 */
import moment from 'moment'
export default {
  name: 'financeNew',
  namespaced: true,
  state: {
    all: {
      dateIndex: 0,
      dateDay: '',
      dateWeek: '',
      dateMon: ''
    },
    financeMenuAuth: {}, // 财务模块菜单权限
    // 财务概况总览
    survey: {
      finBusinessOverviewData: {} // 情况总览数据
    },
    // 财务细项总览
    detail: {
      detailResponseData: {} // 成本、收入细项数据
    }
  },
  getters: {
    dateValue(state, getters) {
      const dateDay = state.all.dateDay
      return dateDay
    }
  },
  mutations: {
    setDate(state, { type, key, date }) {
      state[type][key] = date
    },
    setDateIndex(state, { type, index }) {
      state[type].dateIndex = index
    },
    setPageData(state, { type, dataType, data }) {
      state[type][dataType] = data
    },
    setFinaMenuAuth(state, data) {
      state['financeMenuAuth'] = data
    }
  },
  actions: {
    initDate({ state }) {
      state.all.dateDay = moment().add(-1, 'days').format('YYYYMMDD')
      const tody = moment().format('DD')
      state.all.dateMon =
        tody < 15 ? moment().add(-2, 'month').format('YYYYMM') : moment().add(-1, 'month').format('YYYYMM')
    }
  }
}
