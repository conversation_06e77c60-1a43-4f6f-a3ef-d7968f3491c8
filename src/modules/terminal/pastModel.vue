<!--
 * @Author: shigl
 * @Date: 2023-12-11 17:12:55
 * @LastEditTime: 2024-01-29 10:15:19
 * @Description:
-->
<template>
  <div>
    <CardList title="历史趋势">
      <!-- <<PERSON><PERSON><PERSON><PERSON> @btnOpenDia="btnOpenDia" />
      <Tihuo @btnOpenDia="btnOpenDia" />
      <Paisong @btnOpenDia="btnOpenDia" />
      <Shishi @btnOpenDia="btnOpenDia" /> -->
      <component
        v-for="item in ['Jiaohuo', 'Tihuo', 'Paisong', 'Shishi']"
        :is="item"
        :key="item"
        @btnOpenDia="btnOpenDia"
        :resultOptions="resultOptions"
      ></component>
    </CardList>
    <KyDataDrawer
      :visible="visible"
      :title="diaTitle"
      height="80%"
      @close="diaClose"
      @drawerHeight="mixinsDrawerHeight"
    >
      <div class="flex_between pd_lr20">
        <KyDatePicker
          style="width: 3rem"
          isBottom
          :type="diaDateType"
          :dateValue="diaDateValue"
          @onChange="diaDateChange"
          :holidayData="holidayData"
        />
        <Tabs :options="['日', '月']" :tabIndex="diaDateIndex" @tabSelect="diaTabSelect"></Tabs>
      </div>
      <NormalTable :columns="diaTableColumns" :dataSource="diaTableDataSource" :maxHeight="mixinsTableMaxHeight" />
    </KyDataDrawer>
  </div>
</template>
<script>
import Jiaohuo from './pastChart/jiaohuo.vue'
import Tihuo from './pastChart/tihuo.vue'
import Paisong from './pastChart/paisong.vue'
import Shishi from './pastChart/shishi.vue'
import request from './request'
import {
  createDiaJiaohuoTableColumns,
  createDiaTihuoTableColumns,
  createDiaPaisongTableColumns,
  createDiaShishiTableColumns
} from './config'
const baseData = {
  jiaohuoData: [],
  tihuoData: [],
  paisongData: [],
  shishiData: []
}
export default {
  mixins: [request],
  components: { Jiaohuo, Tihuo, Paisong, Shishi },
  data() {
    return {
      visible: false,
      diaType: '',
      diaTitle: '',
      diaTableColumns: [],
      diaTableDataSource: [],
      diaIncDay: '',
      diaIncMon: '',
      diaDateIndex: 0,
      resultOptions: {
        ...baseData
      }
    }
  },
  computed: {
    diaDateType() {
      return ['day', 'month'][this.diaDateIndex]
    },
    diaDateValue() {
      return [this.diaIncDay, this.diaIncMon][this.diaDateIndex]
    },
    diaEndDay() {
      return this.$moment(this.diaIncDay).format('YYYY-MM-DD')
    },
    diaEndMon() {
      return this.$moment(this.diaIncMon).format('YYYY-MM')
    }
  },
  watch: {
    dateValue() {
      this.initOptions()
    },
    zoneCode() {
      this.initOptions()
    },
    diaDateValue(val) {
      if (val) {
        this.getDiaData(this.diaType)
      }
    }
  },
  methods: {
    diaDateChange(date) {
      if (this.diaDateType === 'day') {
        this.diaIncDay = date
      }
      if (this.diaDateType === 'month') {
        this.diaIncMon = date
      }
    },
    diaTabSelect(index) {
      this.diaDateIndex = index
    },
    btnOpenDia(type) {
      this.diaType = type
      this.visible = true
      this.diaIncDay = this.incDay
      this.diaIncMon = this.incMon
      this.diaDateIndex = this.dateIndex
      // this.getDiaData(type)
    },
    diaClose() {
      this.visible = false
      this.diaIncDay = ''
      this.diaIncMon = ''
    },
    initOptions() {
      this.resultOptions = { ...baseData }
      this.getJiaohuoData()
      this.getTihuoData()
      this.getPaisongData()
      this.getShishiData()
    },
    async getJiaohuoData() {
      const { data } = await this._getJiaohuoData({}, this.dateIndex)
      this.resultOptions.jiaohuoData = data
    },
    async getTihuoData() {
      const { data } = await this._getTihuoData({}, this.dateIndex)
      this.resultOptions.tihuoData = data
    },
    async getPaisongData() {
      const { data } = await this._getPaisongData({}, this.dateIndex)

      this.resultOptions.paisongData = data
    },
    async getShishiData() {
      const { data } = await this._getShishiData({}, this.dateIndex)
      this.resultOptions.shishiData = data
    },
    async getDiaData(type) {
      // this.diaTableDataSource = []
      // this.diaTableColumns = []
      if (type === 'jiaohuo') {
        this.diaTitle = '交货及时率' + this.zoneData.dLevelName + '明细'
        const params = {
          [['st_arrival_day_ge', 'st_arrival_month_ge'][this.diaDateIndex]]: [this.diaEndDay, this.diaEndMon][
            this.diaDateIndex
          ],
          [['st_arrival_day_le', 'st_arrival_month_le'][this.diaDateIndex]]: [this.diaEndDay, this.diaEndMon][
            this.diaDateIndex
          ],
          level_code: this.dZoneLevel
        }
        const { data } = await this._getJiaohuoData(params, this.diaDateIndex)
        this.$objectSortDown(data, 'intime_d_rate')
        this.diaTableDataSource = data
        this.diaTableColumns = createDiaJiaohuoTableColumns(this.diaDateIndex, this.levelKey)
      }
      if (type === 'tihuo') {
        this.diaTitle = '提货留仓率' + this.zoneData.dLevelName + '明细'
        const params = {
          [['shoud_pick_up_date_ge', 'shoud_pick_up_month_ge'][this.diaDateIndex]]: [this.diaEndDay, this.diaEndMon][
            this.diaDateIndex
          ],
          [['shoud_pick_up_date_le', 'shoud_pick_up_month_le'][this.diaDateIndex]]: [this.diaEndDay, this.diaEndMon][
            this.diaDateIndex
          ],
          level_code: this.dZoneLevel
        }
        const { data } = await this._getTihuoData(params, this.diaDateIndex)
        this.$objectSortDown(data, 'pick_stay_rate')
        this.diaTableDataSource = data
        this.diaTableColumns = createDiaTihuoTableColumns(this.diaDateIndex, this.levelKey)
      }
      if (type === 'paisong') {
        this.diaTitle = '派送及时率' + this.zoneData.dLevelName + '明细'
        const params = {
          [['stand_distribute_date_ge', 'stand_distribute_month_ge'][this.diaDateIndex]]: [
            this.diaEndDay,
            this.diaEndMon
          ][this.diaDateIndex],
          [['stand_distribute_date_le', 'stand_distribute_month_le'][this.diaDateIndex]]: [
            this.diaEndDay,
            this.diaEndMon
          ][this.diaDateIndex],
          level_code: this.dZoneLevel
        }
        const { data } = await this._getPaisongData(params, this.diaDateIndex)
        this.$objectSortDown(data, 'intime_rate')
        this.diaTableDataSource = data
        this.diaTableColumns = createDiaPaisongTableColumns(this.diaDateIndex, this.levelKey)
      }
      if (type === 'shishi') {
        this.diaTitle = '实时签收率' + this.zoneData.dLevelName + '明细'
        const params = {
          [['cnt_date_ge', 'cnt_month_ge'][this.diaDateIndex]]: [this.diaEndDay, this.diaEndMon][this.diaDateIndex],
          [['cnt_date_le', 'cnt_month_le'][this.diaDateIndex]]: [this.diaEndDay, this.diaEndMon][this.diaDateIndex],
          level_code: this.dZoneLevel
        }
        const { data } = await this._getShishiData(params, this.diaDateIndex)
        this.$objectSortDown(data, 'in_dis_rate')
        this.diaTableDataSource = data
        this.diaTableColumns = createDiaShishiTableColumns(this.diaDateIndex, this.levelKey)
      }
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang="less" scoped></style>
