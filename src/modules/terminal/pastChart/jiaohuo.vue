<!--
 * @Author: shigl
 * @Date: 2023-12-12 10:24:17
 * @LastEditTime: 2024-05-13 15:03:10
 * @Description: 交货及时率
-->
<template>
  <div class="mt24 pd_lr20">
    <div class="flex_between">
      <div class="fs28 fw700">交货及时率</div>
      <div class="right-btn-wrap" @click="btnOpenDia">
        展开{{ zoneData.dLevelName }} <i class="iconfont icon-dayuhao fs20"></i>
      </div>
    </div>
    <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableOption">
      <div class="mt24" style="height: 3.2rem" ref="chart-dom-trend"></div>
    </KydChartModel>
  </div>
</template>
<script>
import request from '../request'
import { drawScrollChart } from 'common/charts/chartOption'
export default {
  mixins: [request],
  props: ['resultOptions'],
  data() {
    return {
      tableOption: {},
      legendDataSource: []
    }
  },
  computed: {
    jiaohuoData() {
      return this.resultOptions.jiaohuoData
    },
    legendOption() {
      return {
        type: 'column',
        options: [
          {
            label: '交货及时率',
            per: [1]
          },
          {
            label: '指标',
            // int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'target_value',
            color: '#fff',
            per: [1]
          },
          {
            label: '达成',
            // int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'target_ach_rate',
            color: '#fff',
            per: [1]
          },
          {
            label: '应交',
            int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'wb_cnt',
            color: '#fff'
          },
          {
            label: '已交',
            int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'intime_wb_d_sum',
            color: '#fff'
          },
          {
            label: '超时',
            int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'not_intime_wb_d_sum',
            color: '#fff'
          }
          // {
          //   label: '罚款'
          // }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    jiaohuoData(val) {
      this.setChartTrend(val)
    }
  },
  methods: {
    btnOpenDia() {
      this.$emit('btnOpenDia', 'jiaohuo')
    },
    setChartTrend(result) {
      const xData = []
      const sData = []
      this.legendDataSource = []
      if (result.length) {
        let tmp = [...result]
        if (this.levelKey === 'manager') {
          const deptCode = tmp[0]['src_dept_code']
          tmp = tmp.filter(item => item['src_dept_code'] === deptCode)
        }
        this.$objectSortUp(tmp, this.key_date)
        tmp.forEach(item => {
          xData.push(item[this.key_date])
          sData.push({
            value: item['intime_d_rate'],
            ...item
          })
        })
      }
      const options = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$perFormat(value)
            }
          }
        ],
        series: [
          {
            data: sData,
            type: 'line'
          }
        ]
      }
      drawScrollChart(options, this.$refs['chart-dom-trend'], { dateIndex: this.dateIndex, isOneLine: true })
      this.tableOption = { options }
    }
  }
}
</script>
<style lang="less" scoped>
@import url('../common.less');
</style>
