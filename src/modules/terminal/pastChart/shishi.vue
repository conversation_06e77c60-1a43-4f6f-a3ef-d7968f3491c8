<!--
 * @Author: shigl
 * @Date: 2023-12-12 10:24:17
 * @LastEditTime: 2024-05-13 15:04:03
 * @Description: 实时签收率
-->
<template>
  <div class="mt64 pd_lr20">
    <div class="flex_between">
      <div class="fs28 fw700">实时签收率</div>
      <div class="right-btn-wrap" @click="btnOpenDia">
        展开{{ zoneData.dLevelName }} <i class="iconfont icon-dayuhao fs20"></i>
      </div>
    </div>
    <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableOption">
      <div class="mt24" style="height: 3.2rem" ref="chart-dom-trend"></div>
    </KydChartModel>
  </div>
</template>
<script>
import request from '../request'
import { drawScrollChart } from 'common/charts/chartOption'
export default {
  mixins: [request],
  props: ['resultOptions'],
  data() {
    return {
      tableOption: {},
      legendDataSource: []
    }
  },
  computed: {
    shishiData() {
      return this.resultOptions.shishiData
    },
    legendOption() {
      return {
        type: 'column',
        options: [
          {
            label: '实时签收率',
            per: [1]
          },
          {
            label: '指标',
            // int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'target_value',
            color: '#fff',
            per: [1]
          },
          {
            label: '达成',
            // int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'target_ach_rate',
            color: '#fff',
            per: [1]
          },
          {
            label: '签收',
            int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'total',
            color: '#fff'
          },
          {
            label: '距离内',
            int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'in_dis_sum',
            color: '#fff'
          },
          {
            label: '距离外',
            int: [0, 1, 0],
            seriesIndex: 0,
            dataIndex: 'not_in_dis_sum',
            color: '#fff'
          }
          // {
          //   label: '罚款'
          // }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    shishiData(val) {
      this.setChartTrend(val)
    }
  },
  methods: {
    btnOpenDia() {
      this.$emit('btnOpenDia', 'shishi')
    },
    setChartTrend(result) {
      const xData = []
      const sData = []
      this.legendDataSource = []
      if (result.length) {
        const tmp = [...result]
        this.$objectSortUp(tmp, this.key_date)
        tmp.forEach(item => {
          xData.push(item[this.key_date])
          sData.push({
            value: item['in_dis_rate'],
            ...item
          })
        })
      }
      const options = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$perFormat(value)
            }
          }
        ],
        series: [
          {
            data: sData,
            type: 'line'
          }
        ]
      }
      drawScrollChart(options, this.$refs['chart-dom-trend'], { dateIndex: this.dateIndex, isOneLine: true })
      this.tableOption = { options }
    }
  }
}
</script>
<style lang="less" scoped>
@import url('../common.less');
</style>
