<!--
 * @Author: shigl
 * @Date: 2023-12-11 16:48:14
 * @LastEditTime: 2024-02-26 18:30:20
 * @Description:
-->
<template>
  <CardList title="今日实时">
    <div class="pd_lr20 mt24">
      <KydSummaryDataList
        class="summary-custom-wrap"
        :isAuth="true"
        :columns="columns"
        :dataSource="realTimeData"
        :toDetail="toDetail"
        :showDetailBtn="true"
      ></KydSummaryDataList>
    </div>
    <KyDataDrawer
      :visible="visible"
      :title="diaTitle"
      height="80%"
      @close="diaClose"
      @drawerHeight="mixinsDrawerHeight"
    >
      <div class="drawer-custom-wrap">
        <NormalTable :columns="diaTableColumns" :dataSource="realTimeDetailDataSource" :maxHeight="mixinsTableMaxHeight" :loadMore="loadMore" :hasMore="hasMore"/>
      </div>
    </KyDataDrawer>
  </CardList>
</template>
<script>
import request from './request'
import KydSummaryDataList from 'common/components/kydSummaryDataList'
import NormalTable from 'common/components/normalTable'
import {
  realJiaohuoTableColumns,
  realYingtiTableColumns,
  realPaisongTableColumns,
  realQianshouTableColumns
} from './config'
export default {
  mixins: [request],
  components: {
    KydSummaryDataList,
    NormalTable
  },
  data() {
    return {
      realTimeData: [],
      diaTableColumns: [],
      realTimeDetailDataSource: [],
      visible: false,
      diaTitle: '',
      hasMore: false,
      pages: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0
    }
  },

  computed: {
    columns() {
      return [
        {
          subTitle: {
            label: '交货及时率'
          },
          parent: {
            label: '',
            dataIndex: 'arrivalInTimeRate',
            per: [1]
          },
          completeRate: {
            label: '达成率',
            dataIndex: 'arrivalInTimeTargetAchRate',
            per: [1]
          },
          detailBtn: {
            name: this.zoneData.dLevelName
          },
          child: [
            {
              label: '应交',
              int: [0, 1, 0],
              type: 'column',
              dataIndex: 'shouldArrivalCnt'
            },
            {
              label: '已交',
              int: [0, 1, 0],
              type: 'column',
              dataIndex: 'arrivalCnt'
            }
          ],
          footer: [
            {
              label: '超时',
              int: [0, 1, 0],
              // dataIndex: 'notInTimeArrivalCnt'
              dataIndex: 'notInTimeNotArrivalCnt'
            },
            {
              label: '及时',
              int: [0, 1, 0],
              dataIndex: 'inTimeArrivalCnt'
            }
          ]
        },
        {
          subTitle: {
            label: '提货留仓率'
          },
          parent: {
            label: '',
            dataIndex: 'pickStayRate',
            per: [1]
          },
          completeRate: {
            label: '达成率',
            dataIndex: 'pickStayTargetAchRate',
            per: [1]
          },
          detailBtn: {
            name: this.zoneData.dLevelName
          },
          child: [
            {
              label: '应提',
              int: [0, 1, 0],
              type: 'column',
              dataIndex: 'shouldPickCnt'
            },
            {
              label: '已提',
              int: [0, 1, 0],
              type: 'column',
              dataIndex: 'pickedCnt'
            }
          ],
          footer: [
            {
              // label: '超时',
              // label: '未提',
              label: '在库',
              int: [0, 1, 0],
              // dataIndex: 'notInTimePickCnt'
              dataIndex: 'inTimeUnPickCnt'
            },
            {
              label: '留仓',
              int: [0, 1, 0],
              dataIndex: 'pickStayCnt'
            }
            // {
            //   label: '罚款',
            //   int: [0, 1, 0],
            //   dataIndex: '5'
            // }
          ]
        },
        {
          subTitle: {
            // label: '派件及时率'
            label: '派送成功率'
          },
          parent: {
            label: '',
            dataIndex: 'deliveryInTimeRate',
            per: [1]
          },
          completeRate: {
            label: '达成率',
            dataIndex: 'deliveryInTimeAchRate',
            per: [1]
          },
          detailBtn: {
            name: this.zoneData.dLevelName
          },
          child: [
            {
              label: '应派',
              int: [0, 1, 0],
              type: 'column',
              // dataIndex: 'metricsShouldDeliveryCnt'
              dataIndex: 'shouldDeliveryCnt'
            },
            {
              label: '已签',
              int: [0, 1, 0],
              type: 'column',
              // dataIndex: 'metricsDeliveryCnt'
              dataIndex: 'deliveryCnt'
            }
          ],
          footer: [
            {
              label: '未签',
              int: [0, 1, 0],
              // dataIndex: 'notInTimeDeliveryCnt',
              // render: (h, val, item) => item['metricsShouldDeliveryCnt'] - item['metricsDeliveryCnt']
              dataIndex: 'shouldDeliveryCnt',
              render: (h, val, item) => item['shouldDeliveryCnt'] - item['deliveryCnt']
            },
            {
              label: '超时',
              int: [0, 1, 0],
              type: 'column',
              // dataIndex: 'metricsDeliveryCnt'
              dataIndex: 'notInTimeNotDeliveryCnt'
            }
            // {
            //   label: '罚款',
            //   int: [0, 1, 0],
            //   dataIndex: '5'
            // }
          ]
        },
        {
          subTitle: {
            label: '实时签收率'
          },
          parent: {
            label: '',
            dataIndex: 'signInTimeRate',
            per: [1]
          },
          completeRate: {
            label: '达成率',
            dataIndex: 'signInTimeAchRate',
            per: [1]
          },
          detailBtn: {
            name: this.zoneData.dLevelName
          },
          child: [
            {
              label: '签收',
              type: 'column',
              int: [0, 1, 0],
              dataIndex: 'signCnt'
            },
            {
              label: '距离内',
              int: [0, 1, 0],
              type: 'column',
              dataIndex: 'signInDisCnt'
            }
          ],
          footer: [
            // {
            //   label: '罚款',
            //   int: [0, 1, 0],
            //   dataIndex: '4'
            // },
            {
              label: '距离外',
              int: [0, 1, 0],
              dataIndex: 'signNotInDisCnt'
            }
          ]
        }
      ]
    }
  },
  watch: {
    zoneCode() {
      this.getRealTimeData()
    }
  },
  methods: {
    async getRealTimeData() {
      const res = await this._getRealTimeData()
      this.realTimeData = res
    },
    async toDetail(data) {
      console.log("data--", data)
      this.visible = true
      this.pages = {
        pageNum: 1,
        pageSize: 50
      }
      this.diaTitle = '今日' + data.subTitle.label
      this.getRealTimeDetailData(data)
    },
    diaClose() {
      this.visible = false
      this.realTimeDetailDataSource = []
    },
    async getRealTimeDetailData(data) {
      const res = await this._getRealTimeDetailData(this.pages)
      if (res.pageNum < res.pages) {
        this.hasMore = true
      } else {
        this.hasMore = false
      }
      if (data.subTitle.label === '交货及时率') {
        this.diaTableColumns = realJiaohuoTableColumns()
      } else if (data.subTitle.label === '提货留仓率') {
        this.diaTableColumns = realYingtiTableColumns()
      } else if (data.subTitle.label === '派送成功率') {
        this.diaTableColumns = realPaisongTableColumns()
      } else if (data.subTitle.label === '实时签收率') {
        this.diaTableColumns = realQianshouTableColumns()
      }
      this.pages.pageNum = res.pageNum || 1
      this.total = res.total || 0
      this.realTimeDetailDataSource = [...this.realTimeDetailDataSource, ...(res.list || [])]
    },
    async loadMore() {
      this.hasMore = false
      this.pages.pageNum++
      const res = await this._getRealTimeDetailData(this.pages)
      if (res.pageNum < res.pages) {
        this.hasMore = true
      } else {
        this.hasMore = false
      }
      this.pages.pageNum = res.pageNum || 1
      this.total = res.total || 0
      this.realTimeDetailDataSource = [...this.realTimeDetailDataSource, ...(res.list || [])]
    }
  },
  mounted() {
    this.getRealTimeData()
  }
}
</script>
<style lang="less" scoped>
.summary-custom-wrap {
  /deep/ .card_footer {
    display: flex;
    justify-content: flex-start !important;
    padding: 0.12rem 0.2rem !important;
    .footer_wrap {
      flex: 1;
      &:nth-child(2n) {
        padding-left: 0.16rem;
      }
    }
  }
}
.drawer-custom-wrap {
  margin-top: 0.24rem;
}
</style>
