/*
 * @Author: shigl
 * @Date: 2023-12-15 13:54:26
 * @LastEditTime: 2023-12-19 14:37:45
 * @Description:
 */
import moment from 'moment'
export default {
  name: 'terminal',
  namespaced: true,
  state: {
    todayDate: '',
    incDay: '',
    incMon: '',
    dateIndex: 0
  },
  getters: {
    dateValue(state) {
      const dateIndex = state.dateIndex
      return [state.incDay, state.incMon][dateIndex]
    }
  },
  mutations: {
    setState(state, payload) {
      for (const key in payload) {
        if (Object.hasOwnProperty.call(payload, key)) {
          state[key] = payload[key]
        }
      }
    }
  },
  actions: {
    initDate({ state }) {
      state.todayDate = moment().format('YYYYMMDD')
      state.incDay = moment().subtract(1, 'days').format('YYYYMMDD')
      state.incMon = moment().subtract(1, 'days').format('YYYYMM')
    }
  }
}
