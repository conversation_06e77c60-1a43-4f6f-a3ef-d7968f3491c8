/*
 * @Author: shigl
 * @Date: 2023-12-12 10:19:08
 * @LastEditTime: 2024-01-24 18:18:27
 * @Description:
 */
import { mapState, mapActions, mapGetters, mapMutations } from 'vuex'
import { reqHttp, reqJava } from 'common/js/req'
export default {
  mixins: [],
  data() {
    return {
      mixinsTableMaxHeight: '8rem'
    }
  },
  computed: {
    isJmq() {
      return this.zoneData.zoneName.includes('加盟区')
    },
    zoneLevel() {
      console.log('this.zoneData--isJmq', this.isJmq)
      console.log('this.zoneData--', this.zoneData)
      console.log('this.zoneParams--', this.zoneParams)
      if (+this.zoneData.zoneLevel === 34 && (this.zoneData.levelKey === 'manager' || this.isJmq)) {
        // return '37'
        return '35'
      }
      return this.zoneData.zoneLevel
    },
    dZoneLevel() {
      if (+this.zoneData.zoneLevel === 34 && (this.zoneData.levelKey === 'manager' || this.isJmq)) {
        return '39'
      }
      if (+this.zoneData.zoneLevel === 33) {
        // return '37'
        return '35'
      }
      return this.zoneData.dZoneLevel
    },

    zoneCode() {
      return this.zoneData.zoneCode
    },
    levelKey() {
      return this.isJmq ? 'manager' : this.zoneData.levelKey
    },
    key_date() {
      return ['inc_day', 'inc_month'][this.dateIndex]
    },
    startDay() {
      return this.$moment(this.dateValue)
        .subtract(14, 'days')
        .format('YYYY-MM-DD')
    },
    startMon() {
      return this.$moment(this.dateValue)
        .subtract(11, 'months')
        .format('YYYY-MM')
    },
    endDay() {
      return this.$moment(this.dateValue).format('YYYY-MM-DD')
    },
    endMon() {
      return this.$moment(this.dateValue).format('YYYY-MM')
    },
    ...mapState({
      holidayData: state => state.holidayData,
      zoneParams: state => state.zoneParams
    }),
    ...mapGetters({
      isAuth: 'isAuth',
      zoneData: 'zoneData'
    }),
    ...mapState('terminal', {
      dateIndex: state => state.dateIndex,
      incDay: state => state.incDay,
      incMon: state => state.incMon
    }),
    ...mapGetters('terminal', {
      dateValue: 'dateValue'
    })
  },
  methods: {
    ...mapMutations('terminal', ['setState']),
    ...mapActions('terminal', ['initDate']),
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    getHeaders() {
      const orgCode = this.zoneParams.zoneCode || ''
      const orgLevel = ['30', '31', '32', '33', '34'][this.zoneParams.zoneLevel]
      let isJmq = false
      if (this.zoneParams.zoneName) {
        isJmq = this.zoneParams.zoneName.includes('加盟区')
      }
      if (isJmq) {
        return {
          headers: {
            'orgCode': orgCode,
            'orgLevel': orgLevel
          }
        }
      } else {
        return {}
      }
    },
    _getRealTimeData() {
      // startTime	Long	是		开始时间 时间撮，传入当日起始时间
      // endTime	Long	是		截止时间 时间撮，传入当日结束时间
      // provinceAreaCode	String	否		省区编码
      // areaCode	String	否		区域编码
      // orgCode	String	否		网点编码
      // netManagerCode	String	否		网点网管code
      const levelMap = {
        bu: {},
        province: { provinceAreaCode: this.zoneCode },
        area: { provinceAreaCode: this.zoneParams.province_area_code, areaCode: this.zoneCode },
        manager: { areaCode: this.zoneParams.area_code, netManagerCode: this.zoneCode },
        dept: { areaCode: this.zoneParams.area_code, orgCode: this.zoneCode }
      }
      const config = this.getHeaders()
      const params = {
        // level_code: this.zoneLevel,
        startTime: this.$moment()
          .startOf('day')
          .valueOf(),
        endTime: this.$moment()
          .endOf('day')
          .valueOf(),
        ...levelMap[this.levelKey]
      }
      // 旧接口：/resourceServices/jtpqRealtime/counting
      return reqJava('/cockpit/realtimeQuery/jtpqRealtime2/counting', {
        data: params,
        headers: config.headers
      })
    },
    // 1. 交货-维度
    _getJiaohuoData(data, dateIndex) {
      // level_code	String	是	30	30总部，32省区，33区域，39网点，35加盟片区
      // st_arrival_month	String	否	2023-12	交货考核月份
      // st_arrival_month_ge	String	否	2023-12	交货考核月份 >=
      // st_arrival_month_le	String	否	2023-12	交货考核日期 <=
      // src_pro_area_code	String	否		开单省区编码
      // src_area_code	String	否		开单区域编码
      // src_dept_code	String	否		开单网点编码
      // src_dept_manager_code	String	否		开单网点网管code
      const levelMap = {
        bu: {},
        province: { src_pro_area_code: this.zoneCode },
        area: { src_pro_area_code: this.zoneParams.province_area_code, src_area_code: this.zoneCode },
        manager: { src_area_code: this.zoneParams.area_code, src_dept_manager_code: this.zoneCode },
        dept: { src_area_code: this.zoneParams.area_code, src_dept_code: this.zoneCode }
      }
      const config = this.getHeaders()
      const params = {
        level_code: this.zoneLevel,
        [['st_arrival_day_ge', 'st_arrival_month_ge'][dateIndex]]: [this.startDay, this.startMon][dateIndex],
        [['st_arrival_day_le', 'st_arrival_month_le'][dateIndex]]: [this.endDay, this.endMon][dateIndex],
        ...levelMap[this.levelKey],
        ...data
      }
      return reqHttp(['dm_sx_sxzk_jptq_jh_sum_day', 'dm_sx_sxzk_jptq_jh_sum_month'][dateIndex], {
        data: params,
        ...config
      })
    },
    //  提货-维度
    _getTihuoData(data, dateIndex) {
      // level_code	String	是	30	30总部，32省区，33区域，39网点，35加盟片区
      // shoud_pick_up_date	String	否	2023-12-14	应提货日期
      // shoud_pick_up_date_ge	String	否	2023-12-14	应提货日期 >=
      // shoud_pick_up_date_le	String	否	2023-12-14	应提货日期 <=
      // pick_up_province_area_code	String	否		应提省区编码
      // pick_up_area_code	String	否		应提区域编码
      // pick_up_dept_code	String	否		应提网点编码
      // pick_up_dept_manager_code	String	否		应提网点网管code
      const levelMap = {
        bu: {},
        province: { pick_up_province_area_code: this.zoneCode },
        area: { pick_up_province_area_code: this.zoneParams.province_area_code, pick_up_area_code: this.zoneCode },
        manager: { pick_up_area_code: this.zoneParams.area_code, pick_up_dept_manager_code: this.zoneCode },
        dept: { pick_up_area_code: this.zoneParams.area_code, pick_up_dept_code: this.zoneCode }
      }
      const config = this.getHeaders()
      const params = {
        level_code: this.zoneLevel,
        [['shoud_pick_up_date_ge', 'shoud_pick_up_month_ge'][dateIndex]]: [this.startDay, this.startMon][dateIndex],
        [['shoud_pick_up_date_le', 'shoud_pick_up_month_le'][dateIndex]]: [this.endDay, this.endMon][dateIndex],
        ...levelMap[this.levelKey],
        ...data
      }
      return reqHttp(['dm_sx_sxzk_jptq_th_sum_day', 'dm_sx_sxzk_jptq_th_sum_month'][dateIndex], {
        data: params,
        ...config
      })
    },
    //  派送-维度
    _getPaisongData(data, dateIndex) {
      // level_code	String	是	30	30总部，32省区，33区域，39网点
      // stand_distribute_date	String	否	2023-12-14	标准派送日期
      // stand_distribute_date_ge	String	否	2023-12-14	标准派送日期 >=
      // stand_distribute_date_le	String	否	2023-12-14	标准派送日期 <=
      // dest_province_area_code	String	否		目的省区编码
      // dest_area_code	String	否		目的区域编码
      // dest_dept_code	String	否		目的网点编码
      // dest_dpt_manager_code	String	否		目的网点网管code

      const levelMap = {
        bu: {},
        province: { dest_province_area_code: this.zoneCode },
        area: { dest_province_area_code: this.zoneParams.province_area_code, dest_area_code: this.zoneCode },
        manager: { dest_area_code: this.zoneParams.area_code, dest_dept_manager_code: this.zoneCode },
        dept: { dest_area_code: this.zoneParams.area_code, dest_dept_code: this.zoneCode }
      }
      const config = this.getHeaders()
      const params = {
        level_code: this.zoneLevel,
        [['stand_distribute_date_ge', 'stand_distribute_month_ge'][dateIndex]]: [this.startDay, this.startMon][
          dateIndex
        ],
        [['stand_distribute_date_le', 'stand_distribute_month_le'][dateIndex]]: [this.endDay, this.endMon][dateIndex],
        ...levelMap[this.levelKey],
        ...data
      }
      return reqHttp(['dm_sx_sxzk_jptq_ps_sum_day', 'dm_sx_sxzk_jptq_ps_sum_month'][dateIndex], {
        data: params,
        ...config
      })
    },
    //  实时-维度
    _getShishiData(data, dateIndex) {
      // level_code	String	是	30	30总部，32省区，33区域，39网点
      // cnt_date	String	否	2023-12-14	统计日期
      // cnt_date_ge	String	否	2023-12-14	统计日期 >=
      // cnt_date_le	String	否	2023-12-14	统计日期 <=
      // dest_province_area_code	String	否		目的省区编码
      // dest_area_code	String	否		目的区域编码
      // dest_dept_code	String	否		目的网点编码
      // dest_dept_manager_code	String	否		目的网点网管code
      const levelMap = {
        bu: {},
        province: { dest_province_area_code: this.zoneCode },
        area: { dest_province_area_code: this.zoneParams.province_area_code, dest_area_code: this.zoneCode },
        manager: { dest_area_code: this.zoneParams.area_code, dest_dept_manager_code: this.zoneCode },
        dept: { dest_area_code: this.zoneParams.area_code, dest_dept_code: this.zoneCode }
      }
      const config = this.getHeaders()
      const params = {
        level_code: this.zoneLevel,
        [['cnt_date_ge', 'cnt_month_ge'][dateIndex]]: [this.startDay, this.startMon][dateIndex],
        [['cnt_date_le', 'cnt_month_le'][dateIndex]]: [this.endDay, this.endMon][dateIndex],
        ...levelMap[this.levelKey],
        ...data
      }
      return reqHttp(['dm_sx_sxzk_jptq_qs_sum_day', 'dm_sx_sxzk_jptq_qs_sum_month'][dateIndex], {
        data: params,
        ...config
      })
    },
    //  实时-下钻
    _getRealTimeDetailData(param) {
      // startTime	Long	是		开始时间 时间撮，传入当日起始时间
      // endTime	Long	是		截止时间 时间撮，传入当日结束时间
      // provinceAreaCode	String	否		省区编码
      // areaCode	String	否		区域编码
      // orgCode	String	否		网点编码
      // netManagerCode	String	否		网点网管code
      const levelMap = {
        bu: {},
        province: { provinceAreaCode: this.zoneCode },
        area: { provinceAreaCode: this.zoneParams.province_area_code, areaCode: this.zoneCode },
        manager: { areaCode: this.zoneParams.area_code, netManagerCode: this.zoneCode },
        dept: { areaCode: this.zoneParams.area_code, orgCode: this.zoneCode }
      }
      const config = this.getHeaders()
      const params = {
        startTime: this.$moment().startOf('day').valueOf(),
        endTime: this.$moment().endOf('day').valueOf(),
        ...param,
        ...levelMap[this.levelKey]
      }
      // 旧接口 /resourceServices/jtpqRealtime/listSubCounting
      return reqJava('/cockpit/realtimeQuery/jtpqRealtime2/listSubCounting', {
        data: params,
        headers: config.headers
      })
    }
  }
}
