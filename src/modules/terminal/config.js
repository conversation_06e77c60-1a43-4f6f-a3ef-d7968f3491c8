/*
 * @Author: shigl
 * @Date: 2023-12-15 17:12:39
 * @LastEditTime: 2023-12-20 14:22:02
 * @Description:
 */
// import moment from 'moment'
// {
//   label: '日期',
//   fixed: 'left',
//   dataIndex: ['inc_day', 'inc_month'][dateIndex],
//   render: (h, val) => moment(String(val)).format(['YYYY.MM.DD', 'YYYY.MM'][dateIndex])
// },
import { numToPercent, numToInteger } from 'common/js/numFormat'
export const createDiaJiaohuoTableColumns = (dateIndex, levelKey) => {
  const levelMap = {
    bu: 'src_pro_area_name',
    province: 'src_area_name',
    area: 'src_dept_manager_name',
    dept: 'src_dept_manager_name',
    manager: 'src_dept_name'
  }
  let columns = [
    {
      label: '排名',
      fixed: 'left',
      render: (h, val) => {
        return <div class="default-rank">{val + 1}</div>
      }
    },
    {
      label: '组织',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: levelMap[levelKey]
    },
    {
      label: '网点',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'src_dept_name'
    },
    {
      label: '网管',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'src_dept_manager_name'
    },
    {
      label: '指标',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'target_value',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '达成',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'target_ach_rate',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '交货及时率',
      dataIndex: 'intime_d_rate',
      align: 'right',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '应交',
      dataIndex: 'wb_cnt',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    },
    {
      label: '已交',
      dataIndex: 'intime_wb_d_sum',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    },
    {
      label: '超时',
      dataIndex: 'not_intime_wb_d_sum',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    }
  ]
  if (['bu', 'province'].includes(levelKey)) {
    columns = columns.filter(item => !/网管|网点/.test(item['label']))
  } else if (['area'].includes(levelKey)) {
    columns = columns.filter(item => !/组织|网点/.test(item['label']))
  } else {
    columns = columns.filter(item => !/组织/.test(item['label']))
  }
  return columns
}
export const createDiaTihuoTableColumns = (dateIndex, levelKey) => {
  const levelMap = {
    bu: 'pick_up_province_area_name',
    province: 'pick_up_area_name',
    area: 'pick_up_dept_manager_name',
    dept: 'pick_up_dept_manager_name',
    manager: 'pick_up_dept_name'
  }

  let columns = [
    {
      label: '排名',
      fixed: 'left',
      render: (h, val) => {
        return <div class="default-rank">{val + 1}</div>
      }
    },

    {
      label: '组织',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: levelMap[levelKey]
    },
    {
      label: '网点',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'pick_up_dept_name'
    },
    {
      label: '网管',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'pick_up_dept_manager_name'
    },
    {
      label: '指标',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'target_value',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '达成',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'target_ach_rate',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '提货留仓率',
      dataIndex: 'pick_stay_rate',
      align: 'right',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '应提',
      align: 'right',
      dataIndex: 'shoud_pick_cnt',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    },
    {
      label: '已提',
      align: 'right',
      dataIndex: 'picked_cnt',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    },
    {
      label: '留仓',
      align: 'right',
      dataIndex: 'stay_cnt',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    }
  ]
  if (['bu', 'province'].includes(levelKey)) {
    columns = columns.filter(item => !/网管|网点/.test(item['label']))
  } else if (['area'].includes(levelKey)) {
    columns = columns.filter(item => !/组织|网点/.test(item['label']))
  } else {
    columns = columns.filter(item => !/组织/.test(item['label']))
  }
  return columns
}
export const createDiaPaisongTableColumns = (dateIndex, levelKey) => {
  const levelMap = {
    bu: 'dest_province_area_name',
    province: 'dest_area_name',
    area: 'dest_dept_manager_name',
    dept: 'dest_dept_manager_name',
    manager: 'dest_dept_name'
  }

  let columns = [
    {
      label: '排名',
      fixed: 'left',
      render: (h, val) => {
        return <div class="default-rank">{val + 1}</div>
      }
    },

    {
      label: '组织',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: levelMap[levelKey]
    },
    {
      label: '网点',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'dest_dept_name'
    },
    {
      label: '网管',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'dest_dept_manager_name'
    },
    {
      label: '指标',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'target_value',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '达成',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'target_ach_rate',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '派送及时率',
      dataIndex: 'intime_rate',
      align: 'right',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '应派',
      dataIndex: 'should_delivery_wb_cnt',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    },
    {
      label: '已签',
      dataIndex: 'intime_wb_sum',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    },
    {
      label: '超时',
      dataIndex: 'not_intime_wb_sum',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0)
    }
  ]
  if (['bu', 'province'].includes(levelKey)) {
    columns = columns.filter(item => !/网管|网点/.test(item['label']))
  } else if (['area'].includes(levelKey)) {
    columns = columns.filter(item => !/组织|网点/.test(item['label']))
  } else {
    columns = columns.filter(item => !/组织/.test(item['label']))
  }
  return columns
}
export const createDiaShishiTableColumns = (dateIndex, levelKey) => {
  const levelMap = {
    bu: 'dest_province_area_name',
    province: 'dest_area_name',
    area: 'dest_dept_manager_name',
    dept: 'dest_dept_manager_name',
    manager: 'dest_dept_name'
  }

  let columns = [
    {
      label: '排名',
      fixed: 'left',
      render: (h, val) => {
        return <div class="default-rank">{val + 1}</div>
      }
    },

    {
      label: '组织',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: levelMap[levelKey]
    },
    {
      label: '网点',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'dest_dept_name'
    },
    {
      label: '网管',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'dest_dept_manager_name'
    },
    {
      label: '指标',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'target_value',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '达成',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'target_ach_rate',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '实时签收率',
      dataIndex: 'in_dis_rate',
      align: 'right',
      render: (h, val) => numToPercent(val)
    },
    {
      label: '签收',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0),
      dataIndex: 'total'
    },
    {
      label: '距离内',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0),
      dataIndex: 'in_dis_sum'
    },
    {
      label: '距离外',
      align: 'right',
      render: (h, val) => numToInteger(val, 0, 1, 0),
      dataIndex: 'not_in_dis_sum'
    }
  ]
  if (['bu', 'province'].includes(levelKey)) {
    columns = columns.filter(item => !/网管|网点/.test(item['label']))
  } else if (['area'].includes(levelKey)) {
    columns = columns.filter(item => !/组织|网点/.test(item['label']))
  } else {
    columns = columns.filter(item => !/组织/.test(item['label']))
  }
  return columns
}

export const realJiaohuoTableColumns = (exeColumn, levelKey) => ([
  {
    label: '排名',
    fixed: 'left',
    render: (h, val) => {
      return <div class="default-rank">{val + 1}</div>
    }
  },
  {
    label: '组织',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'orgName'
  },
  {
    label: '达成',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'arrivalInTimeTargetAchRate',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '指标',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'arrivalInTimeTarget',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '及时率',
    dataIndex: 'arrivalInTimeRate',
    align: 'right',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '应交',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'shouldArrivalCnt'
  },
  {
    label: '已交',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'arrivalCnt'
  },
  {
    label: '超时',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'notInTimeNotArrivalCnt'
    // dataIndex: 'notInTimeArrivalCnt'
  }
])
export const realYingtiTableColumns = (exeColumn, levelKey) => ([
  {
    label: '排名',
    fixed: 'left',
    render: (h, val) => {
      return <div class="default-rank">{val + 1}</div>
    }
  },
  {
    label: '组织',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'orgName'
  },
  {
    label: '达成',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'pickStayTargetAchRate',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '指标',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'pickStayTarget',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '及时率',
    dataIndex: 'pickStayRate',
    align: 'right',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '应提',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'shouldPickCnt'
  },
  {
    label: '已提',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'pickedCnt'
  },
  {
    label: '留仓',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'pickStayCnt'
  }
])

export const realPaisongTableColumns = (exeColumn, levelKey) => ([
  {
    label: '排名',
    fixed: 'left',
    render: (h, val) => {
      return <div class="default-rank">{val + 1}</div>
    }
  },
  {
    label: '组织',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'orgName'
  },
  {
    label: '达成',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'deliveryInTimeAchRate',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '指标',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'deliveryInTimeTarget',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '成功率',
    dataIndex: 'deliveryInTimeRate',
    align: 'right',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '应派',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'shouldDeliveryCnt'
    // dataIndex: 'metricsShouldDeliveryCnt'
  },
  {
    label: '已签',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'deliveryCnt'
    // dataIndex: 'metricsDeliveryCnt'
  },
  {
    label: '超时',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'notInTimeNotDeliveryCnt'
    // dataIndex: 'notInTimeDeliveryCnt'
  }
])

export const realQianshouTableColumns = (exeColumn, levelKey) => ([
  {
    label: '排名',
    fixed: 'left',
    render: (h, val) => {
      return <div class="default-rank">{val + 1}</div>
    }
  },
  {
    label: '组织',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'orgName'
  },
  {
    label: '达成',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'signInTimeAchRate',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '指标',
    maxWidth: '2.2rem',
    fixed: 'left',
    dataIndex: 'signInTimeTarget',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '签收率',
    dataIndex: 'signInTimeRate',
    align: 'right',
    render: (h, val) => numToPercent(val)
  },
  {
    label: '签收',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'signCnt'
  },
  {
    label: '距离内',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'signInDisCnt'
  },
  {
    label: '距离外',
    align: 'right',
    render: (h, val) => numToInteger(val, 0, 1, 0),
    dataIndex: 'signNotInDisCnt'
  }
])
