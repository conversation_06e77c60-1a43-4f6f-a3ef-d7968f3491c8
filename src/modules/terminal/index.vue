<!--
 * @Author: shigl
 * @Date: 2023-12-11 16:12:11
 * @LastEditTime: 2023-12-20 10:58:19
 * @Description: 末端
-->
<template>
  <div class="page_bgc">
    <PageContent>
      <TodayModel class="mt12" />
      <div class="date_container">
        <KyDatePicker
          style="width: 3rem"
          isScroll
          :type="dateType"
          :dateValue="dateValue"
          @onChange="dateChange"
          :holidayData="holidayData"
        />
        <Tabs :options="['日', '月']" :tabIndex="dateIndex" @tabSelect="tabSelect"></Tabs>
      </div>
      <PastModel />
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>
<script>
import TodayModel from './todayModel.vue'
import PastModel from './pastModel.vue'
import request from './request'
export default {
  mixins: [request],
  components: { TodayModel, PastModel },
  data() {
    return {}
  },
  computed: {
    dateType() {
      return ['day', 'month'][this.dateIndex]
    }
  },
  methods: {
    tabSelect(index) {
      this.setState({
        dateIndex: index
      })
    },
    dateChange(date) {
      this.setState({
        [['incDay', 'incMon'][this.dateIndex]]: date
      })
    }
  },
  created() {
    this.initDate()
  }
}
</script>
<style lang="less" scoped>
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f4f4f4;
  position: sticky;
  top: 0px;
  z-index: 10;
}
</style>
