import BScroll from 'better-scroll'

export default {
  filters: {
    percentage(v) {
      return v ? `${(v * 100).toFixed(0)}%` : '-'
    }
  },
  methods: {
    /**
     * @param {string} minus 额外计算的高度 格式如默认值
     * @description 内容主体默认减去顶部导航和底部导航的高度
     * */
    setContentBoxHeight(minus = '+ 0px') {
      const topNavH = ' - 2.64rem' // 顶部导航高度 0.88*2
      const bottomNavH = this.isShowTab ? '- 1.9rem' : '' // 底部可能会有的导航高度
      this.contentWrapper.style.height = `calc(100vh ${topNavH} ${bottomNavH} ${minus})`
    },

    initBS(contentWrapper) {
      this.contentWrapper = contentWrapper || this.$refs.contentWrapper
      this.setContentBoxHeight()
      if (this.bsInstance) {
        this.bsInstance.refresh()
      } else {
        this.bsInstance = new BScroll(this.contentWrapper, {
          scrollX: false,
          scrollY: true,
          probeType: 3,
          bounce: false,
          click: true
        })
      }
      const { bsInstance } = this
      bsInstance.on('scroll', () => {
        if (bsInstance.movingDirectionY > 0 && !this.hasScrollDown) {
          document.querySelector('.first-level').classList.add('dispear')
          this.hasScrollDown = true
          this.hasScrollUp = false
          this.setContentBoxHeight('+ 0.88rem')
          this.$nextTick(() => {
            bsInstance.refresh()
          })
        } else if (
          bsInstance.movingDirectionY < 0 &&
          !this.hasScrollUp &&
          bsInstance.y === 0
        ) {
          this.hasScrollDown = false
          this.hasScrollUp = true
          document.querySelector('.first-level').classList.remove('dispear')
          this.setContentBoxHeight()
          this.$nextTick(() => {
            bsInstance.refresh()
          })
        }
      })
    }
  }
}
