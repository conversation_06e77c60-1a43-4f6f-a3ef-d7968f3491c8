<?xml version="1.0" encoding="UTF-8"?>
<svg width="732px" height="153px" viewBox="0 0 732 153" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>orange_bg</title>
    <defs>
        <path d="M109,0 C109,6.627417 114.372583,12 121,12 C127.627417,12 133,6.627417 133,0 L702,0 C706.418278,0 710,3.581722 710,8 L710,123 C710,127.418278 706.418278,131 702,131 L133,131 C133,124.372583 127.627417,119 121,119 C114.372583,119 109,124.372583 109,131 L8,131 C3.581722,131 0,127.418278 0,123 L0,8 C0,3.581722 3.581722,0 8,0 L109,0 Z" id="path-1"></path>
        <filter x="-2.5%" y="-11.8%" width="104.9%" height="126.7%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.939877717   0 0 0 0 0.939877717   0 0 0 0 0.939877717  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M108.999514,0 C108.999514,5.92846139 113.839718,10.7914921 119.998998,11.2706064 L119.999533,119.039399 C113.840225,119.52659 109,124.4716 109,130.5 C109,130.667795 109.00375,130.834751 109.011174,131.000794 L8,131 C3.581722,131 5.41083001e-16,127.418278 0,123 L0,8 C-5.41083001e-16,3.581722 3.581722,8.11624501e-16 8,0 L108.999514,0 Z" id="path-4"></path>
    </defs>
    <g id="组件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="orange_bg" transform="translate(11.000000, 9.000000)">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="路径">
                <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-1"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <mask id="mask-5" fill="white">
                <use xlink:href="#path-4"></use>
            </mask>
            <use id="形状结合" fill="#FF8066" xlink:href="#path-4"></use>
        </g>
    </g>
</svg>