<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-09-02 16:07:20
 * @LastEditTime: 2022-11-03 12:09:35
 * @Description:
-->
<template>
  <div>
    <TitleTabs color='black' style="background: #fff" :tabValue="tabValue" :tabData="tabData" @change="tabChange" :sp='true'/>
    <keep-alive>
      <component :is="tabValue" :zoneCode="zoneData.zoneCode" :zoneLevel="zoneData.level" :defaultDate="defaultDate"></component>
    </keep-alive>
  </div>
</template>

<script>
import project from './project'
// 融通
import capitalFlow from './capitalFlow'
// 中转
import transferWeight from './transferWeight'
// 干线
import trunkcost from './trunkcost'
import { buTabs, provinceTabs, areaTabs, deptTabs } from './config'
import { mapGetters } from 'vuex'
export default {
  components: {
    project,
    capitalFlow,
    transferWeight,
    trunkcost
  },

  data() {
    return {
      tabValue: 'project',
      defaultDate: ''
    }
  },
  computed: {
    ...mapGetters({
      zoneData: 'zoneData'
    }),
    tabData() {
      const level = {
        30: buTabs,
        32: provinceTabs,
        33: areaTabs,
        34: deptTabs
      }
      return level[+this.zoneData.zoneLevel]
    }
  },
  watch: {
    zoneCode: {
      handler() {
        this.$nextTick(() => {
          // 层级筛选后展示对应的模块
          const idx = this.tabData.findIndex(item => item['value'] === this.tabValue)
          if (idx === -1) {
            this.tabValue = 'project'
          } else {
            this.tabValue = this.tabData[idx].value
          }
        })
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      const tmp = this.tabData.find(item => item.value === this.tabValue) || []
      this.$sensors.webClick(`营运-${tmp.label}`)
    })
  },
  activated() {
    const idx = this.tabData.findIndex(item => item.value === this.tabValue)
    this.$sensors.webClick(`营运-${this.tabData[idx === -1 ? 0 : idx]}`)
  },
  created() {
    this.defaultDate = this.$moment().format('YYYY-MM-DD')
  },
  methods: {
    tabChange(val) {
      this.tabValue = val
      const tmp = this.tabData.find(item => item.value === this.tabValue) || []
      this.$sensors.webClick(`营运-${tmp.label}`)
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .roller-bar {
  z-index: 2;
}
/deep/.roller-bar .roller-bar-item--active {
  font-weight: 700;
}
</style>
