.content-wrapper {
  height: calc(100vh - 2.64rem);
  overflow-y: hidden;
}

.calender-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.3rem;
  height: 1rem;
}

.tab-wrap {
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-around;

  > div {
    width: 1.52rem;
    line-height: 0.6rem;
    font-family: PingFangSC-Regular;
    color: #414042;
    font-size: 0.28rem;
    border-radius: 28px;
    text-align: center;
    &.active {
      font-family: PingFangSC-Medium;
      background-color: #dc1e32;
      color: white;
    }
  }
}

.index-box-content {
  display: flex;
  margin: 0.25rem;
  padding: 0.3rem 0;
  box-shadow: 0 0 0.1rem 0.06rem rgba(221, 221, 221, 0.47);
  border-radius: 0.08rem;
  background-color: #fff;
  z-index: 2;

  > div {
    flex: 1;
    position: relative;
    font-family: PingFangSC-Regular;
    font-size: 0.24rem;
    &::after {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      right: 0;
      height: 98%;
      border-right: 1px solid #eee;
    }
    &:last-child::after {
      border: none;
    }

    .title {
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      color: #808285;
      line-height: 0.33rem;
      text-align: center;
    }
    .val {
      margin-top: 0.1rem;
      font-family: Helvetica-Condensed-Bold;
      font-size: 0.48rem;
      color: #414042;
      height: 0.6rem;
      line-height: 0.6rem;
      text-align: center;
    }
    .sub-index {
      margin-top: 0.37rem;
      padding-left: 0.44rem;
      > p {
        display: flex;
        align-items: center;
        margin-bottom: 0.2rem;
        color: #808285;
        &:last-child {
          margin-bottom: 0;
        }
        .sub-index_title {
          flex: 4;
        }
        .sub-index_val {
          flex: 5;
          font-size: 0.26rem;
          line-height: 0.4rem;
          &.orange {
            color: #ff6a17;
          }
          &.green {
            color: #08bca0;
          }
        }
      }
    }
  }
}

.index-box {
  background-color: #fff;

  .index-title {
    padding: 0.18rem 0 0.2rem 0.3rem;
    font-family: PingFangSC-Medium;
    font-size: 0.32rem;
    color: #414042;
    text-align: left;
    line-height: 0.42rem;
  }
}

.trend-chart-wrap {
  .legend {
    padding-left: 0.3rem;
    margin-top: 0.3rem;
    .date {
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      color: #6d6e71;
      line-height: 0.33rem;
    }
    .index-details {
      margin-top: 0.3rem;
      display: flex;
      .index-item {
        display: flex;
        flex: 1;
        > img {
          margin-top: 0.09rem;
          width: 0.3rem;
          height: 0.12rem;
        }
        > div {
          margin-left: 0.2rem;
          .title {
            font-family: PingFangSC-Regular;
            font-size: 0.24rem;
            color: #6d6e71;
            line-height: 0.33rem;
          }
          .val {
            margin-top: 0.06rem;
            line-height: 0.3rem;
            font-family: Helvetica-Condensed-Bold;
            font-size: 0.24rem;
            color: #414042;
          }
        }
      }
    }
  }

  .trend--chart {
    height: 3.5rem;
  }
}
