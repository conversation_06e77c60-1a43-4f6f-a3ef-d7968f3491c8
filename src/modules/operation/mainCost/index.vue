<template>
  <div class="content-wrapper" ref="contentWrapper">
    <div>
      <div class="calender-bar">
        <KyDatePicker
          type="day"
          :dateValue="dateValue"
          @onChange="dateChange"
          :holidayData="holidayData"
        ></KyDatePicker>
      </div>

      <div class="tab-wrap">
        <div
          v-for="item in tabData"
          @click="tabChange(item)"
          :class="{ active: tabSelected === item }"
          :key="item.value"
        >
          {{ item.label }}
        </div>
      </div>

      <div class="index-box-content">
        <div v-for="item in dashboard" :key="item.label">
          <h4 class="title">{{ item.label }}{{ item.unit || '' }}</h4>
          <div class="val">{{ item.value }}</div>
          <div class="sub-index">
            <p v-for="(one, index) in item.subIndex" :key="one.label">
              <span class="sub-index_title">{{ one.label }}</span>
              <span class="sub-index_val orange" :class="{ orange: !index, green: index }">{{
                one.value | percentage
              }}</span>
            </p>
          </div>
        </div>
      </div>

      <div class="index-box trend-chart-wrap">
        <h5 class="index-title">近8天趋势</h5>
        <div class="legend">
          <div class="date">{{ lengedVM.date ? $moment(lengedVM.date).format('YYYY年MM月DD日') : '-' }}</div>
          <div class="index-details">
            <div class="index-item" v-for="(item, index) in dashboard" :key="item.label">
              <img v-if="index === 0" src="../img/blue.png" alt="log" />
              <img v-if="index === 1" src="../img/orange.png" alt="log" />
              <img v-if="index === 2" src="../img/green.png" alt="log" />
              <div>
                <p class="title">{{ item.label }}</p>
                <p v-if="index === 0" class="val">{{ lengedVM.s1 | hasVal }}</p>
                <p v-if="index === 1" class="val">{{ lengedVM.s2 | hasVal }}</p>
                <p v-if="index === 2" class="val">{{ lengedVM.s3 | hasVal }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="trend--chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import sxMonitorMixins from '../../sxMonitor/sxMonitor.js'
import selfMixin from '../self'

import { timeTable } from '../../sxMonitor/common/ulit'
import { mapState } from 'vuex'

export default {
  mixins: [sxMonitorMixins, selfMixin],

  props: {
    zoneCode: {
      type: [String]
    },
    zoneLevel: {
      type: [String, Number]
    },
    rightName: {
      type: String
    },
    typeName: {
      type: String
    },
    isShowTab: {
      type: Boolean
    },
    defaultDate: {
      type: String
    }
  },

  watch: {
    zoneCode() {
      this.init()
    }
  },

  data() {
    return {
      dateValue: this.defaultDate,
      lengedVM: {
        date: '',
        s1: '',
        s2: '',
        s3: ''
      },
      tabData: [
        {
          label: '干线成本',
          value: 'A',
          children: [
            {
              label: '干线成本',
              unit: '(万元)',
              value: 2112,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            },
            {
              label: '一级干线',
              unit: '(万元)',
              value: 22,
              subIndex: [
                { label: '环比', value: 0.21 },
                { label: '同比', value: 0.31 },
                { label: '同期', value: 0.41 }
              ]
            },
            {
              label: '二级干线',
              unit: '(万元)',
              value: 543,
              subIndex: [
                { label: '环比', value: 0.22 },
                { label: '同比', value: 0.32 },
                { label: '同期', value: 0.42 }
              ]
            }
          ]
        },
        {
          label: '车次',
          value: 'B',
          children: [
            {
              label: '车次',
              value: 2122,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            },
            {
              label: '一级车次',
              value: 3412,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            },
            {
              label: '二级车次',
              value: 312,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            }
          ]
        },
        {
          label: '装载率',
          value: 'C',
          children: [
            {
              label: '装载率',
              value: 331,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            },
            {
              label: '一级装载率',
              value: 34613,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            },
            {
              label: '二级装载率',
              value: 34123,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            }
          ]
        },
        {
          label: '吨公里成本',
          value: 'D',
          children: [
            {
              label: '吨公里成本',
              value: 123,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            },
            {
              label: '一级吨公里成本',
              value: 123,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            },
            {
              label: '二级吨公里成本',
              value: 4421,
              subIndex: [
                { label: '环比', value: 0.2 },
                { label: '同比', value: 0.3 },
                { label: '同期', value: 0.4 }
              ]
            }
          ]
        }
      ],
      tabSelected: {},
      dashboard: []
    }
  },

  mounted() {
    this.tabChange()
    this.init()
  },

  computed: {
    ...mapState({
      holidayData: 'holidayData'
    }),
    // T-1
    t1() {
      const t = this.$moment()
      if (this.isSameDay(t, this.dateValue)) {
        return t.add(-1, 'd').format('YYYY-MM-DD')
      }
      return this.$moment(this.dateValue).format('YYYY-MM-DD')
    }
  },

  methods: {
    init() {
      this.initBS(this.$refs.contentWrapper)
      this.fetchRecentEightDTrendData()
    },

    // 时间改变
    dateChange(value) {
      this.dateValue = value
      this.init()
    },

    // 日期可选范围
    dateRange(min) {
      if (min) {
        const yesterday = this.$moment().add(-1, 'd').add(-1, 'M')
        const y = yesterday.year()
        const m = yesterday.month()
        return new Date(y, m, 1)
      }
      return new Date()
    },

    tabChange(item) {
      const record = item || this.tabData[0]
      const { children } = record
      this.dashboard = children
      this.tabSelected = record
      this.drawRecentEightDTrendChart()
    },

    // 获取近8天趋势数据
    fetchRecentEightDTrendData() {
      const dayTable = timeTable(this.t1, 8, -7, '-')
      const [startDay] = dayTable
      const endDay = dayTable[dayTable.length - 1]
      const level = this.zoneLevel
      const conditionList = [
        { key: 'startDay', value: this.$moment(startDay).format('YYYYMMDD') },
        { key: 'endDay', value: this.$moment(endDay).format('YYYYMMDD') },
        { key: 'zoneLevel', value: level },
        { key: this.roleKeyMap[level], value: this.zoneCode }
      ]
      this.sendTwoDimenRequest('sx_manage_monit_eight_days_info', conditionList)
        .then(res => {
          const { obj = [] } = res // eslint-disable-line
          const serie1 = []
          const serie2 = []
          const serie3 = []
          const seriesData = [serie1, serie2, serie3]
          const random = (mult = 1) => (Math.random() * mult).toFixed(1)

          dayTable.forEach((dateItem, index) => {
            const A_1 = random(1000)
            const A_2 = random(1000)
            const A_3 = random(1000)

            const B_1 = random(1000)
            const B_2 = random(1000)
            const B_3 = random(1000)

            const C_1 = random(100)
            const C_2 = random(100)
            const C_3 = random(100)

            const D_1 = random(10000)
            const D_2 = random(10000)
            const D_3 = random(10000)

            serie1.push([dateItem, [A_1, B_1, C_1, D_1]])
            serie2.push([dateItem, [A_2, B_2, C_2, D_2]])
            serie3.push([dateItem, [A_3, B_3, C_3, D_3]])
          })
          this.recentEightDTrendData = { seriesData, xAxisData: dayTable }
          this.drawRecentEightDTrendChart()
        })
        .catch(err => {
          this.$toast({
            duration: 2000,
            message: '请求近八日收入数据错误'
          })
          console.error('请求近八日收入数据错误', err)
        })
    },

    // 绘制近8天趋势数据图表
    drawRecentEightDTrendChart() {
      const { seriesData = [], xAxisData = [] } = this.recentEightDTrendData || {}
      const [serie1 = [], serie2 = [], serie3 = []] = seriesData
      const valueFrom = this.tabData.indexOf(this.tabSelected)
      const seriesLine = (data = [], color, indexKey) => {
        const result = data.map(item => {
          const [category, dataOrigin] = item
          const val = dataOrigin[valueFrom]
          return [category, val]
        })
        if (result.length) {
          const activeVal = result[result.length - 1]
          this.lengedVM.date = activeVal[0]
          this.lengedVM[indexKey] = activeVal[1]
        }
        return {
          type: 'line',
          data: result,
          showSymbol: false,
          itemStyle: { color },
          lineStyle: {
            shadowColor: color,
            shadowOffsetX: 0,
            shadowOffsetY: 8,
            shadowBlur: 12
          }
        }
      }
      const option = {
        tooltip: {
          show: true,
          padding: [5, 15, 5, 15],
          trigger: 'axis',
          formatter: params => {
            const [s1, s2, s3] = params
            Object.assign(this.lengedVM, {
              date: s1.data[0],
              s1: s1.data[1],
              s2: s2.data[1],
              s3: s3.data[1]
            })
          }
        },
        grid: { top: '25', left: '5', right: '5', bottom: '60' },
        xAxis: [
          {
            type: 'category',
            data: xAxisData,
            axisTick: { show: false },
            axisLabel: {
              formatter: val => {
                return this.$moment(val).format('MM/DD')
              },
              show: true,
              interval: 0,
              textStyle: {
                color: '#939393'
              }
            },
            axisPointer: {
              type: 'shadow',
              color: '#5594F2',
              value: xAxisData[xAxisData.length - 1]
            },
            axisLine: { show: false }
          }
        ],
        yAxis: { show: false },
        series: [
          seriesLine(serie1, '#2E55EC', 's1'),
          seriesLine(serie2, '#FF6A17', 's2'),
          seriesLine(serie3, '#09BCA0', 's3')
        ]
      }

      this.drawChart(document.querySelector('.trend--chart'), option)
    }
  }
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>
