/*
 * @Author: shigl
 * @Date: 2022-07-20 18:28:55
 * @LastEditTime: 2022-07-20 18:43:25
 * @Description:
 */
import moment from 'moment'
export default {
  namespaced: true,
  name: 'capitalFlow',
  state: {
    dateValue: moment().add(-2, 'd').format('YYYY-MM-DD'), // 日期
    zoneParams: {
      zoneLevel: '0',
      zoneCode: '001',
      zoneName: '顺心捷达'
    },
    qualityFlowWebData: [], // 融通品质总览(线路融通传入)
    qualityFlowTrendData: [] // 融通品质趋势(线路融通传入)
  },
  mutations: {
    setDateValue(state, date) {
      state.dateValue = date
    },
    setZoneParams(state, data) {
      state.zoneParams = data
    },
    setQualityFlowWebData(state, data) {
      state.qualityFlowWebData = data
    },
    setQualityFlowTrueData(state, data) {
      state.qualityFlowTrendData = data
    }
  },
  actions: {
  }
}

