/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-03-20 15:55:56
 * @Description:
 */
import requestMixins from 'common/mixins/requestMixins'
import { mapState } from 'vuex'
export default {
  mixins: [requestMixins],
  data() {
    return {}
  },
  computed: {
    levelKyCompanyCodeMap() {
      return {
        0: {},
        2: { kyCompanyCode: this.isDev ? 'K021Y' : this.zoneCode }
      }
    },

    leveldeptCodeMap() {
      return {
        0: {},
        2: { deptCode: this.isDev ? 'K021Y' : this.zoneCode }
      }
    },
    levelKyHqCode() {
      return {
        0: {},
        2: { kyHqCode: this.isDev ? 'K021Y' : this.zoneCode }
      }
    },
    levelProvinceAreaCode() {
      return {
        0: {},
        2: { levelProvinceAreaCode: this.isDev ? 'S029Y005' : this.zoneCode }
      }
    },
    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('capitalFlow', {
      dateValue: state => state.dateValue,
      zoneLevel: state => state.zoneParams.zoneLevel,
      zoneCode: state => state.zoneParams.zoneCode,
      zoneName: state => state.zoneParams.zoneName
    })
  },
  methods: {
    // 产品融通
    _getFrontPlaceData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_pd_front_financing_dtl_di', setData)
    },
    // 场地融通
    _getSitePlaceWedData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_bm_rt_dept_info_dtl_mi', setData)
    },
    // 共操作场
    _getSitePlaceData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_bm_dept_rt_dtl_mi', setData)
    },
    // 共操作场人均效能
    _getManData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_bm_sxcodept_emp_weight_dtl_di', setData)
    },
    // 线路融通--融通品质
    _getLineData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('sfsx_rt_line', setData)
    },
    // 效益融通
    _getBenefitData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_rt_benefit', setData)
    }
  }
}
