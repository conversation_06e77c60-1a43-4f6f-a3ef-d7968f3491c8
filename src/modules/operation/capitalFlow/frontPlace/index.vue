<template>
    <div>
        <CardList title="产品融通">
            <div slot="nav">
                <Tabs :options="['日','月']" :tabIndex="tabIndex" @tabSelect='tabSelect'></Tabs>
            </div>
            <div>
                <div class="pd_lr20 mt24">
                    <DataList :options="frontDataList"></DataList>
                    <!-- <div v-if="!+zoneLevel" class="mt64"> -->
                    <div v-show="isShowChart" class="mt64">
                        <div class="flex_between">
                            <LineLegend :options="sevenDayWeight" style="width:5.9rem"
                                        :style="{ visibility : isShowChart? 'visible':'hidden'}">
                            </LineLegend>
                            <BtnChange @btnChange="btnChange"></BtnChange>
                        </div>
                        <!-- echart -->
                        <div class="mt24">
                            <div class="mt16 fs20 grey999 flex_between">
                                <div>单位:T</div>
                                <div>单位:万</div>
                            </div>
                            <div class="mt16" ref="chart-bar-weight" style="height:3rem"></div>
                        </div>
                    </div>
                </div>
                <!-- <NormalTable v-show="!isShowChart" class="mt24" size="small" :dataSource="dataSource" -->
                <NormalTable v-show="isShowChart" class="mt24" size="small" :dataSource="dataSource"
                             :columns="columns"></NormalTable>
            </div>
        </CardList>
    </div>
</template>
<script>

import DataList from '../../components/dataList'
import LineLegend from '../../components/lineLegend'
import BtnChange from 'common/components/btnChange'

import { drawMoreBarChart } from 'common/charts/chartOption'
import { FRONTDATALIST, SEVENDAYWEIGHT } from '../config'
import request from '../request'

export default {
  mixins: [request],
  components: {
    DataList,
    LineLegend,
    BtnChange
  },
  data() {
    return {
      tabIndex: 1,
      frontDataList: JSON.parse(JSON.stringify(FRONTDATALIST)),
      sevenDayWeight: JSON.parse(JSON.stringify(SEVENDAYWEIGHT)),
      isShowChart: false,
      forntPlaceData: [],
      forntPlaceBarData: [],
      dataSource: [],
      columns: []
    }
  },
  watch: {
    dateValue(val) {
      this.initOptions()
    },
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    tabSelect(index) {
      this.tabIndex = index
      this.setFrontPlaceWeb(this.forntPlaceData)
    //   this.initChartMoreBarWeight(this.forntPlaceBarData)
    //   this.setTable(this.forntPlaceBarData)
    },
    btnChange(isTrue) {
      this.isShowChart = isTrue
    },
    // 前端融通数据
    async getFrontPlaceData() {
      const { obj } = await this._getFrontPlaceData({
        incDay: this.isDev ? '20201105' : this.$moment(this.dateValue).format('YYYYMMDD'),
        zoneLevel: this.zoneLevel,
        ...this.levelKyHqCode[+this.zoneLevel]
      })
      this.forntPlaceData = obj
      this.setFrontPlaceWeb(obj)
    },
    // 总览
    setFrontPlaceWeb(obj) {
      const result = JSON.parse(JSON.stringify(obj))
      this.frontDataList = JSON.parse(JSON.stringify(FRONTDATALIST))

      this.frontDataList.map((item, index) => {
        if (!this.tabIndex) {
          if (index === 1) {
            item.label = '货量同比'
          }
          if (index === 3) {
            item.label = '收入同比'
          }
        } else {
          if (index === 1) {
            item.label = '货量环比'
          }
          if (index === 3) {
            item.label = '收入环比'
          }
        }
      })

      if (result.length) {
        this.$checkNumber(result)
        if (!this.tabIndex) {
          const keyList = ['rt_qty', 'rt_qty_rate', 'rt_freight', 'rt_freight_rate']
          this.frontDataList.map((item, index) => {
            if (!index) {
              item.value = result[0][keyList[index]] / 1000
            } else if (index === 2) {
              item.value = result[0][keyList[index]] / 10000
            } else {
              item.value = result[0][keyList[index]]
            }
          })
        } else {
          const keyList = ['rt_qty_month', 'rt_qty_mon_rate', 'rt_freight_month', 'rt_freight_mon_rate']
          this.frontDataList.map((item, index) => {
            if (!index) {
              item.value = result[0][keyList[index]] / 1000
            } else if (index === 2) {
              item.value = result[0][keyList[index]] / 10000
            } else {
              item.value = result[0][keyList[index]]
            }
          })
        }
      }
    },
    // bar数据
    async getFrontPlaceBarData() {
      const { obj } = await this._getFrontPlaceData({
        incDay: this.isDev ? '20201105' : this.$moment(this.dateValue).format('YYYYMMDD'),
        zoneLevel: '2',
        ...this.levelKyHqCode[+this.zoneLevel]
      })
      this.forntPlaceBarData = obj
      this.initChartMoreBarWeight(obj)
      this.setTable(obj)
    },
    // echart图
    initChartMoreBarWeight(obj) {
      const result = JSON.parse(JSON.stringify(obj))
      this.sevenDayWeight = JSON.parse(JSON.stringify(SEVENDAYWEIGHT))
      const xData = []
      const sData = [[], []]
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#d71c2c',
              type: 'dotted',
              width: 1,
              opacity: 1
            },
            z: 2.0
          },
          formatter: params => {
            if (params.length) {
              params.map((item, index) => {
                if (!index) {
                  this.sevenDayWeight[index].value = this.$numToInteger(item.value, 1, 1000)
                } else {
                  this.sevenDayWeight[index].value = this.$numToInteger(item.value, 1, 10000)
                }
              })
            }
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => {
                if (value) {
                  if (/分拨区/.test(value)) {
                    return value.replace('分拨区', '')
                  }
                  return value
                }
              }
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => {
                return this.$numToInteger(value, 0, 1000)
              }
            }
          },
          {
            axisLabel: {
              formatter: value => {
                return this.$numToInteger(value, 0, 10000)
              }
            }
          }
        ],
        series: [
          {
            data: sData[0],
            z: 0
          },
          {
            type: 'line',
            yAxisIndex: 1,
            data: sData[1]
          }
        ]
      }
      if (result.length) {
        this.$checkNumber(result)
        if (!this.tabIndex) {
          result.map((item, index) => {
            xData.push(item['ky_hq_name'])
            sData[0].push(item['rt_qty'])
            sData[1].push(item['rt_freight'])
          })
        } else {
          result.map((item, index) => {
            xData.push(item['ky_hq_name'])
            sData[0].push(item['rt_qty_month'])
            sData[1].push(item['rt_freight_month'])
          })
        }
        // 默认
        sData.forEach((item, index) => {
          if (!index) {
            this.sevenDayWeight[index].value = this.$numToInteger(item[item.length - 1], 1, 1000)
          } else {
            this.sevenDayWeight[index].value = this.$numToInteger(item[item.length - 1], 1, 10000)
          }
        })
      }
      drawMoreBarChart(option, this.$refs['chart-bar-weight'])
    },
    setTable(obj) {
      const result = JSON.parse(JSON.stringify(obj))
      this.dataSource = []
      this.columns = [
        {
          label: '分拨区',
          dataIndex: '',
          rander: (h, value) => {
            let newValue = value
            if (/分拨区/.test(value)) {
              newValue = value.replace('分拨区', '')
            }
            if (/快运/.test(value)) {
              newValue = value.replace('快运', '')
            }
            return newValue
          }
        },
        {
          label: '转寄货量(T)',
          dataIndex: '',
          render: (h, value) => this.$numToInteger(value, 1, 1000)
        },
        {
          label: '转寄收入(万)',
          dataIndex: '',
          render: (h, value) => this.$numToInteger(value, 1, 10000)
        }
      ]
      if (!this.tabIndex) {
        const keyList = ['ky_hq_name', 'rt_qty', 'rt_freight']
        this.columns.map((item, index) => {
          item.dataIndex = keyList[index]
        })
      } else {
        const keyList = ['ky_hq_name', 'rt_qty_month', 'rt_freight_month']
        this.columns.map((item, index) => {
          item.dataIndex = keyList[index]
        })
      }
      if (result.length) {
        this.$checkNumber(result)
        this.dataSource = result
      }
    },
    initOptions() {
      this.getFrontPlaceData()
      if (!+this.zoneLevel) {
        // this.getFrontPlaceBarData()
      }
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang='less' scoped>
</style>
