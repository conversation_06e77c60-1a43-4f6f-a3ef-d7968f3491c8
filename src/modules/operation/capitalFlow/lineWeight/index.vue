<template>
  <div>
    <CardList title="线路融通">
      <div class="pd_lr20 mt24">
        <MultiDataList v-if="false" :columns="lineWeightColumns" :dataSource="lineWeightDataSource"></MultiDataList>
        <MultiDataList class="mt24" :columns="lineNumberColumns" :dataSource="lineNumberDataSource"></MultiDataList>
        <div v-if="!+zoneLevel" class="mt64 flex_between">
          <div class="fs28 fw700">省区融通线路数</div>
        </div>
        <KydChartModel
          class="mt32"
          isScroll
          :legendOption="{ options: lineLegendData, dataSource: legendDataSource }"
          :tableOption="lineTableData"
          position="left"
          tableLength="5"
          chartLegend="6"
        >
          <div class="mt8" ref="chart-model-line" style="height: 3rem"></div>
        </KydChartModel>
      </div>
    </CardList>
  </div>
</template>
<script>
import { lineLegendData, lineWeightColumns, lineNumberColumns } from '../config'
import { drawBarChart } from 'common/charts/chartOption'
import request from '../request'
import { mapMutations } from 'vuex'
export default {
  mixins: [request],
  components: {
  },
  data() {
    return {
      lineWeightColumns,
      lineWeightDataSource: [],
      lineNumberColumns,
      lineNumberDataSource: [],
      isChange: true,
      lineLegendData,
      legendDataSource: [],
      dataSource: [],
      lineTableData: {}
    }
  },
  computed: {
    incMonth() {
      return this.$moment(this.dateValue).format('YYYYMM')
    }
  },
  watch: {
    dateValue(val) {
      this.initOptions()
    },
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    ...mapMutations('capitalFlow', ['setQualityFlowWebData', 'setQualityFlowTrueData']),
    btnChange(isTrue) {
      this.isChange = isTrue
      // 神策点击
      this.$sensors.webClick(`融通线路数-${this.isChange ? '切换数据' : '切换图表'}`)
    },
    // 线路融通总览
    async getLineWebData() {
      const { obj } = await this._getLineData({
        incMonth: this.isDev ? '202011' : this.incMonth,
        zoneLevel: this.zoneLevel,
        isSfsx: 'SX',
        ...this.levelKyHqCode[+this.zoneLevel]
      })
      this.setLineWebData(obj)
      this.setQualityFlowWebData(obj)
    },
    setLineWebData(result) {
      this.lineWeightDataSource = []
      this.lineNumberDataSource = []
      if (result.length) {
        this.lineWeightDataSource = result[0]
        this.lineNumberDataSource = result[0]
      }
    },
    // 线路融通趋势
    async getLineTrendData() {
      const { obj } = await this._getLineData({
        incMonth: this.isDev ? '202011' : this.incMonth,
        zoneLevel: '2',
        isSfsx: 'SX'
      })
      this.initChartLineWeight(obj)
      this.setQualityFlowTrueData(obj)
    },
    initChartLineWeight(result) {
      const xData = []
      const sData = [[], []]
      const option = {
        tooltip: {
          show: true,
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => {
                if (/分拨区/.test(value)) {
                  return value.replace('分拨区', '')
                }
                return value
              }
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$numToInteger(value, 0, 1, 0)
            }
          }
        ],
        series: [
          {
            data: sData[0]
          },
          {
            data: sData[1]
          }
        ]
      }
      if (result.length) {
        this.$checkNumber(result)
        this.$objectSortDown(result, 'lv1_line_nums')
        const keyList = ['lv1_line_nums', 'lv2_line_nums']
        result.map((item, index) => {
          xData.push(item.ky_hq_name)
          sData[0].push(item[keyList[0]])
          sData[1].push(item[keyList[1]])
        })
      }

      drawBarChart(option, this.$refs['chart-model-line'])
      this.lineTableData = {
        options: option
      }
    },
    initOptions() {
      this.getLineWebData()
      if (!+this.zoneLevel) {
        this.getLineTrendData()
      }
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang='less' scoped>
</style>
