export const FRONTDATALIST = [
  {
    label: '转寄货量/T',
    value: '',
    int: 1
  },
  {
    label: '货量同比',
    value: '',
    indexType: 'up',
    per: '1'
  },
  {
    label: '转寄收入/万',
    value: '',
    int: 1
  },
  {
    label: '收入同比',
    value: '',
    indexType: 'up',
    per: '1'
  }
]

export const SEVENDAYWEIGHT = [
  {
    label: '转寄货量(T)',
    value: '',
    color: '#2E55EC'
  },
  {
    label: '转寄收入(万)',
    value: '',
    color: '#FF6A17'
  }
]

export const MANDATALIST = [
  {
    label: '当天效能',
    value: '',
    int: '1'
  },
  {
    label: '月平均效能',
    value: '',
    int: '1'
  },
  {
    label: '效能环比',
    value: '',
    per: '1',
    indexType: 'up'
  }
]

export const lineWeightColumns = [
  {
    parent: [{ label: 'SX融通量/T', dataIndex: 'sx_rt_weight', int: [1, 1000] }]
  },
  {
    parent: [{ label: 'SX开单量/T', dataIndex: 'sxbill_weight', int: [1, 1000] }]
  },
  {
    parent: [{ label: 'SX融通量占比', dataIndex: 'sxrt_rate', per: [1] }]
  }
]
export const lineNumberColumns = [
  {
    parent: [{ label: '一级线路数', dataIndex: 'lv1_line_nums', int: [0, 1, 0] }]
  },
  {
    parent: [{ label: '二级线路数', dataIndex: 'lv2_line_nums', int: [0, 1, 0] }]
  },
  {
    parent: [{ label: '一级干线占比', dataIndex: 'lv1_rate', per: [1] }]
  }
]
export const LINENUMBERDATA = [
  {
    label: '一级线路数',
    value: '',
    int: '0'
  },
  {
    label: '二级线路数',
    value: '',
    int: '0'
  },
  {
    label: '一级干线占比',
    value: '',
    per: 1
  }
]
export const lineLegendData = [
  {
    label: '一级线路',
    int: [0, 1, 0]
  },
  {
    label: '二级线路',
    int: [0, 1, 0]
  }
]
export const QUALITYDATALIST = [
  [
    {
      label: '月累计',
      value: '-ppm'
    },
    {
      label: '月环比',
      value: '-',
      per: '1',
      indexType: 'down'
    }
  ],
  [
    {
      label: '月累计',
      value: '-ppm'
    },
    {
      label: '月环比',
      value: '',
      per: '1',
      indexType: 'down'
    }
  ],
  [
    {
      label: '月累计',
      value: '-ppm'
    },
    {
      label: '月环比',
      value: '',
      per: '1',
      indexType: 'down'
    }
  ],
  [
    {
      label: '月累计',
      value: '',
      per: 1
    },
    {
      label: '月环比',
      value: '',
      per: '1',
      indexType: 'down'
    }
  ]
]
export const BENEFITLIST = [
  [
    {
      label: '总效益',
      value: '',
      int: '0'
    },
    {
      label: '线路效益',
      value: '',
      int: '0'
    },
    {
      label: '场地效益',
      value: '',
      int: '0'
    }
  ],
  [
    {
      label: '月环比',
      value: '',
      per: '1',
      indexType: 'down'
    },
    {
      label: '月环比',
      value: '',
      per: '1',
      indexType: 'down'
    },
    {
      label: '月环比',
      value: '',
      per: '1',
      indexType: 'down'
    }
  ]
]
export const siteColumns = [
  {
    parent: [{ label: '场地个数', dataIndex: 'sf_rtdept_nums', int: '0' }]
  },
  {
    parent: [{ label: '顺丰中转场个数', dataIndex: 'sf_hub_nums', int: '0' }]
  },
  {
    parent: [{ label: '融通场占比', dataIndex: 'rtdept_rate', per: '1' }]
  }
]

export const fullColumns = [
  {
    parent: [{ label: '场地个数', dataIndex: 'nums', int: '0' }]
  },
  {
    parent: [{ label: '场地饱和度', dataIndex: 'depot_weight_rate_month', per: '1' }]
  },
  {
    parent: [{ label: '共操作场占比', dataIndex: 'codept_rate', per: '1' }]
  }
]

export const manColumns = [
  {
    parent: [{ label: '当天效能', dataIndex: 'weight_sap_emp', int: '1' }]
  },
  {
    parent: [{ label: '月平均效能', dataIndex: 'month_weight_sap_emp', int: '1' }]
  },
  {
    parent: [{ label: '效能环比', dataIndex: 'm_on_m_weight_sap_emp', per: '1', indexType: 'up' }]
  }
]

export const inManColumns = [
  {
    parent: [{ label: '在职人数', dataIndex: 'sap_emp_num', int: [0, 1, 0] }]
  },
  {
    parent: [{ label: '出勤人数', dataIndex: 'sap_in_emp_num', int: [0, 1, 0] }]
  },
  {
    parent: [{ label: '出勤率', dataIndex: 'sap_in_emp_ratio', per: '1' }]
  }
]

export const outManColumns = [
  {
    parent: [{ label: '人数', dataIndex: 'pmp_emp_num', int: [0, 1, 0] }]
  },
  {
    parent: [{ label: '工时', dataIndex: 'pmp_in_time', int: [0, 1, 0] }]
  },
  {
    parent: [{ label: '合同工占比', dataIndex: 'sap_in_emp_prop', per: '1' }]
  }
]
