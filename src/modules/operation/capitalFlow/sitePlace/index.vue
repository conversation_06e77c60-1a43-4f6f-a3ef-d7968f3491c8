<template>
  <div>
    <CardList title="场地融通">
      <div class="pd_lr20">
        <div class="fs28 fw700 mt24">融通场地</div>
        <MultiDataList class="mt24" :columns="siteColumns" :dataSource="siteDataSource"></MultiDataList>
        <div class="fs28 fw700 mt64">共操作场各场地饱和度</div>
        <MultiDataList class="mt24" :columns="fullColumns" :dataSource="fullDataSource"></MultiDataList>
        <!-- charts -->
        <HScroll class="mt64" :width="scrollSiteWidth">
          <div ref="chart-bar-operate" style="height: 3.2rem"></div>
        </HScroll>
        <div class="fs28 fw700 mt64">共操作场人均效能</div>
        <MultiDataList class="mt24" :columns="manColumns" :dataSource="manDataSource"></MultiDataList>
        <!-- charts -->
        <HScroll class="mt64" :width="scrollManWidth">
          <div ref="chart-bar-man" style="height: 3rem"></div>
        </HScroll>
        <div class="mt64">
          <div class="man_container in_man flex_between">
            <div class="text_name flex_column_center">
              <img :src="iconMan" alt="" />
              <p class="fs20 white mt12">自有员工</p>
            </div>
            <div class="data_list_conent flex1">
              <MultiDataList :columns="inManColumns" :dataSource="inManDataSource"></MultiDataList>
            </div>
          </div>
          <div class="man_container out_man mt24 flex_between">
            <div class="text_name flex_column_center">
              <img :src="iconMan" alt="" />
              <p class="fs20 white mt12">外包员工</p>
            </div>
            <div class="data_list_conent flex1">
              <MultiDataList :columns="outManColumns" :dataSource="outManDataSource"></MultiDataList>
            </div>
          </div>
        </div>
      </div>
    </CardList>
  </div>
</template>
<script>
import { drawBarChart, newline } from 'common/charts/chartOption'
import { siteColumns, fullColumns, manColumns, inManColumns, outManColumns } from '../config'
import request from '../request'
import iconMan from '../img/icon_man.png'
export default {
  mixins: [request],
  components: {},
  data() {
    return {
      iconMan,
      siteColumns,
      siteDataSource: [],
      fullColumns,
      fullDataSource: [],
      scrollSiteWidth: '100%',
      manColumns,
      manDataSource: [],
      scrollManWidth: '100%',
      inManColumns,
      inManDataSource: [],
      outManColumns,
      outManDataSource: []
    }
  },
  watch: {
    dateValue(val) {
      this.initOptions()
    },
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    // 场地融通总览
    async getSitePlaceWedData() {
      const incMon = this.$moment(this.dateValue).format('YYYYMM')
      const { obj } = await this._getSitePlaceWedData({
        incMon: this.isDev ? '202011' : incMon,
        zoneLevel: this.zoneLevel,
        ...this.levelKyHqCode[+this.zoneLevel]
      })
      this.setSitePlaceWebData(obj)
    },
    setSitePlaceWebData(result) {
      this.siteDataSource = []
      if (result.length) {
        this.siteDataSource = result[0]
      }
    },
    // 共操作各个场地饱和度总览
    async getSitePlaceData() {
      const { obj } = await this._getSitePlaceData({
        incDay: this.isDev ? '20201105' : this.dateValue.replace(/-/g, ''),
        zoneLevel: this.zoneLevel,
        ...this.levelKyCompanyCodeMap[+this.zoneLevel]
      })
      this.setPlaceNum(obj)
    },
    setPlaceNum(result) {
      this.fullDataSource = []
      if (result.length) {
        this.fullDataSource = result[0]
      }
    },
    // 共操作场趋势部分
    async getOperateTrendData() {
      const { obj } = await this._getSitePlaceData({
        incDay: this.isDev ? '20201105' : this.dateValue.replace(/-/g, ''),
        zoneLevel: '4',
        ...this.levelKyCompanyCodeMap[+this.zoneLevel]
      })
      this.initChartBarOperate(obj)
    },
    initChartBarOperate(obj) {
      const result = JSON.parse(JSON.stringify(obj))
      const xData = []
      const sData = []
      const option = {
        grid: {
          bottom: '20%'
        },
        xAxis: [
          {
            data: xData
          },
          {
            data: sData,
            axisLabel: {
              formatter: value => this.$numToPercent(value, 0)
            }
          }
        ],
        series: [
          {
            data: sData
          }
        ]
      }
      if (result.length) {
        this.$checkNumber(result)
        this.$objectSortDown(result, 'depot_weight_rate_month')
        result.map((item, index) => {
          xData.push(item.dept_name)
          sData.push({
            value: item.depot_weight_rate_month
          })
        })
      }
      // 计算图宽度

      this.scrollSiteWidth = this.$hScrollWidth(xData.length, 6)

      drawBarChart(option, this.$refs['chart-bar-operate'], [], true)
    },
    // 人均效能总览
    async getManWebData() {
      const { obj } = await this._getManData({
        incDay: this.isDev ? '20201110' : this.dateValue.replace(/-/g, ''),
        zoneLevel: this.isDev ? '0.0' : this.zoneLevel,
        ...this.levelProvinceAreaCode[+this.zoneLevel]
      })
      this.setManWebData(obj)
    },
    setManWebData(obj) {
      const result = JSON.parse(JSON.stringify(obj))

      this.manDataSource = []
      this.inManDataSource = []
      this.outManDataSource = []
      if (result.length) {
        this.manDataSource = result[0]
        // 自有
        this.inManDataSource = result[0]
        // 外包
        this.outManDataSource = result[0]
      }
    },
    // 人均效能趋势
    async getManWebTrendData() {
      const { obj } = await this._getManData({
        incDay: this.isDev ? '20201110' : this.dateValue.replace(/-/g, ''),
        zoneLevel: this.isDev ? '4.0' : '4',
        ...this.levelProvinceAreaCode[+this.zoneLevel]
      })
      this.initChartBarMan(obj)
    },
    initChartBarMan(obj) {
      const result = JSON.parse(JSON.stringify(obj))
      const xData = []
      const sData = []
      const option = {
        grid: {
          bottom: '20%'
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => {
                if (value) {
                  if (/【SX】/.test(value)) {
                    return newline(value.replace('【SX】', ''))
                  }
                }
                return newline(value)
              }
            }
          },
          {
            data: sData,
            axisLabel: {
              formatter: value => this.$numToInteger(value, 1)
            }
          }
        ],
        series: [
          {
            data: sData
          }
        ]
      }
      if (result.length) {
        this.$checkNumber(result)
        this.$objectSortUp(result, 'weight_sap_emp')
        result.map((item, index) => {
          xData.push(item.dept_name)
          sData.push(item.weight_sap_emp)
        })
      }
      // 计算图宽度

      this.scrollManWidth = this.$hScrollWidth(xData.length, 6)

      drawBarChart(option, this.$refs['chart-bar-man'], [], true)
    },

    initOptions() {
      this.getSitePlaceWedData()
      this.getSitePlaceData()
      this.getOperateTrendData()
      this.getManWebData()
      this.getManWebTrendData()
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang='less' scoped>
.man_container {
  height: 1.5rem;
  width: 100%;
  position: relative;
  .text_name {
    width: 1.3rem;
    height: 100%;
    img {
      width: 0.64rem;
    }
  }
  .data_list_conent {
    // position: absolute;
    // top: -0.08rem;
    // right: 0;
    /deep/ .multi_data_list_wrapper {
      background-color: rgba(255, 255, 255, 0);
      //   border-radius: 0.1rem;
    }
  }
  &.in_man {
    background: url('../img/blue_bg.png') no-repeat left;
    background-size: 100% 100%;
  }
  &.out_man {
    background: url('../img/orange_bg.svg') no-repeat left;
    background-size: 100% 100%;
  }
}
</style>
