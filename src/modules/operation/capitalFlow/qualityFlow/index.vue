<template>
    <div>
        <CardList title="融通品质">
            <BtnTabs :options="['客诉率','遗失率','损坏率','扫描率']" :activeIndex='activeIndex' @btnConfirm="btnConfirm">
            </BtnTabs>
            <div class="quality_chart_box mt24 pd_lr20">
                <DataList :options="qualityDataList[activeIndex]"></DataList>
                <HScroll class="mt64" :width="qualityWidth">
                    <div v-if="!+zoneLevel" ref="chart-bar-quality" style="height:3rem"></div>
                </HScroll>
                <div v-if="!+zoneLevel && activeIndex !== 3" class="text_conent tr grey999 mt8 fs20">单位:PPM(百万分比)</div>
            </div>
        </CardList>
    </div>
</template>
<script>

import DataList from '../../components/dataList'

import { drawBarChart } from 'common/charts/chartOption'
import { QUALITYDATALIST } from '../config'
import request from '../request'
export default {
  mixins: [request],
  components: {
    DataList
  },
  data() {
    return {
      activeIndex: 0,
      qualityDataList: JSON.parse(JSON.stringify(QUALITYDATALIST)),
      qualityWidth: '100%',
      qualityFlowWebData: [],
      qualityFlowTrendData: []
    }
  },
  computed: {
    incMonth() {
      return this.$moment(this.dateValue).format('YYYYMM')
    }
  },
  watch: {
    dateValue(val) {
      this.initOptions()
    },
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    btnConfirm({ item, index }) {
      this.activeIndex = index
      this.setQualityWebData(this.qualityFlowWebData)
      this.initChartBarQuality(this.qualityFlowTrendData)
    },
    // 融通品质总览
    async getLineWebData() {
      this.qualityFlowWebData = []
      const { obj } = await this._getLineData({
        incMonth: this.isDev ? '202011' : this.incMonth,
        zoneLevel: this.zoneLevel,
        isSfsx: 'SF',
        ...this.levelKyHqCode[+this.zoneLevel]
      })
      this.qualityFlowWebData = obj
      this.setQualityWebData(obj)
    },
    setQualityWebData(val) {
      const result = JSON.parse(JSON.stringify(val))
      this.qualityDataList = JSON.parse(JSON.stringify(QUALITYDATALIST))
      const keyList = [
        ['ks_rate', 'ks_rate_mon'],
        ['lost_rate', 'lost_rate_mon'],
        ['destroy_rate', 'destroy_rate_mon'],
        ['save_rate', 'save_rate_mon']
      ]
      const funData = (activeIndex) => {
        this.qualityDataList[activeIndex].map((item, index) => {
          if (!index && activeIndex !== 3) {
            item.value = this.$numToInteger(result[0][keyList[activeIndex][index]], 0, 1, 0) + 'ppm'
          } else {
            item.value = result[0][keyList[activeIndex][index]]
          }
        })
      }
      if (result.length) {
        this.$checkNumber(result)
        funData(this.activeIndex)
      }
    },
    // 融通品质趋势
    async getLineTrendData() {
      this.qualityFlowTrendData = []
      const { obj } = await this._getLineData({
        incMonth: this.isDev ? '202011' : this.incMonth,
        zoneLevel: '2',
        isSfsx: 'SF'
      })
      this.qualityFlowTrendData = obj
      this.initChartBarQuality(obj)
    },
    initChartBarQuality(val) {
      const result = JSON.parse(JSON.stringify(val))
      const xData = []
      const sData = []
      const option = {
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: (value) => {
                if (/分拨区/.test(value)) {
                  return value.replace('分拨区', '')
                }
                return value
              }
            }
          },
          {
            data: sData,
            axisLabel: {
              formatter: (value) => {
                if (this.activeIndex === 3) {
                  return this.$numToPercent(value)
                }
                return this.$numToInteger(value, 0)
              }
            }
          }
        ],
        yAxis: [
          {
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            data: sData
          }
        ]
      }
      const keyList = ['ks_rate', 'lost_rate', 'destroy_rate', 'save_rate']

      if (result.length) {
        this.$checkNumber(result)
        this.$objectSortDown(result, keyList[this.activeIndex])
        result.map((item, index) => {
          xData.push(item.ky_hq_name)
          sData.push(item[keyList[this.activeIndex]])
        })
      }
      // 图表宽度
      if (sData.length <= 8) {
        this.qualityWidth = '100%'
      } else {
        this.qualityWidth = (100 / 8) * sData.length + '%'
      }
      drawBarChart(option, this.$refs['chart-bar-quality'])
    },
    initOptions() {
      this.getLineWebData()
      this.getLineTrendData()
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang='less' scoped>
// .quality_chart_box {
//     // position: relative;
// // }
// .text_conent {
//     // position: absolute;
//     right: 0.2rem;
//     bottom: 0.2rem;
// }
</style>
