<template>
  <div class="capital_pege page_bgc">
    <div class="date_container">
      <KyDatePicker type="day" :dateValue="dateValue" @onChange="dateChange" :holidayData="holidayData"></KyDatePicker>

    </div>
    <PageContent ref="contentWrapper">
      <!-- <FrontPlace ></FrontPlace> -->
      <SitePlace></SitePlace>
      <LineWeight class="mt24"></LineWeight>
      <QualityFlow v-if="false" class="mt24"></QualityFlow>
      <Benefit v-if="false" class="mt24"></Benefit>
      <div class="placeholder"></div>
    </PageContent>
  </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex'
import flowLayout from 'common/mixins/flowLayout.js'
// import FrontPlace from './frontPlace'
import SitePlace from './sitePlace'
import LineWeight from './lineWeight'
import QualityFlow from './qualityFlow'
import Benefit from './benefit'
export default {
  mixins: [flowLayout],
  components: {
    // FrontPlace,
    SitePlace,
    LineWeight,
    QualityFlow,
    Benefit
  },
  props: {
    zoneCode: {
      type: [String]
    },
    zoneLevel: {
      type: [String, Number]
    },
    rightName: {
      type: String
    }
  },
  data() {
    return {
      dateValue: this.$moment().subtract(2, 'days').format('YYYY-MM-DD')
    }
  },
  computed: {

    ...mapState({
      holidayData: 'holidayData'
    })
  },
  watch: {
    // 层级zoneLeve转换
    zoneCode: {
      handler(val) {
        let zoneLevel
        switch (+this.zoneLevel) {
          case 0:
            zoneLevel = '0'
            break
          case 2:
            zoneLevel = '2'
            break
          case 3:
            zoneLevel = '3'
            break
          default:
            zoneLevel = this.zoneLevel
            break
        }
        this.setZoneParams({
          zoneLevel,
          zoneCode: val,
          zoneName: this.rightName
        })
      },
      immediate: true
    }
  },
  methods: {
    ...mapMutations('capitalFlow', ['setZoneParams', 'setDateValue']),
    dateChange(date) {
      this.dateValue = date
      this.setDateValue(date)
    }
  },
  activated() {},
  mounted() {},
  created() {}
}
</script>
<style lang='less' scoped>
.capital_pege {
  color: #333;
}
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding-left: 0.2rem;
  color: #999;
}

.placeholder {
  height: 0.5rem;
}
</style>
