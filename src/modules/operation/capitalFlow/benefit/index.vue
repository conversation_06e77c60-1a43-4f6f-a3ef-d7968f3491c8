<template>
    <div>
        <CardList :title="cardTitle">
            <div slot='nav'>
                <div class="grey999 fs28">单位: 万</div>
            </div>
            <div class="pd_lr20 mt24">
                <DataList :options="benefitList[0]"></DataList>
                <DataList :options="benefitList[1]"></DataList>
                <!-- echart -->
                <!-- <div class="benefit_chart mt64" style="height:3rem"> -->
                <div v-if="!+zoneLevel" class="mt64" ref="chart-bar-benefit" :style={height:benefitHeight}></div>
                <!-- </div> -->

            </div>
        </CardList>
    </div>
</template>
<script>

import DataList from '../../components/dataList'
import request from '../request'
import { drawMoreBarChart } from 'common/charts/chartOption'
import { BENEFITLIST } from '../config'
export default {
  mixins: [request],
  components: {

    DataList
  },
  data() {
    return {
      cardTitle: '融通效益(-月)',
      benefitList: JSON.parse(JSON.stringify(BENEFITLIST)),
      benefitHeight: '6rem'
    }
  },
  watch: {
    dateValue(val) {
      this.initOptions()
    },
    zoneCode(val) {
      this.initOptions()
    }
  },
  computed: {
    incMonth() {
      return this.$moment(this.dataValue).format('YYYYMM')
    }
  },
  methods: {
    // 总览
    async getBenefitWebData() {
      const ajaxList = [[], [], []]
      for (let index = 0; index < 3; index++) {
        ajaxList[index] = this._getBenefitData({
          incMonth: this.isDev ? this.$moment('202011').subtract(index, 'M').format('YYYYMM') : this.$moment(this.dateValue).subtract(index, 'M').format('YYYYMM'),
          zoneLevel: this.zoneLevel,
          isSfsx: 'SX',
          ...this.levelKyHqCode[+this.zoneLevel]
        })
      }
      const [{ obj: obj1 }, { obj: obj2 }, { obj: obj3 }] = await Promise.all(ajaxList)
      if (obj1.length) {
        this.setBenefitWebData(obj1)
      } else if (obj2.length) {
        this.setBenefitWebData(obj2)
      } else {
        this.setBenefitWebData(obj3)
      }
    },
    setBenefitWebData(obj) {
      const result = JSON.parse(JSON.stringify(obj))
      this.benefitList = JSON.parse(JSON.stringify(BENEFITLIST))
      this.trendMon = ''
      if (result.length) {
        this.$checkNumber(result)
        // 获取最新月份
        this.trendMon = result[0].inc_month
        const mon = this.$moment(this.trendMon).format('M')
        this.cardTitle = `融通效益(${mon}月)`
        const keyList = [
          ['total_benefit', 'line_benefit', 'dept_benefit'],
          ['total_benefit_rate_mon', 'line_benefit_rate_mon', 'dept_benefit_rate_mon']
        ]
        this.benefitList[0].map((item, index) => {
          item.value = result[0][keyList[0][index]]
        })
        this.benefitList[1].map((item, index) => {
          item.value = result[0][keyList[1][index]]
        })
      }
      if (!+this.zoneLevel) {
        this.getBenefitTrend()
      }
    },
    // 下钻数据
    async getBenefitTrend() {
      let result = []
      if (this.trendMon) {
        const { obj } = await this._getBenefitData({
          incMonth: this.isDev ? '202009' : this.trendMon,
          zoneLevel: '2',
          isSfsx: 'SX'
        })
        result = obj
      }
      this.initChartBarBenefit(result)
    },
    initChartBarBenefit(obj) {
      const result = JSON.parse(JSON.stringify(obj))
      const xData = []
      const sData = [[], []]
      const sumData = []
      const legendList = ['线路效益', '场地效益']
      const option = {
        tooltip: {
          show: false
        },
        legend: {
          show: true,
          bottom: '4',
          itemWidth: 10,
          itemHeight: 10,
          icon: 'rect',
          textStyle: {
            fontSize: '10'
          },
          itemGap: 40,
          data: legendList
        },
        grid: {
          top: '0',
          left: '0',
          right: '15',
          bottom: '30',
          containLabel: true
        },
        xAxis: [
          {
            type: 'value',
            max: () => {
              return sumData[sumData.length - 1]
            },
            splitLine: {
              lineStyle: {
                color: '#ddd',
                type: 'dashed'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'category',
            data: xData,
            splitLine: {
              show: false
            },
            axisLabel: {
              formatter: (value) => {
                if (/分拨区/.test(value)) {
                  return value.replace('分拨区', '')
                }
                return value
              }
            }
          },
          {
            data: sumData,
            axisLabel: {
              textStyle: {
                color: '#333333'
              },
              formatter: (value) => {
                return this.$numToInteger(value, 0, 1, 0)
              }
            }
          }
        ],
        series: []
      }
      if (result.length) {
        this.$checkNumber(result)
        this.$objectSortUp(result, 'total_benefit')
        result.map((item, index) => {
          xData.push(item.ky_hq_name)
          sumData.push(item.total_benefit)
          sData[0].push(item.line_benefit)
          sData[1].push(item.dept_benefit)
        })
        const colorList = ['#2E55EC', '#FF6B17', '#09BCA0']
        sData.forEach((item, index) => {
          return option.series.push({
            data: item,
            stack: '占比',
            barWidth: 16,
            barMinHeight: 25,
            showBackground: false,
            name: legendList[index],
            label: {
              show: true,
              position: 'inside',
              fontSize: '10',
              color: '#fff',
              formatter: params => {
                return this.$numToInteger(params.value, 0)
              }
            },
            itemStyle: {
              barBorderRadius: 0,
              color: colorList[index]
            }
          })
        })
      }
      // 计算高度
      this.benefitHeight = (6 / 8) * sData[0].length + 'rem'

      drawMoreBarChart(option, this.$refs['chart-bar-benefit'])
    },
    initOptions() {
      const mon = this.$moment(this.dateValue).format('M')
      this.cardTitle = `融通效益(${mon}月)`
      this.getBenefitWebData()
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang='less' scoped>
.benefit_chart {
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
}
</style>
