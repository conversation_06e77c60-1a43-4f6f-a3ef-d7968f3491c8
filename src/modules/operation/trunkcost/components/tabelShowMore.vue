<template>
    <div class="mt32 pd_lr20">
        <div class="sider_page flex_center" @click="slideBtnChange">
            <div class="mr10 grey333 fs24 fw700">{{isSlide?textList[1]:textList[0]}}</div>
            <i class="iconfont icon-more fs24" :class="{'arrow_up': isSlide}"></i>
        </div>
    </div>
</template>

<script>
export default {
  props: {
    textList: {
      type: Array,
      default: () => ['展开数据', '收起数据']
    }
  },
  data() {
    return {
      isSlide: false
    }
  },
  methods: {
    slideBtnChange() {
      this.isSlide = !this.isSlide
      this.$emit('slideBtnChange', this.isSlide)
    }
  }
}
</script>

<style lang="less" scoped>
.sider_page {
  width: 100%;
  height: 0.65rem;
  background-color: #f8f9fc;
  div {
    font-family: PingFangSC-Regular;
  }
  .arrow_up {
    transform: rotate(-180deg);
    -webkit-transform: rotate(-180deg); /* Safari 和 Chrome */
  }
}
</style>
