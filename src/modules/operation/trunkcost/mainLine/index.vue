<template>
  <div>
    <CardList title="干线成本">
      <div slot="nav">
        <Tabs :options="['日', '月']" :tabIndex="tabIndex" @tabSelect="tabSelect"></Tabs>
      </div>
      <div class="pd_lr20 mt32">
        <div class="show-card flex_around" v-if="!tabIndex">
          <div class="card-item tc" v-for="(item, index) in costForDaysData" :key="index">
            <h4 class="grey666" style="line-height: 0.33rem">{{ item.main_cost_desc }}</h4>
            <span class="grey3D3D3D ff_rbt fs40" style="line-height: 0.62rem">{{
              $numToInteger(item.main_cost / 10000, 0)
            }}</span>
          </div>
        </div>
        <div class="show-card flex_around" v-if="tabIndex">
          <div class="card-item tc" v-for="(item, index) in costForMonsData" :key="index">
            <h4 class="grey666" style="line-height: 0.33rem">{{ item.main_cost_desc }}</h4>
            <span class="grey3D3D3D ff_rbt fs40" style="line-height: 0.62rem">{{
              $numToInteger(item.main_cost / 10000, 0)
            }}</span>
            <div style="line-height: 0.38rem" class="mt20">
              <span class="grey999">月环比</span>
              <span class="grey3D3D3D ff_rbt">{{ $numToPercent(item.mom_main_cost) }}</span>
              <i
                :class="[
                  'ml8',
                  'iconfont',
                  'fs24',
                  item.mom_main_cost > 0 ? 'icon-up' : 'icon-down',
                  item.mom_main_cost > 0 ? 'orange' : 'green'
                ]"
                v-if="item.mom_main_cost"
              ></i>
            </div>
            <div style="line-height: 0.38rem" class="mt20">
              <span class="grey999">月同比</span>
              <span class="grey3D3D3D ff_rbt">{{ $numToPercent(item.yoy_main_cost) }}</span>
              <i
                :class="[
                  'ml8',
                  'iconfont',
                  'fs24',
                  item.mom_main_cost > 0 ? 'icon-up' : 'icon-down',
                  item.mom_main_cost > 0 ? 'orange' : 'green'
                ]"
                v-if="item.yoy_main_cost"
              ></i>
            </div>
          </div>
        </div>
        <!-- 趋势 -->
        <div class="mt64">
          <p class="fs28 grey333 fw700">{{ tabIndex ? '月度趋势' : '近8天趋势' }}</p>
          <KydChartModel class="mt24" isScroll position="right" :legendOption="legendOption" :tableOption="tableOption">
            <div class="mt32" ref="chart-model-line" style="height: 3rem"></div>
          </KydChartModel>
        </div>
      </div>
    </CardList>
  </div>
</template>

<script>
import { drawLineChart } from 'common/charts/chartOption'
import request from '../request'

export default {
  mixins: [request],
  props: {
    trunkCostDaysData: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      tabIndex: 0,
      costForDaysData: [],
      costForMonsData: [],
      dataSource: [],
      tableOption: {}
    }
  },
  computed: {
    legendOption() {
      return {
        type: 'column',
        options: [
          {
            label: '干线成本',
            int: [0, 10000]
          },
          {
            label: '一级干线成本',
            int: [0, 10000]
          },
          {
            label: '二级干线成本',
            int: [0, 10000]
          }
        ],
        dataSource: this.dataSource
      }
    }
  },
  watch: {
    selDateValue() {
      if (this.tabIndex) {
        this.getCurrDaMonsData()
      }
    },
    currZoneCode() {
      if (this.tabIndex) {
        this.getCurrDaMonsData()
      }
    },
    trunkCostDaysData: {
      handler(val) {
        this.$nextTick(() => {
          this.initLastEightDaysTrend(val)
        })
      },
      immediate: true
    }
  },

  created() {},
  mounted() {},
  methods: {
    tabSelect(index) {
      if (this.tabIndex === index) return
      this.tabIndex = index
      if (index) {
        this.getCurrDaMonsData()
      } else {
        this.initLastEightDaysTrend(this.trunkCostDaysData)
      }
      // 神策点击
      this.$sensors.webClick(`干线成本-${['日', '月'][index]}`)
    },
    // 近八天趋势图
    initLastEightDaysTrend(obj) {
      this.costForDaysData = []
      const sData = [[], [], []]
      const xData = []
      if (obj.length) {
        obj.forEach(item => {
          sData[0].push(item.day_main_cost)
          sData[1].push(item.day_fir_main_cost)
          sData[2].push(item.day_sec_main_cost)
          xData.push(item.inc_day)
        })
        const tranList = [
          {
            main_cost_desc: '干线成本(万)',
            main_cost: obj[obj.length - 1].day_main_cost
          },
          {
            main_cost_desc: '一级干线成本(万)',
            main_cost: obj[obj.length - 1].day_fir_main_cost
          },
          {
            main_cost_desc: '二级干线成本(万)',
            main_cost: obj[obj.length - 1].day_sec_main_cost
          }
        ]
        tranList.forEach(item => {
          this.costForDaysData.push(item)
        })
      } else {
        this.costForDaysData = [
          {
            main_cost_desc: '干线成本(万)',
            main_cost: '-'
          },
          {
            main_cost_desc: '一级干线成本(万)',
            main_cost: '-'
          },
          {
            main_cost_desc: '二级干线成本(万)',
            main_cost: '-'
          }
        ]
      }
      this.dataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.dataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => `${+this.$moment(value).format('MM')}.${this.$moment(value).format('DD')}`
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$numToInteger(value / 10000, 0, 1, 0)
            }
          }
        ],
        series: [
          {
            symbolSize: 2,
            showSymbol: false,
            data: sData[0]
          },
          {
            symbolSize: 2,
            showSymbol: false,
            data: sData[1]
          },
          {
            symbolSize: 2,
            showSymbol: false,
            data: sData[2]
          }
        ]
      }
      drawLineChart(option, this.$refs['chart-model-line'])
      this.tableOption = {
        options: option
      }
    },
    // 月份趋势图
    initMonthTrend(obj, res) {
      this.costForMonsData = []
      const sData = [[], [], []]
      const xData = []
      if (obj.length) {
        obj.forEach(item => {
          sData[0].push(item.mtd_main_cost)
          sData[1].push(item.mtd_fir_main_cost)
          sData[2].push(item.mtd_sec_main_cost)
          xData.push(item.inc_mon)
        })
        const tranList = [
          {
            main_cost_desc: '干线成本(万)',
            main_cost: obj[obj.length - 1].mtd_main_cost,
            mom_main_cost: res[0].mom_main_cost,
            yoy_main_cost: res[0].yoy_main_cost
          },
          {
            main_cost_desc: '一线成本(万)',
            main_cost: obj[obj.length - 1].mtd_fir_main_cost,
            mom_main_cost: res[0].mom_fir_main_cost,
            yoy_main_cost: res[0].yoy_fir_main_cost
          },
          {
            main_cost_desc: '二线成本(万)',
            main_cost: obj[obj.length - 1].mtd_sec_main_cost,
            mom_main_cost: res[0].mom_sec_main_cost,
            yoy_main_cost: res[0].yoy_sec_main_cost
          }
        ]
        tranList.forEach(item => {
          this.costForMonsData.push(item)
        })
      } else {
        this.costForMonsData = [
          {
            main_cost_desc: '干线成本(万)',
            main_cost: '-',
            mom_main_cost: '-',
            yoy_main_cost: '-'
          },
          {
            main_cost_desc: '一线成本(万)',
            main_cost: '-',
            mom_main_cost: '-',
            yoy_main_cost: '-'
          },
          {
            main_cost_desc: '二线成本(万)',
            main_cost: '-',
            mom_main_cost: '-',
            yoy_main_cost: '-'
          }
        ]
      }

      this.trendMonWidth = xData.length <= 8 ? '100%' : (100 / 8) * xData.length + '%'
      const option = {
        tooltip: {
          formatter: params => {
            this.dataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => `${+this.$moment(value).format('MM')}月`
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$numToInteger(value / 10000, 0, 1, 0)
            }
          }
        ],
        series: [
          {
            symbolSize: 2,
            showSymbol: false,
            data: sData[0]
          },
          {
            symbolSize: 2,
            showSymbol: false,
            data: sData[1]
          },
          {
            symbolSize: 2,
            showSymbol: false,
            data: sData[2]
          }
        ]
      }

      drawLineChart(option, this.$refs['chart-model-line'])
      this.tableOption = {
        options: option
      }
    },

    async getCurrDaMonsData() {
      const mapCode = {
        31: { bigAreaCode: this.currZoneCode },
        32: { provinceAreaCode: this.currZoneCode },
        33: { areaCode: this.currZoneCode }
      }
      const { obj } = await this._getTrunkCostDaysData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: this.currZoneLevel,
        ...mapCode[+this.currZoneLevel],
        ...this.levelMap[+this.currZoneLevel]
      })
      const res = obj
      this.getMonthsData(res)
    },
    async getMonthsData(res) {
      const mapCode = {
        31: { bigAreaCode: this.currZoneCode },
        32: { provinceAreaCode: this.currZoneCode },
        33: { areaCode: this.currZoneCode }
      }
      const { obj } = await this._getTrunkCostMonthsData({
        sMon: this.isDev ? '202006' : this.$moment(this.selDateValue).subtract(11, 'months').format('YYYYMM'),
        eMon: this.isDev ? '202101' : this.$moment(this.selDateValue).format('YYYYMM'),
        levelCode: this.currZoneLevel,
        ...mapCode[+this.currZoneLevel],
        ...this.levelMap[+this.currZoneLevel]
      })
      this.$checkNumber(obj)
      this.initMonthTrend(obj, res)
    }
  }
}
</script>

<style lang="less" scoped>
.show-card {
  // height: 2.5rem;
  background-color: #f8f9fc;
  border-radius: 0.04rem;
  padding: 0.26rem 0;
}
</style>
