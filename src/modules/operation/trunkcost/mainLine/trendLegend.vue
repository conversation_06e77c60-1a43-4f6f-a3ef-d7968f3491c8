<template>
    <div>
        <ul class="flex_around">
            <li v-for="(item, index) in option" :key="index">
                <div class="flex_start">
                    <!-- <i class="icon-ico_legend iconfont fs24" :style="{color:item.color}"></i> -->
                    <i class="icon-yuan" :style="{'background-color':item.color}"></i>
                    <span class="grey666 ml8" style="line-height:0.34rem;">{{ item.label }}</span>
                </div>
                <p class="ff_rbt grey3D3D3D fw700" style="line-height:0.38rem;padding-left:0.2rem;">{{ item.value }}</p>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
  props: {
    option: {
      type: [Array],
      default: () => []
    }
  }
}
</script>

<style lang="less" scoped>
.icon-yuan{
    height: 0.12rem;
    width: 0.12rem;
    border-radius: 50%
}
</style>
