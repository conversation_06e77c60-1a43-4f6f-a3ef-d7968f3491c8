<template>
  <div class="mt20" style="padding-bottom: 0.4rem; background-color: #fff">
    <CardList title="吨公里成本">
      <div class="mt24">
        <div class="pd_lr20">
          <p class="fs28 grey333 fw700">成本</p>
          <div class="card-ty-per">
            <NormalTable
              class="table-wrapper"
              :dataSource="tableDataSource"
              :columns="tableColumn"
              :border="false"
              minHeight="1rem"
            ></NormalTable>
          </div>
        </div>
        <div class="mt64 pd_lr20">
          <p class="fs28 grey333 fw700">吨公里成本近八天趋势</p>

          <KydChartModel class="mt24" isScroll position="right" :legendOption="legendOption" :tableOption="tableOption">
            <div class="mt32" ref="chart-line-trend" style="height: 3rem"></div>
          </KydChartModel>
        </div>
        <div class="mt64" v-if="+currZoneLevel !== 33">
          <p class="fs28 grey333 fw700 pd_lr20">各{{ +currZoneLevel === 32 ? '省区' : '战区' }}吨公里成本</p>
          <NormalTable
            :dataSource="isSliderPro ? provinceDataAll : provinceDataTen"
            :columns="provinceColumn"
            maxHeight="25rem;"
            class="cumula-table mt24"
          ></NormalTable>
          <TableShowMore @slideBtnChange="slideBtnChangePro" v-if="provinceDataAll.length > 10"></TableShowMore>
        </div>
        <div class="mt64" v-if="+currZoneLevel !== 30">
          <p class="fs28 grey333 fw700 pd_lr20">各{{ +currZoneLevel === 31 ? '省区' : '区域' }}吨公里成本</p>
          <NormalTable
            :dataSource="isSliderArea ? areaDataAll : areaDataTen"
            :columns="areaColumn"
            maxHeight="25rem;"
            class="cumula-table mt24"
          ></NormalTable>
          <TableShowMore @slideBtnChange="slideBtnChangeArea" v-if="areaDataAll.length > 10"></TableShowMore>
        </div>
      </div>
    </CardList>
  </div>
</template>

<script>
import request from '../request'
import { drawOneLineChart } from 'common/charts/chartOption'
import TableShowMore from '../components/tabelShowMore'

export default {
  mixins: [request],
  props: {
    trunkCostDaysData: {
      type: Array,
      default: () => []
    },
    proData: {
      type: Array,
      default: () => []
    },
    areaData: {
      type: Array,
      default: () => []
    },
    areaLoginData: {
      type: Array,
      default: () => []
    },
    bigAreaProvData: {
      type: Array,
      default: () => []
    }
  },
  components: {
    TableShowMore
  },
  data() {
    return {
      isSliderPro: false,
      isSliderArea: false,
      tableDataSource: [],
      tableColumn: [],
      provinceDataAll: [],
      provinceDataTen: [],
      provinceColumn: [],
      areaDataAll: [],
      areaDataTen: [],
      areaColumn: [],

      dataSource: [],
      tableOption: {}
    }
  },
  computed: {
    legendOption() {
      return {
        options: [
          {
            label: '吨公里成本',
            int: [2]
          }
        ],
        dataSource: this.dataSource
      }
    }
  },
  watch: {
    trunkCostDaysData: {
      handler(val) {
        this.setTable(val)
        this.$nextTick(() => {
          this.initLineCharts(val)
        })
      },
      immediate: true
    },
    proData: {
      handler(val) {
        this.setProCalCostTable(val)
      },
      immediate: true
    },
    areaData: {
      handler(val) {
        if (+this.currZoneLevel === 32) {
          this.setAreaCalCostTable(val)
        }
      },
      immediate: true
    },
    areaLoginData: {
      handler(val) {
        if (+this.currZoneLevel === 33) {
          this.setAreaCalCostTable(val)
        }
      },
      immediate: true
    },
    bigAreaProvData: {
      handler(val) {
        if (+this.currZoneLevel === 31) {
          this.setAreaCalCostTable(val)
        }
      },
      immediate: true
    },
    currZoneCode() {
      if (+this.currZoneLevel < 32) {
        this.getBitAreaTableData()
      }
    }
  },

  methods: {
    slideBtnChangePro(isSlider) {
      this.isSliderPro = isSlider
    },
    slideBtnChangeArea(isSlider) {
      this.isSliderArea = isSlider
    },
    // 吨公里成本
    setTable(obj) {
      this.$checkNumber(obj)
      this.tableDataSource = []
      this.tableColumn = [
        {
          label: '成本类型',
          dataIndex: 'value0'
        },
        {
          label: '当日成本',
          dataIndex: 'value1',
          render: (h, value) => <div class=' grey666'>{this.$numToInteger(value, 0)}</div>
        },
        {
          label: '本月成本',
          dataIndex: 'value2',
          render: (h, value) => <div class=' grey666'>{this.$numToInteger(value, 0)}</div>
        },
        {
          label: '月环比',
          dataIndex: 'value3',
          render: (h, value) => {
            if (value > 0) {
              return (
                <div>
                  <span class='grey666'>{this.$numToPercent(value, 1)}</span>
                  <i class='iconfont icon-up orange ml8'></i>
                </div>
              )
            } else if (value < 0) {
              return (
                <div>
                  <span class='grey666'>{this.$numToPercent(value, 1)}</span>
                  <i class='iconfont icon-down green ml8'></i>
                </div>
              )
            } else {
              return <span class='grey666'>{value}</span>
            }
          }
        }
      ]
      if (obj.length) {
        this.tableDataSource = [
          {
            value0: '吨公里成本(元)',
            value1: obj[obj.length - 1].day_cal_dis_cost,
            value2: obj[obj.length - 1].mtd_cal_dis_cost,
            value3: obj[obj.length - 1].mom_cal_dis_cost
          }
        ]
      }
    },
    // 总部 && 战区
    async getBitAreaTableData() {
      const { obj } = await this._getTrunkCostDaysData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: '31'
      })
      this.setProCalCostTable(obj)
    },
    initLineCharts(obj) {
      this.$checkNumber(obj)
      const xData = []
      const sData = []
      if (obj.length) {
        obj.map(item => {
          xData.push(item.inc_day)
          sData.push(item.day_cal_dis_cost)
        })
      }
      const option = {
        tooltip: {
          formatter: params => {
            this.dataSource = params
          }
        },

        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$moment(value).format('MM.DD')
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$numToInteger(value, 0)
            }
          }
        ],
        series: [
          {
            data: sData
          }
        ]
      }
      drawOneLineChart(option, this.$refs['chart-line-trend'], true, 1)
      this.tableOption = {
        options: option
      }
    },
    setProCalCostTable(obj) {
      this.$checkNumber(obj)
      this.provinceDataAll = []
      this.provinceDataTen = []
      // 月环比--吨公里成本
      const momVolRateSortDown = this.$objectSortDown(JSON.parse(JSON.stringify(obj)), 'mom_cal_dis_cost')
      if (obj.length) {
        this.provinceDataAll = momVolRateSortDown
        this.provinceDataTen = momVolRateSortDown.slice(0, 10)
      }
      this.provinceColumn = [
        {
          label: '排名',
          //   align: 'center',
          render: (h, value) => {
            const val = this.provinceDataAll.length - value
            const isShowRed = val >= this.provinceDataAll.length - 2
            return (
              <div class='rank-typeindex flex_start'>
                <span v-show={isShowRed} class='flex_center fw700 red'>
                  {val}
                </span>
                <span v-show={!isShowRed} class='flex_center rank-index-color fw700'>
                  {val}
                </span>
              </div>
            )
          }
        },
        {
          label: +this.currZoneLevel < 32 ? '战区' : '省区',
          dataIndex: +this.currZoneLevel < 32 ? 'big_area_name' : 'province_area_name',
          render: (h, value) => {
            return value.replace(/SX|省区|战区|分拨区|区域/g, '')
          }
        },
        {
          label: '当日吨公里成本',
          dataIndex: 'day_cal_dis_cost',
          render: (h, value) => this.$numToInteger(value, 1)
        },
        {
          label: '月吨公里成本',
          dataIndex: 'mtd_cal_dis_cost',
          render: (h, value) => this.$numToInteger(value, 1)
        },
        {
          label: '上月成本',
          dataIndex: 'ma_cal_dis_cost',
          render: (h, value) => this.$numToInteger(value, 1)
        },
        {
          label: '月环比',
          dataIndex: 'mom_cal_dis_cost',
          render: (h, value) => {
            if (!value) return
            return value > 0 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
          }
        }
      ]
    },
    setAreaCalCostTable(obj) {
      this.$checkNumber(obj)
      this.areaDataAll = []
      this.areaDataTen = []
      const momVolRateSortDown = this.$objectSortDown(JSON.parse(JSON.stringify(obj)), 'mom_cal_dis_cost')
      if (obj.length) {
        this.areaDataAll = momVolRateSortDown
        this.areaDataTen = momVolRateSortDown.slice(0, 10)
      }
      this.areaColumn = [
        {
          label: '排名',
          align: 'center',
          render: (h, value) => {
            const val = this.areaDataAll.length - value
            const isShowRed = val >= this.areaDataAll.length - 2
            return (
              <div class='rank-typeindex flex_center'>
                <span v-show={isShowRed} class='flex_center fw700 red'>
                  {val}
                </span>
                <span v-show={!isShowRed} class='flex_center rank-index-color fw700'>
                  {val}
                </span>
              </div>
            )
          }
        },
        {
          label: +this.currZoneLevel === 31 ? '省区' : '区域',
          dataIndex: +this.currZoneLevel === 31 ? 'province_area_name' : 'area_name',
          render: (h, value) => value.replace(/SX|区域|省区|战区/g, '')
        },
        {
          label: '当日吨公里成本',
          dataIndex: 'day_cal_dis_cost',
          render: (h, value) => this.$numToInteger(value, 1)
        },
        {
          label: '月吨公里成本',
          dataIndex: 'mtd_cal_dis_cost',
          render: (h, value) => this.$numToInteger(value, 1)
        },
        {
          label: '上月成本',
          dataIndex: 'ma_cal_dis_cost',
          render: (h, value) => this.$numToInteger(value, 1)
        },
        {
          label: '月环比',
          dataIndex: 'mom_cal_dis_cost',
          render: (h, value) => {
            if (!value) return
            return value > 0 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
          }
        }
      ]
      //   if (obj.length < 2) {
      //     this.areaColumn.splice(0, 1, {})
      //   }
    }
  },
  mounted() {
    if (+this.currZoneLevel < 32) {
      this.getBitAreaTableData()
    }
  }
}
</script>

<style lang="less" scoped>
.card-ty-per {
  padding-top: 0.25rem;
  .table-wrapper {
    border: 1px solid #f2f2f2;
    border-radius: 0.04rem;
  }
  /deep/.table-outer-wrap .table-wrap.medium .sheader tr th {
    padding: 0.2rem;
    color: #333;
    font-weight: 700;
  }
  /deep/.table-outer-wrap .table-wrap.medium .sbody tr td {
    padding: 0.2rem;
  }
}
.cumula-table {
  /deep/.table-outer-wrap .table-wrap.medium .sheader tr th {
    padding: 0.28rem 0.1rem;
    color: #333;
    font-weight: 700;
    position: sticky;
    top: 0px;
  }
  /deep/.table-outer-wrap .table-wrap.medium .sbody tr td {
    padding: 0.28rem 0.1rem;
    color: #666;
  }
}

.icon-yuan {
  width: 0.12rem;
  height: 0.12rem;
  background: #ff6a17;
  border-radius: 50%;
}
</style>
