<template>
  <div class="trunkcost page_bgc">
    <!-- 加载时动画图片 -->
    <!-- 日期底部弹框选择 -->
    <div class="date-container">
      <KyDatePicker type="day" :dateValue="dateValue" @onChange="dateChange" :holidayData="holidayData"></KyDatePicker>
    </div>
    <PageContent ref="contentWrapper">
      <mainLine :trunkCostDaysData="trunkCostDaysData" />
      <carType :trunkCostDaysData="trunkCostDaysData" />
      <!-- 暂时隐藏装载率 -->
      <!-- <voluRate :trunkCostDaysData="trunkCostDaysData" :proData="proData" :areaData="areaData"
             :areaLoginData="areaLoginData" :bigAreaProvData="bigAreaProvData" /> -->
      <colCost
        :trunkCostDaysData="trunkCostDaysData"
        :proData="proData"
        :areaData="areaData"
        :areaLoginData="areaLoginData"
        :bigAreaProvData="bigAreaProvData"
      />
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>

<script>
import mainLine from './mainLine'
import carType from './carType'
// import voluRate from './voluRate'
import colCost from './colCost'
import { mapState, mapMutations } from 'vuex'
import request from './request'
import flowLayout from 'common/mixins/flowLayout.js'

export default {
  mixins: [request, flowLayout],
  props: {
    zoneCode: {
      type: [String]
    },
    zoneLevel: {
      type: [String, Number]
    },
    rightName: {
      type: String
    }
  },
  components: {
    mainLine,
    carType,
    // voluRate,
    colCost
  },
  computed: {

    ...mapState({
      holidayData: 'holidayData'
    })
  },
  watch: {
    // 层级zoneLeve转换
    zoneCode: {
      // 31--大区
      // 37--中转场
      handler(val) {
        let currZoneLevel
        switch (+this.zoneLevel) {
          case 0:
            currZoneLevel = '30'
            break
          case 1:
            currZoneLevel = '31'
            break
          case 2:
            currZoneLevel = '32'
            break
          case 3:
            currZoneLevel = '33'
            break
          default:
            currZoneLevel = this.zoneLevel
            break
        }
        this.setZoneParams({
          currZoneLevel,
          currZoneCode: val,
          currZoneName: this.rightName
        })
      },
      immediate: true
    },
    currZoneCode() {
      this.init()
    },
    selDateValue() {
      this.init()
    }
  },
  data() {
    return {
      dateValue: this.$moment().subtract(1, 'days').format('YYYY-MM-DD'),
      trunkCostDaysData: [],
      proData: [],
      areaData: [],
      areaLoginData: [],
      bigAreaProvData: []
    }
  },
  mounted() {
    this.init()
  },
  activated() {},
  methods: {
    init() {
      this.getLastEightDaysData()
      if (+this.currZoneLevel === 32) {
        this.getProData()
      }
      if (+this.currZoneLevel === 32 || +this.currZoneLevel === 33) {
        this.getAreaData()
      }
      if (+this.currZoneLevel === 33) {
        this.getAreaDataForArea()
      }
      if (+this.currZoneLevel === 31) {
        this.getBigAreaData()
      }
    },
    dateChange(date) {
      this.dateValue = date
      this.setDateValue(date)
    },
    ...mapMutations('trunkCost', ['setZoneParams', 'setDateValue']),
    async getLastEightDaysData() {
      const levelMap = {
        30: {}, // 总部
        32: { provinceAreaCode: this.currZoneCode }, // 省区
        33: { areaCode: this.currZoneCode }, // 区域,
        37: { areaCode: this.currZoneCode }, // 中转场,
        31: { bigAreaCode: this.currZoneCode } // 大区,
      }
      const { obj } = await this._getTrunkCostDaysData({
        sDay: this.isDev ? '20210123' : this.$moment(this.selDateValue).subtract(7, 'days').format('YYYYMMDD'),
        eDay: this.isDev ? '20210130' : this.selDateValue.replace(/-/g, ''),
        levelCode: this.currZoneLevel,
        ...levelMap[+this.currZoneLevel]
      })
      this.trunkCostDaysData = obj
    },
    // 战区登录展示省区数据--装载率&吨公里成本
    async getBigAreaData() {
      const { obj } = await this._getTrunkCostDaysData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: '32',
        bigAreaCode: this.currZoneCode
      })
      this.bigAreaProvData = obj
    },
    // 省区登录展示省区数据--装载率&吨公里成本
    async getProData() {
      const { obj } = await this._getTrunkCostDaysData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: '32'
      })
      this.proData = obj
    },
    // 省区登录展示区域数据--装载率&吨公里成本
    async getAreaData() {
      const { obj } = await this._getTrunkCostDaysData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: '33',
        provinceAreaCode: this.currZoneCode
      })
      this.areaData = obj
    },
    // 区域登录展示区域数据--装载率&吨公里成本
    async getAreaDataForArea() {
      const { obj } = await this._getAreaLoginData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: '33',
        areaCode: this.currZoneCode
      })
      this.areaLoginData = obj
    }
  }
}
</script>

<style lang="less" scoped>
.date-container {
  width: 100vw;
  height: 0.8rem;
  padding-left: 0.2rem;
  color: #999;
  // background-color: #f8f9fc;
}
</style>
