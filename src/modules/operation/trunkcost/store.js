/*
 * @Author: shigl
 * @Date: 2022-07-20 18:28:55
 * @LastEditTime: 2022-07-20 18:45:23
 * @Description:
 */
import moment from 'moment'
export default {
  namespaced: true,
  name: 'trunkCost',
  state: {
    selDateValue: moment().add(-1, 'd').format('YYYY-MM-DD'),
    zoneParams: {
      currZoneLevel: '30',
      currZoneCode: '001',
      currZoneName: '顺心捷达'
    },
    scrolltop: 0
  },
  mutations: {
    setDateValue(state, date) {
      state.selDateValue = date
    },
    setZoneParams(state, data) {
      state.zoneParams = data
    },
    setScrollTop(state, data) {
      state.scrolltop = data
    }
  }
}
