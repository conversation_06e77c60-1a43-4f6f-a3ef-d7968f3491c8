<template>
    <div class="mt20" style="padding-bottom:0.4rem;background-color:#fff;">
        <CardList title="装载率">
            <div class="mt32">
                <div class="pd_lr20">
                    <div class="show-card flex_around">
                        <div class="card-item" v-for="(item,index) in currDayVolData" :key="index">
                            <h4 class="grey999" style="line-height:0.33rem;">{{ item.vol_rate_desc }}</h4>
                            <span class="grey3D3D3D ff_rbt fs40" style="line-height:0.62rem;">{{ $numToPercent(item.vol_rate, 1) }}</span>
                            <i :class="['ml8', 'iconfont', item.vol_rate > 0 ? 'icon-up':'icon-down' , item.vol_rate < 0 ? 'orange':'green']" v-if="index === 3 && item.vol_rate"></i>
                        </div>
                    </div>
                </div>
                <div v-if="+currZoneLevel !== 33">
                    <p class="fs28 grey333 fw700 mt64 pd_lr20">各{{+currZoneLevel===32?'省区':'战区'}}装载率</p>
                    <NormalTable :dataSource="isSliderPro ? volRateDataAll: volRateDataTen" :columns="volRateColumn" maxHeight="25rem;" class="cumula-table mt24"></NormalTable>
                    <TableShowMore @slideBtnChange="slideBtnChangePro" v-if="volRateDataAll.length > 10"></TableShowMore>
                </div>
                <div v-if="+currZoneLevel !== 30">
                    <p class="fs28 grey333 fw700 mt64 pd_lr20">各{{ +currZoneLevel === 31  ?'省区': '区域' }}装载率</p>
                    <NormalTable :dataSource="isSliderArea ? areaVolRateDataAll: areaVolRateDataTen" :columns="areaColumn" maxHeight="25rem;" class="cumula-table mt24"></NormalTable>
                    <TableShowMore @slideBtnChange="slideBtnChangeArea" v-if="areaVolRateDataAll.length > 10"></TableShowMore>
                </div>
            </div>
        </CardList>
    </div>
</template>

<script>

import TableShowMore from '../components/tabelShowMore'
import request from '../request'

export default {
  mixins: [request],
  props: {
    trunkCostDaysData: {
      type: Array,
      default: () => []
    },
    proData: {
      type: Array,
      default: () => []
    },
    areaData: {
      type: Array,
      default: () => []
    },
    areaLoginData: {
      type: Array,
      default: () => []
    },
    bigAreaProvData: {
      type: Array,
      default: () => []
    }
  },
  components: {

    TableShowMore
  },
  data() {
    return {
      volRateColumn: [],
      volRateDataAll: [],
      volRateDataTen: [],
      currDayVolData: [],
      isSliderPro: false,
      isSliderArea: false,
      areaVolRateDataAll: [],
      areaVolRateDataTen: [],
      areaColumn: []
    }
  },
  watch: {
    currZoneCode() {
      if (+this.currZoneLevel === 30 || +this.currZoneLevel === 31) {
        this.getBigAreaData()
      }
    },
    trunkCostDaysData: {
      handler(val) {
        this.setCurrVolData(val)
      },
      immediate: true
    },
    proData: {
      handler(val) {
        if (+this.currZoneLevel === 32) {
          this.setProAreaTable(val)
        }
      },
      immediate: true
    },
    areaData: {
      handler(val) {
        if (+this.currZoneLevel === 32) {
          this.setAreaAreaTable(val)
        }
      },
      immediate: true
    },
    areaLoginData: {
      handler(val) {
        if (+this.currZoneLevel === 33) {
          this.setAreaAreaTable(val)
        }
      },
      immediate: true
    },
    bigAreaProvData: {
      handler(val) {
        if (+this.currZoneLevel === 31) {
          this.setAreaAreaTable(val)
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (+this.currZoneLevel === 30 || +this.currZoneLevel === 31) {
      this.getBigAreaData()
    }
  },
  methods: {
    slideBtnChangePro(isSlider) {
      this.isSliderPro = isSlider
    },
    slideBtnChangeArea(isSlider) {
      this.isSliderArea = isSlider
    },
    setCurrVolData(obj) {
      this.currDayVolData = []
      if (obj.length) {
        const tranList = [
          {
            vol_rate_desc: '当日装载率',
            vol_rate: obj[obj.length - 1].day_vol_rate
          },
          {
            vol_rate_desc: '本月装载率',
            vol_rate: obj[obj.length - 1].mtd_vol_rate
          },
          {
            vol_rate_desc: '上月装载率',
            vol_rate: obj[obj.length - 1].ma_vol_rate
          },
          {
            vol_rate_desc: '月环比',
            vol_rate: obj[obj.length - 1].mom_vol_rate
          }
        ]
        tranList.forEach(item => {
          this.currDayVolData.push(item)
        })
      }
    },
    setProAreaTable(obj) {
      this.$checkNumber(obj)
      this.volRateDataAll = []
      this.volRateDataTen = []
      // 月环比--装载率
      const momVolRateSortDown = this.$objectSortUp(JSON.parse(JSON.stringify(obj)), 'mom_vol_rate')
      if (obj.length) {
        this.volRateDataAll = momVolRateSortDown
        this.volRateDataTen = momVolRateSortDown.slice(0, 10)
      }
      this.volRateColumn = [
        {
          label: '排名',
          align: 'center',
          render: (h, value) => {
            const val = this.volRateDataAll.length - value
            const isShowRed = val >= this.volRateDataAll.length - 2
            return (
              <div class='rank-typeindex flex_center'>
                <span v-show={ isShowRed } class='flex_center fw700 red'>{ val }</span>
                <span v-show={ !isShowRed } class='flex_center rank-index-color fw700'>{ val }</span>
              </div>
            )
          }
        },
        {
          label: () => {
            if (+this.currZoneLevel === 30 || +this.currZoneLevel === 31) {
              return '战区'
            } else if (+this.currZoneLevel === 32) {
              return '省区'
            }
          },
          dataIndex: (+this.currZoneLevel === 30 || +this.currZoneLevel === 31) ? 'big_area_name' : 'province_area_name',
          render: (h, value) => {
            return (+this.currZoneLevel === 30 || +this.currZoneLevel === 32) ? value.replace(/SX|省区|快运|分拨区|战区/g, '') : value.replace(/SX|区域/g, '')
          }
        },
        {
          label: '当日装载率',
          dataIndex: 'day_vol_rate',
          render: (h, value) => this.$numToPercent(value, 1)
        },
        {
          label: '月累计',
          dataIndex: 'mtd_vol_rate',
          render: (h, value) => this.$numToPercent(value, 1)
        },
        {
          label: '上月累计',
          dataIndex: 'ma_vol_rate',
          render: (h, value) => this.$numToPercent(value, 1)
        },
        {
          label: '月环比',
          dataIndex: 'mom_vol_rate',
          render: (h, value) => {
            if (!value) return
            return value < 0 ? <div class='orange'>{this.$numToPercent(value, 1)}</div> : <div class='green'>{this.$numToPercent(value / 100, 1)}</div>
          }
        }
      ]
    },
    setAreaAreaTable(obj) {
      this.$checkNumber(obj)
      this.areaVolRateDataAll = []
      this.areaVolRateDataTen = []
      // 月环比--装载率
      const momVolRateSortDown = this.$objectSortUp(JSON.parse(JSON.stringify(obj)), 'mom_vol_rate')
      if (obj.length) {
        this.areaVolRateDataAll = momVolRateSortDown
        this.areaVolRateDataTen = momVolRateSortDown.slice(0, 10)
      }
      this.areaColumn = [
        {
          label: '排名',
          align: 'center',
          render: (h, value) => {
            const val = this.areaVolRateDataAll.length - value
            const isShowRed = val >= this.areaVolRateDataAll.length - 2
            return (
              <div class='rank-typeindex flex_center'>
                <span v-show={ isShowRed } class='flex_center fw700 red'>{ val }</span>
                <span v-show={ !isShowRed } class='flex_center rank-index-color fw700'>{ val }</span>
              </div>
            )
          }
        },
        {
          label: +this.currZoneLevel === 31 ? '省区' : '区域',
          dataIndex: +this.currZoneLevel === 31 ? 'province_area_name' : 'area_name',
          render: (h, value) => value.replace(/SX|区域|省区/g, '')
        },
        {
          label: '当日装载率',
          dataIndex: 'day_vol_rate',
          render: (h, value) => this.$numToPercent(value, 1)
        },
        {
          label: '月累计',
          dataIndex: 'mtd_vol_rate',
          render: (h, value) => this.$numToPercent(value, 1)
        },
        {
          label: '上月累计',
          dataIndex: 'ma_vol_rate',
          render: (h, value) => this.$numToPercent(value, 1)
        },
        {
          label: '月环比',
          dataIndex: 'mom_vol_rate',
          render: (h, value) => {
            if (!value) return
            return value < 0 ? <div class='orange'>{this.$numToPercent(value, 1)}</div> : <div class='green'>{this.$numToPercent(value, 1)}</div>
          }
        }
      ]
    },

    // 总部登录和战区登录展示战区数据
    async getBigAreaData() {
    //   }
      const { obj } = await this._getTrunkCostDaysData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: '31'
      })
      this.setProAreaTable(obj)
    }

  }
}
</script>

<style lang="less" scoped>
.show-card {
    // height: 2.5rem;
    background-color: #f8f9fc;
    border-radius: 0.04rem;
    padding: 0.26rem 0;
}
.cumula-table{
    /deep/.table-outer-wrap .table-wrap.medium .sheader tr th{
        padding: 0.28rem 0.2rem;
        color: #333;
        font-weight: 700;
        position: sticky;
        top: 0px;
    }
    /deep/.table-outer-wrap .table-wrap.medium .sbody tr td{
        padding: 0.28rem 0.2rem;
        color: #666;
    }
}
</style>
