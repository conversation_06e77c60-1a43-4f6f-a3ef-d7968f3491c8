import requestMixins from 'common/mixins/requestMixins'
import { mapState } from 'vuex'
export default {
  mixins: [requestMixins],
  data() {
    return {}
  },
  computed: {
    levelMap() {
      return {
        30: {}, // 总部
        32: { provinceAreaCode: this.currZoneCode }, // 省区
        33: { areaCode: this.currZoneCode }, // 区域,
        37: { areaCode: this.currZoneCode } // 中转场,
        // 31: { bigAreaCode: this.currZoneCode } // 大区,
      }
    },
    levelMapToVol() {
      return {
        30: {}, // 总部
        32: {}, // 省区
        33: { provinceAreaCode: this.currZoneCode } // 区域,
        // 37: { deptCode: this.currZoneCode }, // 中转场,
        // 31: { bigAreaCode: this.currZoneCode } // 大区,
      }
    },
    initLevel() {
      switch (+this.currZoneLevel) {
        case 30:
          return '32'

        case 32:
          return '33'

        default:
          return this.currZoneLevel
      }
    },
    initLevelToVol() {
      switch (+this.currZoneLevel) {
        case 30:
          return '32'

        case 32:
          return '32'

        default:
          return this.currZoneLevel
      }
    },
    initLevelToUn() {
      switch (+this.currZoneLevel) {
        case 30:
          return '31'

        case 31:
          return '37'

        case 32:
          return '37'

        case 33:
          return '37'
      }
    },

    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('trunkCost', {
      selDateValue: state => state.selDateValue,
      currZoneLevel: state => state.zoneParams.currZoneLevel,
      currZoneCode: state => state.zoneParams.currZoneCode,
      currZoneName: state => state.zoneParams.currZoneName,
      scrolltop: state => state.scrolltop
    })
  },
  methods: {
    // 干线成本日维度
    _getTrunkCostDaysData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_main_line_cost_sum_di', setData)
    },
    // 干线成本月维度
    _getTrunkCostMonthsData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_main_line_cost_sum_mi', setData)
    },
    // 车次
    _getCardTypeData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_car_type_rate_sum_di', setData)
    },
    // 区域登录---装载率&吨公里成本
    _getAreaLoginData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_main_line_cost_sum_di_area_province', setData)
    }
  }
}
