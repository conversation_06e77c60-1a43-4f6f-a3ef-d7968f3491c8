<template>
  <div class="mt20" style="padding-bottom: 0.4rem; background-color: #fff">
    <CardList title="车次">
      <div class="mt24">
        <div class="pd_lr20">
          <p class="fs28 grey333 fw700">车次占比</p>
          <div class="card-ty-per">
            <NormalTable
              class="table-wrapper"
              :dataSource="tableDataSource"
              :columns="tableColumn"
              :border="false"
            ></NormalTable>
          </div>
        </div>
        <div class="mt64 pd_lr20">
          <p class="fs28 grey333 fw700">本月车型占比</p>
          <div class="mt40 car_profit flex_center">
            <i class="iconfont icon-Step_transport_icon red fs24"></i>
            <span class="grey999 ml8">平均车价(元)</span>
            <span class="grey3D3D3D ff_rbt ml8 fw700">{{ $numToInteger(avgPrice, 0) }}</span>
          </div>
          <div ref="chart-bar-cartype" style="height: 3rem" class="mt32"></div>
        </div>
        <div class="mt64">
          <p class="fs28 grey333 fw700 pd_lr20">本月车型明细</p>
          <BtnTabs class="pd_lr20" :activeIndex="activeIndex" :options="options" @btnConfirm="btnConfirm"></BtnTabs>
          <NormalTable
            :dataSource="isSlide ? typeMoreDataAll : typeMoreDataTen"
            :columns="typeColumn"
            maxHeight="25rem;"
            class="cumula-table mt24"
          ></NormalTable>
          <div class="mt32 pd_lr20" v-if="typeMoreDataAll.length > 10">
            <div class="sider_page flex_center" @click="slideBtnChange">
              <div class="mr10 grey333 fs24 fw700">{{ isSlide ? textList[1] : textList[0] }}</div>
              <i class="iconfont icon-more fs24" :class="{ arrow_up: isSlide }"></i>
            </div>
          </div>
        </div>
        <div class="mt64">
          <p class="fs28 grey333 fw700 pd_lr20">各{{ +currZoneLevel === 30 ? '战区' : '场站' }}不合理发车</p>
          <NormalTable
            :dataSource="isSlideNor ? noReasonDataAll : noReasonDataTen"
            :columns="noReasonColumn"
            maxHeight="25rem;"
            class="cumula-table mt24"
          ></NormalTable>
          <div class="mt32 pd_lr20" v-if="noReasonDataAll.length > 10">
            <div class="sider_page flex_center" @click="slideBtnChangeNor">
              <div class="mr10 grey333 fs24 fw700">
                {{ isSlideNor ? textList[1] : textList[0] }}
              </div>
              <i class="iconfont icon-more fs24" :class="{ arrow_up: isSlide }"></i>
            </div>
          </div>
        </div>
      </div>
    </CardList>
  </div>
</template>

<script>
const MOM_NUM_RATE = [
  'mom_is_big_cap_num_rate',
  'mom_is_unown_num_rate',
  'mom_is_double_num_rate',
  'mom_is_add_num_rate',
  'mom_is_vol_car_num_rate'
]
const MA_NUM_RATE = [
  'ma_is_big_cap_num_rate',
  'ma_is_unown_num_rate',
  'ma_is_double_num_rate',
  'ma_is_add_num_rate',
  'ma_is_vol_car_num_rate'
]
const IS_NUM_RATE = [
  'is_big_cap_num_rate',
  'is_unown_num_rate',
  'is_double_num_rate',
  'is_add_num_rate',
  'is_vol_car_num_rate'
]

import request from '../request'

import { drawBarChart } from 'common/charts/chartOption'

export default {
  mixins: [request],
  props: {
    trunkCostDaysData: {
      type: Array,
      default: () => []
    }
  },
  components: {},
  watch: {
    trunkCostDaysData: {
      handler(val) {
        this.setTable(val)
        if (val.length) {
          this.avgPrice = val[val.length - 1].avg_cost
        }
      },
      immediate: true
    },
    currZoneCode() {
      this.init()
    },
    selDateValue() {
      this.init()
    }
  },
  data() {
    return {
      textList: ['展开数据', '收起数据'],
      isSlide: false,
      isSlideNor: false,
      tableDataSource: [],
      tableColumn: [],
      options: ['大车型', '外租车', '双边车', '增派车', '大方车'],
      activeIndex: 0,
      typeMoreDataAll: [],
      typeMoreDataTen: [],
      typeColumn: [],
      noReasonDataAll: [],
      noReasonDataTen: [],
      noReasonColumn: [],
      avgPrice: ''
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getCardTypeData()
      this.getCurrMonData()
      this.getNoReasonData()
    },
    slideBtnChange() {
      this.isSlide = !this.isSlide
    },
    slideBtnChangeNor() {
      this.isSlideNor = !this.isSlideNor
    },
    btnConfirm({ item, index }) {
      if (this.activeIndex === index) return
      this.activeIndex = index
      this.setCurrMonTable(this.typeListSave)
      // 神策点击
      this.$sensors.webClick(`干线成本-本月车型明细-${item}`)
    },
    // 车次占比
    setTable(obj) {
      this.$checkNumber(obj)
      this.tableDataSource = []
      this.tableColumn = [
        {
          label: '车次类型',
          dataIndex: 'value0'
        },
        {
          label: '当日车次',
          dataIndex: 'value1',
          render: (h, value) => <div class=' grey666'>{this.$numToInteger(value, 0)}</div>
        },
        {
          label: '本月车次',
          dataIndex: 'value2',
          render: (h, value) => <div class=' grey666'>{this.$numToInteger(value, 0)}</div>
        },
        {
          label: '月环比',
          dataIndex: 'value3',
          render: (h, value) => {
            if (value > 0) {
              return (
                <div>
                  <span class='grey666'>{this.$numToPercent(value, 1)}</span>
                  <i class='iconfont fs24 icon-up green ml8'></i>
                </div>
              )
            } else if (value < 0) {
              return (
                <div>
                  <span class='grey666'>{this.$numToPercent(value, 1)}</span>
                  <i class='iconfont fs24 icon-down orange ml8'></i>
                </div>
              )
            } else {
              return <span class='grey666'>{value}</span>
            }
          }
        }
      ]
      if (obj.length) {
        this.tableDataSource = [
          {
            value0: '总车次',
            value1: obj[obj.length - 1].day_shift_num,
            value2: obj[obj.length - 1].mtd_shift_num,
            value3: obj[obj.length - 1].mom_shift_num
          },
          {
            value0: '一级车次',
            value1: obj[obj.length - 1].day_fir_shift_num,
            value2: obj[obj.length - 1].mtd_fir_shift_num,
            value3: obj[obj.length - 1].mom_fir_shift_num
          },
          {
            value0: '二级车次',
            value1: obj[obj.length - 1].day_sec_shift_num,
            value2: obj[obj.length - 1].mtd_sec_shift_num,
            value3: obj[obj.length - 1].mom_sec_shift_num
          }
        ]
      }
    },
    async getCardTypeData() {
      const { obj } = await this._getCardTypeData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: this.currZoneLevel,
        ...this.levelMap[+this.currZoneLevel]
      })
      this.initChartBar(obj)
    },
    initChartBar(obj) {
      this.$checkNumber(obj)
      const sData = []
      const xData = []
      if (obj.length) {
        const sortDownList = [
          { type_rate: obj[0].is_big_cap_num_rate, label: '大车型' },
          { type_rate: obj[0].is_unown_num_rate, label: '外租车' },
          { type_rate: obj[0].is_double_num_rate, label: '双边车' },
          { type_rate: obj[0].is_add_num_rate, label: '增派车' },
          { type_rate: obj[0].is_vol_car_num_rate, label: '大方车' }
        ]
        this.$objectSortDown(sortDownList, 'type_rate')
        sortDownList.forEach((item, index) => {
          xData.push(item.label)
          sData.push(item.type_rate)
        })
      }
      const option = {
        xAxis: [
          {
            data: xData
          },
          {
            data: sData,
            axisLabel: {
              formatter: val => (val ? this.$numToPercent(val, 1) : '-')
            }
          }
        ],
        series: [
          {
            data: sData
          }
        ]
      }

      const colorList = [['#4C83F9', '#4C83F9']]
      drawBarChart(option, this.$refs['chart-bar-cartype'], colorList)
    },
    // 本月车型明细
    async getCurrMonData() {
      const mapCode = {
        30: {}, // 总部
        31: { bigAreaCode: this.currZoneCode }, // 大区,
        32: { provinceAreaCode: this.currZoneCode }, // 省区
        33: { areaCode: this.currZoneCode }, // 区域,
        37: { areaCode: this.currZoneCode } // 中转场,
      }
      const mapLevel = {
        30: '31',
        31: '32',
        32: '33',
        33: '33'
      }
      const { obj } = await this._getCardTypeData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: mapLevel[+this.currZoneLevel],
        ...mapCode[+this.currZoneLevel]
      })

      this.typeListSave = obj
      this.setCurrMonTable(obj)
    },
    setCurrMonTable(obj) {
      this.$checkNumber(obj)
      this.typeMoreDataAll = []
      this.typeMoreDataTen = []
      // 月环比--大型车
      const bigSortDown = this.$objectSortUp(JSON.parse(JSON.stringify(obj)), 'mom_is_big_cap_num_rate')
      //   月环比--外请车
      const unownSortDown = this.$objectSortUp(JSON.parse(JSON.stringify(obj)), 'mom_is_unown_num_rate')
      // 月环比--双边车
      const doubleSortDown = this.$objectSortUp(JSON.parse(JSON.stringify(obj)), 'mom_is_double_num_rate')
      // 月环比--增派车
      const addSortDown = this.$objectSortUp(JSON.parse(JSON.stringify(obj)), 'mom_is_add_num_rate')
      // 月环比--大方车
      const volSortDown = this.$objectSortUp(JSON.parse(JSON.stringify(obj)), 'mom_is_vol_car_num_rate')
      const allData = [bigSortDown, unownSortDown, doubleSortDown, addSortDown, volSortDown]
      if (obj.length) {
        this.typeMoreDataAll = allData[this.activeIndex]
        this.typeMoreDataTen = allData[this.activeIndex].slice(0, 10)
      }
      this.typeColumn = [
        {
          label: '排名',
          align: 'center',
          render: (h, value) => {
            const val = this.typeMoreDataAll.length - value
            const isShowRed = val >= this.typeMoreDataAll.length - 2
            return (
              <div class='rank-typeindex flex_center'>
                <span v-show={isShowRed} class='flex_center fw700 red'>
                  {val}
                </span>
                <span v-show={!isShowRed} class='flex_center rank-index-color fw700'>
                  {val}
                </span>
              </div>
            )
          }
        },
        {
          label: () => {
            if (+this.currZoneLevel === 30) {
              return '战区'
            } else if (+this.currZoneLevel === 31) {
              return '省区'
            } else {
              return '区域'
            }
          },
          dataIndex:
            +this.currZoneLevel === 30
              ? 'big_area_name'
              : +this.currZoneLevel === 31
                ? 'province_area_name'
                : 'area_name',
          render: (h, value) => {
            return +this.currZoneLevel === 30
              ? value.replace(/SX|省区|快运|战区|分拨区/g, '')
              : value.replace(/SX|区域/g, '')
          }
        },
        {
          label: '月累计',
          dataIndex: IS_NUM_RATE[this.activeIndex],
          render: (h, value) => this.$numToPercent(value, 1)
        },
        {
          label: '上月累计',
          dataIndex: MA_NUM_RATE[this.activeIndex],
          render: (h, value) => this.$numToPercent(value, 1)
        },
        {
          label: '月环比',
          dataIndex: MOM_NUM_RATE[this.activeIndex],
          render: (h, value) => {
            if (!value) return
            return value < 0 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
          }
        }
      ]
    },
    // 不合理发车
    async getNoReasonData() {
      const mapCode = {
        30: {}, // 总部
        31: { bigAreaCode: this.currZoneCode }, // 大区,
        32: { provinceAreaCode: this.currZoneCode }, // 省区
        33: { areaCode: this.currZoneCode }, // 区域,
        37: { areaCode: this.currZoneCode } // 中转场,
      }
      const { obj } = await this._getTrunkCostDaysData({
        incDay: this.isDev ? '20210123' : this.selDateValue.replace(/-/g, ''),
        levelCode: this.initLevelToUn,
        ...mapCode[+this.currZoneLevel]
      })
      this.setNoReasonTabel(obj)
    },
    setNoReasonTabel(obj) {
      this.$checkNumber(obj)
      this.noReasonDataAll = []
      this.noReasonDataTen = []
      // 月环比--大型车
      const momUnSortDown = this.$objectSortDown(JSON.parse(JSON.stringify(obj)), 'mom_unreason')
      if (obj.length) {
        this.noReasonDataAll = momUnSortDown
        this.noReasonDataTen = momUnSortDown.slice(0, 10)
      }
      this.noReasonColumn = [
        {
          label: '排名',
          align: 'center',
          render: (h, value) => {
            const val = this.noReasonDataAll.length - value
            const isShowRed = val >= this.noReasonDataAll.length - 2
            return (
              <div class='rank-typeindex flex_center'>
                <span v-show={isShowRed} class='flex_center fw700 red'>
                  {val}
                </span>
                <span v-show={!isShowRed} class='flex_center rank-index-color fw700'>
                  {val}
                </span>
              </div>
            )
          }
        },
        {
          label: () => {
            if (+this.currZoneLevel === 30) {
              return '战区'
            } else {
              return '场站'
            }
          },
          dataIndex: +this.currZoneLevel === 30 ? 'big_area_name' : 'dept_name',
          render: (h, value) => {
            return +this.currZoneLevel === 30
              ? value.replace(/SX|省区|快运|战区|分拨区/g, '')
              : value.replace(/【SX】/g, '')
          }
        },
        {
          label: '月累计',
          dataIndex: 'mtd_unreason',
          render: (h, value) => this.$numToInteger(value, 0)
        },
        {
          label: '上月累计',
          dataIndex: 'ma_unreason',
          render: (h, value) => this.$numToInteger(value, 0)
        },
        {
          label: '月环比',
          dataIndex: 'mom_unreason',
          render: (h, value) => {
            if (!value) return
            return value > 0 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
          }
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.card-ty-per {
  // padding: 0.25rem 0.2rem 0;
  padding-top: 0.25rem;
  .table-wrapper {
    border: 1px solid #f2f2f2;
    border-radius: 0.04rem;
  }
  /deep/.table-outer-wrap .table-wrap.medium .sheader tr th {
    padding: 0.2rem;
    color: #333;
    font-weight: 700;
  }
  /deep/.table-outer-wrap .table-wrap.medium .sbody tr td {
    padding: 0.2rem;
  }
}
.car_profit {
  height: 0.65rem;
  background: #f8f9fc;
  border-radius: 2px;
  i {
    width: 0.4rem;
    height: 0.4rem;
    border-radius: 50%;
    background-color: rgba(220, 30, 50, 0.1);
    text-align: center;
    line-height: .4rem;
  }
}
.sider_page {
  width: 100%;
  height: 0.65rem;
  background-color: #f8f9fc;
  div {
    font-family: PingFangSC-Regular;
  }
  .arrow_up {
    transform: rotate(-180deg);
    -webkit-transform: rotate(-180deg); /* Safari 和 Chrome */
  }
}
.cumula-table {
  /deep/.table-outer-wrap .table-wrap.medium .sheader tr th {
    padding: 0.28rem 0.2rem;
    color: #333;
    font-weight: 700;
    position: sticky;
    top: 0px;
  }
  /deep/.table-outer-wrap .table-wrap.medium .sbody tr td {
    padding: 0.28rem 0.2rem;
    color: #666;
  }
}
</style>
