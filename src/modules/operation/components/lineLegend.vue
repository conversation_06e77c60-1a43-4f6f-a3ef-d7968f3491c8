<template>
  <div class="flex_start">
    <div class="flex1" v-for="(item, index) in options" :key="index">
      <i class="iconfont icon-ico_legend fs24" :style="{ color: item.color }"></i>
      <span class="grey666 ml8">{{ item.label }}</span>
      <span class="fs28 fw700 grey333 ff_rbt ml8">{{ item.value }}</span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    }
  }
}
</script>
<style lang='less' scoped>
</style>
