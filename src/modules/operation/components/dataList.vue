<!--
 * @Author: gilshi
 * @Date: 2020-11-24 18:57:57
 * @LastEditTime: 2021-12-30 10:29:54
 * @Description:
-->
<template>
    <div class="page_container flex_around">
        <div class="flex_column_center sub_container" v-for="(item, index) in options" :key="index" style="height:100%">
            <div class="grey999">{{ item.label }}</div>
            <div class="fs32 mt20 ff_rbt">
                <div v-if="item.indexType">
                    <span v-if="item.per" class="mr4 fw700">{{ $numToPercent(item.value, item.per, true) }}</span>
                    <span v-if="item.int"
                          class="mr4 fw700">{{ $numToInteger(item.value, item.int,  item.divisor||1 , 2, true )}}</span>
                    <PerDom :num="item.value" :perType="item.indexType"></PerDom>
                </div>
                <div v-else>
                    <span v-if="item.per" class="fw700">{{ $numToPercent(item.value, item.per) }}</span>
                    <span v-else-if="item.int"
                          class="fw700">{{ $numToInteger(item.value, item.int, item.divisor||1 )}}</span>
                    <span v-else class="fw700">{{ item.value }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import PerDom from 'common/components/perDom'
export default {
  /*
   * options: [
   *  {
   *    label:''
   *    value:'',
   *    perType:  'up' 正向指标 ,'down'负向指标,(百分比)
   *    per: 百分比 Number, 如果是0, 必须为String '0',
   *    intType :  'up' 正向指标 ,'down'负向指标,(整数),
   *    int: 取整 Number, 如果是0, 必须为String '0',
   *    divisor:除数因子
   *   }
   * ]
   */
  props: {
    options: {
      type: Array,
      default: () => []
    }
  },
  components: {
    PerDom
  }
}
</script>
<style lang="less" scoped>
.page_container {
  width: 100%;
  height: 1.35rem;
  background-color: #f8f9fc;
  border-radius: 0.08rem;
}
</style>
