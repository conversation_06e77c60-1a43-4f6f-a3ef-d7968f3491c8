<template>
    <div>
        <div class="more_content" :class="{activeUp :isSlide ,activeDown :!isSlide}"
             :style="{height: isSlide? height: '0'}" ref='moreContent'>
            <slot></slot>
        </div>
        <div class="sider_page flex_center" @click="slideBtnChange">
            <div class="mr10 grey333 fs24 fw700">{{isSlide?textList[1]:textList[0]}}</div>
            <i class="iconfont icon-more fs24" :class="{'arrow_up': isSlide}" src="" alt=""></i>
        </div>
    </div>
</template>
<script>
export default {
  props: {
    textList: {
      type: Array,
      default: () => ['展开数据', '收起数据']
    }
  },
  data() {
    return {
      isSlide: false,
      height: 0
    }
  },
  watch: {
    isSlide(val) {
      if (val) {
        if (!this.$refs['moreContent'].firstChild) {
          return
        }
        const { height } = this.$refs['moreContent'].firstChild.getBoundingClientRect()
        this.height = height + 10 + 'px'
      }
    }
  },
  methods: {
    slideBtnChange() {
      this.isSlide = !this.isSlide
      this.$emit('slideBtnChange', this.isSlide)
    }
  },
  mounted() {
  }
}
</script>
<style lang='less' scoped>
.more_content {
  overflow: hidden;
  height: 0;
  transition: 200ms linear;
}
.sider_page {
  width: 100%;
  height: 0.65rem;
  background-color: #f8f9fc;
  div {
    font-family: PingFangSC-Regular;
  }
  .arrow_up {
    transform: rotate(-180deg);
    -webkit-transform: rotate(-180deg); /* Safari 和 Chrome */
  }
}
</style>
