<template>
  <CardList :title="cardTitle">
    <div class="pd_lr20">
      <HScroll class="mt32" :width="barWidth">
        <div ref="chart-bar-weight" style="height: 3rem"></div>
      </HScroll>
    </div>
  </CardList>
</template>
<script>
import request from './request'
import { drawBarChart, newline } from 'common/charts/chartOption'

export default {
  mixins: [request],
  components: {},
  data() {
    return {
      barWidth: '120%'
    }
  },
  computed: {
    cardTitle() {
      if (+this.zoneLevel === 10) {
        return '各战区当月完成情况'
      }
      if (+this.zoneLevel === 11) {
        return '各省区当月完成情况'
      }
      return '各区域当月完成情况'
    }
  },
  watch: {
    zoneCode(val) {
      this.initOptions()
    },
    dateValue(val) {
      this.initOptions()
    }
  },
  methods: {
    // 总览
    getCargoWeightWebData() {
      return this._getCargoWeightMonthData({
        iD1: this.isDev ? '20210120' : this.dateValue.replace(/-/g, ''),
        levelCode: this.zoneLevel,
        ...this.levelMap[+this.zoneLevel]
      })
    },
    // 下钻趋势
    getCargoWeightTrendData() {
      return this._getCargoWeightMonthData({
        iD1: this.isDev ? '20210120' : this.dateValue.replace(/-/g, ''),
        levelCode: this.initLevel,
        ...this.levelMap[+this.zoneLevel]
      })
    },
    async getbarData() {
      const [{ obj: value1 }, { obj: value2 }] = await Promise.all([
        this.getCargoWeightWebData(),
        this.getCargoWeightTrendData()
      ])
      this.initChartBar(value1, value2)
    },
    initChartBar(value1, value2) {
      const xData = []
      const sData = []
      const option = {
        grid: {
          bottom: '20'
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: val => {
                if (val === '全网') {
                  return val
                }
                return newline(val.replace(/战区|省区|区域|中转场/, ''))
              }
            }
          },
          {
            data: sData,
            axisLabel: {
              formatter: val => this.$numToInteger(val, 0)
            }
          }
        ],
        series: [
          {
            showBackground: true,
            backgroundStyle: {
              color: '#f8f9fc'
            },
            data: sData
          }
        ]
      }
      if (value1.length) {
        this.$checkNumber(value1)
        value1.map((item, index) => {
          if (+this.zoneLevel === 10) {
            xData.push('全网')
          } else if (+this.zoneLevel === 11) {
            xData.push(item.big_area_name)
          } else {
            xData.push(item.province_area_name)
          }
          sData.push(item.weight_mon_total)
          if (item.province_area_name.length > 4) {
            option.grid.bottom = '30'
          }
        })
      }
      if (value2.length) {
        this.$checkNumber(value2)
        this.$objectSortDown(value2, 'weight_mon_total')
        value2.map((item, index) => {
          if (+this.zoneLevel === 10) {
            xData.push(item.big_area_name)
          } else if (+this.zoneLevel === 11) {
            xData.push(item.province_area_name)
          } else {
            xData.push(item.area_name)
          }
          sData.push(item.weight_mon_total)
          if (item.province_area_name.length > 4) {
            option.grid.bottom = '30'
          }
        })
      }
      if (sData.length <= 6) {
        this.barWidth = '100%'
      } else {
        this.barWidth = (100 / 6) * sData.length + '%'
      }
      const colorList = [['#4C83F9', '#4C83F9']]
      drawBarChart(option, this.$refs['chart-bar-weight'], colorList, true)
    },
    initOptions() {
      this.getbarData()
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang='less' scoped>
/deep/.container {
  padding-bottom: 0;
}
/deep/.container .header_box .header .left {
  font-weight: 500;
}
/deep/.scroll-bar-container {
  margin-top: 0.06rem;
}
</style>
