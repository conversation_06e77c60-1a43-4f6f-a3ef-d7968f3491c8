<!--
 * @Author: JieJw
 * @Date: 2021-07-28 17:40:36
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-03-20 16:08:42
 * @Description:
-->
<template>
  <div>
    <HeadBar title="中转场出勤效能" :homePage="false" :showPublicSelect="false" />
    <div style="height: 0.24rem; background: #f5f5f5"></div>
    <div class="contentWrapper">
      <TitleTabs class="mt24" :tabData="tabData" :tabValue="tabValue" @change="tabChange" />
      <div class="pd_lr20 mt24">
        <RadiusButton
          :buttonData="buttonData"
          :activeIndex="activeIndex"
          @click="buttonClick"
          :num="3"
        />
      </div>
      <NormalTable
        :dataSource="perDeptEfficData"
        :columns="deptTableCol"
        size="small"
        maxHeight="8.1rem"
        :width="deptTableWidth"
        isIndicator
      />
    </div>
  </div>
</template>

<script>
import HeadBar from 'common/components/head'
import request from './request'
export default {
  mixins: [request],
  components: {
    HeadBar
  },
  data() {
    return {
      tabData: [
        { label: '出勤', value: 0 },
        { label: '在职', value: 1 }
      ],
      tabValue: 0,
      buttonData: ['枢纽', '中转场', '集配站'],
      activeIndex: 0,
      perDeptEfficData: [],
      deptTableCol: [],
      backDeptData: [],
      deptTableWidth: '130%'
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      await this.getDeptEfficData()
      this.setDeptEfficTable(this.backDeptData)
    },
    async getDeptEfficData() {
      const selDate = this.$moment(this.dateValue).format('YYYYMMDD')
      const { obj } = await this._getWorkEfficData({
        begin_date: selDate,
        end_date: selDate,
        zone_level: '38',
        ...this.deptLevelMap[+this.zoneLevel],
        center_type: this.activeIndex + 1
      })
      this.backDeptData = obj.length ? obj : []
    },
    setDeptEfficTable(obj) {
      this.perDeptEfficData = []
      this.deptTableCol = [
        {
          label: '排名',
          align: 'center',
          fixed: 'left',
          render: (h, value) => {
            return (
              <div class='rank-typeindex flex_center'>
                <span class='flex_center rank-index-color fw700'>{value + 1}</span>
              </div>
            )
          }
        },
        {
          label: '场站',
          fixed: 'left',
          width: '1.6rem',
          dataIndex: 'zone_name',
          render: (h, value) => (value || '').replace(/【SX】/g, '')
        },
        {
          label: '等级',
          dataIndex: 'center_level_name',
          render: (h, value) => value
        },
        {
          label: this.tabValue ? '在职效能' : '出勤效能',
          dataIndex: this.tabValue ? 'on_job_eff_sum_mon' : 'emp_attend_eff_sum_mon',
          render: (h, value) => this.$numToInteger(value, 2)
        },
        {
          label: '月环比',
          dataIndex: this.tabValue ? 'on_job_eff_mom' : 'emp_attend_eff_mom',
          render: (h, value) =>
            value < 0 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
        },
        {
          label: '目标效能',
          dataIndex: this.tabValue ? 'on_job_eff_target_value' : 'emp_attend_eff_target_value',
          render: (h, value) => this.$numToInteger(value, 2)
        },
        {
          label: '效能达成率',
          dataIndex: this.tabValue ? 'on_job_reach_rate' : 'emp_attend_eff_reach_rate',
          render: (h, value) => {
            if (!value) return
            return value < 1 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
          }
        }
      ]
      if (obj.length) {
        if (this.tabValue === 3) {
          this.$objectSortDown(obj, this.tabValue ? 'on_job_eff_mom' : 'emp_attend_eff_mom')
          this.perDeptEfficData = obj
        } else {
          const levleOneData = obj.filter((item, index) => {
            return item.center_level === '1'
          })
          const levleTwoData = obj.filter((item, index) => {
            return item.center_level === '2'
          })
          const levleThreeData = obj.filter((item, index) => {
            return item.center_level === '3'
          })
          if (levleOneData.length) {
            this.$objectSortDown(
              levleOneData,
              this.tabValue ? 'on_job_eff_mom' : 'emp_attend_eff_mom'
            )
          }
          if (levleTwoData.length) {
            this.$objectSortDown(
              levleTwoData,
              this.tabValue ? 'on_job_eff_mom' : 'emp_attend_eff_mom'
            )
          }
          if (levleThreeData.length) {
            this.$objectSortDown(
              levleThreeData,
              this.tabValue ? 'on_job_eff_mom' : 'emp_attend_eff_mom'
            )
          }
          this.perDeptEfficData = [...levleOneData, ...levleTwoData, ...levleThreeData]
        }
      }
    },
    tabChange(val) {
      this.tabValue = val
      this.setDeptEfficTable(this.backDeptData)
    },
    buttonClick(val) {
      this.activeIndex = val
      this.init()
    }
  }
}
</script>

<style lang="less" scoped>
.contentWrapper {
  background: #fff;
  height: calc(100vh - 1rem);
}
</style>
