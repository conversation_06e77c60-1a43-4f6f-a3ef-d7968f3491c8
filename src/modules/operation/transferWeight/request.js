/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-03-20 15:56:24
 * @Description:
 */
import requestMixins from 'common/mixins/requestMixins'
import { mapState } from 'vuex'
export default {
  mixins: [requestMixins],
  data() {
    return {}
  },
  computed: {
    levelMap() {
      return {
        10: {}, // 总部
        11: { bigAreaCode: this.zoneCode }, // 战区
        13: { provinceAreaCode: this.zoneCode }, // 省区
        14: { areaCode: this.zoneCode } // 区域
      }
    },
    deptLevelMap() {
      return {
        10: {}, // 总部
        11: { big_area_code: this.zoneCode }, // 战区
        13: { province_area_code: this.zoneCode }, // 省区
        14: { area_code: this.zoneCode } // 区域
      }
    },
    initLevel() {
      switch (+this.zoneLevel) {
        case 10:
          return '11'

        case 11:
          return '13'

        case 13:
          return '14'

        default:
          return this.zoneLevel
      }
    },
    humanEfficlLevel() {
      switch (+this.zoneLevel) {
        case 10:
          return '30'

        case 11:
          return '31'

        case 13:
          return '32'

        case 14:
          return '33'
      }
    },
    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('transferWeight', {
      dateValue: state => state.dateValue,
      zoneLevel: state => state.zoneParams.zoneLevel,
      zoneCode: state => state.zoneParams.zoneCode,
      zoneName: state => state.zoneParams.zoneName
    })
  },
  methods: {
    // 顺心-营运-货量1 日
    _getCargoWeightDayData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_sxzk_cargo_daily_sum_di', setData)
    },
    // 顺心-营运-货量1 月
    _getCargoWeightMonthData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_sxzk_cargo_mon_sum_di', setData)
    },
    // 在职效能
    _getWorkEfficData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_sxzk_employee_effect_sum_di', setData)
    },
    // 效能月度数据
    _getMonthEfficData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_sxzk_employee_effect_sum_mf', setData)
    },
    // 明细
    getRatioData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('sx_op_tf_tray_sum_di', setData)
    },
    // 趋势图
    getTrednData(data) {
      const setData = this.forMapData(data)
      return this.sendTwoDimenRequest('ads_sx_op_tf_tray_trend_sum_di', setData)
    }
  }
}
