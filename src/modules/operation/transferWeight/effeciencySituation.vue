<template>
  <CardList :title="cardListTitle">
    <div slot="tip">
      <ShowTipIcon width="5rem">
        在职效能=(总操作货量/2)/（二线在职+三线在职+外包操作人数+外包叉车出勤人数）；<br /><br />
        出勤效能=(总操作货量/2)/（自有操作出勤人次+外包操作出勤人次+自有叉车出勤人次+外包叉车出勤人次）；
      </ShowTipIcon>
    </div>
    <span v-if="+zoneLevel === 10" slot="nav" class="check-trend" @click="jumpRouter">
      我的中转场<i class="iconfont icon-dayuhao"></i>
    </span>
    <Tabs
      v-else
      slot="nav"
      :options="['出勤', '在职']"
      :tabIndex="tabIndex"
      @tabSelect="tabSelect"
    />

    <div v-if="+zoneLevel === 10">
      <TitleTabs class="mt24" :tabData="tabData" :tabValue="tabValue" @change="tabChange" />
      <NormalTable class="mt32" :dataSource="bigAreaEfficData" :columns="bigAreaCol" size="small" />
    </div>

    <div v-else>
      <TitleTabs class="mt24" :tabData="tabData2" :tabValue="tabValue2" @change="tabChange2" />
      <NormalTable
        class="mt24"
        :dataSource="perDeptEfficData"
        :columns="deptTableCol"
        size="small"
        maxHeight="8.1rem"
        :width="deptTableWidth"
        isIndicator
      />
    </div>
  </CardList>
</template>

<script>
import request from './request'
export default {
  mixins: [request],
  data() {
    return {
      laborIndex: 0,
      tabValue: 0,
      tabData: [
        { label: '出勤', value: 0 },
        { label: '在职', value: 1 }
      ],
      tabValue2: 0,
      tabData2: [
        { label: '枢纽', value: 0 },
        { label: '中转场', value: 1 },
        { label: '集配站', value: 2 }
      ],
      deptTableWidth: '130%',
      perDeptEfficData: [],
      deptTableCol: [],
      bigAreaEfficData: [],
      bigAreaCol: [],
      backEfficData: [],
      backDeptData: [],
      tabIndex: 0
    }
  },
  computed: {
    cardListTitle() {
      return `${+this.zoneLevel !== 10 ? '中转场' : '战区'}出勤效能`
    }
  },
  watch: {
    zoneCode() {
      this.init()
    },
    dateValue() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      if (+this.zoneLevel === 10) {
        await this.getBigAreaEfficData()
        this.setBigAreaTable(this.backEfficData)
      } else {
        await this.getDeptEfficData()
        this.setDeptEfficTable(this.backDeptData)
      }
    },
    tabChange(val) {
      this.tabValue = val
      this.setBigAreaTable(this.backEfficData)
    },
    async tabChange2(val) {
      this.tabValue2 = val
      await this.getDeptEfficData()
      this.setDeptEfficTable(this.backDeptData)
    },
    async getDeptEfficData() {
      const selDate = this.$moment(this.dateValue).format('YYYYMMDD')
      const { obj } = await this._getWorkEfficData({
        begin_date: selDate,
        end_date: selDate,
        zone_level: '38',
        ...this.deptLevelMap[+this.zoneLevel],
        center_type: this.tabValue2 + 1
      })
      this.backDeptData = obj.length ? obj : []
    },
    setDeptEfficTable(obj) {
      this.perDeptEfficData = []
      this.deptTableCol = [
        {
          label: '排名',
          align: 'center',
          fixed: 'left',
          render: (h, value) => {
            return (
              <div class='rank-typeindex flex_center'>
                <span class='flex_center rank-index-color fw700'>{value + 1}</span>
              </div>
            )
          }
        },
        {
          label: '场站',
          fixed: 'left',
          width: '1.6rem',
          dataIndex: 'zone_name',
          render: (h, value) => (value || '').replace(/【SX】/g, '')
        },
        {
          label: '等级',
          dataIndex: 'center_level_name',
          render: (h, value) => value
        },
        {
          label: this.tabIndex ? '在职效能' : '出勤效能',
          dataIndex: this.tabIndex ? 'on_job_eff_sum_mon' : 'emp_attend_eff_sum_mon',
          render: (h, value) => this.$numToInteger(value, 2)
        },
        {
          label: '月环比',
          dataIndex: this.tabIndex ? 'on_job_eff_mom' : 'emp_attend_eff_mom',
          render: (h, value) =>
            value < 0 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
        },
        {
          label: '目标效能',
          dataIndex: this.tabIndex ? 'on_job_eff_target_value' : 'emp_attend_eff_target_value',
          render: (h, value) => this.$numToInteger(value, 2)
        },
        {
          label: '效能达成率',
          dataIndex: this.tabIndex ? 'on_job_reach_rate' : 'emp_attend_eff_reach_rate',
          render: (h, value) => {
            if (!value) return
            return value < 1 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
          }
        }
      ]
      if (obj.length) {
        if (this.tabIndex === 3) {
          this.$objectSortDown(obj, this.tabIndex ? 'on_job_eff_mom' : 'emp_attend_eff_mom')
          this.perDeptEfficData = obj
        } else {
          const levleOneData = obj.filter((item, index) => {
            return item.center_level === '1'
          })
          const levleTwoData = obj.filter((item, index) => {
            return item.center_level === '2'
          })
          const levleThreeData = obj.filter((item, index) => {
            return item.center_level === '3'
          })
          if (levleOneData.length) {
            this.$objectSortDown(
              levleOneData,
              this.tabIndex ? 'on_job_eff_mom' : 'emp_attend_eff_mom'
            )
          }
          if (levleTwoData.length) {
            this.$objectSortDown(
              levleTwoData,
              this.tabIndex ? 'on_job_eff_mom' : 'emp_attend_eff_mom'
            )
          }
          if (levleThreeData.length) {
            this.$objectSortDown(
              levleThreeData,
              this.tabIndex ? 'on_job_eff_mom' : 'emp_attend_eff_mom'
            )
          }
          this.perDeptEfficData = [...levleOneData, ...levleTwoData, ...levleThreeData]
        }
      }
    },
    async getBigAreaEfficData() {
      const selDate = this.$moment(this.dateValue).format('YYYYMMDD')
      const { obj } = await this._getWorkEfficData({
        begin_date: selDate,
        end_date: selDate,
        zone_level: '31'
      })
      this.backEfficData = obj.length ? obj : []
    },
    setBigAreaTable(obj) {
      this.bigAreaEfficData = []
      this.bigAreaCol = [
        {
          label: '排名',
          align: 'center',
          render: (h, value) => {
            return (
              <div class='rank-typeindex flex_center'>
                <span class='flex_center rank-index-color fw700'>
                  {this.bigAreaEfficData.length - value}
                </span>
              </div>
            )
          }
        },
        {
          label: '战区',
          dataIndex: 'zone_name',
          render: (h, value) => value.replace(/战区/g, '')
        },
        {
          label: this.tabValue ? '在职效能' : '出勤效能',
          dataIndex: this.tabValue ? 'on_job_eff_sum_mon' : 'emp_attend_eff_sum_mon',
          render: (h, value) => this.$numToInteger(value, 2)
        },
        {
          label: '月环比',
          dataIndex: this.tabValue ? 'on_job_eff_mom' : 'emp_attend_eff_mom',
          render: (h, value) =>
            value < 0 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
        },
        {
          label: '目标效能',
          dataIndex: this.tabValue ? 'on_job_eff_target_value' : 'emp_attend_eff_target_value',
          render: (h, value) => this.$numToInteger(value, 2)
        },
        {
          label: '达成率',
          dataIndex: this.tabValue ? 'on_job_reach_rate' : 'emp_attend_eff_reach_rate',
          render: (h, value) => {
            if (!value) return
            return value < 1 ? (
              <div class='orange'>{this.$numToPercent(value, 1)}</div>
            ) : (
              <div class='green'>{this.$numToPercent(value, 1)}</div>
            )
          }
        }
      ]
      if (obj.length) {
        this.$objectSortUp(obj, this.tabValue ? 'on_job_eff_mom' : 'emp_attend_eff_mom')
        this.bigAreaEfficData = obj
      }
    },
    tabSelect(val) {
      this.tabIndex = val
      this.setDeptEfficTable(this.backDeptData)
    },
    jumpRouter() {
      this.$router.push({
        name: 'onworkEfficiency'
      })
    }
  }
}
</script>

<style lang="less" scoped>
</style>
