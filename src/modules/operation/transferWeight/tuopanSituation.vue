<!--
 * @Author: Jie<PERSON>w
 * @Date: 2021-07-29 11:03:10
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-10-29 18:36:48
 * @Description:
-->
<template>
  <CardList title="托盘绑定情况">
      <ShowTipIcon
        slot="tip"
        @btnShowTip="btnShowTip($event, 'yy-zz-tpbdqk')"
        :isFeedback="isFeedback"
        @feedback="btnFeedback"
        :explaindDetail="explaindDetail"
      >
      </ShowTipIcon>
    <Tabs slot="nav" :tabIndex="tabIndex" :options="['日', '周', '月']" @tabSelect="tabSelect" />
    <div class="mt24 pd_lr20">
      <MultiDataList :columns="columns" :dataSource="dataSource" :isAuth="true" />
    </div>
    <CardList title="同票同位率趋势" type="sub" class="mt40" />
    <div class="pd_lr20">
      <CommonLegend :columns="columns2" :dataSource="dataSource2" inline />
      <div class="ht_chart" ref="lineBarChart"></div>
      <BtnShowMore class="mt24">
        <NormalTable
          :dataSource="tableDataSource"
          :columns="tableColumns"
          :width="tableWidth"
          size="little"
          minHeight="1rem"
          isIndicator
        />
      </BtnShowMore>
    </div>
  </CardList>
</template>

<script>
import { drawOneLineChart } from 'common/charts/chartOption'
import request from './request'
export default {
  mixins: [request],
  data() {
    return {
      tabIndex: 0,
      dataSource: {},
      dataSource2: {},
      tableDataSource: [],
      tableColumns: [],
      tableWidth: '100%'
    }
  },
  computed: {
    field() {
      return ['same_p_num_rate', 'same_p_num_wtd_rate', 'same_p_num_mtd_rate'][this.tabIndex]
    },
    columns() {
      const labelArr = ['同票同位率', '件托比(件)', '重托比(KG)']
      const fieldArr = [this.field, 'tran_quantity_pallet', 'weight_pallet']
      return labelArr.map((item, index) => {
        return {
          parent: [
            {
              label: item,
              dataIndex: fieldArr[index],
              render: (h, val) =>
                index ? this.$numToInteger(val, 2, 1) : this.$numToPercent(val, 1)
            }
          ]
        }
      })
    },
    columns2() {
      return [
        {
          icon: <div class='rect bgblue'></div>,
          label: '同票同位率',
          dataIndex: 'same_p_num_rate',
          render: val => this.$numToPercent(val, 1)
        }
      ]
    }
  },
  watch: {
    zoneCode() {
      this.init()
    },
    dateValue() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getSeatRatioData()
      this.getTrendChartData()
    },
    // 坑位数据
    async getSeatRatioData() {
      const { obj } = await this.getRatioData({
        level_code: this.humanEfficlLevel,
        inc_day: this.$moment(this.dateValue).format('YYYYMMDD'),
        ...this.deptLevelMap[+this.zoneLevel]
      })
      this.dataSource = obj[0] || {}
    },
    // 趋势图数据
    async getTrendChartData() {
      const { obj } = await this.getTrednData({
        inc_day:
          process.env.NODE_ENV === 'development'
            ? '20210718'
            : this.$moment(this.dateValue).format('YYYYMMDD'),
        level_code: this.humanEfficlLevel,
        flag: String(this.tabIndex),
        ...this.deptLevelMap[+this.zoneLevel]
      })
      this.trendData = obj.sort((a, b) => a.dt.replace(/-/g, '') - b.dt.replace(/-/g, ''))
      this.handlerTrendData()
    },
    handlerTrendData() {
      const lineData = this.trendData.map(item => {
        return {
          value: item.same_p_num_rate,
          ...item
        }
      })
      const field = this.tabIndex === 1 ? 'wid' : 'dt'
      const xAxisData = this.trendData.map(item => item[field])
      this.dataSource2 = this.trendData[this.trendData.length - 1] || {}
      const res = this.$setTable(
        this.trendData,
        this.columns2,
        field,
        ['day', 'week', 'month'][this.tabIndex]
      )
      this.tableColumns = res[0]
      this.tableDataSource = res[1]
      this.tableWidth = res[2]

      const option = {
        tooltip: {
          formatter: params => {
            this.dataSource2 = (params[0] || {}).data || {}
          }
        },
        xAxis: [
          {
            data: xAxisData,
            axisLabel: {
              formatter: val =>
                this.tabIndex === 1
                  ? `${val.slice(4, 6)}周`
                  : this.$dateFormat(val, this.tabIndex ? 1 : 0)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: val => this.$numToPercent(val, 0)
            }
          }
        ],
        series: [
          {
            data: lineData
          }
        ]
      }
      drawOneLineChart(option, this.$refs.lineBarChart)
    },
    tabSelect(val) {
      this.tabIndex = val
      this.init()
    }
  }
}
</script>

<style lang="less" scoped>
</style>
