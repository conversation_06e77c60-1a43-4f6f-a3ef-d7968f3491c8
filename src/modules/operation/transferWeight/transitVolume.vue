<template>
  <div>
    <CardList title="中转货量">
      <ShowTipIcon
        slot="tip"
        @btnShowTip="btnShowTip($event, 'yy-zz-zzhl')"
        :isFeedback="isFeedback"
        @feedback="btnFeedback"
        :explaindDetail="explaindDetail"
      >
      </ShowTipIcon>
      <div slot="nav">
        <Tabs :tabIndex="tabIndex" :options="['日', '月']" @tabSelect="tabSelect"></Tabs>
      </div>
      <div class="pd_lr20" style="padding-top: 0.24rem">
        <MultiDataList :columns="columns1" :dataSource="dataSource1" :isAuth="true" class="customer-datalist" />
      </div>
      <CardList type="sub" :title="`近八${dateName1}货量趋势`" class="mt40">
        <span slot="nav" class="grey999 fs20">货量:吨</span>
      </CardList>
      <HScroll class="mt8" :width="barWidth">
        <div ref="chart-bar-weight" style="height: 3rem"></div>
      </HScroll>
    </CardList>
  </div>
</template>
<script>
import { drawBarChart } from 'common/charts/chartOption'
// import PerDom from 'common/components/perDom'
import request from './request'
export default {
  mixins: [request],
  data() {
    return {
      dataSource1: {},
      tabIndex: 0,
      barWidth: '100%',
      webData: [
        ['', '', '', '', ''],
        ['', '', '', '']
      ],
      resultDataDay: [],
      resultDataMon: []
    }
  },
  computed: {
    columns1() {
      return [
        {
          parent: [
            {
              label: `当${this.dateName1}总货量(吨)`,
              dataIndex: this.tabIndex ? 'weight_mon_total' : 'all_cargo',
              int: [0, 1]
            }
          ],
          child: [
            {
              label: '进港货量(T)',
              dataIndex: `${this.tabIndex ? 'mon_' : ''}arrive_port_cargo`,
              int: [0, 1]
            },
            {
              label: '占比',
              dataIndex: `${this.tabIndex ? 'mon_' : ''}arrive_port_rate`,
              per: '1'
            },
            {
              label: '出港货量(T)',
              dataIndex: `${this.tabIndex ? 'mon_' : ''}leave_port_cargo`,
              int: [0, 1]
            },
            {
              label: '占比',
              dataIndex: `${this.tabIndex ? 'mon_' : ''}leave_port_rate`,
              per: '1'
            }
          ]
        },
        {
          parent: [
            {
              label: `当${this.dateName2}总货量(吨)`,
              dataIndex: this.tabIndex ? 'year_all_cargo' : 'weight_mon_total',
              int: [0, 1]
            }
          ],
          child: [
            !this.tabIndex && {
              label: '同步',
              dataIndex: 'year_on_year',
              per: '1',
              indexType: 'up'
            },
            !this.tabIndex && {
              label: '环比',
              dataIndex: 'mon_on_mon',
              per: '1',
              indexType: 'up'
            },
            {
              label: `${this.dateName1}均货量(T)`,
              dataIndex: this.tabIndex ? 'avg_year_all_cargo' : 'avg_weight_mon',
              int: [0, 1]
            },
            this.tabIndex && {
              label: () => <div style={{ color: '#f8f9fc' }}>zw</div>
            },
            this.tabIndex && {
              label: () => <div style={{ color: '#f8f9fc' }}>zw</div>
            },
            {
              label: () => <div style={{ color: '#f8f9fc' }}>zw</div>
            }
          ]
        }
      ]
    },
    dateName1() {
      return !this.tabIndex ? '日' : '月'
    },
    dateName2() {
      return !this.tabIndex ? '月' : '年'
    }
  },
  watch: {
    dateValue(val) {
      this.initOptions()
    },
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    tabSelect(index) {
      this.tabIndex = index
      this.$refs['chart-bar-weight'].setAttribute('_echarts_instance_', null)
      this.setWebData(this.resultDataDay, this.resultDataMon)
      if (!index) {
        this.getCargoWeightTrendDayData()
      } else {
        this.getCargoWeightTrendMonthData()
      }
      // 神策点击
      this.$sensors.webClick(`中转-中转货量-${['日', '月'][index]}`)
    },
    // 货量日
    getCargoWeightDayData() {
      return this._getCargoWeightDayData({
        incDay: this.dateValue.replace(/-/g, ''),
        levelCode: this.zoneLevel,
        ...this.levelMap[+this.zoneLevel]
      })
    },
    // 货量月
    getCargoWeightMonthData() {
      return this._getCargoWeightMonthData({
        iD1: this.dateValue.replace(/-/g, ''),
        levelCode: this.zoneLevel,
        ...this.levelMap[+this.zoneLevel]
      })
    },
    // 统一获取日月
    async getWebData() {
      const [{ obj: valueDay }, { obj: valueMon }] = await Promise.all([
        this.getCargoWeightDayData(),
        this.getCargoWeightMonthData()
      ])
      this.resultDataDay = valueDay || []
      this.resultDataMon = valueMon || []
      this.setWebData(valueDay, valueMon)
    },
    setWebData(objDay = [], objMon = []) {
      this.dataSource1 = { ...(objDay[0] || {}), ...(objMon[0] || {}) }
    },
    // 趋势日
    async getCargoWeightTrendDayData() {
      const startDay = this.$moment(this.dateValue).subtract(7, 'days').format('YYYYMMDD')
      const { obj } = await this._getCargoWeightDayData({
        sDay: startDay,
        eDay: this.dateValue.replace(/-/g, ''),
        levelCode: this.zoneLevel,
        ...this.levelMap[+this.zoneLevel]
      })
      this.initChartBar(obj)
    },
    // 趋势月
    async getCargoWeightTrendMonthData() {
      const dataList = {
        iD1: '',
        iD2: '',
        iD3: '',
        iD4: '',
        iD5: '',
        iD6: '',
        iD7: '',
        iD8: '',
        iD9: '',
        iD10: '',
        iD11: '',
        iD12: ''
      }
      for (let index = 0; index < 8; index++) {
        const incDay = index
          ? this.$moment(this.dateValue).subtract(index, 'month').endOf('month').format('YYYYMMDD')
          : this.dateValue.replace(/-/g, '')
        dataList['iD' + (index + 1)] = incDay
      }
      const { obj } = await this._getCargoWeightMonthData({
        ...dataList,
        levelCode: this.zoneLevel,
        ...this.levelMap[+this.zoneLevel]
      })
      this.initChartBar(obj)
    },
    initChartBar(obj) {
      const result = obj || []
      const xData = []
      const sData = []
      const option = {
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: val => {
                if (!this.tabIndex) {
                  return val ? this.$moment(val).format('MM.DD') : '-'
                } else {
                  return val ? this.$moment(val).format('M') + '月' : '-'
                }
              }
            }
          },
          {
            data: sData,
            axisLabel: {
              formatter: val => this.$numToInteger(val, 0)
            }
          }
        ],
        series: [
          {
            data: sData
          }
        ]
      }
      if (result.length) {
        this.$checkNumber(result)
        if (!this.tabIndex) {
          result.forEach((item, index) => {
            xData.push(item.inc_day)
            sData.push(item.all_cargo || '')
          })
        } else {
          result.forEach((item, index) => {
            xData.push(item.inc_day)
            sData.push(item.weight_mon_total || '')
          })
        }
      }
      // 计算宽度(默认显示8个柱子)
      if (sData.length <= 6) {
        this.barWidth = '100%'
      } else {
        this.barWidth = (100 / 6) * sData.length + '%'
      }
      const colorList = [['#4C83F9', '#4C83F9']]
      drawBarChart(option, this.$refs['chart-bar-weight'], colorList, false, true)
    },
    initOptions() {
      this.getWebData()
      if (!this.tabIndex) {
        this.getCargoWeightTrendDayData()
      } else {
        this.getCargoWeightTrendMonthData()
      }
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang='less' scoped>
.weight_container {
  background-color: #f8f9fc;
  div {
    > div {
      margin-top: 0.24rem;
      span {
        font-weight: 700;
      }
    }
  }
  padding-bottom: 0.24rem;
}
.pd-l40 {
  padding-left: 0.6rem;
}
// /deep/.iconfont {
//   font-size: 0.2rem;
// }
/deep/.container {
  padding-bottom: 0;
}
/deep/.container .header_box .header .left {
  font-weight: 500;
}
/deep/.scroll-bar-container {
  margin-top: 0.04rem;
}
.customer-datalist {
  /deep/ .multi_data_list_body {
    align-items: flex-start;
  }
  /deep/ .multi_data_list_parent {
    align-self: center;
  }
  /deep/ .multi_data_list_child {
    padding-left: 20%;
  }
}
</style>
