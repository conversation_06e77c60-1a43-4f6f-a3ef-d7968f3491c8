<template>
  <div class="weight_pege page_bgc">
    <MAlertWarning v-if="noTarget && zoneLevel <= 1" :isScroll="true" type="blue">
      本月劳效目标值未导入，还请联系梁晓霖（sx21090162）导入目标值
    </MAlertWarning>
    <div class="date_container">
      <KyDatePicker type="day" :dateValue="dateValue" @onChange="dateChange" :holidayData="holidayData"></KyDatePicker>
    </div>
    <PageContent ref="contentWrapper">
      <TransitVolume />
      <HandlerVolume v-if="+zoneLevel <= 2" class="mt20" />
      <DuringEffeciency class="mt20" v-if="zoneLevel <= 1" />
      <EffeciencySituation class="mt20" />
      <TuopanSituation class="mt20" />
      <div class="placeholder"></div>
    </PageContent>
  </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex'

import TransitVolume from './transitVolume'
import HandlerVolume from './handlerVolume'
import DuringEffeciency from './duringEffeciency'
import EffeciencySituation from './effeciencySituation'
import TuopanSituation from './tuopanSituation'
// import LaborEffic from './laborEffic'/
export default {

  components: {
    TransitVolume,
    HandlerVolume,
    DuringEffeciency,
    EffeciencySituation,
    TuopanSituation
    // LaborEffic
  },
  props: {
    zoneCode: {
      type: [String]
    },
    zoneLevel: {
      type: [String, Number]
    },
    rightName: {
      type: String
    }
  },
  data() {
    return {
      dateValue: this.$moment().subtract(2, 'days').format('YYYY-MM-DD'),
      wrapperRef: '',
      laborRef: '',
      laborIndex: 0,
      headerRef: '',
      efficRef: ''
    }
  },
  computed: {
    dateMin() {
      return this.$moment().subtract(1, 'year').toDate()
    },
    dateMax() {
      return this.$moment().subtract(0, 'month').toDate()
    },
    ...mapState({
      holidayData: 'holidayData'
    }),
    ...mapState('transferWeight', {
      noTarget: state => {
        console.log(state.noTarget, 'state.noTarget')
        return state.noTarget
      }
    })
  },
  watch: {
    // 层级zoneLeve转换
    zoneCode: {
      handler(val) {
        let zoneLevel
        switch (+this.zoneLevel) {
          case 0:
            zoneLevel = '10'
            break
          case 1:
            zoneLevel = '11'
            break
          case 2:
            zoneLevel = '13'
            break
          case 3:
            zoneLevel = '14'
            break
          default:
            zoneLevel = this.zoneLevel
            break
        }
        this.setZoneParams({
          zoneLevel,
          zoneCode: val,
          zoneName: this.rightName
        })
      },
      immediate: true
    }
  },
  methods: {
    ...mapMutations('transferWeight', ['setZoneParams', 'setDateValue']),
    dateChange(date) {
      this.dateValue = date
      this.setDateValue(date)
    }
  },
  created() {
    console.log(this.zoneLevel)
  },

  mounted() {
    this.$nextTick(() => {
      this.wrapperRef = document.querySelector('.content-wrapper')
      this.headerRef = document.querySelector('.wrapper-header')
      this.laborRef = document.querySelector('.volume-labor-effic')
      this.efficRef = document.querySelector('.effic-next')
    })
  }
}
</script>
<style lang='less' scoped>
.weight_pege {
  color: #333;
}
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding-left: 0.2rem;
}
.content-wrapper {
  //   background-color: #f8f9fc;
  height: calc(100vh - 3.3rem);
  overflow-y: scroll;
  position: relative;
}
.effic-scroll {
  position: fixed;
  top: 2.3rem;
  left: 0;
  z-index: 10;
}
.placeholder {
  height: 1rem;
}
/deep/.calender-tool-bar {
  font-size: 0.24rem;
}
.volume-labor-effic {
  /deep/.container {
    padding-bottom: 0;
  }
}
.effic-next {
  padding-top: 0.24rem;
}
.effic-top {
  padding-top: 1.2rem;
}
</style>
