<template>
  <div>
    <CardList title="效能">
      <div class="flex0 mt24 tc pd_lr20" v-if="+zoneLevel === 10 || +zoneLevel === 11">
        <div>
          <canvas id="circle" class="circle"></canvas>
          <p class="reach-rate ff_PFSR fw500">{{ $numToPercent(reachRate, 1) }}</p>
        </div>
        <div style="flex: 1; padding-top: 0.56rem">
          <ul class="flex_start">
            <li class="flex1" v-for="(item, index) in efficList" :key="index">
              <div class="effic-label">{{ item.label }}</div>
              <div class="effic-numb mt20 flex_center">
                <span class="fw500">{{
                  item.val ? (index === 2 ? $numToPercent(item.val, 1) : $numToInteger(item.val, 2)) : '-'
                }}</span>
                <i class="iconfont icon-up green fs20" v-if="index === 2"></i>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div style="padding: 0 0.1rem" :class="[+zoneLevel === 10 || +zoneLevel === 11 ? 'mt64' : 'mt24']" v-if="false">
        <div class="own-staff flex_center tc">
          <div class="left">
            <img src="../../img/organization.svg" alt="" />
            <div class="white fs20 fw500 ff_ASM mt12">自有员工</div>
          </div>
          <div class="right">
            <ul class="flex_start">
              <li class="flex1" v-for="(item, index) in ownStaffList" :key="index">
                <div class="ff_PFSR grey666 fs24">{{ item.label }}</div>
                <div class="mt20 ff_RM fs32 grey333 fw500">
                  {{ item.val ? (index === 2 ? $numToPercent(item.val, 1) : item.val) : '-' }}
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="out-staff mt32 flex_center tc">
          <div class="left">
            <img src="../../img/organization.svg" alt="" />
            <div class="white fs20 fw500 ff_ASM mt12">外包员工</div>
          </div>
          <div class="right">
            <ul class="flex_start">
              <li class="flex1" v-for="(item, index) in outStaffList" :key="index">
                <div class="ff_PFSR grey666 fs24">{{ item.label }}</div>
                <div class="mt20 ff_RM fs32 grey333 fw500">
                  {{ item.val ? (index === 1 ? $numToPercent(item.val, 1) : item.val) : '-' }}
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="pd_lr20" v-if="+zoneLevel === 10 || +zoneLevel === 11">
        <div class="flex_between mt68">
          <span class="ff_ASM grey333 fw700 fs28">{{ tabIndex ? '月度' : '近八日' }}效能趋势</span>
          <Tabs :options="['日', '月']" :tabIndex="tabIndex" @tabSelect="tabSelect" />
        </div>
        <div style="padding-top: 0.32rem">
          <CommonLegend :num="4" :dataSource="dataSource" :columns="columns" />
          <HScroll :width="efficWidth" class="mt24">
            <div ref="chart-line-effic" style="height: 3rem"></div>
          </HScroll>
          <BtnShowMore class="mt32">
            <NormalTable :dataSource="efficTrendData" :columns="trendColumns" :width="efficTableWidth" size="little" />
          </BtnShowMore>
        </div>
      </div>
    </CardList>
  </div>
</template>

<script>
const EFFIC_LIST = [
  { label: '当月劳效', val: '' },
  { label: '目标劳效', val: '' },
  { label: '月环比', val: '' }
]
const OWN_STAFF_LIST = [
  { label: '在职人数', val: '' },
  { label: '出勤人数', val: '' },
  { label: '出勤率', val: '' }
]
const OUT_STAFF_LIST = [
  //   { label: '总人数', val: '' },
  { label: '出勤人数', val: '' },
  { label: '自有员工占比', val: '' }
]

import CommonLegend from 'common/components/commonLegend'
import { mapMutations } from 'vuex'
import { drawGaugeByCanvas } from './humanResou/drawGaugeByCanvas'
import { drawLineChart } from 'common/charts/chartOption'
import request from './request'
export default {
  mixins: [request],
  components: {
    CommonLegend
  },
  data() {
    return {
      reachRate: '',
      efficList: JSON.parse(JSON.stringify(EFFIC_LIST)),
      ownStaffList: JSON.parse(JSON.stringify(OWN_STAFF_LIST)),
      outStaffList: JSON.parse(JSON.stringify(OUT_STAFF_LIST)),
      tabIndex: 0,
      dataSource: {
        onJobEffi: '-',
        empAttend: '-',
        effTargetValue: '-',
        attendTargetValue: '-'
      },
      efficWidth: '100%',

      efficTrendData: [],
      trendColumns: [],
      efficTableWidth: '100%'
    }
  },
  watch: {
    zoneCode() {
      this.initOptions()
    },
    dateValue() {
      this.initOptions()
    }
  },
  computed: {
    columns() {
      return [
        {
          icon: <div style='margin-top: 0.08rem' class='bgblue rect'></div>,
          label: '在职效能',
          dataIndex: 'onJobEffi',
          render: val => val
        },
        {
          icon: <div style='margin-top: 0.08rem' class='bgorange rect'></div>,
          label: '出勤效能',
          dataIndex: 'empAttend',
          render: val => val
        },
        {
          icon: <div style='margin-top: 0.08rem' class='bggreen rect'></div>,
          label: '在职效能目标',
          dataIndex: 'effTargetValue',
          render: val => val
        },
        {
          icon: <div style='margin-top: 0.08rem' class='bgyellow rect'></div>,
          label: '出勤效能目标',
          dataIndex: 'attendTargetValue',
          render: val => val
        }
      ]
    }
  },
  created() {
    this.initCavans(0)
  },
  mounted() {
    this.initOptions()
  },
  methods: {
    ...mapMutations('transferWeight', ['setNoTarget']),
    tabSelect(index) {
      if (this.tabIndex === index) return
      this.tabIndex = index
      this.initOptions()
      // 神策点击
      this.$sensors.webClick(`中转-在职效能-${['日', '月'][index]}`)
    },
    initOptions() {
      if (this.tabIndex) {
        if (+this.zoneLevel === 10 || +this.zoneLevel === 11) {
          this.getMonthEfficData()
        }
      } else {
        this.getWorkEfficData()
      }
    },
    initCavans(val) {
      if (+this.zoneLevel === 10 || +this.zoneLevel === 11) {
        this.$nextTick(() => {
          drawGaugeByCanvas('circle', val, 0)
        })
      }
    },
    // 在职效能
    async getWorkEfficData() {
      const beginDate = this.$moment(this.dateValue).subtract(7, 'days').format('YYYYMMDD')
      const endDate = this.$moment(this.dateValue).format('YYYYMMDD')
      const { obj } = await this._getWorkEfficData({
        begin_date: beginDate,
        end_date: endDate,
        zone_level: this.humanEfficlLevel,
        zone_code: this.zoneCode
      })
      if (obj.length) {
        const res = JSON.parse(JSON.stringify(obj))
        this.$objectSortUp(res, 'inc_day')
        if (res[res.length - 1].inc_day === endDate) {
          if (!res[res.length - 1].on_job_eff_target_value && res[res.length - 1].on_job_eff_target_value !== 0) {
            this.setNoTarget(true)
          } else {
            this.setNoTarget(false)
          }
          this.reachRate = res[res.length - 1].on_job_reach_rate
          const efficKeyList = ['on_job_eff_sum_mon', 'on_job_eff_target_value', 'on_job_eff_mom']
          const ownList = ['zy_emp_sum', 'zy_emp_attend_sum_d', 'emp_attend_rate_d']
          const outList = ['wb_emp_attend_sum_d', 'contract_worker_rate']
          this.efficList.map((item, index) => {
            item.val = res[res.length - 1][efficKeyList[index]]
          })
          this.ownStaffList.map((item, index) => {
            item.val = res[res.length - 1][ownList[index]]
          })
          this.outStaffList.map((item, index) => {
            item.val = res[res.length - 1][outList[index]]
          })
          this.initCavans(this.reachRate * 100)
        } else {
          this.setNoTarget(true)
          this.efficList = JSON.parse(JSON.stringify(EFFIC_LIST))
          this.ownStaffList = JSON.parse(JSON.stringify(OWN_STAFF_LIST))
          this.outStaffList = JSON.parse(JSON.stringify(OUT_STAFF_LIST))
          this.initCavans(0)
          this.reachRate = ''
        }
      } else {
        this.setNoTarget(true)
        this.efficList = JSON.parse(JSON.stringify(EFFIC_LIST))
        this.ownStaffList = JSON.parse(JSON.stringify(OWN_STAFF_LIST))
        this.outStaffList = JSON.parse(JSON.stringify(OUT_STAFF_LIST))
        this.initCavans(0)
        this.reachRate = ''
      }
      if (+this.zoneLevel === 10 || +this.zoneLevel === 11) {
        this.initEfficLine(obj)
        this.setTable(obj)
      }
    },
    // 效能月度数据
    async getMonthEfficData() {
      const { obj } = await this._getMonthEfficData({
        begin_mon: this.$moment(this.dateValue).subtract(11, 'months').format('YYYYMM'),
        end_mon: this.$moment(this.dateValue).format('YYYYMM'),
        zone_level: this.humanEfficlLevel,
        zone_code: this.zoneCode
      })
      this.initEfficLine(obj)
      this.setTable(obj)
    },
    initEfficLine(obj) {
      const legendKeyListMon = [
        'on_job_eff_sum_mon',
        'emp_attend_eff_sum_mon',
        'on_job_eff_target_value',
        'emp_attend_eff_target_value'
      ]
      const legendKeyListDay = [
        'on_job_eff_sum_d',
        'emp_attend_eff_d',
        'on_job_eff_target_value',
        'emp_attend_eff_target_value'
      ]
      const xData = []
      const sData = [[], [], [], []]
      const disList = []
      this.dataSource = {
        onJobEffi: '-',
        empAttend: '-',
        effTargetValue: '-',
        attendTargetValue: '-'
      }
      const option = {
        tooltip: {
          formatter: params => {
            const keyList = ['onJobEffi', 'empAttend', 'effTargetValue', 'attendTargetValue']
            params.map((item, index) => {
              this.dataSource[keyList[index]] = item.value ? this.$numToInteger(item.value, 2) : '-'
            })
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: val =>
                this.tabIndex ? `${+this.$moment(val).format('MM')}月` : `${this.$moment(val).format('MM.DD')}`
            }
          }
        ],
        series: [
          {
            data: sData[0]
          },
          {
            data: sData[1]
          },
          {
            data: sData[2]
          },
          {
            data: sData[3]
          }
        ]
      }
      if (obj.length) {
        this.$objectSortUp(obj, this.tabIndex ? 'inc_month' : 'inc_day')
        sData.forEach((sItem, sIndex) => {
          obj.forEach((oItem, oIndex) => {
            sItem.push(oItem[this.tabIndex ? legendKeyListMon[sIndex] : legendKeyListDay[sIndex]])
            if (sIndex === 1) {
              xData.push(this.tabIndex ? oItem.inc_month : oItem.inc_day)
            }
          })
        })
      }
      drawLineChart(option, this.$refs['chart-line-effic'])

      const charts = this.$echarts.init(this.$refs['chart-line-effic'])
      this.efficWidth = this.$hScrollWidth(xData.length, 8)
      sData.map(() => {
        return disList.push({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: option.series[0].data.length - 1
        })
      })
      setTimeout(() => {
        charts.dispatchAction(...disList)
      }, 200)
    },
    // 效能表格
    setTable(obj) {
      this.efficTrendData = [
        {
          dt: '在职效能'
        },
        {
          dt: '出勤效能'
        },
        {
          dt: '在职效能目标'
        },
        {
          dt: '出勤效能目标'
        }
      ]
      this.trendColumns = [
        {
          label: () => {
            return <div>日期</div>
          },
          //   width: '1.4rem',
          dataIndex: 'dt',
          fixed: 'left'
        }
      ]
      const cumulaColumns2 = []
      if (obj.length) {
        const deepCloneData = JSON.parse(JSON.stringify(obj))
        this.tabIndex
          ? deepCloneData.sort((a, b) => +a.inc_month - +b.inc_month)
          : deepCloneData.sort((a, b) => +a.inc_day - +b.inc_day)
        deepCloneData.map((item, index) => {
          cumulaColumns2.push({
            label: this.tabIndex
              ? +this.$moment(item.inc_month, 'YYYYMM').format('MM') + '月'
              : this.$moment(item.inc_day, 'YYYYMMDD').format('MM.DD'),
            dataIndex: 'date' + index
          })
          this.efficTrendData[0]['date' + index] = this.$numToInteger(
            this.tabIndex ? item.on_job_eff_sum_mon : item.on_job_eff_sum_d,
            2
          )
          this.efficTrendData[1]['date' + index] = this.$numToInteger(
            this.tabIndex ? item.emp_attend_eff_sum_mon : item.emp_attend_eff_d,
            2
          )
          this.efficTrendData[2]['date' + index] = this.$numToInteger(item.on_job_eff_target_value, 2)
          this.efficTrendData[3]['date' + index] = this.$numToInteger(item.emp_attend_eff_target_value, 2)
        })
        this.trendColumns = [...this.trendColumns, ...cumulaColumns2]
      }
      this.efficTableWidth = this.trendColumns.length <= 5 ? '100%' : (7.1 / 5) * this.trendColumns.length + 'rem'
    }
  }
}
</script>

<style lang="less" scoped>
.circle {
  height: 1rem;
  width: 2rem;
}
.ff_PFSR {
  font-family: PingFangSC-Regular;
}
.ff_ASM {
  font-family: AlibabaSans-Medium;
}
.ff_RM {
  font-family: Roboto-Medium;
}
.reach-rate {
  font-size: 0.32rem;
  color: #3d3d3d;
  letter-spacing: 0;
  font-weight: 700;
}
.effic-label {
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #666;
  line-height: 0.33rem;
}
.effic-numb {
  font-family: Roboto-Medium;
  font-size: 0.32rem;
  color: #333;
  // font-weight: 700;
  // line-height: 0.38rem;
}
.own-staff {
  padding: 0 0.12rem;
  // width: 7.1rem;
  height: 1.4rem;
  background: url('../img/blueBg.png') no-repeat center;
  background-size: 100%;
}
.out-staff {
  padding: 0 0.12rem;
  // width: 100%;
  height: 1.4rem;
  background: url('../img/orangeBg.png') no-repeat center;
  background-size: 100%;
}
.left {
  width: 1.21rem;
  height: 100%;
  padding: 0.16rem 0 0.13rem;
  img {
    height: 0.64rem;
  }
}
.right {
  flex: 1;
}
</style>
