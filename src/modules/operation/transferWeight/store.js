/*
 * @Author: shigl
 * @Date: 2022-07-20 18:28:55
 * @LastEditTime: 2022-07-20 18:48:19
 * @Description:
 */
import moment from 'moment'
export default {
  namespaced: true,
  name: 'transferWeight',
  state: {
    dateValue: moment().add(-2, 'd').format('YYYY-MM-DD'), // 日期
    zoneParams: {
      zoneLevel: '10',
      zoneCode: '001',
      zoneName: '顺心捷达'
    },
    noTarget: false
  },
  mutations: {
    setDateValue(state, date) {
      state.dateValue = date
    },
    setZoneParams(state, data) {
      state.zoneParams = data
    },
    setNoTarget(state, data) {
      state.noTarget = data
    }
  },
  actions: {
  }
}

