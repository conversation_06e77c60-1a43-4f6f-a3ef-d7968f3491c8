.content-wrapper {
  height: calc(100vh - 2.64rem);
  overflow-y: hidden;

  .calender-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.3rem;
    height: 1rem;
  }

  .index-box-content {
    position: relative;
    margin: 0 0.25rem;
    padding: 0.3rem;
    box-shadow: 0 0 0.1rem 0.06rem rgba(221, 221, 221, 0.47);
    border-radius: 0.08rem;
    background-color: #fff;
    z-index: 2;

    .index-box-content-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-bottom: 0.25rem;
      font-family: PingFangSC-Medium;

      .trend {
        display: flex;
        align-items: center;
        font-size: 0.28rem;
        text-align: center;
        color: #dc1e32;
        /deep/ a {
          color: #dc1e32;
        }
        &::after {
          content: '>';
          margin-left: 0.133rem;
          height: 0.228rem;
          line-height: 0.228rem;
        }
      }
    }

    .content-box {
      display: flex;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        left: 50%;
        height: 100%;
        border-left: 1px solid #f2f2f2;
      }
      .box {
        padding-left: 0.3rem;
        flex: 1;
        font-family: PingFangSC-Regular;

        h3 {
          margin-bottom: 0.18rem;
          font-size: 0.24rem;
          color: #808285;
          line-height: 0.33rem;
          text-align: center;
        }

        .index-value {
          margin-bottom: 0.08rem;
          text-align: center;

          .box-p {
            font-size: 0.58rem;
            font-weight: bold;
            font-family: Helvetica-Condensed-Bold, sans-serif;
            color: #414042;
            line-height: 0.72rem;
          }

          .real-time-data {
            display: flex;
            align-items: center;
            justify-content: center;
            > span {
              font-size: 0.25rem;
              margin-left: 0.1rem;
            }
            > img {
              height: 0.3rem;
              margin-left: 0.1rem;
              width: 0.15rem;
            }
          }
        }

        h5 {
          margin: 0.14rem 0;
          font-size: 0.24rem;
          color: #808285;
          line-height: 0.33rem;
          &:first-child {
            margin-top: 0;
          }
          span {
            font-size: 0.2rem;
          }
        }

        .box-p-sec {
          margin-top: 0.25rem;
          font-size: 0.26rem;
          line-height: 0.4rem;
          color: #1a1a1a;
          span,
          em {
            font-size: 0.22rem;
            line-height: 0.3rem;
          }
          span {
            margin-left: 0.43rem;
            padding: 1px 0.12rem;
            color: #898989;
            border-radius: 0.15rem;
            &.tag {
              margin-left: 0.42rem;
              background: rgba(216, 216, 216, 0.3);
            }
          }
          .success {
            color: #08bca0;
          }
          .warning {
            color: #ff6a17;
          }
        }
      }
    }
  }

  .index-box {
    margin-top: .25rem;
    background-color: #fff;

    .index-title {
      padding: 0.18rem 0 0.2rem 0.3rem;
      font-family: PingFangSC-Medium;
      font-size: 0.32rem;
      color: #414042;
      text-align: left;
      line-height: 0.42rem;
    }
    .tab-wrap {
      margin: 0.12rem 0;
      padding: 0 9.7%;
    }

    .legendBoxMixin() {
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      line-height: 0.33rem;
    }

    .legend-box {
      .legendBoxMixin();
      display: flex;
      margin-top: 0.34rem;
      padding: 0 0.3rem;
      font-size: 0.24rem;
      color: #6d6e71;

      > li {
        display: flex;
        flex: 1;
        i.line {
          display: block;
          margin-top: 0.105rem;
          margin-right: 0.2rem;
          width: 0.3rem;
          height: 0.12rem;
          background: url('../img/blue.png') no-repeat center center;
          background-size: 100% 100%;
        }

        .title {
          .legendBoxMixin();
          margin-bottom: 0.06rem;
        }

        .val {
          height: 0.3rem;
          line-height: 0.3rem;
          font-family: Helvetica-Condensed-Bold;
          font-size: 0.24rem;
          color: #414042;
        }
      }
    }

    .weight-anlysisi-chart {
      height: 3.8rem;
    }

    &.each-area-compl-situ-wrap {
      .current-performance {
        margin: 0.5rem 0 0.3rem 0.56rem;
        font-size: 0.24rem;
        .title {
          margin-right: 0.2rem;
          font-family: PingFangSC-Regular;
          color: #6d6e71;
          line-height: 0.33rem;
        }
        .val {
          color: #414042;
          line-height: 0.3rem;
          font-family: Helvetica-Condensed-Bold;
        }
      }

      .each-area-compl-situ--chart {
        height: 4.8rem;
      }
    }
  }
}
