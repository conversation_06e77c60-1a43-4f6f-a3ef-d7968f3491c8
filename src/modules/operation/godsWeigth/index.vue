<template>
  <div class="content-wrapper" ref="contentWrapper">
    <div>
      <div class="calender-bar">
        <CalenderBar
          type="date"
          @onChange="dateChange"
          :value="dateValue"
          :min="dateRange(true)"
          :max="dateRange()"
        ></CalenderBar>
      </div>

      <div class="index-box-content">
        <div class="content-box">
          <!-- 左侧 -->
          <div class="left-box box">
            <h3>总体货量(T)</h3>
            <div class="index-value">
              <p class="box-p">{{ null | thousands }}</p>
            </div>

            <h5>进港货量(T)</h5>
            <p class="box-p-sec">
              {{ vm.max_income | thousands }}
              <span class="tag">占比{{ null }}</span>
            </p>

            <h5>进港货量(T)</h5>
            <p class="box-p-sec">
              {{ null | thousands }}
              <span class="tag">占比{{ null }}</span>
            </p>
          </div>

          <!-- 右侧 -->
          <div class="right-box box">
            <h3>当月流转货量(T)</h3>

            <div class="index-value">
              <p class="box-p">
                {{
                null | thousands
                }}
              </p>
            </div>

            <h5>环比、同比</h5>
            <p class="box-p-sec">
              <em :class="setClassByValue(-1)">{{ null | hasVal }}%</em>、
              <em :class="setClassByValue(1)">{{ null | hasVal }}%</em>
            </p>

            <h5>日均货量(T)</h5>
            <p class="box-p-sec">{{ null | thousands }}</p>
          </div>
        </div>
      </div>

      <div class="index-box">
        <h5 class="index-title">整体操作货量分析</h5>
        <div class="tab-wrap">
          <MultiTab
            @tabSelect="switchWeightType"
            :tabData="weightAnysisTabData"
            :tabIndex="weightAnysisTabIndex"
          ></MultiTab>
        </div>

        <ul class="legend-box">
          <li>{{legendVM.date}}</li>
          <li>
            <i class="line"></i>
            <div>
              <h6 class="title">货量（T）</h6>
              <p class="val">{{legendVM.godsWeight | thousands}}</p>
            </div>
          </li>
          <li></li>
        </ul>

        <div class="chart-box weight-anlysisi-chart"></div>
      </div>

      <div class="index-box each-area-compl-situ-wrap">
        <h5 class="index-title">各省区当月完成情况</h5>
        <ul class="current-performance">
          <li>
            <span class="title">货量(T)</span>
            <span class="val">{{areaWeight | hasVal}}</span>
          </li>
        </ul>
        <HScroll :width="barChartWidth">
          <div class="each-area-compl-situ--chart"></div>
        </HScroll>
      </div>
    </div>
  </div>
</template>

<script>
import sxMonitorMixins from '../../sxMonitor/sxMonitor.js'
import MultiTab from '../../sxMonitor/components/multiTab'
import CalenderBar from '../../sxMonitor/components/calenderBar'
import { timeTable } from '../../sxMonitor/common/ulit'
import selfMixin from '../self'

export default {
  components: { MultiTab, CalenderBar },
  mixins: [sxMonitorMixins, selfMixin],
  props: {
    zoneCode: {
      type: [String]
    },
    zoneLevel: {
      type: [String, Number]
    },
    rightName: {
      type: String
    },
    typeName: {
      type: String
    },
    isShowTab: {
      type: Boolean
    },
    defaultDate: {
      type: String
    }
  },
  data() {
    return {
      dateValue: this.defaultDate,
      weightAnysisTabIndex: 0,
      weightAnysisTabData: [
        { label: '近八日货量', value: 'day' },
        { label: '月度货量', value: 'month' }
      ],
      vm: {},
      legendVM: {
        date: '',
        godsWeight: ''
      },
      barChartWidth: '100vw',
      areaWeight: null
    }
  },

  watch: {
    zoneCode() {
      this.init()
    }
  },

  mounted() {
    this.init()
  },

  computed: {
    // T-1
    t1() {
      const t = this.$moment()
      if (this.isSameDay(t, this.dateValue)) {
        return t.add(-1, 'd').format('YYYY-MM-DD')
      }
      return this.$moment(this.dateValue).format('YYYY-MM-DD')
    }
  },

  methods: {
    init() {
      this.switchWeightType(this.weightAnysisTabIndex)
      this.fetchEachAreaComplSituData()
      this.initBS(this.$refs.contentWrapper)
    },

    // 时间改变
    dateChange(value) {
      this.dateValue = value
      this.init()
    },

    // 日期可选范围
    dateRange(min) {
      if (min) {
        const yesterday = this.$moment()
          .add(-1, 'd')
          .add(-1, 'M')
        const y = yesterday.year()
        const m = yesterday.month()
        return new Date(y, m, 1)
      }
      return new Date()
    },

    // 根据数值设置相应的css calss
    setClassByValue: value => (value <= 0 ? 'warning' : 'success'),

    // 货量分析tab
    switchWeightType(index) {
      this.weightAnysisTabIndex = index
      if (index) {
        this.fetchMonthlyIncomeData()
      } else {
        this.fetchRecentEightDaysIncomeData()
      }
    },

    // 完成情况头部详细
    setLegendView(axisValue = new Date(), godsWeight = 0, date = 'month') {
      Object.assign(this.legendVM, {
        date: this.$moment(axisValue).format(
          date === 'month' ? 'YYYY年MM月' : 'YYYY年MM月DD日'
        ),
        godsWeight: Number((godsWeight / 1000).toFixed(1))
      })
    },

    // 获取近八日货量分析数据
    fetchRecentEightDaysIncomeData() {
      const dayTable = timeTable(this.t1, 8, -7, '-')
      const [startDay] = dayTable
      const endDay = dayTable[dayTable.length - 1]
      const level = this.zoneLevel
      const conditionList = [
        { key: 'startDay', value: this.$moment(startDay).format('YYYYMMDD') },
        { key: 'endDay', value: this.$moment(endDay).format('YYYYMMDD') },
        { key: 'zoneLevel', value: level },
        { key: this.roleKeyMap[level], value: this.zoneCode }
      ]
      this.sendTwoDimenRequest('sx_manage_monit_eight_days_info', conditionList)
        .then(res => {
          const { obj = [] } = res
          const serie1 = []
          const seriesData = [serie1]
          // 过滤&组织数据
          const filterResult = obj.reduce((ret, item) => {
            const { consigned_tm } = item
            const isExist = ret.some(one => one.consigned_tm === consigned_tm)
            if (!isExist) {
              ret.push(item)
            }
            return ret
          }, [])
          // 排序
          filterResult.sort(
            (a, b) => new Date(a.consigned_tm) - new Date(b.consigned_tm)
          )
          dayTable.forEach((dateItem, index) => {
            const match = filterResult.find(item =>
              this.isSameDay(item.consigned_tm, dateItem)
            )
            const { income = index * 1000 } = match || {}
            serie1.push([dateItem, income])
          })
          const lastIndex = filterResult.length - 1
          const lastItem = filterResult[lastIndex <= 0 ? 0 : lastIndex] || {}
          this.setLegendView(lastItem.consigned_tm, lastItem.income)
          this.recentEightDaysIncomeChart(seriesData, dayTable)
        })
        .catch(err => {
          this.$toast({
            duration: 2000,
            message: '请求近八日收入数据错误'
          })
          console.error('请求近八日收入数据错误', err)
        })
    },

    // 近八日货量分析图表
    recentEightDaysIncomeChart(seriesData = [], xAxisData) {
      const options = {
        xAxisData,
        seriesData,
        tooltipFormatter: params => {
          const [one] = params
          this.setLegendView(one.axisValue, one.data[1] || 0, 'day')
        },
        xAxisLabelFormatter(val, index) {
          return `${val.slice(5, 7)}/${val.slice(8)}`
        }
      }
      this.drawWeightAnysisChart(options)
    },

    // 获取月度货量分析数据
    fetchMonthlyIncomeData() {
      const monthTable = timeTable(this.t1, 12, -11, '-', 'M')
      const endMonth = monthTable[11]
      const startMonth = monthTable[0]
      const level = this.zoneLevel
      const conditionList = [
        { key: 'startDay', value: startMonth },
        { key: 'endDay', value: endMonth },
        { key: 'zoneLevel', value: level },
        { key: this.roleKeyMap[level], value: this.zoneCode }
      ]
      this.sendTwoDimenRequest('sx_manage_monit_august_info', conditionList)
        .then(data => {
          const { obj = [] } = data
          const lastIndex = obj.length - 1
          const lastItem = obj[lastIndex <= 0 ? 0 : lastIndex] || {}
          const serie1 = []
          const seriesData = [serie1]
          // 如果返回数据大于12条，则用后12条数据
          const sliceStart = lastIndex <= 11 ? 0 : -12
          const validData = obj.slice(sliceStart)
          monthTable.forEach((dateItem, index) => {
            const match = validData.find(item =>
              this.isSameDay(item.consigned_tm, dateItem)
            )
            const { income = index * 1000 } = match || {}
            serie1.push([dateItem, income])
          })
          this.setLegendView(lastItem.consigned_tm, 112)
          this.drawMonthlyIncomeChart(seriesData, monthTable)
        })
        .catch(err => {
          this.$toast({
            duration: 2000,
            message: '请求月度收入数据错误'
          })
          console.error(`请求月度收入数据错误`, err)
        })
    },

    // 月度货量分析-图表
    drawMonthlyIncomeChart(seriesData, xAxisData) {
      const options = {
        xAxisData,
        seriesData,
        tooltipFormatter: params => {
          const [one] = params
          this.setLegendView(one.axisValue.slice(0, 7), one.data[1])
        },
        xAxisLabelFormatter(val) {
          return `${Number(val.slice(5, 7))}月`
        }
      }
      this.drawWeightAnysisChart(options)
    },

    // 货量分析图表
    drawWeightAnysisChart(props) {
      const {
        xAxisData = [],
        seriesData = [],
        tooltipFormatter,
        xAxisLabelFormatter
      } = props
      const [serie1] = seriesData
      const seriesLine = (data, color) => ({
        type: 'line',
        data: data,
        showSymbol: false,
        itemStyle: { color },
        lineStyle: {
          shadowColor: color,
          shadowOffsetX: 0,
          shadowOffsetY: 8,
          shadowBlur: 12
        }
      })
      const option = {
        tooltip: {
          show: true,
          padding: [5, 15, 5, 15],
          trigger: 'axis',
          formatter: tooltipFormatter
        },
        grid: { top: '25', left: '10', right: '10', bottom: '60' },
        xAxis: [
          {
            type: 'category',
            data: xAxisData,
            axisTick: { show: false },
            axisLabel: {
              formatter: xAxisLabelFormatter,
              show: true,
              interval: 0,
              textStyle: {
                color: '#939393'
              }
            },
            axisPointer: {
              type: 'shadow',
              color: '#5594F2',
              value: xAxisData[xAxisData.length - 1]
            },
            axisLine: { show: false }
          }
        ],
        yAxis: { show: false },
        series: [seriesLine(serie1, '#2E55EC')]
      }

      this.drawChart(document.querySelector('.weight-anlysisi-chart'), option)
    },

    // 获取省当月指标完成情况图表
    fetchEachAreaComplSituData() {
      const zoneLevel = Number(this.zoneLevel)
      const nextOrgLevel = zoneLevel === 0 ? 2 : zoneLevel + 1
      const zoneCode = this.zoneCode
      const incDay = this.$moment(this.dateValue).format('YYYYMMDD')
      const common = {
        groupByCode: 'province_area_code',
        groupByName: 'province_area_name',
        incDay,
        zoneLevel: nextOrgLevel
      }
      if (nextOrgLevel === 2) {
        Object.assign(common, {
          qw_code: '001'
        })
      }
      if (nextOrgLevel === 3) {
        Object.assign(common, {
          groupByCode: 'area_code',
          groupByName: 'area_name',
          province_area_code: zoneCode
        })
      }
      if (nextOrgLevel === 4) {
        Object.assign(common, {
          groupByCode: 'dept_code',
          groupByName: 'dept_code',
          area_code: zoneCode
        })
      }
      const conditionList = this.formatParams(common)
      this.sendTwoDimenRequest('sx_manage_monit_info', conditionList)
        .then(res => {
          const { obj = [] } = res
          const zoneNameKey = 'groupByName'
          const zoneCodeKey = 'groupByCode'
          const complSituChartData = []
          const areaIndexVM = {}
          obj.forEach(item => {
            const businessId = item[zoneCodeKey]
            // 过滤重复业务区数据
            const isExist = complSituChartData.some(
              one => one.businessId === businessId
            )
            if (isExist) {
              return
            }
            const nameExp = /^SX/g
            // 删除前缀 过滤不合法名称数据
            const zoneName = (item[zoneNameKey] || '')
              .trim()
              .replace(nameExp, '')
            if (zoneName === 'N') {
              return
            }
            complSituChartData.push({
              zoneName,
              weight: item.weight_achive_rate
            })
          })
          this.areaIndexVM = areaIndexVM
          this.complSituChartData = complSituChartData
          this.drawEachAreaComplSituChart()
        })
        .catch(err => {
          console.log('err', err)
        })
    },

    // 省当月指标完成情况图表
    drawEachAreaComplSituChart() {
      const catoryData = []
      const seriesData = []
      const complSituChartData = this.complSituChartData
      if (!complSituChartData.length) {
        Array.apply(null, { length: 12 }).forEach((item, index) => {
          const value = Math.random()
          complSituChartData.push({
            zoneName: '地区名' + index,
            weight: Math.random() * 1000,
            value
          })
        })
      }
      const len = complSituChartData.length - 6
      // 每个bar的宽度，会根据name的长度计算
      let perBarWidth = 20
      complSituChartData.forEach((item, index) => {
        const { zoneName, weight, value } = item
        if (!zoneName) {
          return
        }
        perBarWidth = zoneName.length * 4
        seriesData.push([zoneName, value, weight])
        catoryData.push(zoneName)
      })
      this.barChartWidth = `${100 + (len > 0 ? len : 0) * perBarWidth}vw`

      const option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            color: '#5594F2'
          },
          formatter: params => {
            const [fristParams = {}] = params
            this.areaWeight = fristParams.value[2].toFixed(1)
          }
        },
        grid: {
          top: '10%',
          left: '2%',
          right: '2%',
          bottom: '15%'
          // containLabel: true
        },
        xAxis: [
          {
            data: catoryData,
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#939393'
              },
              interval: 0
            },
            axisLine: {
              show: false
            }
          }
        ],
        yAxis: {
          show: false
        },
        series: [
          {
            name: '完成率',
            type: 'bar',
            barWidth: 10,
            data: seriesData,
            label: {
              show: true,
              position: 'top',
              formatter: params => {
                const v = params.data[1]
                return `${(v * 100).toFixed(1)}%`
              }
            },
            itemStyle: {
              barBorderRadius: 5,
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#27AEFF' },
                { offset: 1, color: '#2E55EC' }
              ])
            }
          }
        ]
      }
      const chartsWrap = document.querySelector('.each-area-compl-situ--chart')
      this.drawChart(chartsWrap, option)
    }
  }
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>
