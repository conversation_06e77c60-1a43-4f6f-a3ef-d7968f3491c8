/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-11-29 16:43:08
 * @Description:
 */
export default [

  {
    path: '/sxMonitor/onworkEfficiency',
    name: 'onworkEfficiency',
    component: 'operation/transferWeight/onworkEffeciency',
    params: {
      title: '中转场出勤效能',
      pageLevel: '2'
    }
  }
  //   {
  //     path: '/sxMonitor/countyRange',
  //     name: 'sxCountyRange',
  //     component: 'operation/coverage/countyRange/index',
  //     params: {
  //       title: '覆盖范围',
  //       pageLevel: '2'
  //     }
  //   },
  //   {
  //     path: '/sxMonitor/coverageCapability',
  //     name: 'sxCoverageCapability',
  //     component: 'operation/coverage/coverageCapability/county',
  //     params: {
  //       title: '覆盖能力',
  //       pageLevel: '2'
  //     }
  //   },
  //   {
  //     path: '/sxMonitor/areaDetail',
  //     name: 'sxAreaDetail',
  //     component: 'operation/netManage/cargo/areaDetail',
  //     params: {
  //       title: '区域货量情况',
  //       pageLevel: '2'
  //     }
  //   },
  //   {
  //     path: '/sxMonitor/netDetail',
  //     name: 'sxNetDetail',
  //     component: 'operation/netManage/cargo/netDetail',
  //     params: {
  //       title: '网管货量情况',
  //       pageLevel: '2'
  //     }
  //   }
]
