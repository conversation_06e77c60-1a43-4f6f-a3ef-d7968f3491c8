<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-09-18 15:09:49
 * @LastEditTime: 2021-09-26 17:46:49
 * @Description:
-->
<template>
  <div>
    <CardList title="运单时效达成监控">
      <ShowTipIcon
        slot="tip"
        @btnShowTip="btnShowTip($event, 'yy-gh-ydsxdcjk')"
        :isFeedback="isFeedback"
        @feedback="btnFeedback"
        :explaindDetail='explaindDetail'
      >
      </ShowTipIcon>
      <Tabs slot="nav" :options="tabData" :tabIndex="tabIndex" @tabSelect="tabSelect"></Tabs>
      <div class="pd_lr20">
        <div class="fw700 fs28 mt32">运单时效达成监控</div>
        <div class="flex_center">
          <div style="width: 2.4rem; height: 2rem" ref="chart-gauge-rate"></div>
          <div class="flex1">
            <MultiDataList class="data_list_custom mt12" :dataSource="dataSource" :columns="columns"></MultiDataList>
          </div>
        </div>
        <div class="fw700 fs28 mt64">近八日运单时长达成率</div>
        <HScroll :width="trendWidth" class="mt32">
          <div ref="chart-trend-eight" style="height: 3rem"></div>
        </HScroll>
        <div class="mt64 flex_between">
          <div class="fw700 fs28">各{{ subTabData[subTabIndex] }}运单时长达成率</div>
          <Tabs
            v-if="subTabData.length > 1"
            :options="subTabData"
            :tabIndex="subTabIndex"
            @tabSelect="subTabSelect"
          ></Tabs>
        </div>
        <HScroll :width="nextWidth" class="mt32">
          <div ref="chart-bar-next" style="height: 3rem"></div>
        </HScroll>
      </div>
    </CardList>
  </div>
</template>
<script>
import { drawGaugeChart, drawBarChart } from 'common/charts/chartOption'
import { yundanColumns } from './config'
import request from './request'
export default {
  mixins: [request],
  props: ['dateValue', 'overData', 'nextData', 'levelData'],
  data() {
    return {
      tabData: ['日', '周', '月'],
      tabIndex: 0,
      dataSource: [],
      columns: yundanColumns,
      trendWidth: '100%',
      subTabIndex: 0,
      nextWidth: '100%'
    }
  },
  computed: {
    subTabData() {
      switch (this.zoneLevel) {
        case '30':
          return ['战区']
        case '31':
          return ['战区', '省区']
        case '32':
          return ['省区', '区域']
        case '33':
          return ['区域']
      }
      return []
    },
    key_date_type() {
      return ['td', 'wtd', 'mtd'][this.tabIndex]
    },
    key_hb_type() {
      return ['dod', 'wow', 'mom'][this.tabIndex]
    },
    key_date() {
      return 'inc_day'
    }
  },
  watch: {
    zoneCode() {
      this.initOptions()
      if (!['31', '32'].includes(this.zoneLevel)) {
        this.subTabIndex = 0
      }
    },
    dateValue() {
      this.initOptions()
    },
    overData(val) {
      this.initGaugeChart(val)
      this.setDataSource(val)
    },

    nextData(val) {
      if (+this.zoneLevel === 30) {
        this.initNextChart(val)
      }
    },
    levelData(val) {
      if (this.zoneLevel > 30) {
        this.subTabSelect(this.subTabIndex)
      }
    }
  },
  methods: {
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
      this.initOptions()
    },
    subTabSelect(index) {
      this.subTabIndex = index
      if (!index) {
        this.initNextChart(this.levelData)
      } else {
        this.initNextChart(this.nextData)
      }
    },
    initGaugeChart(result) {
      let num = ''
      if (result.length) {
        num = result[0][`duration_reach_rate_${this.key_date_type}`]
      }
      const option = {
        series: [
          {
            center: ['50%', '65%'],
            radius: '100%',
            axisLine: {
              lineStyle: {
                color: [[1, '#f8f9fc']]
              }
            }
          },
          {
            center: ['50%', '65%'],
            radius: '100%',
            axisLine: {
              lineStyle: {
                color: [[], [num]]
              }
            },
            detail: {
              show: true,
              formatter: [`{value|${this.$numToPercent(num)}}`, `{name|${'达成率'}}`].join('\n')
            }
          }
        ]
      }

      drawGaugeChart(option, this.$refs['chart-gauge-rate'], num >= 1 ? 0 : 1)
    },
    setDataSource(result) {
      this.dataSource = []
      this.columns[2].parent[0].label = `${this.tabData[this.tabIndex]}环比`
      const key = 'avg_waybill'
      this.columns[0].parent[0].dataIndex = `${key}_plan_hr_${this.key_date_type}`
      this.columns[1].parent[0].dataIndex = `${key}_hr_${this.key_date_type}`
      this.columns[2].parent[0].dataIndex = `${key}_hr_${this.key_hb_type}`
      if (result.length) {
        this.dataSource = result[0]
      }
    },
    // 日趋势
    async getTrendOverData() {
      const { obj } = await this._getOverData({
        start_day: this.startDay,
        end_day: this.incDay,
        dept_code: this.zoneCode
      })
      this.initTrendChart(obj)
    },
    // 周月趋势
    async getWeekMonthTrendData() {
      const { obj } = await this._getWeekMonthTrendData({
        inc_day: this.incDay,
        date_flag: ['', 'week', 'month'][this.tabIndex],
        dept_code: this.zoneCode
      })
      this.initTrendChart(obj)
    },
    initTrendChart(obj) {
      const xData = []
      const sData = []
      const options = {
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.tabIndex, true, 'GGGGMMDD')
            }
          },
          {
            data: sData,
            axisLabel: {
              formatter: value => this.$numToPercent(value)
            }
          }
        ],
        series: [
          {
            data: sData
          }
        ]
      }
      if (obj.length) {
        const result = JSON.parse(JSON.stringify(obj))
        const key = this.tabIndex ? 'duration_reach_rate' : `duration_reach_rate_${this.key_date_type}`
        this.$objectSortUp(result, this.key_date)
        result.forEach((item, index) => {
          xData.push(item['inc_day'])
          sData.push({
            value: item[key],
            itemStyle: {
              color: item[key] >= 1 ? '#4C83F9' : '#dc1e32'
            }
          })
        })
      }
      drawBarChart(options, this.$refs['chart-trend-eight'])
      this.trendWidth = this.$hScrollWidth(xData.length, 6)
    },
    initNextChart(obj) {
      const result = JSON.parse(JSON.stringify(obj))
      const xData = []
      const sData = []
      const options = {
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$valueFormat(value, true)
            }
          },
          {
            data: sData,
            axisLabel: {
              formatter: value => this.$numToPercent(value)
            }
          }
        ],
        series: [
          {
            data: sData
          }
        ]
      }
      if (result.length) {
        const key = `duration_reach_rate_${this.key_date_type}`
        this.$objectSortDown(result, key)
        result.forEach((item, index) => {
          xData.push(item['dept_name'])
          sData.push({
            value: item[key],
            itemStyle: {
              color: item[key] >= 1 ? '#4C83F9' : '#dc1e32'
            }
          })
        })
      }
      drawBarChart(options, this.$refs['chart-bar-next'], [], true)
      this.nextWidth = this.$hScrollWidth(xData.length, 6)
    },
    initOptions() {
      if (!this.tabIndex) {
        this.getTrendOverData()
      } else {
        this.getWeekMonthTrendData()
      }
    },
    initData() {
      this.initGaugeChart(this.overData)
      this.setDataSource(this.overData)
      if (+this.zoneLevel === 30) {
        this.initNextChart(this.nextData)
      } else {
        if (!this.subTabIndex) {
          this.initNextChart(this.levelData)
        } else {
          this.initNextChart(this.nextData)
        }
      }
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang='less' scoped>
.data_list_custom {
  background-color: #fff !important;
}
</style>
