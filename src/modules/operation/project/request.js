/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-03-20 15:56:13
 * @Description:
 */
import mixins from './mixins'
export default {
  mixins: [mixins],
  data() {
    return {}
  },
  computed: {
    defaultData() {
      return {
        // inc_day: this.incDay,
        level_code: this.zoneLevel
        // dept_code:'001',
        // upper_dept_code:'001'
      }
    }
  },
  methods: {
    // 页面全部指标(包含日趋势)
    _getOverData(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_qc_dept_level_dwm_sum_di', this.forMapData(tmp))
    },
    // 趋势(周,月)
    _getWeekMonthTrendData(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_qc_dept_level_weekmonth_sum_di', this.forMapData(tmp))
    }
  }
}
