/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-01-11 11:29:30
 * @Description:
 */
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
export default {
  mixins: [requestMixins],
  components: {},
  data() {
    return {}
  },
  computed: {
    incDay() {
      return this.isDev ? '20210917' : this.$moment(this.dateValue).format('YYYYMMDD')
    },
    startDay() {
      return this.$moment(this.incDay)
        .subtract(7, 'days')
        .format('YYYYMMDD')
    },
    zoneLevel() {
      return this.zoneData.zoneLevel
    },
    zoneCode() {
      return this.zoneData.zoneCode
    },
    levelName() {
      return this.zoneData.levelName
    },
    ...mapGetters({
      zoneData: 'zoneData'
    }),
    ...mapState({
      isDev: state => state.isDev,
      holidayData: state => state.holidayData
    })
  },
  methods: {}
}
