export const yundanColumns = [
  {
    parent: [
      {
        label: '规划时长(H)',
        int: [1]
      }
    ]
  },
  {
    parent: [
      {
        label: '实际时长(H)',
        int: [1]
      }
    ]
  },
  {
    parent: [
      {
        label: '日环比',
        per: [1],
        indexType: 'down'
      }
    ]
  }
]
export const detailTimeColumns = [
  {
    parent: [
      {
        label: '平均中转时长(H)',
        int: [1]
      }
    ]
  },
  {
    parent: [
      {
        label: '日环比',
        per: [1],
        indexType: 'down'
      }
    ]
  }
]
