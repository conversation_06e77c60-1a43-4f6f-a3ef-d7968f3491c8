<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-07-23 13:43:10
 * @LastEditTime: 2024-05-13 13:59:14
 * @Description:规划
-->
<template>
  <div class="page_bgc">
    <div class="date_container flex_between">
      <KyDatePicker type="day" :dateValue="dateValue" @onChange="dateChange" :holidayData="holidayData"></KyDatePicker>
    </div>
    <PageContent>
      <YunDanControl
        :dateValue="dateValue"
        :overData="overData"
        :nextData="nextData"
        :levelData="levelData"
      ></YunDanControl>
      <DetailTimeControl
        class="mt24"
        :dateValue="dateValue"
        :overData="overData"
        :nextData="nextData"
        :levelData="levelData"
      ></DetailTimeControl>
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>
<script>
import YunDanControl from './yunDanControl.vue'
import DetailTimeControl from './detailTimeControl.vue'
import request from './request'

export default {
  mixins: [request],
  components: {
    YunDanControl,
    DetailTimeControl
  },
  data() {
    return {
      dateValue: '',
      overData: [],
      nextData: [],
      levelData: []
    }
  },
  computed: {},
  watch: {
    dateValue() {
      this.initOptions()
    },
    zoneCode() {
      this.initOptions()
    },
    overData(val) {
      if (this.zoneLevel > 30) {
        this.getLevelOverData(val)
      }
    }
  },
  methods: {
    dateChange(date) {
      this.dateValue = this.$moment(date).format('YYYYMMDD')
    },
    async getOverData() {
      const { obj } = await this._getOverData({
        inc_day: this.incDay,
        dept_code: this.zoneCode
      })
      this.overData = obj
    },

    async getNextOverData() {
      const { obj } = await this._getOverData({
        inc_day: this.incDay,
        level_code: String(+this.zoneLevel + 1),
        upper_dept_code: this.zoneCode
      })
      this.nextData = obj
    },
    // 取同层级的数据(战区,省区,区域)
    async getLevelOverData(data) {
      const upper_dept_code = data.length ? data[0].upper_dept_code : ''
      const { obj } = await this._getOverData({
        inc_day: this.incDay,
        level_code: this.zoneLevel,
        upper_dept_code
      })
      this.levelData = obj
    },

    initOptions() {
      this.getOverData()
      this.getNextOverData()
    }
  },

  created() {
    this.dateValue = this.$moment()
      .subtract(2, 'days')
      .format('YYYYMMDD')
  }
}
</script>
<style lang="less" scoped>
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding-left: 0.2rem;
}
</style>
