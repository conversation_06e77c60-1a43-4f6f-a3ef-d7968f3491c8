<!--
 * @Author: gilshi
 * @Date: 2021-09-22 11:16:45
 * @LastEditTime: 2021-09-27 10:26:17
 * @Description: 各环节时长监控
-->
<template>
  <div>
    <CardList title="各环节时长监控">
      <ShowTipIcon
        slot="tip"
        @btnShowTip="btnShowTip($event, 'yy-gh-ghjscjk')"
        :isFeedback="isFeedback"
        @feedback="btnFeedback"
        :explaindDetail="explaindDetail"
      >
      </ShowTipIcon>
      <Tabs slot="nav" :options="tabData" :tabIndex="tabIndex" @tabSelect="tabSelect"></Tabs>
      <div class="pd_lr20">
        <BtnTabs class="mt24" :options="btnData" :activeIndex="btnIndex" :column="4" @btnConfirm="btnConfirm"></BtnTabs>
        <MultiDataList class="mt24" :dataSource="dataSource" :columns="columns"></MultiDataList>
        <div class="mt64 flex_between">
          <div class="fw700 fs28">{{ subTabData[subTabIndex] }}{{ btnData[btnIndex] }}</div>
          <Tabs
            v-if="subTabData.length > 1"
            :options="subTabData"
            :tabIndex="subTabIndex"
            @tabSelect="subTabSelect"
          ></Tabs>
        </div>
      </div>
      <NormalTable
        class="mt24"
        size="small"
        isIndicator
        :width="tableWidth"
        :dataSource="tableDataSource"
        :columns="tableColumns"
      >
      </NormalTable>
      <BtnShowMore
        v-show="isShowMore"
        class="pd_lr20 mt24"
        :textList="['查看更多', '收起']"
        @slideBtnChange="slideBtnChange"
      ></BtnShowMore>
    </CardList>
  </div>
</template>
<script>
import { detailTimeColumns } from './config'
import request from './request'
export default {
  props: ['overData', 'nextData', 'levelData'],
  mixins: [request],
  data() {
    return {
      tabData: ['日', '周', '月'],
      tabIndex: 0,
      btnData: ['中转时长', '运行时长', '交货时长', '提配时长'],
      btnIndex: 0,
      dataSource: [],
      columns: detailTimeColumns,
      subTabIndex: 0,
      tableDataSource: [],
      tableColumns: [],
      tableWidth: '100%',
      isMore: false
    }
  },
  computed: {
    subTabData() {
      switch (this.zoneLevel) {
        case '30':
          return ['战区']
        case '31':
          return ['战区', '省区']
        case '32':
          return ['省区', '区域']
        case '33':
          return ['区域']
      }
      return []
    },
    key_date_type() {
      return ['td', 'wtd', 'mtd'][this.tabIndex]
    },
    key_hb_type() {
      return ['dod', 'wow', 'mom'][this.tabIndex]
    },
    key_value() {
      return ['avg_actual_trans', 'avg_actual_run', 'avg_actual_pickup', 'avg_actual_delivery'][this.btnIndex]
    },
    isShowMore() {
      if (+this.zoneLevel === 30) {
        return this.nextData.length > 5
      }
      if (!this.subTabIndex) {
        return this.levelData.length > 5
      }
      return this.nextData.length > 5
    }
  },
  watch: {
    zoneCode() {
      if (!['31', '32'].includes(this.zoneLevel)) {
        this.subTabIndex = 0
      }
    },
    overData(val) {
      this.setDataSource(val)
    },
    nextData(val) {
      if (+this.zoneLevel === 30) {
        this.setTable(val)
      }
    },
    levelData(val) {
      if (this.zoneLevel > 30) {
        this.subTabSelect(this.subTabIndex)
      }
    }
  },
  methods: {
    tabSelect(index) {
      this.tabIndex = index
      this.setDataSource(this.overData)
      this.initTable()
    },
    btnConfirm({ index }) {
      this.btnIndex = index
      this.setDataSource(this.overData)
      this.initTable()
    },
    subTabSelect(index) {
      this.subTabIndex = index
      if (!index) {
        this.setTable(this.levelData)
      } else {
        this.setTable(this.nextData)
      }
    },
    setDataSource(result) {
      this.columns = JSON.parse(JSON.stringify(detailTimeColumns))
      this.columns[0].parent[0].label = `平均${this.btnData[this.btnIndex]}(H)`
      this.columns[1].parent[0].label = `${this.tabData[this.tabIndex]}环比`
      this.columns[0].parent[0].dataIndex = `${this.key_value}_hr_${this.key_date_type}`
      this.columns[1].parent[0].dataIndex = `${this.key_value}_hr_${this.key_hb_type}`
      this.dataSource = []
      if (result) {
        this.dataSource = result[0]
      }
    },
    setTable(result) {
      this.tableDataSource = []
      this.tableColumns = [
        {
          label: '排名',
          width: '1rem',
          render: (h, value, res, index) => {
            return <div class={'default-rank'}>{result.length - index}</div>
          }
        },
        {
          label: `${this.subTabData[this.subTabIndex]}`,
          dataIndex: 'dept_name',
          width: '2.2rem',
          render: (h, value) => {
            return this.$valueFormat(value)
          }
        },
        {
          label: `平均${this.btnData[this.btnIndex]}`.slice(0, 4),
          dataIndex: `${this.key_value}_hr_${this.key_date_type}`,
          render: (h, value) => {
            return this.$numToInteger(value)
          }
        },
        {
          label: `${this.tabData[this.tabIndex]}环比`,
          dataIndex: `${this.key_value}_hr_${this.key_hb_type}`,
          render: (h, value) => {
            return <div class={value > 0 ? 'orange' : 'green'}>{this.$numToPercent(value)}</div>
          }
        }
      ]
      if (result.length) {
        const obj = JSON.parse(JSON.stringify(result))
        this.$objectSortDown(obj, `${this.key_value}_hr_${this.key_hb_type}`)
        this.tableDataSource = this.isMore ? obj : obj.slice(0, 5)
      }
    },
    slideBtnChange(isMore) {
      this.isMore = isMore
      this.initTable()
    },
    initTable() {
      if (+this.zoneLevel === 30) {
        this.setTable(this.nextData)
      } else {
        if (!this.subTabIndex) {
          this.setTable(this.levelData)
        } else {
          this.setTable(this.nextData)
        }
      }
    }
  },
  mounted() {}
}
</script>
<style lang='less' scoped>
</style>
