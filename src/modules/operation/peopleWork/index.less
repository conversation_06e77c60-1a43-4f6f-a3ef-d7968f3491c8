.content-wrapper {
  height: calc(100vh - 2.64rem);
  overflow-y: hidden;
}

.calender-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.3rem;
  height: 1rem;
}

.index-box-content {
  display: flex;
  margin: 0 0.25rem;
  padding: 0 0 0.3rem;
  box-shadow: 0 0 0.1rem 0.06rem rgba(221, 221, 221, 0.47);
  border-radius: 0.08rem;
  background-color: #fff;
  z-index: 2;

  > div {
    flex: 1;
    position: relative;
    font-family: PingFangSC-Regular;
    font-size: 0.24rem;
    &::after {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      right: 0;
      height: 98%;
      border-right: 1px solid #eee;
    }
    &:last-child::after {
      border: none;
    }

    .title {
      padding-top: 0.3rem;
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      color: #808285;
      line-height: 0.33rem;
      text-align: center;
    }
    .val {
      margin-top: 0.1rem;
      font-family: Helvetica-Condensed-Bold;
      font-size: 0.48rem;
      color: #414042;
      height: 0.6rem;
      line-height: 0.6rem;
      text-align: center;
    }
  }
}

.index-box {
  margin-top: 0.25rem;
  background-color: #fff;

  .index-title {
    padding: 0.18rem 0 0.2rem 0.3rem;
    font-family: PingFangSC-Medium;
    font-size: 0.32rem;
    color: #414042;
    text-align: left;
    line-height: 0.42rem;
  }
}

.trend-chart-wrap {
  padding-top: 0.19rem;
  .tab-warp {
    margin-bottom: 0.13rem;
    padding: 0 9.7%;
  }
  .legend {
    padding-left: 0.3rem;
    margin-top: 0.3rem;
    .date {
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      color: #6d6e71;
      line-height: 0.33rem;
    }
    .index-details {
      margin-top: 0.3rem;
      display: flex;
      .index-item {
        display: flex;
        flex: 1;
        > img {
          margin-top: 0.09rem;
          width: 0.3rem;
          height: 0.12rem;
        }
        > div {
          margin-left: 0.2rem;
          .title {
            font-family: PingFangSC-Regular;
            font-size: 0.24rem;
            color: #6d6e71;
            line-height: 0.33rem;
          }
          .val {
            margin-top: 0.06rem;
            line-height: 0.3rem;
            font-family: Helvetica-Condensed-Bold;
            font-size: 0.24rem;
            color: #414042;
          }
        }
      }
    }
  }

  .trend--chart {
    height: 3.5rem;
  }
}

.each-area-situ-wrap {
  .tab-wrap {
    margin: 0.12rem 0;
    padding: 0 9.7%;
    /deep/ .multiTab {
      > div {
        border-right: none;
        &:last-child {
          border-right: 1px solid #dc1e32;
        }
      }
    }
  }
  .index-details-list {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 0.51rem;
    margin-bottom: 0.7rem;
    .item {
      position: relative;
      flex: 1;
      text-align: center;
      &::after {
        content: '';
        display: block;
        position: absolute;
        top: 20%;
        right: 0;
        height: 60%;
        width: 1px;
        border-right: 1px solid #eee;
      }
      &:last-child::after {
        border-right-color: transparent;
      }
      .title {
        font-family: PingFangSC-Regular;
        font-size: 0.24rem;
        color: #808285;
        line-height: 0.33rem;
      }
      .val {
        margin-top: 0.1rem;
        font-family: Helvetica-Condensed-Bold;
        font-size: 0.36rem;
        color: #414042;
        line-height: 0.45rem;
      }
    }
  }

  .each-area-situ--chart {
    height: 4.03rem;
  }
}
