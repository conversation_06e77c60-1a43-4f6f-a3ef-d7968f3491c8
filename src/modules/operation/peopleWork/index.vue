<template>
  <div class="content-wrapper" ref="contentWrapper">
    <div>
      <div class="calender-bar">
        <CalenderBar
          type="date"
          @onChange="dateChange"
          :value="dateValue"
          :min="dateRange(true)"
          :max="dateRange()"
        ></CalenderBar>
      </div>

      <div class="index-box-content">
        <div>
          <h4 class="title">人效(T)</h4>
          <div class="val">111</div>
          <h4 class="title">出勤率</h4>
          <div class="val">111</div>
        </div>
        <div>
          <h4 class="title">自有人效(T)</h4>
          <div class="val">111</div>
          <h4 class="title">自有出勤率</h4>
          <div class="val">111</div>
        </div>
        <div>
          <h4 class="title">外包人效(T)</h4>
          <div class="val">111</div>
          <h4 class="title">外包出勤率</h4>
          <div class="val">111</div>
        </div>
      </div>

      <div class="index-box trend-chart-wrap">
        <div class="tab-warp">
          <MultiTab
            @tabSelect="trendTypeTabChange"
            :tabData="trendTypeTabData"
            :tabIndex="trendTypeTabIndex"
          ></MultiTab>
        </div>
        <h5 class="index-title">近8天趋势</h5>
        <div class="legend">
          <div class="date">{{ lengedVM.date ? $moment(lengedVM.date).format('YYYY年MM月DD日') : '-' }}</div>
          <div class="index-details">
            <div class="index-item">
              <img src="../img/blue.png" alt="log" />
              <div>
                <p class="title">总{{ costTypeSelected.label }}</p>
                <p class="val">{{ lengedVM.s1 | hasVal }}</p>
              </div>
            </div>
            <div class="index-item">
              <img src="../img/orange.png" alt="log" />
              <div>
                <p class="title">自有</p>
                <p class="val">{{ lengedVM.s2 | hasVal }}</p>
              </div>
            </div>
            <div class="index-item">
              <img src="../img/green.png" alt="log" />
              <div>
                <p class="title">外包</p>
                <p class="val">{{ lengedVM.s3 | hasVal }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="trend--chart"></div>
      </div>

      <div class="index-box each-area-situ-wrap">
        <h5 class="index-title">各省区当月情况</h5>
        <div class="tab-wrap">
          <MultiTab @tabSelect="costTypeTabChange" :tabData="costTypeTabData" :tabIndex="costTypeTabIndex"></MultiTab>
        </div>
        <div class="index-details-list">
          <div class="item">
            <h4 class="title">排名</h4>
            <p class="val">{{ areaIndexVM[`${costTypeFlag}_${businessId}__rank`] | hasVal }}</p>
          </div>
          <div class="item">
            <h4 class="title">当月累计</h4>
            <p class="val">{{ areaIndexVM[`${costTypeFlag}_${businessId}__sum`] | hasVal }}</p>
          </div>
          <div class="item">
            <h4 class="title">当月累计环比</h4>
            <p class="val">{{ areaIndexVM[`${costTypeFlag}_${businessId}__sum_hb`] | hasVal }}</p>
          </div>
          <div class="item">
            <h4 class="title">环比</h4>
            <p class="val">{{ areaIndexVM[`${costTypeFlag}_${businessId}__hb`] | hasVal }}</p>
          </div>
        </div>
        <HScroll :width="chart1Width">
          <div class="each-area-situ--chart"></div>
        </HScroll>
      </div>
    </div>
  </div>
</template>

<script>
import sxMonitorMixins from '../../sxMonitor/sxMonitor.js'
import selfMixin from '../self'
import CalenderBar from '../../sxMonitor/components/calenderBar'
import MultiTab from '../../sxMonitor/components/multiTab'
import HScroll from '../../sxMonitor/components/hScroll'
import { timeTable } from '../../sxMonitor/common/ulit'

export default {
  mixins: [sxMonitorMixins, selfMixin],
  components: { CalenderBar, MultiTab, HScroll },
  props: {
    zoneCode: {
      type: [String]
    },
    zoneLevel: {
      type: [String, Number]
    },
    rightName: {
      type: String
    },
    typeName: {
      type: String
    },
    isShowTab: {
      type: Boolean
    },
    defaultDate: {
      type: String
    }
  },

  watch: {
    zoneCode() {
      this.init()
    }
  },

  data() {
    return {
      dateValue: this.defaultDate,
      lengedVM: {
        date: '',
        s1: '',
        s2: '',
        s3: ''
      },
      trendTypeTabIndex: 0,
      costTypeSelected: {},
      trendTypeTabData: [
        { label: '人效', value: 'rx' },
        { label: '出勤率', value: 'rate' }
      ],
      costTypeTabIndex: 0,
      costTypeFlag: '',
      costTypeTabData: [
        {
          label: () => {
            const { label } = this.costTypeSelected
            return `总${label}`
          },
          value: 'total'
        },
        { label: '自有', value: 'own' },
        { label: '外包', value: 'outsource' }
      ],
      areaIndexVM: {},
      businessId: '',
      chart1Width: '100vw'
    }
  },

  mounted() {
    this.trendTypeTabChange()
    this.costTypeTabChange()
    this.init()
  },

  computed: {
    // T-1
    t1() {
      const t = this.$moment()
      if (this.isSameDay(t, this.dateValue)) {
        return t.add(-1, 'd').format('YYYY-MM-DD')
      }
      return this.$moment(this.dateValue).format('YYYY-MM-DD')
    }
  },

  methods: {
    init() {
      this.initBS(this.$refs.contentWrapper)
      this.fetchRecentEightDTrendData()
      this.fetchAreaIndexData()
    },

    // 时间改变
    dateChange(value) {
      this.dateValue = value
      this.init()
    },

    // 日期可选范围
    dateRange(min) {
      if (min) {
        const yesterday = this.$moment().add(-1, 'd').add(-1, 'M')
        const y = yesterday.year()
        const m = yesterday.month()
        return new Date(y, m, 1)
      }
      return new Date()
    },

    // 获取近8天趋势数据 - 人效 and 出勤率
    fetchRecentEightDTrendData() {
      const dayTable = timeTable(this.t1, 8, -7, '-')
      const [startDay] = dayTable
      const endDay = dayTable[dayTable.length - 1]
      const level = this.zoneLevel
      const conditionList = [
        { key: 'startDay', value: this.$moment(startDay).format('YYYYMMDD') },
        { key: 'endDay', value: this.$moment(endDay).format('YYYYMMDD') },
        { key: 'zoneLevel', value: level },
        { key: this.roleKeyMap[level], value: this.zoneCode }
      ]
      this.sendTwoDimenRequest('sx_manage_monit_eight_days_info', conditionList)
        .then(res => {
          const { obj = [] } = res // eslint-disable-line
          const serie1 = []
          const serie2 = []
          const serie3 = []
          const seriesData = [serie1, serie2, serie3]
          const random = (mult = 100, precision = 1) => (Math.random() * mult).toFixed(precision)
          dayTable.forEach((dateItem, index) => {
            const index1_rx = random(10000, 0)
            const index1_rate = random()
            const index2_rx = random(10000, 0)
            const index2_rate = random()
            const index3_rx = random(10000, 0)
            const index3_rate = random()

            serie1.push([dateItem, [index1_rx, index1_rate]])
            serie2.push([dateItem, [index2_rx, index2_rate]])
            serie3.push([dateItem, [index3_rx, index3_rate]])
          })
          this.recentEightDTrendData = seriesData
          this.recentEightDTrendXAxisData = dayTable
          this.drawRecentEightDTrendChart()
        })
        .catch(err => {
          this.$toast({
            duration: 2000,
            message: '请求近八日收入数据错误'
          })
          console.error('请求近八日收入数据错误', err)
        })
    },

    // 绘制近8天趋势数据图表
    drawRecentEightDTrendChart() {
      const seriesData = this.recentEightDTrendData || []
      const xAxisData = this.recentEightDTrendXAxisData || []
      const [serie1, serie2, serie3] = seriesData
      const valIndex = this.trendTypeTabIndex // 根据标识读取不同数据重新渲染图表
      const seriesLine = (data = [], color, indexKey) => {
        const result = data.map(item => {
          const [category, dataOrigin] = item
          const val = dataOrigin[valIndex]
          return [category, val]
        })
        if (result.length) {
          const activeVal = result[result.length - 1]
          this.lengedVM.date = activeVal[0]
          this.lengedVM[indexKey] = activeVal[1]
        }
        return {
          type: 'line',
          data: result,
          showSymbol: false,
          itemStyle: { color },
          lineStyle: {
            shadowColor: color,
            shadowOffsetX: 0,
            shadowOffsetY: 8,
            shadowBlur: 12
          }
        }
      }
      const option = {
        tooltip: {
          show: true,
          padding: [5, 15, 5, 15],
          trigger: 'axis',
          formatter: params => {
            const [s1, s2, s3] = params
            Object.assign(this.lengedVM, {
              date: s1.data[0],
              s1: s1.data[1],
              s2: s2.data[1],
              s3: s3.data[1]
            })
          }
        },
        grid: { top: '25', left: '5', right: '5', bottom: '60' },
        xAxis: [
          {
            type: 'category',
            data: xAxisData,
            axisTick: { show: false },
            axisLabel: {
              formatter: val => {
                return this.$moment(val).format('MM/DD')
              },
              show: true,
              interval: 0,
              textStyle: {
                color: '#939393'
              }
            },
            axisPointer: {
              type: 'shadow',
              color: '#5594F2',
              value: xAxisData[xAxisData.length - 1]
            },
            axisLine: { show: false }
          }
        ],
        yAxis: { show: false },
        series: [
          seriesLine(serie1, '#2E55EC', 's1'),
          seriesLine(serie2, '#FF6A17', 's2'),
          seriesLine(serie3, '#09BCA0', 's3')
        ]
      }

      this.drawChart(document.querySelector('.trend--chart'), option)
    },

    trendTypeTabChange(index = 0, value, item) {
      let record = item
      if (!item) {
        record = this.trendTypeTabData[index]
      }
      this.costTypeSelected = record
      this.trendTypeTabIndex = index
      this.drawRecentEightDTrendChart()
      this.costTypeTabChange()
    },

    costTypeTabChange(index = this.costTypeTabIndex, value, item) {
      //   let record = item
      let val = value
      if (!item) {
        // record = this.costTypeTabData[index]
        // val = record.value
        val = this.costTypeTabData[index].value
      }
      this.costTypeFlag = val
      this.costTypeTabIndex = index
      this.drawChart1()
    },

    // 获取各省区当月情况数据
    fetchAreaIndexData() {
      const zoneLevel = Number(this.zoneLevel)
      const nextOrgLevel = zoneLevel === 0 ? 2 : zoneLevel + 1
      const zoneCode = this.zoneCode
      const incDay = this.$moment(this.indexDate).format('YYYYMMDD')
      const common = {
        groupByCode: 'province_area_code',
        groupByName: 'province_area_name',
        incDay,
        zoneLevel: nextOrgLevel
      }
      if (nextOrgLevel === 2) {
        Object.assign(common, {
          qw_code: '001'
        })
      }
      if (nextOrgLevel === 3) {
        Object.assign(common, {
          groupByCode: 'area_code',
          groupByName: 'area_name',
          province_area_code: zoneCode
        })
      }
      if (nextOrgLevel === 4) {
        Object.assign(common, {
          groupByCode: 'dept_code',
          groupByName: 'dept_code',
          area_code: zoneCode
        })
      }
      const conditionList = this.formatParams(common)
      this.sendTwoDimenRequest('sx_manage_monit_info', conditionList)
        .then(res => {
          const { obj = [] } = res
          console.log('获取各省区当月情况数据', obj)
          const chart1DataSource = []
          const areaIndexVM = {}
          const random = (mult = 1) => (Math.random() * mult).toFixed(1)
          Array.apply(null, { length: 12 }).forEach((item, index) => {
            const businessId = 'businessId_' + index
            // 命名空间 类型+区域代码+字段类型
            // total_总人力成本 own_自有 outsource_外包
            // ${businessId}标识选择的区域ID
            // __rank排名 __sum当月累计 __sum_hb当月累计环比 __hb环比
            Object.assign(areaIndexVM, {
              [`total_${businessId}__rank`]: index,
              [`total_${businessId}__sum`]: random(1000),
              [`total_${businessId}__sum_hb`]: random(),
              [`total_${businessId}__hb`]: random(),

              [`own_${businessId}__rank`]: index,
              [`own_${businessId}__sum`]: random(1000),
              [`own_${businessId}__sum_hb`]: random(),
              [`own_${businessId}__hb`]: random(),

              [`outsource_${businessId}__rank`]: index,
              [`outsource_${businessId}__sum`]: random(1000),
              [`outsource_${businessId}__sum_hb`]: random(),
              [`outsource_${businessId}__hb`]: random()
            })
            chart1DataSource.push({
              zoneName: '地区' + index,
              businessId,
              total: random(10050),
              own: random(1000),
              outsource: random(1100)
            })
          })
          this.areaIndexVM = areaIndexVM
          this.chart1DataSource = chart1DataSource
          this.drawChart1()
        })
        .catch(err => {
          console.log('err', err)
        })
    },

    // 绘制各省区当月情况图表
    drawChart1() {
      // 每个bar的宽度，会根据name的长度计算
      let perBarWidth = 20
      const costTypeFlag = this.costTypeFlag
      const catoryData = []
      const seriesData = []
      const chart1DataSource = this.chart1DataSource || []
      const len = chart1DataSource.length - 6
      chart1DataSource.forEach((item, index) => {
        const { zoneName, businessId } = item
        if (!zoneName) {
          return
        }
        perBarWidth = zoneName.length * 4
        const value = item[costTypeFlag]
        seriesData.push([zoneName, value, businessId])
        catoryData.push(zoneName)
        if (!index) {
          this.businessId = businessId
        }
      })
      this.chart1Width = `${100 + (len > 0 ? len : 0) * perBarWidth}vw`

      const option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            color: '#5594F2'
          },
          formatter: params => {
            const [fristParams = {}] = params
            this.businessId = fristParams.data[2]
          }
        },
        grid: {
          top: '10%',
          left: '2%',
          right: '2%',
          bottom: '15%'
        },
        xAxis: [
          {
            data: catoryData,
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#939393'
              },
              interval: 0
            },
            axisLine: {
              show: false
            }
          }
        ],
        yAxis: {
          show: false
        },
        series: [
          {
            name: '成本',
            type: 'bar',
            barWidth: 10,
            data: seriesData,
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              barBorderRadius: 5,
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#27AEFF' },
                { offset: 1, color: '#2E55EC' }
              ])
            }
          }
        ]
      }
      const chartsWrap = document.querySelector('.each-area-situ--chart')
      this.drawChart(chartsWrap, option)
    }
  }
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>
