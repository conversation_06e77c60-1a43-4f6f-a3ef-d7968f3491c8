<!--
 * @Author: shigl
 * @Date: 2022-07-19 15:33:09
 * @LastEditTime: 2024-05-13 14:11:34
 * @Description:
-->
<template>
  <div class="new-app-container">
    <Platform @onComplete="onComplete" />
    <HeadBar
      :title="moduleTitle"
      :isLeft="false"
      :homePage="true"
      :rightName="zoneData.zoneName"
      :showPublicSelect="true"
      :tabBarClassName="'normal-tab-bar'"
    ></HeadBar>
    <MAlertWarning v-show="!isAuth" type="error">当前模块您暂无权限...</MAlertWarning>
    <Kyloading :load="isLoading"></Kyloading>
    <!-- <TabFooter @changeTab="changeTab"></TabFooter> -->
    <!-- 新功能弹出框 -->
    <NewFunction @close="newFunctionVisible = false" :visible="newFunctionVisible" />
    <TabFooterBar
      v-if="isFooterTab"
      @onSelectedTab="onSelectedTab"
      @onEndMove="onEndMove"
      @onModify="onModify"
      :isAuth="isAuth"
      :latestModules="levelthemeArray"
      :showThemeTab="showThemeTab"
      :moduleTitle="moduleTitle"
      :noAuthBtn="false"
      :iconList="[]"
    ></TabFooterBar>

    <!-- 点击tab路由跳转页面 -->
    <router-view></router-view>
  </div>
</template>
<script>
import HeadBar from 'common/components/head'
import { mapGetters, mapState, mapMutations } from 'vuex'
import versionsConfig from './newFunction/config'
import NewFunction from './newFunction'
import requestMixins from 'common/mixins/requestMixins'
import TabFooterBar from './tabFooterBar'
import Platform from '../platform'
export default {
  mixins: [requestMixins],
  components: { HeadBar, TabFooterBar, Platform, NewFunction },
  data() {
    return {
      isFooterTab: false,
      selectedTab: '',
      newFunctionVisible: false
    }
  },
  computed: {
    peakMode() {
      if (this.moduleTitle === '实时') {
        return true
      }
      return false
    },
    ...mapState({
      showThemeTab: state => state.showThemeTab,
      moduleTitle: state => state.moduleTitle
    }),
    ...mapGetters({
      zoneData: 'zoneData',
      isAuth: 'isAuth',
      isLoading: 'isLoading',
      levelthemeArray: 'levelthemeArray'
    })
  },
  watch: {},
  methods: {
    ...mapMutations(['setModuleProblem', 'setHolidayData', 'setModuleTitle']),
    // 闪屏逻辑结束
    onComplete() {
      this.isFooterTab = true
      this.initOptions()
    },
    onSelectedTab(item) {
      if (this.selectedTab === item.value) return
      this.selectedTab = item.value
      this.setModuleTitle(item.label)
      this.$router.replace({
        path: item.route
      })
      this.$sensors.functionClick(item.label)
    },
    onModify(data) {
      this.saveModifyTab(data)
    },
    onEndMove(data) {
      if (data.length <= 5) {
        this.saveModifyTab(data)
      }
    },
    initOptions() {
      // 神策
      this.$sensors.setProfile({
        zone_code: this.zoneCode || ''
      })

      this.getDataProblemWarning()
      this.getHolidayData()
      this.judgeNewFunction()
    },
    // 判断是否弹出新功能弹窗
    judgeNewFunction() {
      if (process.env.NODE_ENV === 'development') {
        if (versionsConfig.alwaysShow) {
          this.newFunctionVisible = true
          return
        }
      }
      if (!versionsConfig.open) return
      const sign = versionsConfig.sign
      const localSign = localStorage.getItem('SXZH_SIGN')
      if (!localSign || sign !== localSign) {
        localStorage.setItem('SXZH_SIGN', sign)
        this.newFunctionVisible = true
      }
    },

    // 获取战况数据权限问题(全局跑马灯)
    getDataProblemWarning() {
      const tableName = 'marquee'
      const conditionList = [{ key: 'product_code', value: 'sxzk' }]
      return this.sendTwoDimenRequest(tableName, conditionList, false, false).then(res => {
        this.setModuleProblem(res.obj)
      })
    },
    // 获取近一年节假日,日历用
    async getHolidayData() {
      const { obj } = await this.getHoliday(
        this.$moment()
          .subtract(1, 'years')
          .format('YYYYMMDD'),
        this.$moment()
          .endOf('month')
          .format('YYYYMMDD')
      )
      sessionStorage.setItem('holidayData', JSON.stringify(obj))
      this.setHolidayData(obj)
    },
    // 保存tab顺序， 并请求接口保存
    saveModifyTab(dataList) {
      const userId = sessionStorage.getItem('userId')

      const labelShow = dataList.map(item => item.label)
      if (!userId) return
      // /resourceServices/logbuch/update

      this.$http
        .post('/resourceServices/logbuch/sx/update', {
          userNo: userId,
          menuContent: JSON.stringify(labelShow)
        })
        .then(res => {
          if (res.success) {
            console.log('修改成功')
          } else {
            console.log('修改失败')
          }
        })
        .catch(err => {
          console.log(err, '修改失败')
        })
    }
  },
  async mounted() {}
}
</script>
<style lang="less" scoped>
.new-app-container {
  // width: 100vw;
  overflow: hidden;
}
</style>
