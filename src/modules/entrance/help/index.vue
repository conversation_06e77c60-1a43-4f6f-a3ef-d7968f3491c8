<template>
  <div class="help-wrapper">
    <transition enter-active-class="animate__slideInUp" leave-active-class="animate__slideOutDown">
      <div class="help-wrapper-content animate__animated" v-if="mainShow">
        <div class="help-close-btn" @click="videoClose"><i class="iconfont icon-guanbi"></i></div>
        <video id="help-video" controls="controls">
          <source src="https://freight.sf-express.com/assets/sfimPubResources/2021-6-21%2011-34-9262.mp4" type="video/mp4" />
        </video>
      </div>
    </transition>
  </div>
</template>
<script>
export default {
  data() {
    return {
      mainShow: false
    }
  },
  computed: {
  },
  methods: {
    videoClose() {
      document.getElementById('help-video').pause()
      this.mainShow = false
      setTimeout(() => {
        this.$emit('closeVideo')
      }, 2000)
    }
  },
  mounted() {
    setTimeout(() => {
      this.mainShow = true
    }, 200)
    setTimeout(() => {
      document.getElementById('help-video').play()
    }, 2200)
  }
}
</script>

<style lang='less' scoped>
.help-wrapper {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9998;
  .help-wrapper-content {
    background-color: #fff;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    .help-close-btn {
      position: absolute;
      width: 1.7rem;
      height: 1rem;
      left: 0;
      top: 0;
      line-height: 1rem;
      background: #333;
      z-index: 10;
      border-bottom-right-radius: 0.5rem;
      border-top-right-radius: 0.5rem;
      padding-left: 0.55rem;
      i {
        color: #fff;
        font-size: 0.4rem;
      }
    }
    video {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }
  }
}
</style>
