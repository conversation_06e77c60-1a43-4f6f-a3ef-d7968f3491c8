<!--
 * @Author: JieJw
 * @Date: 2021-07-19 15:45:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-11-10 18:44:52
 * @Description:
-->
<template>
  <KyDataDrawer
    direction="center"
    :visible="visible"
    height="10.1rem"
    width="92.4%"
    @close="closeDrawer"
    class="customize-drawer-wrap"
  >
    <div class="xfd-bg"></div>
    <div class="functionContent">
      <div class="edition-info">{{ config.date }}</div>
      <div class="header flex_center">
        <div class="img"></div>
        <span>更新内容</span>
        <div class="img"></div>
      </div>
      <div class="functionData">
        <div v-for="(item, index) in config.options" :key="index">
          <div class="title" :class="{ mt12: index }">{{ item.title }}</div>
          <div class="content" v-for="(data, i) in item.content" :key="i">
            <span>{{ i + 1 }}</span
            >、
            {{ data }}
          </div>
        </div>
      </div>
    </div>
    <div class="functionButton" @click="closeDrawer">我知道了</div>
  </KyDataDrawer>
</template>

<script>
import config from './config'
export default {
  props: ['visible'],
  data() {
    return {
      config
    }
  },
  methods: {
    closeDrawer() {
      this.$emit('close', false)
    }
  }
}
</script>

<style lang="less" scoped>
.customize-drawer-wrap {
  position: relative;
  z-index: 9998 !important;
  width: 100vw;
  height: 100vh;
  border: 1px solid #0f0;
  /deep/ .drawer-content {
    background: url('../img/newFuncBg.png') no-repeat;
    background-size: 100% 100%;
    border-radius: 0.16rem;
    padding: 0 0.32rem;
    box-sizing: border-box;
    z-index: 9998 !important;
  }
  /deep/ .ky-data-drawer-mask {
    z-index: 9997 !important;
    width: 100vw;
    height: 100vh;
  }
  /deep/ .close {
    display: none;
  }
  .xfd-bg {
    position: absolute;
    top: -0.2rem;
    right: -0.2rem;
    background: url('../img/xfd.png') no-repeat;
    background-size: cover;
    height: 2.6rem;
    width: 2.5rem;
  }
  .functionContent {
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
    top: 24.3%;
    height: 6.5rem;
    padding: 0.28rem 0.32rem 0.4rem;
    background: #fff;
    box-shadow: inset 0.04rem 0 0.24rem 0 rgba(255, 255, 255, 0.6);
    border-radius: 0.16rem;
    .header {
      display: flex;
      align-self: center;
      flex-direction: row;
      .img {
        height: 0.84rem;
        width: 0.84rem;
        background: url('../img/functionHeader.png') no-repeat;
        background-size: contain;
        &:last-child {
          transform: rotateY(180deg);
        }
      }
      > span {
        font-family: PingFangSC-Medium;
        font-size: 0.48rem;
        color: #333333;
        line-height: 0.67rem;
        font-weight: bold;
        margin-top: -0.12rem;
      }
    }
    .functionData {
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      line-height: 0.4rem;
      .title {
        font-size: 0.24rem;
        font-weight: 700;
        position: relative;
        padding-left: 0.2rem;
        color: #333;
        &::before {
          content: '';
          position: absolute;
          width: 0.08rem;
          height: 60%;
          background-color: #dc1e32;
          border-radius: 2px;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .content {
        font-family: PingFangSC-Regular;
        font-size: 0.24rem;
        color: #333;
        line-height: 0.36rem;
      }
    }
  }
  .functionButton {
    background: #dc1e32;
    border-radius: 0.04rem;
    font-family: PingFangSC-Medium;
    font-size: 0.28rem;
    color: #fff;
    width: 3.77rem;
    height: 0.8rem;
    text-align: center;
    line-height: 0.8rem;
    position: absolute;
    bottom: 0.2rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .edition-info {
    width: 0.87rem;
    height: 0.32rem;
    position: absolute;
    top: 0.5rem;
    right: 0.2rem;
    background: rgba(51, 51, 51, 0.1);
    text-align: center;
    line-height: 0.32rem;
    font-size: 0.2rem;
    font-weight: 500;
    color: #333;
    border-radius: 0.02rem;
  }
}
</style>
