<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Label bar_more_icon_nor</title>
    <defs>
        <path d="M19.4094911,26.3030303 C21.1645946,26.3030303 22.5984641,27.6783964 22.6921053,29.4101341 L22.6969697,29.5905089 L22.6969697,38.7125214 C22.6969697,40.4676249 21.3216036,41.9014944 19.5898659,41.9951356 L19.4094911,42 L10.2874786,42 C8.53237509,42 7.09850559,40.6246339 7.0048644,38.8928962 L7,38.7125214 L7,29.5905089 C7,27.8354054 8.37536606,26.4015359 10.1071038,26.3078947 L10.2874786,26.3030303 L19.4094911,26.3030303 Z M38.7125214,26.3030303 C40.4676249,26.3030303 41.9014944,27.6783964 41.9951356,29.4101341 L42,29.5905089 L42,38.7125214 C42,40.4676249 40.6246339,41.9014944 38.8928962,41.9951356 L38.7125214,42 L29.5905089,42 C27.8354054,42 26.4015359,40.6246339 26.3078947,38.8928962 L26.3030303,38.7125214 L26.3030303,29.5905089 C26.3030303,27.8354054 27.6783964,26.4015359 29.4101341,26.3078947 L29.5905089,26.3030303 L38.7125214,26.3030303 Z M19.4094911,7 C21.1645946,7 22.5984641,8.37536606 22.6921053,10.1071038 L22.6969697,10.2874786 L22.6969697,19.4094911 C22.6969697,21.1645946 21.3216036,22.5984641 19.5898659,22.6921053 L19.4094911,22.6969697 L10.2874786,22.6969697 C8.53237509,22.6969697 7.09850559,21.3216036 7.0048644,19.5898659 L7,19.4094911 L7,10.2874786 C7,8.53237509 8.37536606,7.09850559 10.1071038,7.0048644 L10.2874786,7 L19.4094911,7 Z M34.1515152,7 C38.4861158,7 42,10.5138853 42,14.8484848 C42,19.1830895 38.4861198,22.6969697 34.1515152,22.6969697 C29.8169105,22.6969697 26.3030303,19.1830895 26.3030303,14.8484848 C26.3030303,10.5138853 29.8169145,7 34.1515152,7 Z" id="path-1"></path>
        <filter x="-16.1%" y="-16.1%" width="132.1%" height="132.1%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="1.12121212" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.905882353   0 0 0 0 0   0 0 0 0 0.149019608  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFB2A8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FF0002" offset="0%"></stop>
            <stop stop-color="#5200FF" offset="100%"></stop>
        </linearGradient>
        <path d="M19.4499834,26.5167274 C21.0693815,26.5167274 22.3923835,27.7857495 22.4787843,29.3835885 L22.4832726,29.5500166 L22.4832726,37.9667108 C22.4832726,39.5861089 21.2142505,40.9091109 19.6164115,40.9955117 L19.4499834,41 L11.0332892,41 C9.41389113,41 8.09088909,39.7309779 8.00448828,38.1331389 L8,37.9667108 L8,29.5500166 C8,27.9306185 9.26902211,26.6076165 10.8668611,26.5212157 L11.0332892,26.5167274 L19.4499834,26.5167274 Z M37.9667108,26.5167274 C39.5861089,26.5167274 40.9091109,27.7857495 40.9955117,29.3835885 L41,29.5500166 L41,37.9667108 C41,39.5861089 39.7309779,40.9091109 38.1331389,40.9955117 L37.9667108,41 L29.5500166,41 C27.9306185,41 26.6076165,39.7309779 26.5212157,38.1331389 L26.5167274,37.9667108 L26.5167274,29.5500166 C26.5167274,27.9306185 27.7857495,26.6076165 29.3835885,26.5212157 L29.5500166,26.5167274 L37.9667108,26.5167274 Z M19.4499834,8 C21.0693815,8 22.3923835,9.26902211 22.4787843,10.8668611 L22.4832726,11.0332892 L22.4832726,19.4499834 C22.4832726,21.0693815 21.2142505,22.3923835 19.6164115,22.4787843 L19.4499834,22.4832726 L11.0332892,22.4832726 C9.41389113,22.4832726 8.09088909,21.2142505 8.00448828,19.6164115 L8,19.4499834 L8,11.0332892 C8,9.41389113 9.26902211,8.09088909 10.8668611,8.00448828 L11.0332892,8 L19.4499834,8 Z M33.7583637,8 C37.757811,8 41,11.24219 41,15.2416363 C41,19.2410873 37.7578147,22.4832726 33.7583637,22.4832726 C29.7589127,22.4832726 26.5167274,19.2410873 26.5167274,15.2416363 C26.5167274,11.24219 29.7589164,8 33.7583637,8 Z" id="path-5"></path>
        <filter x="-8.6%" y="-8.6%" width="123.3%" height="123.3%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.342041729" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="1" dy="1" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.211764706   0 0 0 0 0.164705882   0 0 0 0 0.694117647  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-7.1%" y="-7.1%" width="120.3%" height="120.3%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="🧩组件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Label-bar_more_icon_nor">
            <rect id="矩形" fill="#000000" fill-rule="nonzero" opacity="0" x="0" y="0" width="48" height="48"></rect>
            <g id="形状">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use stroke-opacity="0.5" stroke="#FF2C2C" stroke-width="2.24242424" fill="#FF1919" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <g id="形状备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                <use fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-5"></use>
                <use stroke="url(#linearGradient-4)" stroke-width="0.684083458" xlink:href="#path-5"></use>
            </g>
        </g>
    </g>
</svg>