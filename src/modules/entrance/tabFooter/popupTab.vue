<!--
 * @Author: shigl
 * @Date: 2022-05-20 10:54:47
 * @LastEditTime: 2022-07-20 14:38:03
 * @Description: 底部导航栏弹出框
-->

<template>
  <div>
    <div class="ky-popup-mask" @click="closeModal" v-show="visible"></div>
    <div
      :class="{
        'ky-popup-content': true,
        'ky-popup-content--show': visible,
        'ky-popup-content--hide': !visible,
        'ky-popup-content--peak': peakMode
      }"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'kypopup',
  props: {
    visible: Boolean,
    peakMode: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    const targetEle = document.querySelector('.ky-popup-wrap')
    if (!targetEle) {
      const bodyDom = document.body
      const bgDiv = document.createElement('div')
      bgDiv.classList.add('ky-popup-wrap')
      bgDiv.appendChild(this.$el)
      bodyDom.appendChild(bgDiv)
    }
  },
  methods: {
    closeModal() {
      this.$emit('closePopup')
    },
    dispatchFun() {
      this.closeModal()
    }
  }
}
</script>

<style lang="less" scoped>
.ky-popup-wrap {
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
}
.ky-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5553;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  overflow: hidden;
}
.ky-popup-content {
  position: fixed;
  width: 100%;
  background: #fff;
  z-index: 5554;
  bottom: 0;
  left: 0;
  transform: translate(0, 100%);
  border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
}

.ky-popup-content--show {
  transition: all 0.3s ease-in;
  transform: translate(0, 0%);
  bottom: 0rem;
}
.ky-popup-content--hide {
  transition: all 0.3s ease-in;
}
.ky-popup-content--peak {
  background: #020e22 !important;
}
</style>
