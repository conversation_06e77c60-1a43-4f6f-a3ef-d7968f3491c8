<!--
 * @Author: shigl
 * @Date: 2022-07-07 16:01:05
 * @LastEditTime: 2024-01-15 17:28:51
 * @Description: 底部菜单栏
-->
<template>
  <div>
    <!-- 底部tab页面 -->
    <div :class="peakMode ? 'tab-footer-container tab-footer-container--peak' : 'tab-footer-container'">
      <div class="tab-footer__content" ref="tab-show">
        <div
          v-for="(item, index) in modulesShow"
          :key="item.label"
          :class="{
            'tab-item--normal': true,
            'tab-item--active': item.value === selectedTab,
            'tab-item--modify': modifyMode,
            'tab-item--active-popup': popupMode && item.value === selectedTab,
            'tab-item--normal-popup': popupMode && item.value !== selectedTab,
            'tab-item--noAuth': judgeTabNoAuth(item),
            'tab-item--peak': peakMode,
            'tab-item--peak--noAuth': peakMode && judgeTabNoAuth(item),
            'tab-item--peak--active': peakMode && item.value === 'peekPop' && selectedTab === 'peekPop'
          }"
          @click="changeSelected(item)"
          @touchstart="touchstart($event, 'tab-item--normal', 'show-tab', index)"
          @touchmove="touchmove"
          @touchend="touchend"
        >
          <i v-if="!showThemeTab" :class="`iconfont icon-${item.value}`"></i>
          <img
            v-if="showThemeTab"
            class="theme-img"
            :class="{ active: item.value === selectedTab && !isMoreActive && !popupMode }"
            :src="themeIcon[index]"
          />
          <div>{{ item.label }}</div>
        </div>
        <div
          :class="{
            'tab-footer__content__shift': true,
            'tab-footer__content__shift--active': popupMode,
            'tab-footer__content__shift--peak': peakMode,
            'tab-footer__content__shift--peak--active': peakMode && popupMode
          }"
        >
          <div @click="changePopuoMode" style="margin-bottom: 0.08rem">
            <i v-if="!showThemeTab" class="iconfont icon-tab-shift"></i>
            <img
              v-if="showThemeTab"
              class="theme-img"
              :class="{ active: isMoreActive || popupMode }"
              :src="themeIcon[4]"
            />
          </div>
          <span>更多</span>
        </div>
      </div>
    </div>
    <!-- 隐藏的tab页面 -->
    <PopupTab :visible="popupVisible" @closePopup="closePopup" :peakMode="peakMode">
      <div :class="peakMode ? 'tab-footer__hide--peak tab-footer__hide' : 'tab-footer__hide'" ref="tab-hide">
        <div class="tab_title" :class="peakMode ? 'peak' : 'normal'">
          <div class="btn_edit fs28" @click="modifyTab">{{ modityText }}</div>
        </div>
        <div class="tab-footer__hide__content" ref="tab-content">
          <div
            v-for="(item, index) in modulesHide"
            :key="item.label"
            :class="{
              'tab-footer__hide__item': true,
              'tab-footer__hide__item--active': item.value === selectedTab,
              'tab-footer__hide__item--modify': modifyMode,
              'tab-footer__hide__item--noAuth': judgeTabNoAuth(item),
              'tab-footer__hide__item--peak': peakMode,
              'tab-footer__hide__item--peak--active': peakMode && item.value === 'peekPop' && selectedTab === 'peekPop',
              'tab-footer__hide__item--peak--noAuth': peakMode && judgeTabNoAuth(item)
            }"
            @touchstart="touchstart($event, 'tab-footer__hide__item', 'hide-tab', index)"
            @touchmove="touchmove"
            @touchend="touchend"
          >
            <div @click="changeSelected(item)">
              <i :class="`iconfont icon-${item.value}`"></i>
            </div>
            <div>{{ item.label }}</div>
          </div>
        </div>
        <div class="tab-footer__hide__footer">—— 编辑可以随意进行位置替换 ——</div>
      </div>
    </PopupTab>
  </div>
</template>
<script>
import PopupTab from './popupTab.vue'
const userId = sessionStorage.getItem('userId')
import { mapState, mapMutations, mapGetters } from 'vuex'
import d1 from '../img/double11/d1.png'
import d2 from '../img/double11/d2.png'
import d3 from '../img/double11/d3.png'
import d4 from '../img/double11/d4.png'
import d5 from '../img/double11/d5.png'
// import modulesConfig from '../config'
export default {
  components: { PopupTab },
  data() {
    return {
      // 底部展示四个tab
      modulesShow: [],
      // 弹窗的所有剩余tab
      modulesHide: [],
      // 选中tab值
      selectedTab: '',
      popupVisible: false,
      modifyMode: false,
      // 拖拽的tab值
      dragSelected: '',
      // 存储tab之间的相对位置的变量，在拖动的时候记录
      modulesLocal: [],
      // 拖动tab的几个变量
      dragX: '',
      dragY: '',
      dragIndex: '',
      dragType: '',

      // 底部tab栏弹出展示visi
      popupMode: false
    }
  },
  computed: {
    ...mapState({
      showThemeTab: state => state.showThemeTab,
      themeArray: state => state.themeArray,
      moduleTitle: state => state.moduleTitle
    }),
    ...mapGetters({
      levelthemeArray: 'levelthemeArray'
    }),
    // 节假日图标
    themeIcon() {
      return [d1, d2, d3, d4, d5]
    },
    peakMode() {
      if (this.moduleTitle === '实时') {
        return true
      }
      return false
    },
    // 获取所有tab的默认属性
    defaultModules() {
      return this.levelthemeArray.map(item => item.label)
    },
    // 底部tab的所有label值
    modulesShowLabel() {
      return this.modulesShow.map(item => item.label)
    },
    // 隐藏tab的所有label值
    modulesHideLabel() {
      return this.modulesHide.map(item => item.label)
    },
    isMoreActive() {
      return this.modulesHide.findIndex(item => item['label'] === this.moduleTitle) !== -1
    },
    // tab顺序修改文本值
    modityText() {
      return !this.modifyMode ? '编辑' : '完成'
    }
  },
  watch: {},
  methods: {
    ...mapMutations(['setModuleTitle']),
    // 初始化tab展示以及路由跳转
    initTab() {
      // 取排序后的前四个放在底部tab展示
      this.modulesShow = this.levelthemeArray.slice(0, 4)
      // 取剩下的tab到隐藏的popup中展示
      this.modulesHide = this.levelthemeArray.slice(4)
      // 如果第一个底部tab是实时战报，默认跳转第二个tab路由
      // 相反，默认跳转第一个tab路由
      // 通过头条跳转过来的必须带有 moduleTitle(合并路由传参)
      const query = this.$route.query
      if (!query.from) {
        if (this.moduleTitle) {
          const obj = this.levelthemeArray.find(item => item['label'] === this.moduleTitle)
          this.$router.replace({
            path: obj.route
          })
          this.selectedTab = obj.value
          this.setModuleTitle(obj.label)
          this.$sensors.functionClick(this.moduleTitle)
        } else if (this.levelthemeArray.length >= 2 && (this.modulesShow[0] || {}).label === '实时') {
          this.$router.replace({
            path: this.modulesShow[1].route
          })
          this.selectedTab = this.modulesShow[1].value
          this.setModuleTitle(this.modulesShow[1].label)
          this.$sensors.functionClick(this.modulesShow[1].label)
        } else {
          this.$router.replace({
            path: this.modulesShow[0].route
          })
          this.selectedTab = this.modulesShow[0].value
          this.setModuleTitle(this.modulesShow[0].label)
          this.$sensors.functionClick(this.modulesShow[0].label)
        }
      }
    },
    changePopuoMode() {
      this.popupMode = !this.popupMode
      this.popupVisible = this.popupMode
      if (this.popupMode) {
        this.getModulesLocal()
      } else {
        this.modifyMode = false
      }
    },
    // 切换模块
    changeSelected(item) {
      if (this.judgeTabNoAuth(item) && item.value !== 'shift') {
        return
      }
      if (this.modulesShowLabel.includes(item.label) || this.modulesHideLabel.includes(item.label)) {
        this.popupVisible = false
        this.modifyMode = false
      }
      this.selectedTab = item.value
      this.$emit('selectedTab', item.value)
      this.setModuleTitle(item.label)
      this.popupMode = false

      this.$router.replace({
        path: item.route
      })
      this.$sensors.functionClick(item.label)
    },

    // 获取各个模块的热点区域
    getModulesLocal() {
      this.modulesLocal = []
      const hidewrap = this.$refs['tab-hide']
      const { height } = hidewrap.getBoundingClientRect()
      const tabShow = this.$refs['tab-show']
      const tabHide = this.$refs['tab-content']
      // const addPx = document.documentElement.style.fontSize.slice(0, -2) * 0.98
      const tabShowChild = tabShow.childNodes
      const tabHideChild = tabHide.childNodes
      tabShowChild.forEach((item, index) => {
        const classListArr = Array.from(item.classList)
        // 判断具有可点击的tab
        if (classListArr.includes('tab-item--normal')) {
          const rect = item.getBoundingClientRect()
          this.modulesLocal.push({
            top: rect.top,
            right: rect.right,
            bottom: rect.bottom,
            left: rect.left,
            width: rect?.width,
            height: rect.height,
            x: rect.x,
            y: rect.y,
            label: item.lastChild.innerHTML,
            type: 'show-tab',
            index
          })
        }
      })
      tabHideChild.forEach((item, index) => {
        const classListArr = Array.from(item.classList)
        // 判断具有可点击的tab
        if (classListArr.includes('tab-footer__hide__item')) {
          const rect = item.getBoundingClientRect()
          this.modulesLocal.push({
            top: rect.top - height, // 不知道为啥定位会定位到隐藏的地址
            right: rect.right,
            bottom: rect.bottom - height,
            left: rect.left,
            width: rect?.width,
            height: rect.height,
            x: rect.x,
            y: rect.y,
            label: item.lastChild.innerHTML,
            type: 'hide-tab',
            index
          })
        }
      })
    },
    // 点击编辑按钮之后
    // 开始拖动
    touchstart(e, classname, type, index) {
      if (!this.modifyMode) {
        return
      }
      e.preventDefault()
      this.dragIndex = index
      this.dragType = type
      const { touches } = e
      let { target } = touches[0]
      const { pageX, pageY } = touches[0]
      while (!Array.from(target.classList).includes(classname)) {
        target = target.parentNode
      }
      this.moveDom = target.cloneNode(true)
      this.moveDom.classList.add('tab-item--drag')
      this.moveDom.style.top = `${pageY}px`
      this.moveDom.style.left = `${pageX}px`
      const bodyDom = document.body
      bodyDom.appendChild(this.moveDom)
    },
    // 拖动tab时候修改tab的值
    // 保存tab滚动时候的x，y值
    touchmove(e) {
      if (!this.modifyMode) {
        return
      }
      e.preventDefault()
      const { touches } = e
      const { pageX, pageY } = touches[0]
      this.dragX = pageX
      this.dragY = pageY
      this.moveDom.style.top = `${pageY - 20}px`
      this.moveDom.style.left = `${pageX - 20}px`
    },

    // 拖动结束后 判断拖动tab是否当前位置有tab存在
    // 存在着交换tab，并清空拖动的变量
    touchend(e) {
      if (!this.modifyMode) {
        return
      }
      e.preventDefault()
      const target = this.judgeMoveIn()
      if (target) {
        const { index, type } = target
        this.changeModulesData(index, type)
      }
      const targetEle = document.querySelectorAll('.tab-item--drag')
      const targetEleArr = Array.from(targetEle)
      while (targetEleArr.length) {
        const item = targetEleArr.shift()
        if (item) {
          const pElement = item.parentNode
          if (pElement) {
            pElement.removeChild(item)
          }
        }
      }
      this.dragX = ''
      this.dragY = ''
      this.dragIndex = ''
      this.dragType = ''
    },
    /*
     * 判断用户是否有tab权限
     * item 每一个tab值
     * return true 没有权限
     * return false 有权限
     */
    judgeTabNoAuth(item) {
      return !item['isAuth']
    },
    // 判断最后的touch点是否在tab内
    judgeMoveIn() {
      const target = this.modulesLocal.find(item => {
        const { top, bottom, left, right } = item
        const yBool = this.dragY >= top && this.dragY <= bottom
        const xBool = this.dragX >= left && this.dragX <= right
        return yBool && xBool
      })
      return target
    },
    // 切换drag后的tab数据
    changeModulesData(index, type) {
      if (type === this.dragType) {
        if (index === this.dragIndex) {
          return
        }
        if (type === 'show-tab') {
          const oldItem = this.modulesShow[this.dragIndex]
          const newItem = this.modulesShow[index]
          this.modulesShow.splice(this.dragIndex, 1, newItem)
          this.modulesShow.splice(index, 1, oldItem)
        } else {
          const oldItem = this.modulesHide[this.dragIndex]
          const newItem = this.modulesHide[index]
          this.modulesHide[index] = oldItem
          this.modulesHide[this.dragIndex] = newItem
          this.modulesHide.splice(this.dragIndex, 1, newItem)
          this.modulesHide.splice(index, 1, oldItem)
        }
      } else {
        // 隐藏向显示移动
        if (type === 'show-tab') {
          const oldItem = this.modulesHide[this.dragIndex]
          const newItem = this.modulesShow[index]
          this.modulesShow.splice(index, 1, oldItem)
          this.modulesHide.splice(this.dragIndex, 1, newItem)
        } else {
          // 显示向隐藏移动
          const oldItem = this.modulesShow[this.dragIndex]
          const newItem = this.modulesHide[index]
          this.modulesHide.splice(index, 1, oldItem)
          this.modulesShow.splice(this.dragIndex, 1, newItem)
        }
      }
    },

    // 保存tab顺序， 并请求接口保存
    modifyTab() {
      this.modifyMode = !this.modifyMode
      if (!this.modifyMode) {
        const labelShow = this.modulesShow.map(item => item.label)
        const labelHide = this.modulesHide.map(item => item.label)
        const newLabel = [...labelShow, ...labelHide]
        this.$http
          .post('/resourceServices/logbuch/sx/update', {
            userNo: userId,
            menuContent: JSON.stringify(newLabel)
          })
          .then(res => {
            if (res.success) {
              console.log('修改成功')
            } else {
              console.log('修改失败')
            }
          })
          .catch(err => {
            console.log(err, '修改失败')
          })
      }
    },
    // 关闭底部弹窗
    closePopup() {
      this.popupMode = false
      this.popupVisible = false
      this.modifyMode = false
    }
  },
  mounted() {
    // 初始化tab展示以及路由跳转
    this.initTab()
  }
}
</script>
<style lang="less" scoped>
.tab-footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 5555;
  background: #ffffff;
  // box-shadow: 0 1px 11px 0 rgba(184, 186, 190, 0.5);
  box-shadow: 0 0.01rem 0.11rem 0 rgba(184, 186, 190, 0.5);
  height: 1.1rem;
  width: 100%;
  // background: url(../img/tab-bg-normal.png) no-repeat;
  // background-color: transparent;
  // background-size: cover;
  &--peak {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 5555;
    height: 1.1rem;
    width: 100%;
    box-shadow: 0 0.01rem 0.11rem 0 rgba(184, 186, 190, 0.5);
    background: #020e22;
    // background: url(../img/tab-bg-peek.png) no-repeat !important;
    // background-size: cover !important;
    // background-color: #0e169f !important;
  }
}
.tab-footer {
  &__content {
    position: relative;
    z-index: 5555;
  }
  &__content {
    &:first-child {
      // padding: 0 0.48rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;
    }
    > div {
      flex: 1;
    }
  }
  &__content__shift {
    position: absolute;
    top: 50%;
    left: 90%;
    transform: translate(-50%, -50%);
    // height: 0.72rem;
    // width: 0.72rem;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 20%;
    // background: #3d3d3d;
    span {
      font-family: PingFangSC-Regular;
      font-size: 0.2rem;
      color: #3d3d3d;
    }
    .theme-img {
      height: 0.58rem;
      width: 0.58rem;
      opacity: 0.5;
      transform: scale(1);
      &.active {
        opacity: 1;
        transform: scale(1.2);
        animation: themeActive 0.15s linear;
      }
    }
    i {
      color: #d0d5df;
      font-size: 0.48rem;
    }
    &--active {
      i {
        color: #dc1e23 !important;
        // margin-top: 0.06rem;
        font-size: 0.48rem;
      }
      span {
        color: #dc1e23 !important;
      }
      // background: #dc1e23;
    }
    &--peak {
      // background: rgba(255, 255, 255, 0.5) !important;
      i {
        color: rgba(255, 255, 255, 0.6) !important;
      }
      span {
        color: rgba(255, 255, 255, 0.6) !important;
      }
    }
    &--peak--active {
      // background: #fff !important;
      i {
        color: #fff !important;
      }
      span {
        color: #fff !important;
      }
    }
  }
  &__hide {
    width: 100%;
  }
}

.tab-footer__hide {
  padding: 0.2rem 0 1.18rem;
  .tab_title {
    padding-bottom: 0.2rem;
    padding-right: 0.2rem;
    display: flex;
    justify-content: flex-end;
    font-family: PingFangSC-Regular;
    font-size: 0.24rem;
    color: #3d3d3d;
    &.normal {
      border-bottom: 1px solid #f2f2f2;
      .btn_edit {
        color: #3d3d3d;
        width: 1.2rem;
        height: 0.6rem;
        // border-radius: 0.1rem;
        // border: 1px solid #dc1e32;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    &.peak {
      border-bottom: 1px solid #020e22;
      .btn_edit {
        color: #fff;
        width: 1.2rem;
        height: 0.6rem;
        border-radius: 0.1rem;
        border: 1px solid #fff;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  &__content {
    margin: 0.64rem 0;
    padding: 0 0.2rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.64rem 0px;
  }
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 1.29rem;
    i {
      color: #dddddd;
    }
    &--active {
      > div {
        color: rgba(220, 30, 35, 0.5) !important;
        &:first-child {
          background: rgba(220, 30, 35, 0.5) !important;
          i {
            color: #fff !important;
          }
        }
      }
    }
    &--modify {
      i {
        animation: modifyMode 0.3s infinite;
      }
    }
    > div {
      &:first-child {
        background: rgba(221, 221, 221, 0.3);
        height: 0.8rem;
        width: 0.8rem;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 0.16rem;
        i {
          font-size: 0.48rem;
        }
      }
    }
    &--noAuth {
      color: #dddddd;
      i {
        color: #dddddd;
      }
    }
    &--peak {
      div:first-child {
        background: rgba(221, 221, 221, 0.3) !important;
        i {
          color: rgba(255, 255, 255, 0.6) !important;
        }
      }
      color: rgba(255, 255, 255, 0.6) !important;
    }
    &--peak--noAuth {
      div:first-child {
        background: rgba(221, 221, 221, 0.1) !important;
        i {
          color: rgba(255, 255, 255, 0.2) !important;
        }
      }
      div:last-child {
        color: rgba(255, 255, 255, 0.2) !important;
      }
    }
    &--peak--active {
      div:first-child {
        background: rgba(221, 221, 221, 0.4) !important;
        i {
          color: #fff !important;
        }
      }
      div:last-child {
        color: #fff !important;
      }
    }
  }
  &__footer {
    font-family: PingFangSC-Regular;
    font-size: 0.2rem;
    color: #999999;
    text-align: center;
    margin-bottom: 0.2rem;
  }
}

.tab-footer__hide--peak {
  .tab-footer__hide__footer {
    color: #fff !important;
  }
}
.tab-item {
  &__icon {
    height: 0.48rem;
    width: 0.48rem;
  }
  &--normal {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    // padding-top: 0.16rem;
    i {
      font-size: 0.48rem;
      margin-bottom: 0.08rem;
      color: #d0d5df;
    }
    div {
      font-family: PingFangSC-Regular;
      font-size: 0.2rem;
      color: #3d3d3d;
    }
    &:nth-child(4) {
      margin-right: 20%;
    }
    .theme-img {
      height: 0.58rem;
      width: 0.58rem;
      margin-bottom: 0.08rem;
      opacity: 0.5;
      transform: scale(1);
      &.active {
        opacity: 1;
        transform: scale(1.2);
        animation: themeActive 0.15s linear;
      }
    }
  }
  &--active {
    color: #dc1e23;
    i {
      color: #dc1e23;
    }
    div {
      color: #dc1e23;
    }
  }
  &--normal-popup {
    color: rgba(61, 61, 61, 0.5);
    div {
      color: rgba(61, 61, 61, 0.5);
    }
  }
  &--active-popup {
    color: rgba(220, 30, 35, 0.5);
    div {
      color: rgba(220, 30, 35, 0.5);
    }
  }
  &--active--sp {
    background: #dc1e23 !important;
    color: #fff !important;
  }
  &--modify {
    i {
      animation: modifyMode 0.3s infinite;
    }
  }
  &--drag {
    position: absolute;
  }
  &--noAuth {
    div {
      color: #dddddd;
    }
    i {
      color: #dddddd;
    }
  }
  &--peak {
    div {
      color: rgba(255, 255, 255, 0.6) !important;
    }
    i {
      color: rgba(255, 255, 255, 0.6) !important;
    }
  }
  &--peak--noAuth {
    div {
      color: rgba(255, 255, 255, 0.2) !important;
    }
    i {
      color: rgba(255, 255, 255, 0.2) !important;
    }
  }
  &--peak--active {
    div {
      color: #fff !important;
    }
    i {
      color: #fff !important;
    }
  }
}

@keyframes themeActive {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes modifyMode {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-20deg);
  }
  75% {
    transform: rotate(20deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.tab-item--drag {
  position: absolute;
  z-index: 9999;
  opacity: 0.8;
  transform: scale(1.1);
}
</style>
