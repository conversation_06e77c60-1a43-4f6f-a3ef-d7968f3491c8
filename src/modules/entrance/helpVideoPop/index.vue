<template>
  <div class="help-pop-frame">
    <transition enter-active-class="animate__bounceIn" leave-active-class="animate__bounceOut">
      <div class="popup-wrap animate__animated" v-if="mainShow">
        <div class="pop-main">
          <img src="./img/popup_little_princess_bg.png"/>
        </div>
        <div class="pop-btn" @click="viewVideo">
          立即观看
        </div>
        <div class="pop-close" @click="closeHelpPopup">
          关闭
        </div>
      </div>
    </transition>
    <img src="./img/popup_little_princess_bg.png" @load="imgFinish" style="visibility: hidden; position: absolute; z-index: -1"/>
  </div>
</template>
<script>
export default {
  name: 'helpPop',
  props: {
  },
  computed: {},
  data() {
    return {
      mainShow: false
    }
  },
  methods: {
    closeHelpPopup() {
      this.mainShow = false
      setTimeout(() => {
        this.$emit('closeHelpPopup')
      }, 1000)
    },
    async viewVideo() {
      await this.closeHelpPopup()
      this.$emit('selectHelp')
    },
    imgFinish() {
      this.mainShow = true
    }
  },
  mounted() {
  }
}
</script>
<style lang="less" scoped>
.help-pop-frame {
  position: fixed;
  top: 0;
  z-index: 9999;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.7);
  overflow: hidden;
  display: flex;
  .popup-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    align-self: center;
    .pop-main {
      width: 85%;
      img {
        width: 100%;
      }
    }
    .pop-btn {
      width: 35%;
      margin-top: -16%;
      background-color: #DC1E32;
      height: .8rem;
      line-height: .8rem;
      text-align: center;
      color: #fff;
      font-size: .28rem;
      border-radius: 0.04rem;
      animation: mymove 2s infinite;
      animation-direction: alternate; /*轮流反向播放动画。*/
      animation-timing-function: ease-in-out; /*动画的速度曲线*/
      /* Safari 和 Chrome */
      -webkit-animation: mymove 2s infinite;
      -webkit-animation-direction: alternate; /*轮流反向播放动画。*/
      -webkit-animation-timing-function: ease-in-out; /*动画的速度曲线*/
    }
    .pop-close {
      width: 1.32rem;
      height: .66rem;
      border-radius: .04rem;
      border: 1px solid #fff;
      color: #fff;
      line-height: .66rem;
      margin-top: 1.2rem;
      text-align: center;
    }
  }
}
@keyframes mymove {
  0% {
    transform: scale(1); /*开始为原始大小*/
  }
  25% {
    transform: scale(1.2); /*放大1.1倍*/
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.2);
  }
}

@-webkit-keyframes mymove /*Safari and Chrome*/ {
  0% {
    transform: scale(1); /*开始为原始大小*/
  }
  25% {
    transform: scale(1.2); /*放大1.1倍*/
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.2);
  }
}
</style>
