/*
 * @Author: shigl
 * @Date: 2022-07-18 15:18:13
 * @LastEditTime: 2024-05-13 14:03:47
 * @Description:
 */
export default [
  {
    path: '/sxMonitor',
    name: 'sxMonitor',
    component: 'entrance/index',
    params: {
      title: '顺心战况',
      keepAlive: true,
      pageLevel: '1' // 当前模块主路由的值为1， 其他页面为2或3或更大数据，如果是1，则路由跳转时，会卸载改路由的缓存，导致功能出错
    },
    children: [
      {
        path: '/sxMonitor/terminal',
        component: 'terminal/index',
        params: {
          title: '末端',
          keepAlive: true,
          pageLevel: '1'
        }
      },
      {
        path: '/sxMonitor/market',
        component: 'market/index',
        params: {
          title: '市场',
          keepAlive: true,
          pageLevel: '1'
        }
      },
      {
        path: '/sxMonitor/operationNew',
        component: 'operationNew/index',
        params: {
          title: '营运',
          keepAlive: true,
          pageLevel: '1'
        }
      },
      {
        path: '/sxMonitor/qualityBord',
        component: 'qualityBord/index',
        params: {
          title: '质控',
          keepAlive: true,
          pageLevel: '1'
        }
      },

      {
        path: '/sxMonitor/finance',
        component: 'financeNew/index',
        params: {
          title: '财务',
          keepAlive: true,
          pageLevel: '1'
        }
      }
      // 下线
      // {
      //   path: '/sxMonitor/network',
      //   component: 'network/index',
      //   params: {
      //     title: '网络',
      //     keepAlive: true,
      //     pageLevel: '1'
      //   }
      // },
      // {
      //   path: '/sxMonitor/peekPop',
      //   component: 'businessPeak/index',
      //   params: {
      //     title: '实时',
      //     keepAlive: true,
      //     pageLevel: '1'
      //   }
      // },
    ]
  }
]
