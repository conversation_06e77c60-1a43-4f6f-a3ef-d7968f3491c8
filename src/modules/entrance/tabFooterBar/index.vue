<!--
 * @Author: shigl
 * @Date: 2023-08-27 00:44:55
 * @LastEditTime: 2024-05-14 11:28:10
 * @Description:
-->
<template>
  <div>
    <div class="tab-footter-container" :class="{ 'theme-peak': false }">
      <!-- 有forbid样式不可推拽 -->
      <draggable
        :list="showTabData"
        style="height: 100%"
        :group="groupA"
        :move="onMove"
        @start="onStart"
        @end="onEnd"
        filter=".forbid"
        animation="300"
        chosenClass="chosenClass"
        delay="1000"
        :disabled="isMove ? !isMove : !isModify"
      >
        <transition-group class="tab-content-wrap" :css="false">
          <!-- key用index,用item.value页面会出现闪烁 -->
          <div
            class="tab-item"
            :class="{
              'modify-animation': isModify && item.value !== 'tab-shift',
              'forbid': item.value === 'tab-shift',
              'active': selectedTab === item.value || (item.value === 'tab-shift' && popupVisible),
              'no-level': !item.isAuth && item.value !== 'tab-shift'
            }"
            v-for="(item, index) in showTabData"
            :key="item.value"
            @click="changeSelected(item)"
          >
            <i v-if="!showThemeTab" :class="`iconfont icon-${item.icon}`"></i>
            <img
              :class="{ 'img-active': selectedTab === item.value || (item.value === 'tab-shift' && popupVisible) }"
              v-if="showThemeTab"
              :src="iconList[index]"
              alt=""
            />
            <div class="label">{{ item.label }}</div>
          </div>
        </transition-group>
      </draggable>
    </div>
    <PopupTab
      :visible="popupVisible"
      :isModify="isModify"
      :peakMode="false"
      @closePopup="closePopup"
      @changeModifyTab="changeModifyTab"
    >
      <draggable
        :list="hideTabData"
        :group="groupB"
        @start="onStart"
        :move="onMove"
        @end="onEnd"
        animation="300"
        ghostClass="ghostClass"
        chosenClass="chosenClass"
        :disabled="!isModify"
      >
        <!-- <div slot="content" class="tab-hide-wrap" :class="{ 'theme-hide-peak': selectedTab === 'peekPop' }"> -->
        <transition-group class="tab-hide-wrap" :class="{ 'theme-hide-peak': selectedTab === 'peekPop' }" :css="false">
          <div
            class="hide-item"
            :class="{
              'modify-animation': isModify,
              'active': selectedTab === item.value,
              'no-level': !item.isAuth
            }"
            v-for="item in hideTabData"
            :key="item.value"
            @click="changeSelected(item)"
            group="people"
            :sort="false"
          >
            <div class="icon-box">
              <i :class="`iconfont icon-${item.icon}`"></i>
            </div>
            <div class="label">{{ item.label }}</div>
          </div>
        </transition-group>
        <!-- </div> -->
      </draggable>
    </PopupTab>
  </div>
</template>
<script>
import PopupTab from './popupTab.vue'
import draggable from 'vuedraggable'

export default {
  props: {
    isAuth: {
      type: Boolean
    },
    // 菜单数据
    latestModules: {
      type: Array,
      default: () => []
      // icon:"finance"
      // isAuth:true
      // isLevel:true
      // label:"财务"
      // parentId:"5ffbc326967711eaadbdd4ae52789958"
      // route:"/sxMonitor/finance"
      // themeCode:"shunxinzhankuangcaiwu"
      // themeId:"a3c119d51b4347098f5cf5806d4eb914"
      // themeName:"财务"
      // value:"finance"
    },
    // 菜单标题 (为兼容旧的菜单点击事件,必传)
    moduleTitle: {
      type: String
    },
    // 是否显示iconList菜单图标
    showThemeTab: {
      type: Boolean,
      default: false
    },
    // 菜单icon
    iconList: {
      type: Array,
      default: () => []
    },
    // 显示的菜单个数
    showNum: {
      type: Number,
      default: 5
    },
    // 无权限的tab是否能点击
    noAuthBtn: {
      type: Boolean,
      default: true
    }
  },
  components: { PopupTab, draggable },
  data() {
    return {
      moreData: {
        label: '更多',
        value: 'tab-shift',
        icon: 'tab-shift',
        route: ''
      },

      selectedTab: '',
      popupVisible: false,
      isModify: false,
      isBtnMore: false,
      groupA: {
        name: 'itxst',
        pull: false,
        put: true // 可以拖入
      },
      groupB: {
        name: 'itxst',
        pull: true,
        put: true
      },
      disabled: true,
      showTabData: [],
      hideTabData: [],
      replaceData: {}, // 被替换的数据
      dragData: {}, // 拖拽的数据
      dragIndex: 0,
      isHideDrag: false, // 判断是否在隐藏菜单中拖动
      endModulesData: []
    }
  },
  computed: {
    isMove() {
      return this.latestModules.length <= this.showNum
    },
    isShowMore() {
      return this.latestModules.length > this.showNum
    }
  },
  watch: {
    latestModules(val, old) {
      if (val.length) {
        this.initData()
        this.initTab()
      }
    },
    isModify(val) {
      if (!val) {
        this.$emit('onModify', this.endModulesData)
      }
    }
  },
  mounted() {
    this.initData()
    this.initTab()
  },
  methods: {
    initData() {
      console.log(JSON.parse(JSON.stringify(this.latestModules)), 'latestModules')
      let tmp = []
      if (this.isShowMore) {
        tmp = this.latestModules.slice(0, this.showNum)
        tmp.push(this.moreData)
      } else {
        tmp = this.latestModules.slice(0)
      }
      this.showTabData = tmp
      this.hideTabData = this.latestModules.slice(this.showNum)
    },
    // 初始化tab展示以及路由跳转
    initTab() {
      this.initData()
      const query = this.$route.query
      if (!query.from) {
        if (this.moduleTitle) {
          const obj = this.latestModules.find(item => item['label'] === this.moduleTitle) || {}
          this.selectedTab = obj.value
          this.$emit('onSelectedTab', obj)
        } else {
          console.log(this.showTabData[0]?.value, 'this.showTabData[0]?.value')
          this.selectedTab = this.showTabData[0]?.value
          this.$emit('onSelectedTab', this.showTabData[0] || {})
        }
      }
    },
    changeSelected(item) {
      if (item.value === 'tab-shift') {
        this.popupVisible = !this.popupVisible
        this.isModify = false
      } else {
        if (!item['isAuth'] && !this.noAuthBtn) return // 无权限的菜单是否点击
        this.selectedTab = item.value
        this.$emit('onSelectedTab', item)
        this.popupVisible = false
        this.isModify = false
      }
    },
    changeModifyTab() {
      this.isModify = !this.isModify
    },
    closePopup() {
      this.popupVisible = false
      this.isModify = false
    },
    onStart(e) {
      this.replaceData = {}
      this.dragData = {}
    },

    onEnd(e) {
      if (this.replaceData.value) {
        const hideData = JSON.parse(JSON.stringify(this.hideTabData))
        // 分组拖拽,重新赋值有页面闪烁问题(需要取消transition-group动画 css=false)
        if (!this.isHideDrag) {
          const replaceIndex = this.showTabData.findIndex(item => item['value'] === this.replaceData.value)
          if (replaceIndex !== -1) {
            this.showTabData.splice(replaceIndex, 1, this.dragData)
          }
          const dragIndex = this.hideTabData.findIndex(item => item.value === this.dragData.value)
          if (dragIndex !== -1) {
            this.hideTabData.splice(dragIndex, 1, this.replaceData)
          }
          // 只是在隐藏菜单中拖拽
        } else {
          const replaceIndex = this.hideTabData.findIndex(item => item['value'] === this.replaceData.value)
          if (replaceIndex !== -1) {
            hideData.splice(replaceIndex, 1, this.dragData)
          }
          const dragIndex = this.hideTabData.findIndex(item => item.value === this.dragData.value)
          if (dragIndex !== -1) {
            hideData.splice(dragIndex, 1, this.replaceData)
          }
          this.hideTabData = hideData
        }
      }
      this.endModulesData = [...this.showTabData, ...this.hideTabData]
      this.$emit('onEndMove', this.endModulesData)
    },
    onMove(e) {
      this.replaceData = {}
      if (e.relatedContext.element.value === 'tab-shift') {
        return false
      }
      if (e.draggedContext.element.value === 'tab-shift') {
        return false
      }

      if (/tab-hide-wrap/.test(e.from.className)) {
        // 在隐藏菜单中
        if (/tab-hide-wrap/.test(e.to.className)) {
          // 如果先移动了,同步更新数据
          this.hideTabData = e.relatedContext.list
          // this.replaceData = {}
          const futureIndex = e.relatedContext.index
          // 被替换的数据
          this.replaceData = this.hideTabData[futureIndex]
          // 拖拽的数据
          this.dragData = e.draggedContext.element
          this.isHideDrag = true
          return false
        }
        // 在底部菜单中
        if (/tab-content-wrap/.test(e.to.className)) {
          const futureIndex = e.relatedContext.index
          // 被替换的数据
          this.replaceData = this.showTabData[futureIndex]
          // 拖拽的数据
          this.dragData = e.draggedContext.element
          this.isHideDrag = false
          return false
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
@active: #dc1e23;
@hide-active: rgba(220, 30, 35, 0.7);
@peak-bgc-color: #020e22;
// 020e22
@peak-icon-color: rgba(255, 255, 255, 0.6);
@peak-label-color: rgba(255, 255, 255, 0.6);
@peak-active: rgba(255, 255, 255, 1);

.ghostClass {
  // box-shadow: 2px 2px 2px rgba(255, 255, 255, 0.6) !important;
  // border: 1px solid #f2f2f2 !important;
  // z-index: 999999 !important;
  // position: absolute !important;
}
.ghostClassPeak {
  // border: 1px solid #3747b1 !important;
}
.chosenClass {
  // z-index: 999999 !important;
  // position: absolute !important;
}
.tab-footter-container {
  position: fixed;
  width: 100vw;
  height: 1.1rem;
  bottom: 0;
  left: 0;
  z-index: 5555;
}
.tab-content-wrap {
  display: flex;
  align-items: center;
  height: 100%;
  box-shadow: 0 0.01rem 0.11rem 0 rgba(184, 186, 190, 0.5);
  background-color: #fff;
}
.tab-hide-wrap {
  margin: 0.2rem 0;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.4rem 0rem;
}
.tab-item {
  height: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  padding: 0.08rem;
  position: relative;
  // border: 1px solid #000;
  border-radius: 0.08rem;
  box-sizing: border-box;
  &.modify-animation {
    .iconfont {
      animation: modifyMode 0.3s infinite;
    }
  }
  .iconfont {
    font-size: 0.46rem;
    color: #d0d5df;
  }
  img {
    width: 0.46rem;
    height: 0.46rem;
    &.img-active {
      transform: scale(1.2);
      transition: all 300ms linear;
    }
  }
  .label {
    font-family: PingFangSC-Regular;
    font-size: 0.2rem;
    color: #3d3d3d;
    margin-top: 0.08rem;
  }
  // 遮罩
  &.shade {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.5);
      top: 0;
      left: 0;
    }
  }
  //选中
  &.active {
    .iconfont {
      color: @active;
    }
    .label {
      color: @active;
    }
  }
}
.hide-item {
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  padding: 0.08rem;
  position: relative;
  // border: 1px solid #0f0;
  border-radius: 0.08rem;
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0);
  &.modify-animation {
    .iconfont {
      animation: modifyMode 0.3s infinite;
    }
  }
  .icon-box {
    width: 0.76rem;
    height: 0.76rem;
    border-radius: 50%;
    background: rgba(221, 221, 221, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    .iconfont {
      font-size: 0.46rem;
      color: #d0d5df;
    }
  }
  .label {
    margin-top: 0.1rem;
    font-family: PingFangSC-Regular;
    font-size: 0.2rem;
    color: #3d3d3d;
  }
  &.active {
    .icon-box {
      background: @hide-active;
      .iconfont {
        color: #fff;
      }
    }
    .label {
      color: @active;
    }
  }
}
.theme-peak {
  .tab-content-wrap {
    background-color: @peak-bgc-color;
  }
  .tab-item {
    .iconfont {
      color: @peak-icon-color;
    }
    .label {
      color: @peak-label-color;
    }
    &.active {
      .iconfont {
        color: @peak-active;
      }
      .label {
        color: @peak-active;
      }
    }
    &.shade {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(14, 22, 159, 0.3);
        top: 0;
        left: 0;
      }
    }
  }
}
.theme-hide-peak {
  .hide-item {
    .icon-box {
      .iconfont {
        font-size: 0.46rem;
        color: @peak-icon-color;
      }
    }
    .label {
      color: @peak-label-color;
    }
    &.active {
      .icon-box {
        background: rgba(221, 221, 221, 0.6);
        .iconfont {
          color: @peak-active;
        }
      }
      .label {
        color: @peak-active;
      }
    }
  }
}
// 无权限
.no-level {
  opacity: 0.5;
  // .icon-box {
  //   .iconfont {
  //   }
  // }
  .label {
    opacity: 0.5;
  }
}
@keyframes modifyMode {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-20deg);
  }
  75% {
    transform: rotate(20deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
</style>
