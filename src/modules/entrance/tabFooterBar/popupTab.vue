<!--
 * @Author: shigl
 * @Date: 2022-05-20 10:54:47
 * @LastEditTime: 2023-12-04 11:35:02
 * @Description: 底部导航栏弹出框
-->

<template>
  <div class="tab-footer-popup-container">
    <div class="ky-popup-mask" @click="closeModal" v-show="visible"></div>
    <div
      :class="{
        'ky-popup-content': true,
        'show': visible,
        'hide': !visible,
        'peak': peakMode
      }"
      ref="kyPopupContent"
    >
      <div class="popup-title">
        <div class="btn-edit" @click="modifyTab">{{ modityText }}</div>
      </div>
      <div class="popup-content">
        <slot></slot>
      </div>
      <div class="popup-footer">—— 编辑可以随意进行位置替换 ——</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PopupTab',
  props: {
    visible: Boolean,
    isModify: Boolean,
    peakMode: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    modityText() {
      return !this.isModify ? '编辑' : '完成'
    }
  },
  watch: {
    isModify(val) {
      if (val) {
        this.$refs['kyPopupContent'].style.zIndex = '5555'
      } else {
        this.$refs['kyPopupContent'].style.zIndex = '5554'
      }
    }
  },
  methods: {
    closeModal() {
      this.$emit('closePopup')
    },
    modifyTab() {
      this.$emit('changeModifyTab')
    }
  },
  mounted() {
    const targetEle = document.querySelector('.tab-footer-popup-container')
    if (targetEle) {
      const bodyDom = document.body
      // const bgDiv = document.createElement('div')
      // bgDiv.classList.add('tab-footer-popup-container')
      // bgDiv.appendChild(this.$el)
      bodyDom.appendChild(targetEle)
    }
  }
}
</script>

<style lang="less" scoped>
// .tab-footer-popup-container {
// }
.ky-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5553;
  height: 100vh;
  width: 100vw;
  background: rgba(0, 0, 0, 0.6);
  overflow: hidden;
}
.ky-popup-content {
  position: fixed;
  width: 100%;
  background: #fff;
  z-index: 5554;
  bottom: 0rem;
  left: 0;
  transform: translate(0, 100%);
  border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
  transition: all 1s linear;
  // border-bottom: 1px solid #f2f2f2;
  .popup-title {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 0.8rem;
    padding: 0 0.2rem;
    font-family: PingFangSC-Regular;
    font-size: 0.24rem;
    color: #3d3d3d;
    border-bottom: 1px solid #f2f2f2;
    .btn-edit {
      font-size: 0.28rem;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0.08rem 0.2rem;
      border: 1px solid #f2f2f2;
      border-radius: 0.08rem;
    }
  }
  .popup-content {
    min-height: 2rem;
    padding: 0.16rem 0.2rem;
  }
  .popup-footer {
    font-family: PingFangSC-Regular;
    font-size: 0.2rem;
    color: #999999;
    text-align: center;
    margin-bottom: 0.2rem;
  }
  &.show {
    transition: all 0.3s ease-in;
    transform: translate(0, -1.1rem);
  }
  &.hide {
    transition: all 0.3s ease-in;
  }
  &.peak {
    background: #020e22 !important;
    .popup-title {
      border-bottom: 1px solid #3d3d3d;
      .btn-edit {
        border: 1px solid #3d3d3d;
        color: #fff;
      }
    }
    .popup-footer {
      color: rgba(255, 255, 255, 0.6);
    }
  }
}
</style>
