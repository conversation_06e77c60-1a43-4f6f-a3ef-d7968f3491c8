<template>
  <div class="roller-bar">
    <div
      :class="['roller-bar-item', value === item.value ? 'roller-bar-item--active' : '']"
      v-for="item in dataSource"
      :key="item.value"
      @click="handleRollerBarClick(item.value, item)"
    >
      {{item.label}}
      <span></span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    dataSource: {
      type: Array
    },
    value: {
      type: Number
    }
  },
  methods: {
    handleRollerBarClick(val, item) {
      this.$emit('handleClick', val, item)
    }
  }
}
</script>
<style lang='less' scoped>
.roller-bar {
  display: flex;
  height: 0.88rem;
  align-items: center;
  box-shadow: 0 0.1rem 0.2rem rgba(190, 190, 206, 0.2);
  position: relative;
  background-color: #ffffff;
  .roller-bar-item {
    height: 0.88rem;
    line-height: 0.88rem;
    text-align: center;
    flex: 1;
    font-size: 0.28rem;
    color: #808285;
    box-sizing: border-box;
    position: relative;
    > span {
      position: absolute;
      width: 0.48rem;
      height: 2px;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
    }
  }
  .roller-bar-item--active {
    color: #dc1e32;
    > span {
      border-bottom: 2px solid #dc1e32;
    }
  }
}
</style>
