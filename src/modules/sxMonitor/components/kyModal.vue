<template>
  <div>
    <div class="ky-modal-content" v-show="visible" :style="{ width: width, height: height }">
      <slot></slot>
    </div>
    <div class="modal-close" @click="closeModal" v-show="visible"></div>
  </div>
</template>

<script>
export default {
  name: 'kymodal',
  props: {
    visible: Boolean,
    height: {
      type: String,
      default: '45%'
    },
    width: {
      type: String,
      default: '85.3%'
    }
  },
  watch: {
    visible(newVal, oldVal) {
      const targetEle = document.querySelector('.ky-modal-wrap')
      if (newVal) {
        const bodyDom = document.body
        const bgDiv = document.createElement('div')
        bgDiv.setAttribute('class', 'ky-modal-wrap')
        bgDiv.appendChild(this.$el)
        bodyDom.appendChild(bgDiv)
      } else if (!newVal && targetEle) {
        const pElement = targetEle.parentNode
        if (pElement) {
          pElement.removeChild(targetEle)
        }
      }
    }
  },
  methods: {
    closeModal() {
      this.$emit('closeModal')
    }
  }
}
</script>

<style>
.ky-modal-wrap {
  position: absolute;
  top: 0;
  z-index: 9;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
}
.ky-modal-content {
  position: absolute;
  background: #fff;
  border-radius: 0.08rem;
  left: 50%;
  top: 45%;
  transform: translate(-50%, -50%);
}
.modal-close {
  height: 0.6rem;
  width: 0.6rem;
  margin-top: 0.5rem;
  background: url('../img/close2.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 67.5%;
  border: 0;
  left: 50%;
  transform: translate(-50%, 0);
}
</style>
