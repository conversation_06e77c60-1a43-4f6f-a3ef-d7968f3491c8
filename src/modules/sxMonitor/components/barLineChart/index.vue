<template>
  <div class="barLineChart" id="barLineCharts">
  </div>
</template>

<script>
export default {
  props: {
    barSeriesData: {
      type: Array
    },
    lineSeriesData: {
      type: Array
    },
    xAxisData: {
      type: Array
    },
    formatter: {
      type: Function
    },
    grid: {
      type: Object
    },
    xAxis: {
      type: Array
    },
    yAxis: {
      type: Array
    },
    tooltip: {
      type: Object
    },
    series: {
      type: Array
    }
  },
  computed: {
    chartOption() {
      const baseOption = {
        baseTooltip: {
          show: true,
          padding: [5, 15, 5, 15],
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: '#E9E9E9',
              opacity: 0.5
            },
            z: -1
          },
          formatter: params => {
          }
        },
        baseGrid: {
          left: '-8%',
          right: '2%',
          top: '10%',
          bottom: '2%',
          containLabel: true
        },
        baseXAxis: [
          {
            type: 'category',
            data: this.xAxisData,
            axisTick: { show: false },
            axisLabel: {
              show: true,
              interval: 0,
              margin: 10,
              textStyle: {
                color: '#666666'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#ccc'
              }
            }
          }
        ],
        baseYAxis: [
          {
            show: false
          },
          {
            show: false
          }
        ],
        baseSeries: [
          {
            type: 'bar',
            barWidth: 10,
            data: this.barSeriesData,
            yAxisIndex: 0,
            itemStyle: {
              normal: {
                barBorderRadius: 5,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#27AEFF' },
                  { offset: 1, color: '#2E55EC' }
                ])
              }
            }
          },
          {
            type: 'line',
            yAxisIndex: 1,
            itemStyle: {
              normal: {
                color: '#FF6A17'
              }
            },
            data: this.lineSeriesData
          }
        ]
      }
      const newXaxis = this.xAxis ? this.xAxis.map(item => {
        return { ...baseOption.baseXAxis, ...item }
      }) : baseOption.baseXAxis

      const newYaxis = this.yAxis ? this.yAxis.map((item, index) => {
        return { ...baseOption.baseYAxis[index], ...item }
      }) : baseOption.baseYAxis

      const newSeries = this.series ? this.series.map((item, index) => {
        return { ...baseOption.baseSeries[index], ...item }
      }) : baseOption.baseSeries

      const option = {
        tooltip: { ...baseOption.baseTooltip, ...this.tooltip },
        grid: { ...baseOption.baseGrid, ...this.grid },
        xAxis: newXaxis,
        yAxis: newYaxis,
        series: newSeries
      }
      return option
    }
  },
  data() {
    return {
    }
  },
  mounted() {
    const instance = this.$echarts.init(document.querySelector('#barLineCharts'))
    instance.setOption(this.chartOption)
  },
  methods: {
  }
}
</script>

<style lang='less' scoped>
.barLineChart{
  width: 100%;
  height: 100%;
}
</style>
