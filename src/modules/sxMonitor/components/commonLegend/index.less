.common-legend-inline-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .common-legend-inline__item {
    display: flex;
    align-items: center;
    margin-bottom: 0.24rem;

    &__icon {
      margin-right: 0.08rem;
    }
    &__label {
      margin-right: 0.08rem;
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      color: #666666;
    }
    &__value {
      font-size: 0.24rem;
      color: #333;
      font-weight: 700;
    }
  }
}

.common-legend-block-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  .common-legend-block__item {
    display: flex;
    justify-content: center;
    .common-legend-block-icon {
      margin-right: 0.08rem;
      & > i {
        font-size: 0.18rem;
        line-height: 0.33rem;
      }
    }
  }

  .common-legend-block__start {
    justify-content: flex-start;
  }
  .common-legend-block__end {
    justify-content: flex-end;
  }

  .common-legend-block__item__label {
    font-family: PingFangSC-Regular;
    font-size: 0.24rem;
    color: #666;
    line-height: 0.33rem;
  }
  .common-legend-block__item__value {
    font-family: PingFangSC-Medium;
    font-size: 0.24rem;
    line-height: 0.33rem;
    color: #333;
    font-weight: 700;
  }
}
