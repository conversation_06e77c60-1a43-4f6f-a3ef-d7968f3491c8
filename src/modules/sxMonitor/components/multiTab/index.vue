<template>
  <div class="multiTab">
    <div
      v-for="(item, index) in tabData"
      @click="tabSelectClick(index, item)"
      :class="[tabIndex === index ? 'item--active' : '']"
      :key="index"
    >
      {{ typeof item.label === 'function' ? item.label() : item.label }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'multiTab',
  props: {
    tabData: {
      type: Array
    },
    tabIndex: {
      type: Number
    }
  },
  data() {
    return {}
  },
  methods: {
    tabSelectClick(index, item) {
      this.$emit('tabSelect', index, item.value, item)
    }
  }
}
</script>

<style lang="less" scoped>
.multiTab {
  display: flex;
  height: 0.56rem;
  > div {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    border: 1px solid #dc1e32;
    background: #fff;
    color: #dc1e32;
    font-family: sans-serif;
    font-size: 0.28rem;
    &:first-child {
      border-top-left-radius: 0.25rem;
      border-bottom-left-radius: 0.25rem;
    }
    &:last-child {
      border-top-right-radius: 0.25rem;
      border-bottom-right-radius: 0.25rem;
    }
    &:not(:last-child) {
      border-right: 0;
    }
  }
  .item--active {
    color: #fff;
    background: #dc1e32 !important;
  }
}
</style>
