<template>
  <div class="wrapper">
    <div ref="scroll" class="scroll">
      <div ref="contentBox" :style="{ width: width }">
        <slot></slot>
      </div>
    </div>
    <div class="scroll-bar" v-show="showBar">
      <div class="limit">
        <div :style="{ left: leftRatio }"></div>
      </div>
    </div>
  </div>
</template>

<script>
import BScroll from 'better-scroll'
export default {
  props: {
    width: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      leftRatio: 0,
      showBar: true
    }
  },
  watch: {
    width() {
      setTimeout(() => {
        this.setScroll()
      }, 10)
    }
  },
  mounted() {
    this.setScroll()
  },
  activated() {
    this.setScroll()
  },
  methods: {
    setScroll() {
      const scrollBox = this.$refs.scroll
      const { width } = scrollBox.getBoundingClientRect()
      const { width: cWidth } = this.$refs.contentBox.getBoundingClientRect()
      if (this.bsInstance) {
        this.bsInstance.refresh()
      } else {
        this.bsInstance = new BScroll(scrollBox, {
          scrollX: true,
          scrollY: false,
          probeType: 3,
          bounce: false,
          click: true,
          stopPropagation: true
        })
      }
      this.bsInstance.on('scroll', ev => {
        const ratio = Math.abs(ev.x / (cWidth - width))
        this.leftRatio = `${ratio * 100}%`
      })
      // 如不用滚动就隐藏
      this.showBar = cWidth > width
    }
  }
}
</script>

<style lang="less" scoped>
.wrapper {
  position: relative;
  padding-bottom: 0.6rem;
  overflow: hidden;

  .scroll-bar {
    position: absolute;
    top: 100%;
    left: 50%;
    margin: -5%;
    width: 10%;
    height: 0.06rem;
    border-radius: 0.28rem;
    background: #f4f4f4;

    @barWidht: 56.25%;
    .limit {
      position: relative;
      width: 100% - @barWidht;
      height: 100%;
      > div {
        position: absolute;
        top: 0;
        left: 0;
        width: @barWidht / (100% - @barWidht) * 100;
        height: 100%;
        border-radius: 0.28rem;
        background: #dc1e32;
      }
    }
  }
}
</style>
