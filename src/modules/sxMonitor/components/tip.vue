<template>
  <div class="tip-wrap">
    <div class="sp-item">
      <!-- <i
        v-show="(content || []).length"
        ref="questionIcon"
        class="iconfont icon-wenhao1 fs28 fontgrey"
        @click="showTip"
      ></i> -->
      <img :src="helpIcon" ref="questionIcon" @click="showTip" />
    </div>
    <div
      class="header-tip-content"
      v-show="(content || []).length && iconVisible"
      :style="{ left: '-4.76rem' }"
      ref="tipContent"
    >
      <i class="triangle"></i>
      <div v-for="item in content" :key="item">{{ item }}</div>
    </div>
  </div>
</template>

<script>
import helpIcon from 'common/img/helpGreyIcon.svg'

export default {
  props: {
    content: {
      type: Array,
      default: () => []
    },
    iconVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      contentLeft: 0,
      helpIcon
    }
  },
  watch: {
    iconVisible(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          if ((this.content || []).length) {
            // const data = this.$refs['questionIcon'].getBoundingClientRect()
            // const contentData = this.$refs['tipContent'].getBoundingClientRect()
            // const { left } = data
            // const { width } = contentData
            // const fontSize = document.documentElement.style.fontSize.slice(0, -2)
            // const rem = left / fontSize + 0.2
            // const contentWidth = width / fontSize
            // const half = rem - contentWidth / 2 > 0 ? rem - contentWidth / 2 : 0
            // this.contentLeft = `${half}rem`
          }
        })
      }
    },
    content(newVal, oldVal) {
      if (newVal && this.iconVisible) {
        this.$nextTick(() => {
          if ((newVal || []).length) {
            const data = this.$refs['questionIcon'].getBoundingClientRect()
            const contentData = this.$refs['tipContent'].getBoundingClientRect()
            const { left } = data
            const { width } = contentData
            const fontSize = document.documentElement.style.fontSize.slice(0, -2)
            const rem = left / fontSize + 0.2
            const contentWidth = width / fontSize
            const half = rem - contentWidth / 2 > 0 ? rem - contentWidth / 2 : 0
            this.contentLeft = `${half}rem`
          }
        })
      }
    }
  },
  methods: {
    showTip() {
      this.$emit('visiTrigger')
    }
  }
}
</script>

<style lang="less" scoped>
.tip-wrap {
  position: relative;
  img {
    width: 0.3rem;
  }
  .header-tip-content {
    position: absolute;
    top: 0.78rem;
    font-size: 0.22rem;
    color: #f2f2f2;
    height: auto;
    padding: 0.12rem;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    line-height: 0.26rem;
    border-radius: 0.04rem;
    align-items: flex-end;
    width: 5.2rem;
    z-index: 8;
    top: 0.5rem;
    div {
      width: 100%;
    }
  }
  .triangle {
    border-left: 0.1rem solid transparent;
    border-right: 0.1rem solid transparent;
    border-bottom: 0.173rem solid rgba(0, 0, 0, 0.7);
    width: 0px;
    height: 0px;
    position: absolute;
    top: -0.17rem;
    transform: translate(-50%, 0);
    z-index: 8;
  }
}
</style>
