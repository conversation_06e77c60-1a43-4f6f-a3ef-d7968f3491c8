<!--
 * @Author: your name
 * @Date: 2021-03-17 14:30:14
 * @LastEditTime: 2021-07-27 18:02:37
 * @LastEditors: JieJw
 * @Description: In User Settings Edit
 * @FilePath: /fop-web-ica-sfmobile/src/modules/market/components/productItem.vue
-->
<template>
  <div
    :class="{
      'product-item-wrapper': true,
      'product-item-wrapper__active': active
    }"
  >
    <span>{{ itemData.big_business_name }}({{ ['万', '吨'][type] }})</span>
    <span>{{ $numToInteger(itemData[type ? 'weight' : 'income'], 1, unit) }}</span>
    <span
      ><span>目标达成</span
      >{{ $numToPercent(itemData[type ? 'weight_target_ratio' : 'income_target_ratio'], 0) }}</span
    >
  </div>
</template>

<script>
export default {
  props: {
    itemData: Object,
    active: Boolean,
    type: Number
  },
  computed: {
    unit() {
      return [10000, 1000][this.type]
    }
  },
  methods: {}
}
</script>

<style lang="less" scoped>
.product-item-wrapper {
  height: 1.45rem;
  background: #f8f9fc;
  border: 1px solid rgba(221, 221, 221, 0.7);
  display: flex;
  flex-direction: column;
  padding: 0.12rem;
  position: relative;

  &__active {
    border: none;
    background: #dc1e23;
    box-shadow: 0 0.08rem 0.18rem 0 rgba(196, 202, 210, 0.3);
    span {
      color: #fff !important;
    }
  }

  > span {
    &:nth-of-type(1) {
      font-family: PingFangSC-Medium;
      font-size: 0.2rem;
      line-height: 0.28rem;
      color: #333;
      font-weight: 700;
    }
    &:nth-of-type(2) {
      font-family: Roboto-Medium;
      font-size: 0.28rem;
      color: #333;
      font-weight: 500;
      line-height: 0.33rem;
      margin: 0.04rem 0 0.08rem;
    }
    &:nth-of-type(3),
    &:nth-of-type(4) {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Medium;
      font-size: 0.2rem;
      color: #333;
      line-height: 0.28rem;
      font-weight: 400;
      margin-bottom: 0.08rem;
      span:first-child {
        font-family: PingFangSC-Regular;
        color: #999;
        margin-right: 0.08rem;
        font-size: 0.2rem;
      }
    }
    &:nth-of-type(4) {
      margin-bottom: 0;
      justify-content: space-between;
      span {
        margin-right: 0;
      }
    }
  }
}
</style>
