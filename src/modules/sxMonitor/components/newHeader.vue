<template>
  <div class="new-header-wrap" :style="{ borderBottom: sp ? '1px solid #f9f9f9' : 0 }">
    <div :style="{ marginLeft: sp ? '0.2rem' : '0' }" :class="[sp ? 'sp-item' : '']" class="flex_center fw700">
      {{ title
      }}
      <!-- <i
        v-show="(content || []).length"
        ref="questionIcon"
        class="iconfont icon-wenhao1 fs28 fontgrey"
        @click="showTip"
      ></i> -->
      <img v-show="(content || []).length" :src="helpIcon" ref="questionIcon" @click="showTip" class="ml8" />
    </div>
    <slot></slot>
    <div
      class="header-tip-content"
      v-show="(content || []).length && iconVisible"
      :style="{ left: contentLeft }"
      ref="tipContent"
    >
      <i class="triangle"></i>
      <div v-for="item in content" :key="item">{{ item }}</div>
    </div>
  </div>
</template>

<script>
import helpIcon from 'common/img/helpGreyIcon.svg'

export default {
  props: {
    title: String,
    sp: {
      type: Boolean,
      default: true
    },
    content: {
      type: Array,
      default: () => []
    },
    iconVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      contentLeft: 0,
      helpIcon
    }
  },
  watch: {
    iconVisible(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          if ((this.content || []).length) {
            const data = this.$refs['questionIcon'].getBoundingClientRect()
            const contentData = this.$refs['tipContent'].getBoundingClientRect()
            const { left } = data
            const { width } = contentData
            const fontSize = document.documentElement.style.fontSize.slice(0, -2)
            const rem = left / fontSize + 0.2
            const contentWidth = width / fontSize
            const half = rem - contentWidth / 2 > 0 ? rem - contentWidth / 2 : 0
            this.contentLeft = `${half}rem`
          }
        })
      }
    },
    content(newVal, oldVal) {
      if (newVal && this.iconVisible) {
        this.$nextTick(() => {
          if ((newVal || []).length) {
            const data = this.$refs['questionIcon'].getBoundingClientRect()
            const contentData = this.$refs['tipContent'].getBoundingClientRect()
            const { left } = data
            const { width } = contentData
            const fontSize = document.documentElement.style.fontSize.slice(0, -2)
            const rem = left / fontSize + 0.2
            const contentWidth = width / fontSize
            const half = rem - contentWidth / 2 > 0 ? rem - contentWidth / 2 : 0
            this.contentLeft = `${half}rem`
          }
        })
      }
    }
  },
  methods: {
    showTip() {
      this.$emit('visiTrigger')
    }
  }
}
</script>

<style lang="less" scoped>
.new-header-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 0.78rem;
  width: 100%;
  position: relative;
  > div {
    // font-family: PingFangSC-Medium;
    font-size: 0.28rem;
    color: #333333;
    display: flex;
    align-items: center;
    > i {
      margin-left: 0.08rem;
    }
  }
  .sp-item {
    font-size: 0.32rem;
    img {
        width: 0.3rem;
    }
    &::before {
      content: '';
      display: inline-block;
      height: 0.58rem;
      width: 0.08rem;
      border-radius: 0 0.1rem 0.1rem 0;
      background: #dc1e23;
      position: relative;
      left: -0.2rem;
    }
  }
  .header-tip-content {
    position: absolute;
    top: 0.78rem;
    font-size: 0.22rem;
    color: #f2f2f2;
    height: auto;
    padding: 0.12rem;
    background: rgba(0, 0, 0, 0.7);
    max-width: 5rem;
    display: flex;
    flex-direction: column;
    line-height: 0.26rem;
    border-radius: 0.04rem;
    z-index: 8;
    div {
      width: 100%;
    }
  }
  .triangle {
    border-left: 0.1rem solid transparent;
    border-right: 0.1rem solid transparent;
    border-bottom: 0.173rem solid rgba(0, 0, 0, 0.7);
    width: 0px;
    height: 0px;
    position: absolute;
    top: -0.17rem;
    transform: translate(-50%, 0);
    z-index: 8;
  }
}
</style>
