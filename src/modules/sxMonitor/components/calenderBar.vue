<template>
  <div
    class="calendar-bar-wrap"
    :class="{ disabled: disabled }"
    @click="handleDatePickerShow"
  >
    <span>{{ showValue(value) }}</span>
  </div>
</template>

<script>
import { getLastYear } from '../common/ulit'
export default {
  props: {
    value: String,
    min: [Date, Object, String],
    max: [Date, Object, String],
    disabled: {
      type: Boolean,
      default: false
    },
    format: {
      type: String
    },
    type: {
      type: String,
      default: 'day'
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      formatMap: {
        year: 'YYYY',
        month: 'YYYY-MM',
        day: 'YYYY-MM-DD',
        date: 'YYYY-MM-DD',
        week: 'YYYY-ww周'
      }
    }
  },
  methods: {
    showValue(val) {
      const res = val || this.$moment().subtract(1, 'days')
      const format = this.format || this.formatMap[this.type]
      return this.$moment(res, format).format(format)
    },
    handleDatePickerShow() {
      if (this.disabled) {
        return
      }
      let datePicker
      if (this.type === 'week') {
        const column1 = getLastYear(this.$moment().subtract(1, 'days')).map(
          item => {
            const week =
              this.$moment()
                .subtract(1, 'days')
                .year() === item
                ? this.$moment()
                  .subtract(1, 'days')
                  .endOf('week')
                  .week()
                : this.$moment(2019, 'YYYY').weeksInYear()
            return {
              value: item,
              text: item,
              children: new Array(week)
                .fill(1)
                .map((_item, _index) => _index + 1)
                .map(__item => {
                  return { value: `${__item}周`, text: `${__item}周` }
                })
            }
          }
        )
        const len = column1[1].children.length - 1
        let year = this.value.split('-')[0]
        const ww = parseInt(this.value.split('-')[1])
        year = ww === 1 ? year - 1 : year
        const week = parseInt(this.value.split('-')[1], 10)
        const index = column1.findIndex(item => item.value === +year)
        const value = [index, week - 1].length ? [index, week - 1] : [1, len]
        // const value = [index, week - 1] || [1, len]
        datePicker = this.$createCascadePicker({
          title: '',
          data: column1,
          selectedIndex: value,
          onSelect: this.handleDateSelect
        })
      } else {
        const value = this.$moment(this.value || Date.now()).toDate()
        const min = this.min
        const max = this.max
        const dateType = ['year', 'month', 'date', 'day'].indexOf(this.type) + 1
        datePicker = this.$createDatePicker({
          min,
          max,
          value,
          columnCount: dateType >= 3 ? 3 : dateType,
          onSelect: this.handleDateSelect
        })
      }

      datePicker.show()
    },
    handleDateSelect(date) {
      if (this.type === 'week') {
        this.$moment(`${date[0]}-${date[1]}`, 'YYYY-ww周')
        date = date.join('-')
      }
      this.$emit('onChange', this.showValue(date))
    }
  }
}
</script>

<style lang="less" scoped>
.calendar-bar-wrap {
  display: flex;
  align-items: center;
  &.disabled {
    opacity: 0.8;
  }
  &::before {
    content: '';
    display: block;
    margin-right: 0.14rem;
    width: 0.28rem;
    height: 0.29rem;
    // background: url('../image/calendar.png');
    background: url('../img/date.svg');
    background-size: 100% 100%;
  }
  span {
    font-size: 0.3rem;
    color: #6d6e71;
    // line-height: 0.42rem;
    // font-family: PingFangSC-Medium;
    font-family: PingFangSC-Regular;
  }

}
</style>
