<template>
  <div class="pie-chart-wrap">
    <div class="desc">
      <div v-for="item in seriesData" :key="item.name">
        <p>{{ item.name }}</p>
        <span>{{ pieValue }}</span>
      </div>
    </div>
    <div class="chart-wrap" ref="chart-wrap"></div>
  </div>
</template>

<script>
import { numToPercent } from '../../common/filters'

/**
 * 处理过渡颜色
 * @param {array} colors 至少两个元素
 */
const linearColor = (colors = []) => ({
  type: 'linear',
  x: 1,
  y: 0,
  x2: 0,
  y2: 1,
  colorStops: [
    {
      offset: 0,
      color: colors[0] // 0% 处的颜色
    },
    {
      offset: 1,
      color: colors[1] // 100% 处的颜色
    }
  ],
  globalCoord: false // 缺省为 false
})

/**
 * 过渡颜色设置及data子元素value为百分比的处理
 * @param {dataInterface[]} dataProp
 * @return {array} data
 */
const generateData = (dataProp = []) => {
  const len = dataProp.length
  const dataLen = len <= 1 ? 2 : len
  const result = []
  for (let i = 0; i < dataLen; i++) {
    let item = dataProp[i]
    if (!item) {
      const fisrtItem = result[i - 1]
      let fisrtItemValue = (fisrtItem || {}).value || 0
      if (len === 1) {
        fisrtItemValue = fisrtItemValue >= 1 ? 100 : fisrtItemValue * 100
        fisrtItem.value = fisrtItemValue
      }
      item = {
        value: 100 - fisrtItemValue,
        colors: ['#EFEFEF', '#EFEFEF']
      }
    }
    result.push({
      ...item,
      itemStyle: {
        color: linearColor(item.colors || firstItemDefaultColors)
      }
    })
  }
  return result
}

const firstItemDefaultColors = ['#27AEFF', '#2E55EC']

export default {
  props: {
    id: {
      type: String
    },
    radius: {
      type: String,
      default: '82%'
    },

    /**
     * chartData 子元素数据结构
     * @typedef dataInterface
     * @property {string} name 数据项名称
     * @property {number} value 值(数值或者百分比值)
     * @property {string[]} color [color1, color1]
     */
    chartData: {
      type: Array,
      default: () => [
        { value: 0, name: '达成率', colors: firstItemDefaultColors }
      ]
    }
  },
  data() {
    return {
      seriesData: [],
      pieValue: 0
    }
  },
  watch: {
    chartData() {
      this.drawChart()
    }
  },
  mounted() {
    this.drawChart()
  },
  methods: {
    drawChart() {
      const chartWrap = this.$refs['chart-wrap']
      const instance = this.$echarts.init(chartWrap)
      this.pieValue = numToPercent(this.chartData[0].value, 1)
      const seriesData = generateData(this.chartData)
      const chartOption = {
        series: [
          {
            type: 'pie',
            startAngle: 90,
            opacity: 0.1,
            radius: [`${this.radius}`, '98%'],
            hoverAnimation: false,
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: '30',
                  fontWeight: 'bold'
                }
              }
            },
            itemStyle: {
              shadowColor: 'rgba(31，31,31,0.35)',
              shadowBlur: 10,
              shadowOffsetY: 10,
              shadowOffsetX: 2
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: seriesData
          }
        ]
      }
      instance.setOption(chartOption, true)
      this.seriesData = seriesData.filter(item => item.name)
    }
  }
}
</script>

<style lang="less" scoped>
.pie-chart-wrap {
  position: relative;
  width: 100%;
  height: 100%;

  > div {
    width: 100%;
    height: 100%;
  }

  .desc {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
    text-align: center;

    p {
      margin-bottom: 0.28rem;
      font-family: PingFangSC-Medium;
      font-size: 0.24rem;
      color: #4a4a4a;
    }
    span {
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      color: #888;
    }
  }
}
</style>
