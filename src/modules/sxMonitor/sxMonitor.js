/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-12-01 13:56:11
 * @Description:
 */
import BScroll from 'better-scroll'
import requestMixins from 'common/mixins/requestMixins'

export default {
  mixins: [requestMixins],
  computed: {
    // 角色level对应key
    roleKeyMap() {
      return {
        0: 'qwCode',
        2: 'provinceAreaCode',
        3: 'areaCode',
        4: 'deptCode'
      }
    }
  },
  methods: {
    initBS(DOMInstance, config = {}) {
      const defaultOpts = {
        scrollUp() {},
        scrollDown() {},
        ...config
      }
      const { scrollUp, scrollDown } = defaultOpts
      if (this.bsInstance) {
        this.bsInstance.refresh()
      } else {
        this.bsInstance = new BScroll(DOMInstance, {
          scrollX: false,
          scrollY: true,
          probeType: 3,
          bounce: false,
          click: true,
          momentum: true,
          useTransition: true,
          useTransform: true,
          deceleration: 0.001
        })
        this.bsInstance.on('scroll', () => {
          const fisrtLevelDom = document.querySelector('.first-level')
          if (this.bsInstance.movingDirectionY > 0 && !this.hasScrollDown) {
            fisrtLevelDom && fisrtLevelDom.classList.add('dispear')
            this.hasScrollDown = true
            this.hasScrollUp = false
            scrollUp()
            this.$nextTick(() => {
              this.bsInstance.refresh()
            })
          } else if (
            this.bsInstance.movingDirectionY < 0 &&
            !this.hasScrollUp &&
            this.bsInstance.y === 0
          ) {
            this.hasScrollDown = false
            this.hasScrollUp = true
            fisrtLevelDom && fisrtLevelDom.classList.remove('dispear')
            scrollDown()
          }
        })
      }
    },

    // 格式化参数
    formatParams(param = {}) {
      return Object.keys(param).map(key => ({ key, value: param[key] }))
    },

    drawChart(element, option) {
      element.removeAttribute('_echarts_instance_')
      setTimeout(() => {
        const chartInstance = this.$echarts.init(element)
        chartInstance.setOption(option, true)
      }, 300)
    },

    isSameDay(a, b) {
      return this.$moment(a).isSame(b, 'day')
    }
  },
  components: { }
}
