import BScroll from 'better-scroll'
import requestMixins from './requestMixins'
export default {
  mixins: [requestMixins],
  methods: {
    initYBS(
      ref = 'contentWrapper',
      xMove = false,
      height = 1.8,
      height2 = 2.6
    ) {
      if (!this.bsInstance) {
        this.bsInstance = new BScroll(this.$refs[ref], {
          scrollX: xMove,
          scrollY: true,
          probeType: 3,
          bounce: false, // 是否启动回弹效果
          click: true,
          momentum: true,
          useTransition: true,
          useTransform: true,
          deceleration: 0.001
        })
        this.bsInstance.on('scroll', arg => {
          if (this.bsInstance.movingDirectionY > 0) {
            this.$refs.contentWrapper.style.height = `calc(100vh - ${height}rem)`
            document.querySelector('.first-level') &&
              document.querySelector('.first-level').classList.add('dispear')
            document.querySelector('.second-level') &&
              document.querySelector('.second-level').classList.add('dispear')
            this.topDistance = false
            this.$nextTick(() => {
              this.bsInstance.refresh()
            })
          } else if (
            this.bsInstance.movingDirectionY < 0 &&
            this.bsInstance.y === 0
          ) {
            this.$refs.contentWrapper.style.height = `calc(100vh - ${height2}rem)`
            document.querySelector('.first-level') &&
              document.querySelector('.first-level').classList.remove('dispear')
            document.querySelector('.second-level') &&
              document
                .querySelector('.second-level')
                .classList.remove('dispear')
            this.topDistance = true
            this.$nextTick(() => {
              this.bsInstance.refresh()
            })
          }
        })
      } else {
        this.bsInstance.refresh()
      }
    },
    // 集配站获取总数据
    getTotalData(type, date) {
      const elementId = {
        day: 'ff8080817098d1090170bd9e1ab57f76',
        month: 'ff8080817098d1090170c9617f744d8f'
      }
      const graphId = {
        day: 'ff8080817098d1090170b91a412a62be',
        month: 'ff8080817098d1090170c95cf3674d6f'
      }
      const datetype = {
        day: {
          IncDay:
            process.env.NODE_ENV === 'development'
              ? '20200424'
              : this.$moment(date).format('YYYYMMDD')
        },
        month: {
          IncMon:
            process.env.NODE_ENV === 'development'
              ? '202003'
              : this.$moment(date).format('YYYYMM')
        }
      }
      const params = {
        graphId: graphId[type],
        elementId: elementId[type],
        ...datetype[type]
      }
      return this.$http
        .get(`bie/webservice/doQueryDynamicSql.pvt`, {
          params: {
            DeptCode:
              process.env.NODE_ENV === 'development' ? '010AAN' : this.zoneCode,
            ...params
          }
        })
        .then(({ data: { result } }) => {
          return result[0] || {}
        })
    },
    transData(type, data) {
      const newData = {}
      if (type === 'day') {
        Object.keys(data).forEach(key => {
          if (key.match(/_mtd$/)) {
            newData[key.replace(/_mtd/, '_addup')] = data[key]
          } else if (key.match(/_mtd_rate$/)) {
            newData[key.replace(/_mtd_rate/, '_rate_addup')] = data[key]
          } else {
            newData[key] = data[key]
          }
          return
        })
        return newData
      } else {
        Object.keys(data).forEach(key => {
          if (key.match(/_ytd$/)) {
            newData[key.replace(/_ytd/, '_addup')] = data[key]
          } else if (key.match(/_ytd_rate$/)) {
            newData[key.replace(/_ytd_rate/, '_rate_addup')] = data[key]
          } else {
            newData[key] = data[key]
          }
          return
        })
        return newData
      }
    },
    transTransferData(type, data) {
      const newData = {}
      if (type === 'day') {
        return data
      } else {
        Object.keys(data).forEach(key => {
          if (key.match(/_mon/)) {
            newData[key.replace(/_mon/, '_day')] = data[key]
          } else if (key.match(/_ytd/)) {
            newData[key.replace(/_ytd/, '_mtd')] = data[key]
          } else {
            newData[key] = data[key]
          }
          return
        })
        return newData
      }
    },
    getOne(data) {
      const { tableName, conditionList } = data
      return this.sendOneDimenRequest(tableName, conditionList).then(res => {
        const { obj } = res
        return obj || {}
      })
    },
    getTwo(data) {
      const { tableName, conditionList } = data
      return this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        const { obj } = res
        if (obj) {
          return obj
        }
        return []
      })
    },
    // 总数据
    getTotalDataTransfer(data) {
      const { type, date, tableName } = data
      const paramsType = {
        day: [
          {
            key: 'inc_day',
            value: date
          },
          {
            key: 'dept_code',
            value: this.zoneCode
          }
        ],
        month: [
          {
            key: 'inc_day',
            value: date
          },
          {
            key: 'dept_code',
            value: this.zoneCode
          }
        ]
      }
      const params = {
        tableName,
        conditionList: [...paramsType[type]]
      }
      return this.getOne(params)
    },
    // 趋势图
    getTrendTransfer(data) {
      const { type, date, tableName } = data
      const paramsType = {
        day: [
          {
            key: 'inc_day',
            value: process.env.NODE_ENV === 'development' ? '202003' : date
          }
        ],
        month: [
          {
            key: 'inc_day',
            value: process.env.NODE_ENV === 'development' ? '2020' : date
          }
        ]
      }
      const params = {
        tableName,
        conditionList: [
          ...paramsType[type],
          {
            key: 'dept_code',
            value: this.zoneCode
          }
        ]
      }
      return this.getTwo(params)
    },
    // 生成line配置
    getColorLine(color, shadowColor) {
      return {
        itemStyle: {
          color: color,
          borderColor: color,
          borderWidth: 2
        },
        lineStyle: {
          color: color,
          width: 4,
          shadowColor: shadowColor,
          shadowBlur: 10,
          shadowOffsetY: 3
        }
      }
    }
  },
  filters: {
    transUnit: function(value, divisor = 10000, fixNum = 2) {
      if (value === 0) return 0
      value = Number(value)
      if (typeof value === 'number' && !isNaN(value)) {
        return (value / divisor).toFixed(fixNum)
      } else {
        return '-'
      }
    }
  }
}
