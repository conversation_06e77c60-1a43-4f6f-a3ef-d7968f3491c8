// import getOption from 'express/defaultOption'
const defaultColor = ['#3458e3', '#ee7335', '#f7cc46', '#55b9a1', '#7f53d8', '#e55766', '#63bc47', '#f4f5fe']
export default {
  methods: {
    // 绘制图表
    drawChartMain(chartDom, option) {
      if (!chartDom) {
        return
      }
      chartDom.removeAttribute('_echarts_instance_')
      setTimeout(() => {
        const chartInstance = this.$echarts.init(chartDom, {
          devicePixelRatio: 2
        })
        chartInstance.setOption(option, true)
        chartInstance.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: option.series[0].data.length - 1
        })
        // dom元素变化刷新图表
        const resizeChart = new ResizeObserver(() => {
          chartInstance.resize()
        })
        resizeChart.observe(chartDom)
      }, 200)
    },

    // 重置图表,tab切换时需手动触发
    resizeChart(chartId) {
      if (!chartId) {
        return
      }
      const element = this.$refs[chartId]
      setTimeout(() => {
        const chartInstance = this.$echarts.init(element)
        chartInstance.resize()
      }, 100)
    },

    // 滚动bar图
    drawScorllChart(options, chartId) {
      const defaultOption = {
        tooltip: {
          show: false,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            color: '#5594F2'
          }
        },
        grid: {
          top: '15%',
          left: '0%',
          right: '0%',
          bottom: '20%',
          containLabel: false
        },
        xAxis: {
          data: [],
          boundaryGap: ['0', '0'],
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#939393'
            },
            rotate: 0,
            interval: 0,
            // 换行
            formatter: params => {
              return this.newline(params)
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#F2F2F2'
            }
          },
          triggerEvent: true // 触发x轴点击事件
        },
        yAxis: {
          show: false
        },
        series: [
          {
            name: '完成率',
            type: 'bar',
            barWidth: 10,
            data: [],
            barCategoryGap: '50',
            label: {
              show: true,
              position: 'top',
              formatter: params => {
                const v = params.data
                return `${(v * 100).toFixed(1)}%`
              }
            },
            itemStyle: {
              barBorderRadius: [3, 3, 0, 0],
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#DC1E32' },
                { offset: 1, color: '#FF874F' }
              ])
            }
          }
        ]
      }
      const chartContainer = this.$refs[chartId]

      const option = this.deepMerge(defaultOption, options)
      this.drawChartMain(chartContainer, option)
    },
    // bar图
    drawBarChart(options, chartId) {
      const dataShadow = []
      const yMax = 200
      for (var i = 0; i < options.series[0].data.length; i++) {
        dataShadow.push(yMax)
      }
      const defaultOption = {
        tooltip: {
          show: false,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            color: '#5594F2'
          },
          formatter: params => {}
        },
        grid: {
          top: '15%',
          left: '2%',
          right: '2%',
          bottom: '15%'
          // containLabel: true
        },
        xAxis: {
          data: [],
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#939393'
            },
            interval: 0
            // 换行
            // formatter: params => {
            //   return this.newline(params)
            // }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#F2F2F2'
            }
          }
        },
        yAxis: {
          show: false
        },
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: [],
            label: {
              show: false,
              position: 'top',
              formatter: params => {
                const v = params.data
                return `${(v * 1).toFixed(1)}%`
              }
            },
            itemStyle: {
              barBorderRadius: [2, 2, 0, 0],
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2E55EC' },
                { offset: 1, color: '#7A94FF' }
              ])
            },
            z: 10
          },
          {
            type: 'bar',
            barWidth: 10,
            data: dataShadow,
            barGap: '-100%',
            label: {
              show: true,
              color: '#3D3D3D',
              position: 'top',
              formatter: params => {
                const index = params.dataIndex
                return `${Number(options.series[0].data[index]).toFixed(0)}%`
              }
            },
            itemStyle: {
              color: '#F8F9FC'
            }
          }
        ]
      }
      const chartContainer = this.$refs[chartId]
      const option = this.deepMerge(defaultOption, options)
      this.drawChartMain(chartContainer, option)
    },
    // 折线图
    drawLineChart(options, chartId) {
      const defaultColor = ['#2E55EC', '#FF6A17', '#09BCA0']
      // 坐标轴线
      const defaultAxisLine = {
        show: false
      }
      // 背景grid分隔线
      const defaultSplitLine = {
        show: true,
        lineStyle: {
          color: '#f7f7f7',
          type: 'solid'
        }
      }
      // 刻度
      const defaultAxisTick = {
        show: false
      }
      const toolTipData = {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: '#d71c2c',
            type: 'dotted',
            width: 1
          },
          z: -1
        }
      }
      const lineSeries = {
        type: 'line',
        showSymbol: false,
        lineStyle: {
          type: 'solid',
          shadowOffsetX: 0,
          shadowOffsetY: 2,
          shadowBlur: 6
        }
      }
      const seriesOption = []
      for (let index = 0; index < options.series.length; index++) {
        const obj = JSON.parse(JSON.stringify(lineSeries))
        obj.lineStyle.shadowColor = defaultColor[index]
        seriesOption.push(obj)
      }
      const defaultOption = {
        color: defaultColor,
        xAxis: {
          axisLine: defaultAxisLine,
          axisTick: defaultAxisTick,
          boundaryGap: true
        },
        yAxis: {
          axisLine: defaultAxisLine,
          axisTick: defaultAxisTick,
          splitLine: defaultSplitLine,
          axisLabel: {
            show: false
          }
        },

        // 图例
        legend: {
          show: false
        },
        grid: {
          top: '5%',
          right: '5%',
          left: '5%',
          bottom: '15%',
          containLabel: false
        },
        tooltip: toolTipData,
        series: seriesOption
      }

      const chartContainer = this.$refs[chartId]
      const option = this.deepMerge(defaultOption, options)
      this.drawChartMain(chartContainer, option)
    },
    // 饼图
    drawPieChart(options, chartId) {
      const defaultOption = {
        color: defaultColor,
        tooltip: {
          show: false,
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '85%'],
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      }
      const chartContainer = this.$refs[chartId]
      const option = this.deepMerge(defaultOption, options)
      this.drawChartMain(chartContainer, option)
    },
    // 圆环ring,单数
    drawRingChart(num, chartId, color) {
      const option = {
        title: {
          text: '指标达成差值',
          textStyle: {
            color: ' #999999',
            fontSize: 12,
            fontWeight: 300
          },
          subtext: num + '%',
          subtextStyle: {
            color: '#3D3D3D',
            fontWeight: 'bold',
            fontSize: 16
          },
          itemGap: 5, // 主副标题距离
          left: 'center',
          top: '30%'
        },
        angleAxis: {
          max: 100, // 满分
          clockwise: true, // 顺时针
          // 隐藏刻度线
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        radiusAxis: {
          type: 'category',
          // 隐藏刻度线
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        polar: {
          center: ['50%', '50%'],
          radius: '170%' // 图形大小
        },
        series: [
          {
            type: 'bar',
            data: [
              {
                value: num,
                itemStyle: {
                  normal: {
                    color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: color ? color[0] : '#FF954F'
                      },
                      {
                        offset: 1,
                        color: color ? color[1] : '#F44618 '
                      }
                    ])
                  }
                }
              }
            ],
            coordinateSystem: 'polar',
            roundCap: true,
            barWidth: 8,
            barGap: '-100%', // 两环重叠
            z: 2
          },
          {
            // 灰色环
            type: 'bar',
            data: [
              {
                value: 100,
                itemStyle: {
                  color: '#e2e2e2'
                }
              }
            ],
            coordinateSystem: 'polar',
            roundCap: true,
            barWidth: 8,
            barGap: '-100%', // 两环重叠
            z: 1
          }
        ]
      }
      const chartContainer = this.$refs[chartId]
      this.drawChartMain(chartContainer, option)
    },
    // x轴标签换行
    newline(params) {
      let newParamsName = ''
      const paramsNameNumber = params.length
      const provideNumber = 4
      const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
      let paramValue = ''
      if (paramsNameNumber > 8) {
        paramValue = params.substring(0, 6) + '...'
      } else {
        paramValue = params
      }
      for (let row = 0; row < rowNumber; row++) {
        newParamsName += paramValue.substring(row * provideNumber, (row + 1) * provideNumber) + '\n'
      }
      return newParamsName
    },
    // 合并配置
    deepMerge(obj1, obj2) {
      for (const p in obj2) {
        if (Object.hasOwnProperty.call(obj2, p)) {
          try {
            if (obj2[p].constructor === Object || obj2[p].constructor === Array) {
              obj1[p] = this.deepMerge(obj1[p], obj2[p])
            } else {
              obj1[p] = obj2[p]
            }
          } catch (e) {
            obj1[p] = obj2[p]
          }
        }
      }
      return obj1
    }
  }
}
