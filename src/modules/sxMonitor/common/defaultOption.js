import { linearColors } from './colors'

const lineSeries = {
  showSymbol: false,
  lineStyle: {
    type: 'solid',
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowBlur: 6
  },
  label: {
    show: true,
    position: 'top',
    color: '#333',
    formatter: '{c}'
  }
}
const pieSeries = {
  selectedOffset: 5,
  hoverOffset: 5,
  radius: ['55%', '85%'],
  hoverAnimation: false,
  label: {
    show: true,
    position: 'outside',
    formatter: '{b}:{c}',
    margin: '20%'
  }
}

const barSeries = {
  label: {
    show: true,
    position: 'top',
    formatter: '{c}',
    color: '#333'
  },
  itemStyle: {
    barBorderRadius: 5,
    barWidth: 10
  }
}

// 合并对象,obj1是默认对象， obj2是传入对象
function objCombine(...obj) {
  const bool = obj.some(item => Array.isArray(item))
  const res = bool ? [] : {}

  function combine(key, item, res) {
    const isObj = typeof item[key] === 'object'
    if (isObj) {
      if (!res[key]) {
        res[key] = Array.isArray(res[key]) ? [] : {}
      }
      // 判断数组直接拷贝
      if (Array.isArray(item[key])) {
        res[key] = item[key]
      } else {
        Object.keys(item[key]).forEach(_item => {
          combine(_item, item[key], res[key])
        })
      }
    } else {
      res[key] = item[key]
    }
  }
  obj.forEach(item => {
    Object.keys(item).forEach(key => {
      combine(key, item, res)
    })
  })

  return res
}

const getSeries = item => {
  switch (item.type || 'line') {
    case 'line':
      return objCombine(lineSeries, item)
    case 'bar':
      return objCombine(barSeries, item)
    case 'pie':
      return objCombine(pieSeries, item)
    default:
      return item
  }
}

const getOption = (customize, type = false) => {
  const { grid, tooltip, yAxis, xAxis, series, ...rest } = customize
  const defaultxAxis = [
    {
      type: 'category',
      nameTextStyle: {
        fontFamily: 'PingFangSC-Regular, sans-serif',
        fontSize: '22px'
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#ccc',
          type: 'dashed'
        }
      },
      axisLabel: {
        show: true,
        interval: 0,
        fontFamily: 'PingFangSC-Regular, sans-serif',
        fontSize: '22px'
      },
      axisPointer: {
        show: true,
        type: 'line',
        label: {
          show: false
        },
        lineStyle: {
          color: '#979797',
          width: 1
        },
        shadowStyle: {
          color: '#5594F2'
        },
        triggerTooltip: false
      }
    }
  ]
  const defaultyAxis = [
    {
      show: true,
      position: 'left',
      nameTextStyle: {
        fontFamily: 'PingFangSC-Regular, sans-serif',
        fontSize: '22px'
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        fontFamily: 'PingFangSC-Regular, sans-serif',
        fontSize: '22px'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#ccc',
          type: 'dashed'
        }
      }
    },
    {
      show: true,
      position: 'right',
      nameTextStyle: {
        fontFamily: 'PingFangSC-Regular, sans-serif',
        fontSize: '22px'
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        fontFamily: 'PingFangSC-Regular, sans-serif',
        fontSize: '22px',
        lineHeight: '30px'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#ccc',
          type: 'dashed'
        }
      }
    }
  ]
  const defaultTooltip = {
    show: true,
    triggerOn: 'mouseMove|click',
    confine: true,
    formatter: '{b}: {c}',
    backgroundColor: 'rgba(1,27,100,0.7)',
    textStyle: {
      fontSize: 10
    }
  }
  const tempSeries = series.map(item => getSeries(item))

  let tempxAxis = []
  let tempyAxis = []
  let tempTooltip = {}

  tempxAxis = xAxis ? objCombine(defaultxAxis, xAxis) : defaultxAxis
  tempyAxis = yAxis ? objCombine(defaultyAxis, yAxis) : defaultyAxis
  tempTooltip = tooltip ? objCombine(defaultTooltip, tooltip) : defaultTooltip
  return {
    color: type ? linearColors() : null,
    tooltip: tempTooltip,
    grid: {
      containLabel: true,
      top: 10,
      left: 0,
      bottom: 0,
      right: 0,
      ...grid
    },
    xAxis: tempxAxis,
    yAxis: tempyAxis,
    series: tempSeries,
    // series,
    ...rest
  }
}

export default getOption
