import moment from 'moment'

/**
 * 生成日期列表
 * @param {*} dateObject 日期 一般日期格式如 20200515 或者Date对象
 * @param {number} len 生成天数
 * @param {number} begin dateObject为参考点,从前或后begin天数开始
 * @param {string} split 日期分割符
 * @param {string} timeType 日期类型 y M d
 * @return {array} arrayString
 */
export const timeTable = (
  dateObject = new Date(),
  len = 8,
  begin = -6,
  split = '',
  timeType = 'd'
) => {
  const result = []
  const formatMap = {
    y: 'YYYY',
    M: `YYYY${split}MM`,
    d: `YYYY${split}MM${split}DD`
  }
  const format = formatMap[timeType]
  for (let i = 0; i < len; i++) {
    const baseDay = moment(dateObject)
    const date = baseDay.add(begin + i, timeType)
    result.push(date.format(format))
  }
  return result
}

/**
 * @description 将接口的二维数组转换为一维数组
 * @params {object} data 二维接口返回的数据
 * @return {array} 返回一维数组
 */
export const formatTd = (data = {}) => {
  const { colDefList, rows } = data
  if (!data || !colDefList || !rows) return []
  const fieldMap = {}
  for (const index in colDefList) {
    fieldMap[colDefList[index].name] = index
  }
  const newData = []
  for (const item of rows) {
    const obj = {}
    for (const index in colDefList) {
      obj[colDefList[index].name] = item[index]
    }
    newData.push(obj)
  }
  return newData
}

// 获取一个历史年数据数组
// 主要用于图表的很坐标展示
// date表明数据的最后一个可以解析的时间点 count表明你想要的数组长度 - 1
export const getLastYear = (date = new Date(), format = 'YYYY', count = 1) => {
  const res = []
  for (let i = count; i >= 0; i--) {
    res.push(
      moment(date)
        .subtract(i, 'years')
        .year()
    )
  }
  return res
}
