import echarts from 'echarts'
const defaultColor = [
  '#2E55EC',
  '#FF6A17',
  '#56baa7',
  '#ebd750',
  '#9a83ec',
  '#d2544e',
  '#63bc47',
  '#f4f5fe',
  '#71abf6',
  '#d96e8e'
]

// 背景grid分隔线
const splitLine = {
  show: true,
  lineStyle: {
    color: '#ddd',
    type: 'dashed'
  }
}

const grid = {
  top: '5%',
  left: '0',
  right: '0',
  bottom: '15%',
  containLabel: true
}
// x坐标轴线
const xAxisLine = {
  show: true,
  lineStyle: {
    color: '#F2F2F2'
  }
}
// y坐标轴线
const yAxisLine = {
  show: false
}
// 刻度线
const AxisTick = {
  show: false
}
// 轴文字
const axisLabel = {
  show: true,
  textStyle: {
    color: '#666',
    fontSize: 10
  },
  interval: 0
}
const lineSeries = {
  type: 'line',
  showSymbol: true,
  symbol: 'circle',
  symbolSize: 6,
  itemStyle: {
    borderWidth: 2,
    borderColor: '#fff',
    emphasis: {
      color: '#fff', // hover拐点颜色定义
      borderColor: '', // 需配置
      borderWidth: 2,
      symbolSize: 8
    }
  },
  lineStyle: {
    type: 'solid',
    // width: ,
    shadowOffsetX: 0,
    shadowOffsetY: 4,
    shadowBlur: 8,
    shadowColor: ''
  },
  data: []
}

// line折线图
const drawLineChart = (options, chartDom) => {
  const seriesOptions = []
  if (options.series.length) {
    options.series.forEach((item, index) => {
      // 带折线图时配置阴影颜色
      lineSeries.lineStyle.shadowColor = defaultColor[index]
      lineSeries.itemStyle.emphasis.borderColor = defaultColor[index]
      seriesOptions.push(JSON.parse(JSON.stringify(lineSeries)))
    })
  }

  const defaultOption = {
    color: defaultColor,
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#d71c2c',
          type: 'dotted',
          width: 1,
          opacity: 1
        },
        z: -1
      },
      formatter: val => {}
    },
    legend: {
      bottom: '5',
      data: []
    },
    grid,
    xAxis: {
      data: [],
      axisTick: AxisTick,
      axisLabel,
      axisLine: xAxisLine,
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#d71c2c',
          type: 'dotted',
          width: 1,
          opacity: 1
        },
        z: -1
      }
    },
    yAxis: [
      {
        axisLine: yAxisLine,
        axisTick: AxisTick,
        axisLabel,
        splitLine
      },
      {
        axisLine: yAxisLine,
        axisTick: AxisTick,
        axisLabel,
        splitLine
      }
    ],
    series: seriesOptions
  }
  const option = _.defaultsDeep(options, defaultOption)
  drawChartMain(chartDom, option)
}

// 绘制图表
const drawChartMain = (chartDom, option) => {
  if (!chartDom) {
    return
  }
  chartDom.removeAttribute('_echarts_instance_')
  setTimeout(() => {
    const chartInstance = echarts.init(chartDom)
    chartInstance.setOption(option, true)
    // 同一页面多个echarts随窗口大小动态变化
    window.addEventListener('resize', () => {
      chartInstance.resize()
    })
  }, 100)
}
// 重置图表,tab切换时需手动触发
const resizeChart = chartDom => {
  if (!chartDom) {
    return
  }
  setTimeout(() => {
    const chartInstance = echarts.init(chartDom)
    chartInstance.resize()
  }, 100)
}
export { drawLineChart, resizeChart, drawChartMain }
