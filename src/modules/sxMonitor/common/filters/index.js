const numToPercent = (val, fixnum = 1) => {
  if (!val && +val !== 0) {
    return '-'
  } else {
    return `${(val * 100).toFixed(fixnum)}%`
  }
}

const numToFloat = (val, divisor = 1, fixnum = 1) => {
  if (!val && +val !== 0) {
    return '-'
  } else {
    if (val / divisor < 1) {
      return `${(val / divisor).toFixed(2)}`
    }
    return `${(val / divisor).toFixed(fixnum)}`
  }
}

const numToThousands = (val, divisor = 1, fixnum = 1) => {
  if (!val && val !== 0) {
    return '-'
  } else {
    if (Math.abs(val / divisor) < 1) {
      return `${(val / divisor).toFixed(2)}`
    }
    return `${(+(val / divisor).toFixed(fixnum)).toLocaleString()}`
  }
}

export { numToPercent, numToFloat, numToThousands }
