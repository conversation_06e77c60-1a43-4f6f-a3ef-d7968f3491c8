<!--
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-13 15:40:47
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-01 10:34:56
-->
<template>
  <div>
    <headBar :homePage="true" title="权限申请"></headBar>
    <div class="content">
      <div class="top">
        <template v-if="!isApply">
          <div class="title">你好，欢迎进行</div>
          <div class="title">顺心战况权限申请</div>
          <div :class="['input-field', 'first', userIdIsInput ? 'userIdIsInput' : '']">
            <div>
              <span>工号</span>
              <input
                @focus="userIdIsInput = true"
                @blur="userIdIsInput = false"
                v-model="userId"
                placeholder="请输入工号"
              />
            </div>
          </div>
          <div :class="['input-field', userNameIsInput ? 'userNameIsInput' : '']">
            <div>
              <span>姓名</span>
              <input
                @focus="userNameIsInput = true"
                @blur="userNameIsInput = false"
                v-model="userName"
                placeholder="请输入姓名"
              />
            </div>
          </div>
          <div :class="['input-field', userPositionIsInput ? 'userPositionIsInput' : '']">
            <div>
              <span>岗位</span>
              <input
                @focus="userPositionIsInput = true"
                @blur="userPositionIsInput = false"
                v-model="userPosition"
                placeholder="请输入岗位"
              />
            </div>
          </div>
          <div :class="['button', isActive ? '' : 'buttonIsActive']" @click="authApply">权限申请</div>
        </template>
        <template v-else>
          <div class="bg"></div>
          <h3>审核中</h3>
          <div class="text">预计<span>1~2</span>个工作日内完成，敬请留意</div>
        </template>
      </div>
      <div class="bottom" @click="askFs">
        <div class="left"></div>
        <div class="middle">
          <span class="grey333 fs24 fw700 flex_start line_h33"
            >{{ defaultPeople.employeeName
            }}<span class="grey666 fs24 fw400 ml16">{{ defaultPeople.employeeCode }}</span></span
          >
          <span class="mt8 fs20 grey999 line_h28">若有疑问，可联系业务接口人</span>
        </div>
        <div class="right"></div>
      </div>
    </div>
  </div>
</template>

<script>
import headBar from 'common/components/head'
import requestMixins from 'common/mixins/requestMixins'
export default {
  mixins: [requestMixins],
  components: { headBar },

  data() {
    return {
      defaultPeople: {},
      userId: '',
      userName: '',
      userPosition: '', // 岗位信息
      userIdIsInput: false,
      userNameIsInput: false,
      userPositionIsInput: false,
      isApply: false
    }
  },
  computed: {
    isActive() {
      return this.userId && this.userName && this.userPosition
    }
  },
  methods: {
    getDefaultPeople() {
      this.sendTwoDimenRequest('cp_sxzkqxsprgl', [], false, false).then(res => {
        console.log(res, 'res---')
        this.defaultPeople = res.obj[0] || {}
      })
    },
    // 点击申请
    authApply() {
      if (!this.isActive) return
      this.sendJavaRequest(
        {
          url: '/resourceServices/process/apply',
          data: {
            userNo: this.userId,
            userName: this.userName,
            positionName: this.userPosition
          }
        },
        false,
        false
      ).then(res => {
        this.isApply = true
      })
    },
    askFs() {
      try {
        ExpressPlugin.openContactDetail(this.defaultPeople.employeeCode)
      } catch (err) {
        console.log(err)
      }
    }
  },
  created() {
    this.userId = sessionStorage.getItem('userId')
  },
  mounted() {
    this.getDefaultPeople()
  }
}
</script>
<style lang="less" scoped>
.content {
  padding: 0.64rem 0.48rem 0.8rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh - 0.88rem);
  .top {
    align-self: flex-start;
    width: 100%;
  }
  .title {
    font-family: PingFangSC-Medium;
    font-size: 0.56rem;
    line-height: 0.78rem;
    color: #333;
    letter-spacing: 0;
    font-weight: 700;
  }
  .first {
    margin-top: 0.68rem;
    border-top: 1px solid rgba(229, 229, 229, 0.6);
  }
  .input-field {
    height: 1.07rem;
    display: flex;
    align-items: center;
    background: #fff;
    span {
      font-family: PingFangSC-Regular;
      font-size: 0.32rem;
      color: #333;
      font-weight: 400;
      line-height: 0.45rem;
    }
    border-bottom: 1px solid rgba(229, 229, 229, 0.6);
    padding: 0.2rem 0;

    div {
      background: #f5f5f5;
      width: 100%;
      height: 100%;
      padding: 0 0.2rem;
    }

    input {
      margin-left: 0.24rem;
      width: calc(100% - 1rem);
      height: 100%;
      font-family: PingFangSC-Regular;
      font-size: 0.32rem;
      color: #333;
      background: #f5f5f5;
      font-weight: 400;
    }

    input::-webkit-input-placeholder {
      color: #dc1e32;
      font-size: 0.32rem;
    }
  }
  .userIdIsInput,
  .userNameIsInput,
  .userPositionIsInput {
    > div {
      background: #fff;
    }
    input {
      background: #fff;
    }
  }
  .button {
    width: 3.77rem;
    height: 0.8rem;
    background: #dc1e32;
    border-radius: 0.04rem;
    font-family: PingFangSC-Medium;
    font-size: 0.28rem;
    line-height: 0.8rem;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 700;
    margin: 0.4rem auto;
  }
  .buttonIsActive {
    background: #dddddd;
  }

  .bg {
    background: url('./img/under <EMAIL>') no-repeat;
    background-size: contain;
    height: 3.6rem;
    width: 3.6rem;
    margin: 30% auto 0;
  }
  h3 {
    font-family: PingFangSC-Medium;
    font-size: 0.44rem;
    line-height: 0.62rem;
    color: #333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
  .text {
    font-family: PingFangSC-Regular;
    font-size: 0.32rem;
    color: #808080;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    margin-top: 0.16rem;
    span {
      color: #dc1e23;
    }
  }

  .bottom {
    background: #f2f3f7;
    border: 1px solid #dddddd;
    border-radius: 0.04rem;
    width: 5.48rem;
    height: 0.96rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.13rem 0.24rem;
    .left {
      height: 0.64rem;
      width: 0.64rem;
      background: url('./img/<EMAIL>') no-repeat;
      background-size: contain;
    }
    .middle {
      display: flex;
      flex-direction: column;
      flex: 1 0 3.84rem;
      margin-left: 0.24rem;
    }
    .right {
      height: 0.32rem;
      width: 0.32rem;
      background: rgba(0, 107, 234, 0.1);
      border-radius: 0.04rem;
      display: flex;
      align-items: center;
      justify-content: center;
      &::after {
        content: '';
        display: inline-block;
        height: 0.2rem;
        width: 0.18rem;
        background: url('./img/nav_return_icon.svg');
        background-size: 100% 100%;
        transform: rotate(180deg);
      }
    }
  }
}
</style>
