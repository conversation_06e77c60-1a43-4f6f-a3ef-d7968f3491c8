<!--
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-13 15:40:47
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-01 10:34:42
-->
<template>
    <div>
      <headBar :homePage="homePage" title="无权限"></headBar>
      <div class="content">
        <div class="bg"></div>
        <h3>{{str}}</h3>
        <div class="button" @click="routerJump">权限申请</div>
      </div>
    </div>
</template>

<script>
import headBar from 'common/components/head'
export default {
  data() {
    return { homePage: true, str: '哎呀~ 您暂无顺心战况的权限哦~' }
  },
  components: { headBar },
  mounted() {
    // 神策
    this.$sensors.pageview('noRights')
  },
  methods: {
    routerJump() {
      this.$router.push({
        name: 'applyAuth'
      })
    }
  }
}
</script>
<style lang="less" scoped>
.content {
  height: calc(100vh - 0.88rem);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 50%;
  transform: translateY(-15%);
  overflow: hidden;
}
.bg {
  background: url('./img/<EMAIL>') no-repeat;
  background-size: contain;
  height: 3.6rem;
  width: 3.6rem;
}
  h3{
    font-family: PingFangSC-Medium;
    font-size: 0.32rem;
    color: #808080;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
.button {
  width: 3.77rem;
  height: 0.8rem;
  background: #DC1E32;
  border-radius: 0.04rem;
  font-family: PingFangSC-Medium;
  font-size: 0.28rem;
  line-height: 0.8rem;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
  font-weight: 700;
  margin-top: 0.4rem;
}
</style>
