export default [
  {
    // 只包含业务区
    type: '1',
    list: [
      {
        zoneCode: '001',
        value: '001',
        label: '顺心捷达',
        zoneName: '顺心捷达',
        zoneLevel: '0'
      },
      {
        dept_code: 'SF001',
        dept_name: '业务区',
        dept_level: '1'
      }
    ]
  },
  {
    // 包含业务区区分拨区
    type: '2',
    list: [
      {
        dept_code: '001',
        dept_name: '速运事业群',
        dept_level: '0'
      },
      {
        dept_code: 'SF001',
        dept_name: '业务区',
        dept_level: '1'
      },
      {
        dept_code: 'HQOP',
        dept_name: '分拨区',
        dept_level: '2',
        moduleCode: 'JYP24000000' // 根据java端逻辑选取一个模块编码
      }
    ]
  },
  {
    // 只有业务区
    type: '3',
    list: [
      {
        dept_code: 'A',
        dept_name: 'A组',
        dept_level: '2'
      },
      {
        dept_code: 'B',
        dept_name: 'B组',
        dept_level: '2'
      },
      {
        dept_code: 'C',
        dept_name: 'C组',
        dept_level: '2'
      },
      {
        dept_code: 'D',
        dept_name: 'D组',
        dept_level: '2'
      }
      // {
      //   dept_code: 'E',
      //   dept_name: 'E组',
      //   dept_level: '12'
      // },
      // {
      //   dept_code: 'F',
      //   dept_name: 'F组',
      //   dept_level: '12'
      // },
      // {
      //   dept_code: 'G',
      //   dept_name: 'G组',
      //   dept_level: '12'
      // },
      // {
      //   dept_code: 'H',
      //   dept_name: 'H组',
      //   dept_level: '12'
      // }
    ]
  },
  {
    // 包含业务区,大区
    type: '4',
    list: [
      {
        dept_code: '001',
        dept_name: '速运事业群',
        dept_level: '0'
      },
      {
        dept_code: 'SF001',
        dept_name: '业务区',
        dept_level: '1'
      },
      {
        dept_code: 'YWDQ',
        dept_name: '业务区(大区)',
        dept_level: '1',
        moduleCode: 'JYP09000000' // 根据java端逻辑选取一个模块编码
      }
    ]
  },
  {
    // 质量管理平台类型
    type: '5',
    list: [
      {
        dept_code: '001',
        dept_name: '集团',
        dept_level: '0'
      },
      {
        dept_code: 'ZN',
        dept_name: '职能',
        dept_level: '1'
      },
      {
        dept_code: 'BU',
        dept_name: 'BU',
        dept_level: '1'
      },
      {
        dept_code: '001_1',
        dept_name: '大陆业务区',
        dept_level: '1'
      },
      {
        dept_code: 'HQOP',
        dept_name: '分拨区',
        dept_level: '2',
        moduleCode: 'JYP19000000' // 根据java端逻辑选取一个模块编码
      },
      {
        dept_code: 'CN06_1',
        dept_name: '海外业务区',
        dept_level: '1'
      }
    ]
  },
  {
    // 包含业务区区分拨区
    type: '6',
    list: [
      {
        dept_code: '001',
        dept_name: '快运事业部',
        dept_level: '0'
      },
      {
        dept_code: 'SF001',
        dept_name: '业务区分组',
        dept_level: '1'
      },
      {
        dept_code: 'YWDQ',
        dept_name: '分公司',
        dept_level: '1',
        moduleCode: 'JYP09000000' // 根据java端逻辑选取一个模块编码
      }
    ]
  },
  {
    // 冷运事业群
    type: '7',
    list: [
      {
        dept_code: 'CN32',
        dept_name: '冷运事业群',
        dept_level: '2',
        moduleCode: 'JYP25000000'
      }
    ]
  },
  {
    // 包含业务区区分拨区
    type: '8',
    list: [
      {
        dept_code: '001',
        dept_name: '快运事业部',
        dept_level: '0'
      },
      {
        dept_code: 'SF001',
        dept_name: '业务区分组',
        dept_level: '1'
      },
      {
        dept_code: 'YWDQ',
        dept_name: '分公司',
        dept_level: '1',
        moduleCode: 'JYP09000000' // 根据java端逻辑选取一个模块编码
      }
    ]
  },
  {
    // 顺心
    type: '9',
    list: [
      {
        zoneCode: '001',
        value: '001',
        label: '顺心捷达',
        zoneName: '顺心捷达',
        zoneLevel: '0'
      }
    ]
  }
]
