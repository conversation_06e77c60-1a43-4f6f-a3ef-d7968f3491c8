<template>
  <div id="assistant">
    <!-- <HeadBar :title="title" :homePage="false" :showPublicSelect="showPublicSelect">
      <span slot="certain" class="fs28" @click="certainZoneCode" v-show="showSureBtn">确定</span>
    </HeadBar> -->
    <div v-if="!url">
      <div class="heade-content">
        <div>大件顺心数据洞察助理</div>
      </div>
      <div class="content-box" @click="getAssistantUrl" v-if="isNoData">
        <div class="content-tip-text">页面加载失败，请返回重试！</div>
        <div class="content-back-button">
          <div @click="goBack">返回</div>
        </div>
      </div>
    </div>
    <div class="close-button" @click="goBack">退出</div>
    <iframe v-if="url" :src="url" width="100%" height="100%" class="iframe-constent" ref="myIframe" allow="microphone;camera;"></iframe>
  </div>
</template>

<script>
import sxMonitor from '../sxMonitor/sxMonitor.js'
import requestMixins from 'common/mixins/requestMixins'
import { mapMutations } from 'vuex'
export default {
  name: 'assistant',
  mixins: [sxMonitor, requestMixins],
  components: {
  },
  data() {
    return {
      isNoData: false,
      url: '',
      urls: 'https://dtalk-chatbi.sf-express.com/dtalk-copilot?code=106&accessToken=MEJFNjQ5NjcxOTI5OTlFNzE0MjQ3NkQ3MDI3MTAzOTYxQ0RCRDdDRTRGOEU0Qzk0MzZDN0FDRUVGNjZBQTcyNTJDRjgyMzk3ODVENjg2MTkyRjQ0MEU2NDVBQUFFNzExRUVEMjU2MzFDNjg1REE4MjZDMjZEQzhCQ0JENTI3QTNGRjJCRTlERjNGQjNDRTVDOTAwOTcwQ0VCODMzMjFCOQ==&callbackUrl=https://bi.sxjdfreight.com#/'
    }
  },
  computed: {
  },

  mounted() {
    this.getAssistantUrl()
  },

  methods: {
    ...mapMutations(['setZoneParams', 'setRequestQueues']),
    goBack() {
      this.$router.back(-1)
    },
    getAssistantUrl() {
      const _this = this
      _this.url = ''
      _this.isNoData = false
      this.sendJavaRequest(
        {
          url: '/sx-security/sxCdbi/getChatbiUrlForCas',
          method: 'GET'
        },
        true,
        false
      ).then(res => {
        _this.setRequestQueues({})
        if (res.obj && typeof res.obj === 'string') {
          _this.url = res.obj
        } else {
          _this.isNoData = true
          _this.url = ''
        }
      })
      .catch(err => {
        _this.isNoData = true
        _this.isLoading = false
        console.log(err, '修改失败')
      })
    }
  }
}
</script>

<style lang="less" scoped>
#assistant {
  position: relative;
  height: 100%;
  background: #f4f4f4;
}
.iframe-constent {
    position: absolute;
}
.close-button {
  position: absolute;
  font-size: 0.28rem;
  top: 0.42rem;
  right: 0.24rem;
  z-index: 100;
  color: #666;
}
.heade-content {
  font-size: 0.33rem;
  font-weight: 500;
  width: 100vw;
  height: 1.12rem;
  display: flex;
  align-items: center;
  padding: 0 0.2rem;
  justify-content: center;
  align-items: center;
  padding: 0 0.2rem;
  border-bottom: 1px solid #e8e7e7;
  background: #FFFFFF;
}
.content-box {
  margin-top: 0.20rem;
  width: 100vw;
  text-align: center;
  font-size: 0.26rem;
  .content-tip-text {
    color: #666666;
    margin-top: 0.88rem;
  }
  .content-back-button {
    margin-top: 0.42rem;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    div {
      color: #ffffff;
      height: 0.48rem;
      line-height: 0.48rem;
      background: #DC1E32;
      padding: 0.02rem 0.16rem;
      border-radius: 0.08rem;
    }
  }
}
</style>
