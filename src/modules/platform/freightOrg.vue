<template>
  <div id="organization">
    <HeadBar v-if="!isSearch" :title="title" :homePage="false" :showPublicSelect="showPublicSelect">
      <span slot="certain" class="fs28" @click="certainZoneCode" v-show="showSureBtn">确定</span>
    </HeadBar>
    <HeadBar v-if="isSearch" :title="sxtitle" :homePage="false" :showPublicSelect="showPublicSelect">
      <span slot="certain" class="fs28" @click="certainZoneCodeBySearch" v-show="showSureBtn">确定</span>
    </HeadBar>

    <div class="panels-container">
      <div style="background: #fff" class="form_block">
        <!-- <search-block @search="searchList" ref="search" :placeholder="placeholder"></search-block> -->
        <!-- <KydSearch @search="searchList" ref="search" placeholder="请输入组织编码或名称"></KydSearch> -->
      </div>
      <div class="list-header-box" v-if="!isSearch">
        <div v-show="zoneLevel" style="height: 0.24rem; width: 100%; background: #f8f9fc"></div>
        <div v-show="!zoneLevel" class="bg" @click="zbClick">
          <div :class="selectName === '顺心捷达' ? 'selected' : ''">顺心捷达</div>
        </div>
        <div class="header-title" :class="moveLeft ? 'moveLeft' : 'moveNormal'">
          <div v-for="(item, index) in headerArr" :key="index">
            {{ item }}
          </div>
        </div>
      </div>
      <!-- 筛选框 -->
      <div
        v-if="!isSearch"
        class="content-container"
        :class="moveLeft ? 'moveLeft' : 'moveNormal'"
        ref="levelContentDom"
      >
        <div v-for="(item, index) in headerArr.length" :key="index" :style="{ height: contentHeight }">
          <div v-if="arrayList[index].length" :style="{ height: '100%', background: '#f8f9fc' }">
            <ul>
              <li
                v-for="data in arrayList[index]"
                :key="data.value"
                class="selected-classified-item"
                :class="[selectList[index] === data.value ? 'active' : '', selectName === data.label ? 'checked' : '']"
                :style="{ background: !index ? 'f8f9fc' : '#fff' }"
                @click="switchNextOrgTab(data, index + 1)"
              >
                <div>{{ data.label }}</div>
              </li>
            </ul>
            <div style="height: 1.6rem"></div>
          </div>
        </div>
      </div>
      <!-- 搜索内容 -->
      <div v-else>
        <div class="seach-conent" :style="{ height: contentHeight }" ref="searchContentDom">
          <div :style="{ background: '#FFF' }">
            <ul
              v-for="s in serachResultList"
              :key="s.value"
              class="search_each_block"
              @click="chooseSearchZoneParam(s, s.value)"
              :class="[chooseSearchZone === s.value ? 'active' : '']"
            >
              <li>{{ s.value }}</li>
              <li>{{ s.label }}</li>
            </ul>
            <div style="height: 1.6rem"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeadBar from 'common/components/head.vue'
import zoneList from './config/newzoneList.js'
import sxMonitor from '../sxMonitor/sxMonitor.js'
import { mapState, mapMutations } from 'vuex'
// import { departmentData } from './test'

export default {
  name: 'freightOrg',
  mixins: [sxMonitor],
  components: {
    HeadBar
  },
  data() {
    return {
      showSureBtn: true,
      showPublicSelect: false,
      title: '筛选',
      sxtitle: '搜索组织',
      placeholder: '请输入组织编码或名称',
      isSearch: false,
      selectedLabel: '',
      first: '切换日期',
      second: '切换组织',
      tabs: [
        {
          label: '切换日期'
        },
        {
          label: '切换组织'
        }
      ],
      rightContentWidth: 0,
      maxColoumnWidth: 0,
      timeParams: {},
      timeValue: '',
      dept_code: '',
      // zoneLevel: '',
      type: '',
      firstArray: [],
      firstSelect: '',
      secondArray: [],
      secondSelect: '',
      teamArray: [],
      thirdArray: [],
      thirdSelect: '',
      fourArray: [],
      fourSelect: '',
      selectZone: '',
      selectZoneParams: {},
      searchValue: '',
      serachResultList: [],
      chooseSearchZone: '',

      contentHeight: '0px',
      // 组织权限数据-非树形
      originData: [],
      selectName: '',
      moveLeft: false,
      srcollHeight: (window.screen.height * 2) / 100 - 4.1 + 'rem',
      seachHeight: (window.screen.height * 2) / 100 - 1.8 + 'rem'
    }
  },
  computed: {
    selectTimeVal() {
      if (!this.timeParams.type || this.timeParams.type === 'date') {
        return (
          this.timeValue.substring(0, 4) + '-' + this.timeValue.substring(4, 6) + '-' + this.timeValue.substring(6, 8)
        )
      } else if (this.timeParams.type && this.timeParams.type === 'month') {
        return this.timeValue.substring(0, 4) + '-' + this.timeValue.substring(4, 6)
      } else {
        return ''
      }
    },
    headerArr() {
      const data = ['省区', '区域', '场站/加盟区']
      switch (+this.zoneLevel) {
        case 0:
        case 2:
          return data
        case 3:
          return data.slice(1)
        case 4:
          return data.slice(2)
        default:
          return data
      }
    },
    arrayList() {
      return [this.firstArray, this.secondArray, this.thirdArray, this.fourArray]
    },
    selectList() {
      return [this.firstSelect, this.secondSelect, this.thirdSelect, this.fourSelect]
    },
    conentWidth() {
      return `${100 / this.headerArr.length}%`
    },

    ...mapState({
      zoneLevel: state => +state.sxMonitorStore.user.zoneLevel
    })
  },

  methods: {
    ...mapMutations(['setZoneParams']),

    beforeInit() {
      this.selectedLabel = '切换日期'
      this.isSearch = false
      this.searchValue = ''
      const { params } = this.$route
      this.type = params.type
      this.levelNum = params.levelNum || 3 // 默认三列
      this.onlyDistrict = params.onlyDistrict !== undefined
      const clientWidth = document.documentElement.clientWidth
      this.rightContentWidth = this.zoneLevel < 3 ? clientWidth - 100 : clientWidth - 125
      this.maxColoumnWidth = this.rightContentWidth / 2
      this.$nextTick(() => {
        // this.$refs.search.refresh()
        const windowHeight = window.innerHeight
        const { top } = this.$refs['levelContentDom'].getBoundingClientRect()
        this.contentHeight = `${windowHeight - top}px`
      })
      this.getNextZoneList()
    },

    getZoneName() {
      const tableName = 'sx_dim_department_2022'
      const conditionList = []
      return this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        this.$store.commit('setOrgArray', res.obj || [])
        return res.obj
      })
    },

    // 获取组织权限数据
    async getNextZoneList() {
      await function() {
        return new Promise((res, rej) => {
          setTimeout(() => {
            res()
          }, 0)
        })
      }
      let orgArray = this.$store.state.orgArray
      if (!orgArray.length) {
        orgArray = await this.getZoneName()
      }
      const validData = Array.isArray(orgArray) ? orgArray : []
      console.log(validData, 'orgArray')
      const sxState = this.$store.state.sxMonitorStore
      const ysState = this.$store.state.authList
      const {
        user: { zoneLevel, zoneCode }
      } = sxState
      const typeMap = {
        0: 'province_area',
        2: 'province_area',
        3: 'area',
        4: 'dept'
      }
      const originData = []
      let result
      console.log(JSON.parse(JSON.stringify(ysState)), 'ysState------')
      if (ysState.length && +zoneLevel !== 0) {
        const matchData = []
        ysState.forEach(ysItem => {
          validData.forEach(item => {
            const key = typeMap[+ysItem.zone_level]
            const value = (item[`${key}_code`] || '').trim()
            const label = item[`${key}_name`]
            const match = value === ysItem[[`${key}_code`]]
            const judgeRepeat = matchData.find(repeat => repeat.zoneCode === ysItem[`${key}_code`])
            // console.log(match, 'match---')

            // console.log(judgeRepeat, 'judgeRepeat---')
            if (match && !judgeRepeat) {
              matchData.push(
                Object.assign(item, {
                  label,
                  value,
                  zoneCode: value,
                  zoneLevel: item[`${key}_id`],
                  zoneName: label
                })
              )
            }
          })
        })
        result = matchData
        console.log(JSON.parse(JSON.stringify(matchData)), 'matchData-----')
      } else {
        if (+zoneLevel === 0) {
          const matchData = []
          validData.forEach(item => {
            const key = typeMap[+zoneLevel]
            const value = (item[`${key}_code`] || '').trim()
            const label = item[`${key}_name`]
            const judgeRepeat = matchData.find(repeat => repeat.zoneCode === item[`${key}_code`])

            if (!judgeRepeat) {
              matchData.push(
                Object.assign(item, {
                  label,
                  value,
                  zoneCode: value,
                  zoneLevel: item[`${key}_id`],
                  zoneName: label
                })
              )
            }
          })
          result = matchData
        } else {
          result = validData.find(item => {
            const key = typeMap[+zoneLevel]
            const value = (item[`${key}_code`] || '').trim()
            const label = item[`${key}_name`]
            const match = value === zoneCode
            if (match) {
              Object.assign(item, {
                label,
                value,
                zoneCode: value,
                zoneLevel: item[`${key}_id`],
                zoneName: label
              })
            }
            return match
          })
          result = result ? [result] : result
        }
      }
      console.log(JSON.parse(JSON.stringify(result)), 'result-----')
      // 数据归类
      const combineTreeData = (data, deepLevel = 1) => {
        const groupKey = typeMap[deepLevel]
        const groupByKey = `${groupKey}_code`
        return data.reduce((ret, item) => {
          const label = /【SX】/.test(item[`${groupKey}_name`])
            ? item[`${groupKey}_name`].replace('【SX】', 'SX')
            : item[`${groupKey}_name`]
          const zoneLevelVal = item[`${groupKey}_id`]
          const value = (item[groupByKey] || '').trim()
          if (!value || value === 'N') {
            return ret
          }
          const isExist = ret.find(one => one[groupByKey] === value)
          if (!isExist) {
            const children = data.reduce((innerRet, one) => {
              if (one[groupByKey] === value) {
                const oneItem = Object.assign({}, one, { children: null })
                innerRet.push(oneItem)
              }
              return innerRet
            }, [])
            const nextLevel = +deepLevel + 1 // 下一层zoneLevel
            const excuteNext = typeMap[nextLevel] // 是否进行下一层分类
            if (excuteNext && children.length) {
              item.children = combineTreeData(children, nextLevel)
            }
            const itemCopy = {
              ...item,
              label,
              value,
              zoneName: label,
              zoneCode: value,
              zoneLevel: zoneLevelVal
            }
            originData.push({ ...itemCopy, children: null })
            ret.push(itemCopy)
          }
          return ret
        }, [])
      }
      // 树形数据结构
      const collect = combineTreeData(validData, Number(zoneLevel) === 0 ? 2 : zoneLevel)
      // 一般数据结构
      this.originData = originData
      if (Number(zoneLevel) !== 0) {
        const fisrtArr = result.length ? result : []
        this.firstArray = fisrtArr || collect
        this.switchNextOrgTab(this.firstArray[0])
      } else {
        // 表示总部层级进来
        this.selectName = '顺心捷达'
        const fisrtArr = result.length ? result : []
        this.firstArray = fisrtArr || collect
        this.zbClick()
      }
      setTimeout(() => {
        // this.$refs.firstColumn.refresh()
      }, 0)
    },

    switchNextOrgTab(item, level = 1) {
      if (!item) {
        return
      }
      if (this.zoneLevel <= 2 && level === 3) {
        this.moveLeft = false
      }
      if (this.zoneLevel <= 2 && level === 2 && this.moveLeft) {
        this.moveLeft = false
      }
      const columnMap = {
        1: 'secondArray',
        2: 'thirdArray',
        3: 'fourArray'
      }
      const child = columnMap[level]
      const { children } = item
      this[child] = children || []
      this.selectZone = item.label
      this.selectZoneParams = item
      this.selectName = item.zoneName
      switch (level) {
        case 1:
          this.firstSelect = item.value
          this.secondSelect = ''
          this.thirdArray = []
          this.thirdSelect = ''
          this.fourArray = []
          this.fourSelect = ''
          break
        case 2:
          this.secondSelect = item.value
          this.thirdSelect = ''
          this.fourArray = []
          this.fourSelect = ''
          break
        case 3:
          this.thirdSelect = item.value
          this.fourSelect = ''
          break
        case 4:
          this.fourSelect = item.value
          break
      }
    },

    certainZoneCode() {
      const { zoneLevel } = this.selectZoneParams
      const sxState = this.$store.state.sxMonitorStore
      const {
        user: { zoneLevel: typeLevel }
      } = sxState
      if (zoneLevel < typeLevel) {
        this.$toast({
          duration: 2000,
          message: '该组织无法选择'
        })

        return
      }
      this.$store.commit('setSXMonitor', {
        zoneParams: this.selectZoneParams,
        isCertain: true
      })
      this.setZoneParams(this.selectZoneParams)

      // 输出确定选择的区域信息，包括上级信息
      console.log('=== freightOrg: 确定选择区域 ===')
      console.log('选中的区域参数:', this.selectZoneParams)
      console.log('区域级别:', this.selectZoneParams.zoneLevel)
      console.log('区域代码:', this.selectZoneParams.zoneCode)
      console.log('区域名称:', this.selectZoneParams.zoneName)

      // 输出上级信息
      console.log('=== 上级信息 ===')
      console.log('上级省区code:', this.selectZoneParams.province_area_code || '无')
      console.log('上级省区名称:', this.selectZoneParams.province_area_name || '无')
      console.log('上级区域code:', this.selectZoneParams.area_code || '无')
      console.log('上级区域名称:', this.selectZoneParams.area_name || '无')

      // 触发 zoneInfo store 更新（通过 mutation 触发响应式更新）
      this.$store.commit('zoneInfo/setCachedZoneData', {
        updateTime: Date.now(),
        selectedZone: this.selectZoneParams
      })
      if (this.timeValue !== '') {
        this.$store.commit('setSXMonitor', {
          updateTimeValue: this.timeValue
        })
      }
      this.$router.back(-1)
    },

    chooseSearchZoneParam(item, value) {
      this.chooseSearchZone = value
      this.chooseSearchZoneObj = item
    },

    certainZoneCodeBySearch() {
      if (this.chooseSearchZoneObj === undefined) {
        this.$toast({
          duration: 2000,
          message: '请至少选择一个网点'
        })

        return
      }
      const params = {
        ...this.chooseSearchZoneObj,
        zoneCode: this.chooseSearchZoneObj.zoneCode,
        zoneLevel: this.chooseSearchZoneObj.zoneLevel,
        zoneName: this.chooseSearchZoneObj.zoneName
      }
      this.$store.commit('setSXMonitor', {
        zoneParams: params,
        isCertain: true
      })
      this.setZoneParams(params)

      // 输出搜索确定选择的区域信息
      console.log('=== freightOrg: 搜索确定选择区域 ===')
      console.log('搜索选中的区域参数:', params)
      console.log('区域级别:', params.zoneLevel)
      console.log('区域代码:', params.zoneCode)
      console.log('区域名称:', params.zoneName)

      // 输出上级信息
      console.log('=== 搜索选择的上级信息 ===')
      console.log('上级省区code:', params.province_area_code || '无')
      console.log('上级省区名称:', params.province_area_name || '无')
      console.log('上级区域code:', params.area_code || '无')
      console.log('上级区域名称:', params.area_name || '无')

      // 触发 zoneInfo store 更新
      this.$store.commit('zoneInfo/setCachedZoneData', {
        updateTime: Date.now(),
        selectedZone: params
      })

      if (this.timeValue !== '') {
        this.$store.commit('setSXMonitor', {
          updateTimeValue: this.timeValue
        })
      }
      this.$router.back(-1)
    },

    goBack() {
      // util.handleSingleComponentCache(this)
      this.$router.back(-1)
    },

    zbClick() {
      this.moveLeft = false
      this.selectName = '顺心捷达'
      this.firstSelect = ''
      this.secondArray = []
      this.secondSelect = ''
      this.thirdArray = []
      this.thirdSelect = ''
      this.fourArray = []
      this.fourSelect = ''
      // const index = this._.findIndex(zoneList, item => {
      //   return item.type === this.type
      // })
      const firstArray = zoneList[0].list
      this.selectZone = (firstArray[0] || {}).label
      this.selectZoneParams = firstArray[0] || {}
    },

    // 关键字搜索
    searchList(val) {
      if (val.trim()) {
        if (!this.isSearch) {
          this.isSearch = true
        }

        this.chooseSearchZone = ''
        this.chooseSearchZoneObj = undefined
        // const reg = /^[\u4e00-\u9fa5]+$/
        this.serachResultList = this.originData.reduce((ret, item) => {
          const { label, value } = item
          // 没有使用的组织层级没有label and value
          if (!label || !value) {
            return ret
          }
          const matchLabel = label.indexOf(val) >= 0
          const matchValue = value.indexOf(val) >= 0
          if (matchLabel || matchValue) {
            const isExist = ret.some(one => one.value === value)
            if (!isExist) {
              ret.push(item)
            }
          }
          return ret
        }, [])
      } else {
        if (this.isSearch) {
          this.isSearch = false
        }
      }
    }
  },

  watch: {
    $route: {
      deep: true,
      handler() {
        if (this.$route.name === 'organization') {
          this._.forEach(Object.keys(this.$route.params), item => {
            this[item] = this.$route.params[item]
          })
        }
      }
    },
    isSearch(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          const windowHeight = window.innerHeight
          const { top } = this.$refs['searchContentDom'].getBoundingClientRect()
          this.contentHeight = `${windowHeight - top}px`
        })
      }
    }
  },

  created() {
    this.beforeInit()
    // 神策
    this.$sensors.pageview('筛选')
  },

  activated() {
    // this.$setWebViewBackgroundColor('#fff')
    if (this.type !== '') {
      if (this.$route.params.type !== this.type) {
        this.beforeInit()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.panels-container {
  flex: 1;
  overflow: hidden;
  .list-header-box {
    height: auto;
    width: 100%;
    .bg {
      height: 1.36rem;
      width: 100%;
      padding: 0.24rem 0.4rem;
      background: #f8f9fc;
      > div:first-child {
        height: 100%;
        width: 100%;
        background: #fff;
        font-family: PingFangSC-Medium;
        font-size: 0.28rem;
        color: #333333;
        text-align: center;
        line-height: 0.88rem;
      }
      .selected {
        color: #dc1e32 !important;
        display: flex;
        justify-content: center;
        align-items: center;
        &::after {
          content: '';
          display: inline-block;
          background: url('../../common/img/checked.png') no-repeat;
          background-size: contain;
          height: 0.24rem;
          width: 0.24rem;
          margin-left: 0.16rem;
        }
      }
    }
    .header-title {
      display: flex;
      align-items: center;
      height: 0.88rem;
      background: #fff;
      > div {
        min-width: 33.4%;
        background: #fff;
        box-shadow: inset 0 0.02rem 0 0 #f2f2f2, inset 0 -0.02rem 0 0 #f2f2f2;
        font-family: PingFangSC-Medium;
        font-size: 0.28rem;
        color: #333333;
        height: 100%;
        line-height: 0.88rem;
        text-align: left;
        padding-left: 0.2rem;
      }
    }
  }
  .content-container {
    display: flex;
    align-items: flex-start;
    background-color: #fff;
    & > div {
      min-width: 33.4%;
      overflow-y: scroll;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      &::-webkit-scrollbar {
        display: none;
      }
    }
    .selected-classified-item {
      padding: 0.24rem 0 0.24rem 0.24rem;
      line-height: 0.4rem;
      font-size: 0.28rem;
      color: #666;
      box-shadow: inset 0 -0.02rem 0 0 #f2f2f2;
      background: #f8f9fc;

      & > div:first-child {
        max-width: 1.6rem;
        min-width: 1.6rem;
      }

      &.active {
        color: #dc1e32;
        box-shadow: none;
        background: #fff;
      }

      &.checked {
        display: flex;
        align-items: center;
        &::after {
          content: '';
          display: inline-block;
          background: url('../../common/img/checked.png') no-repeat;
          background-size: contain;
          height: 0.24rem;
          width: 0.24rem;
          margin-left: 0.16rem;
        }
      }
    }
  }
  .seach-conent {
    overflow-y: scroll;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

input::-webkit-search-cancel-button {
  display: none;
}

input[type='search']::-ms-clear {
  display: none;
}

.sxFont {
  display: flex;
  // position: absolute;
  align-items: center;
  font-size: 0.28rem;
  color: #fff;
  padding-left: 0.2rem;
}

.search_each_block {
  padding: 0.24rem 0 0.24rem 0.36rem;
  border-bottom: 0.02rem solid #f2f2f2;
  line-height: 0.4rem;
  font-size: 0.28rem;
  color: #565c66;
  background: #fff;

  &.active {
    color: #de1c23;
    border-top: 1px solid #de1c23;
    border-bottom: 1px solid #de1c23;
    background: rgba(220, 30, 50, 0.15);
  }

  & > li:last-child {
    font-size: 0.2rem;
    line-height: 0.28rem;
  }
}

.popup_block {
  background: rgba(0, 0, 0, 0.7);
  color: rgba(255, 255, 255, 0.7);
  height: 1rem;
  font-size: 0.24rem;
  line-height: 1rem;
  width: 4rem;
  text-align: center;
  font-weight: normal;
  border-radius: 0.16rem;
}

.goBack {
  line-height: 0.88rem;
  font-size: 0.64rem;
  color: #fff;
  padding-left: 0.2rem;
}

.moveLeft {
  transform: translate(-33.4%, 0);
  transition: all 0.3s ease;
}
.moveNormal {
  transform: translate(0, 0);
  transition: all 0.3s ease;
}
</style>
