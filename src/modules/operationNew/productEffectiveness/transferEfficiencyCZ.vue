<!--
 * @Author: gilshi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description:中转效率
-->
<template>
  <div>
    <CardList title="中转效率">
      <Tabs slot="nav" :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" />
      <div class="pd_lr20">
        <BtnTabs class="mt32" :options="dataTypeList" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3"></BtnTabs>
        <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
    </CardList>
    <TabsDrawer :show="visible" :handleClose="handleClose" :title="drawerTitle" :tabs="tabOptions"
      :request="getPlanTimeResultDataTable"></TabsDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import request from './commonMixins/request'
import { overAllProfit, planTimeReachTab } from './commonMixins/config'
import { drawScrollChart } from 'common/charts/chartOption'
import TabsDrawer from './tabsDrawer'
import { mapMutations } from 'vuex'
const typeData = [
  {
    label: '中转及时率',
    serise: [
      {
        label: '中转及时率',
        dataIndex: 'transferRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'transferComRate',
        per: [1]
      }
    ]
    // key1: 'transferRate',
    // key2: 'transferComRate'
  },
  {
    label: '发车准点率',
    serise: [
      {
        label: '发车准点率',
        dataIndex: 'transferRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'transferComRate',
        per: [1]
      }
    ]
    // key1: 'transferRate',
    // key2: 'transferComRate'
  },
  {
    label: '移动及时率',
    serise: [
      {
        label: '移动及时率',
        dataIndex: 'transferRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'transferComRate',
        per: [1]
      }
    ]
    // key1: 'transferRate',
    // key2: 'transferComRate'
  },
  {
    label: '干线卸车及时率',
    serise: [
      {
        label: '干线卸车及时率',
        dataIndex: 'transferRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'transferComRate',
        per: [1]
      }
    ]
    // key1: 'transferRate',
    // key2: 'transferComRate'
  },
  {
    label: '干线卸车合格率',
    serise: [
      {
        label: '干线卸车合格率',
        dataIndex: 'transferRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'transferComRate',
        per: [1]
      }
    ]
    // key1: 'transferRate',
    // key2: 'transferComRate'
  },
  {
    label: '必走货留仓率',
    serise: [
      {
        label: '必走货留仓率',
        dataIndex: 'transferRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'transferComRate',
        per: [1]
      }
    ]
    // key1: 'transferRate',
    // key2: 'transferComRate'
  }
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [mixins, request],
  components: { TabsDrawer },
  data() {
    return {
      // btnData: ['规划时效总体达成', '三日内占比动态', '次日达动态', '次日达经济圈动态'],
      dataTypeList: [...dataTypeList],
      tableWidth: '100%',
      tableData: {},
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日', '周', '月'],
      dateKeyList: ['day', 'week', 'month'],
      visible: false,
      drawerTitle: '规划时效达成情况',
      planTimeReachTab
    }
  },
  computed: {
    columns() {
      return _.defaultsDeep([], overAllProfit[this.btnIndex][this.dateIndex])
    },
    tabOptions() {
      return this.planTimeReachTab(this.level_next_name)
    },
    legendOption() {
      const currentSerise = typeData[this.btnIndex]?.serise
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: currentSerise.map((item, index) => {
          return {
            seriesIndex: index,
            ...item
          }
        }),
        // options: [
        //   { label: currentTarget.label, dataIndex: currentTarget.key1, per: [1], seriesIndex: 0 },
        //   { label: '目标完成率', dataIndex: currentTarget.key2, per: [1], seriesIndex: 1 }
        // ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    transferEffDataCZ(val) {
      this.initData(val)
    },
    btnIndex(val) {
      this.initData([this.transferEffDataCZ, this.trendChangeMon][val])
    },
    tabIndex(val) {
      this.init()
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getTransferEffDataCZ().then(res => {
        this.initData()
      })
    },
    async getTransferEffDataCZ() {
      const data = {
        dateType: this.dateKeyList[this.tabIndex],
        levelCode: 39,
        [this.key_level_code]: this.zoneCode,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      // const data = {
      //   "statisticalDate": "2024-09-19",
      //   "dateType": this.dateKeyList[this.tabIndex],
      //   "levelCode": "39",
      //   "provinceAreaCode": "S551Y022",
      //   "areaCode": "S512Y077",
      //   "deptCode": "025WH"
      // }
      const { obj } = await this._getTransferEffDataCZ(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'transferEffDataCZ',
        data: obj
      })
    },
    btnConfirm({ index }) {
      this.btnIndex = index
      this.initData()
    },
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
    },
    async getPlanTimeResultDataTable() {
      const data = {
        dateType: this.dateKeyList[this.tabIndex],
        levelCode: this.level_next_value,
        [this.key_level_code]: this.zoneCode
        // [this.key_date_value[this.tabIndex]]: this.$mount(this.dateValue).format('YYYY-MM-DD')
      }
      const dataType = this.dateKeyList[this.tabIndex]
      const { obj } = await this._getPlanTimeResultData(data)
      return obj[dataType]
    },
    btnOpenDia() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    initData() {
      const tabNames = this.dataTypeList[this.btnIndex]
      const targetObj = this.transferEffDataCZ.find((item) => item.indicatorName === tabNames)
      const targetList = targetObj.indicatorData || []
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: targetList.map(item => {
              if (this.tabIndex === 0) {
                return this.$dateFormat(item.transferDate, this.tabIndex)
              } else if (this.tabIndex === 1) {
                return item.transferDate
              } else {
                return `${this.$moment(item.transferDate).format('MM')}月`
              }
            })
            // axisLabel: {
            //   formatter: (value, i) => {
            //     if (this.tabIndex === 0) {
            //       return this.$moment(value).format('MM-DD')
            //     } else {
            //       return value
            //     }
            //   }
            // }
          }
        ],
        // yAxis: [
        //   {
        //     axisLabel: {
        //       formatter: value => this.$perFormat(value)
        //     }
        //   }
        // ],
        yAxis: typeData[this.btnIndex].serise.map((item, index) => {
          const colorList = ['#4c83f9', '#ff8066']
          return {
            scale: false,
            axisLine: {
              show: true, // 显示y轴线
              lineStyle: {
                  color: colorList[index] // 设置第二个Y轴的颜色为蓝色
              }
            },
            splitLine: {
              lineStyle: {
                  type: 'dashed'
              }
            },
            axisLabel: {
              formatter: (value, ...reset) => {
                return this.$perFormat(value)
              }
            },
            min: (value) => {
              // const num = Math.floor((value.min - 0.01) / 10)
              return value.min - value.min / 100
            }
          }
        }),
        series: [
          {
            type: 'line',
            yAxisIndex: 0,
            // data: [0.2, 0.3, 0.4, 0.5, 0.6, 0.2, 0.1, 0.4]
            data: targetList.map(item => {
              return {
                ...item,
                value: item.transferRate || 0,
                transferRate: item.transferRate || 0
              }
            }),
            smooth: false
          },
          {
            type: 'line',
            // data: [0.4, 0.1, 0.2, 0.5, 0.2, 0.4, 0.1, 0.6],
            data: targetList.map(item => {
              return {
                ...item,
                value: item.transferComRate || 0,
                transferComRate: item.transferComRate || 0
              }
            }),
            smooth: false,
            yAxisIndex: 1
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 85% !important;
}
.chart_btn_wrap {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  // position: absolute;
  // right: 0;
  // top: 0;
}
</style>
