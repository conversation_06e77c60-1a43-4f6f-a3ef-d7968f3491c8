<!--
 * @Author: gilshi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description: 客诉率-场站维度
-->
<template>
  <div>
    <CardList title="客诉率">
      <Tabs slot="nav" :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" />
      <div class="pd_lr20">
        <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
    </CardList>
    <KyDataDrawer
      :visible="visible"
      :title="diaTitle"
      height="80%"
      @close="diaClose"
      @drawerHeight="mixinsDrawerHeight"
    >
      <div class="mt32"></div>
      <NormalTable :columns="diaTableColumns" :dataSource="diaTableDataSource" :maxHeight="mixinsTableMaxHeight" />
    </KyDataDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import request from './commonMixins/request'
import { drawScrollChart } from 'common/charts/chartOption'
import { overAllProfit, diaYundanTableColumns } from './commonMixins/config'
import { mapMutations } from 'vuex'
export default {
  mixins: [mixins, request],
  data() {
    return {
      tableWidth: '100%',
      tableData: {},
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日', '周', '月'],
      dateKeyList: ['day', 'week', 'month'],
      visible: false,
      diaTitle: '运单时长',
      diaTableColumns: [],
      diaTableDataSource: [],
      complaintDataSource: []
    }
  },
  computed: {
    columns() {
      return _.defaultsDeep([], overAllProfit[this.btnIndex][this.dateIndex])
    },
    legendOption() {
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: [
          // { label: '百万票客诉率', seriesIndex: 0 }
          { label: '百万票客诉率', dataIndex: 'ccRate', int: [1], seriesIndex: 0 }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    customerComplaintDataCZ(val) {
      this.initData(val)
    },
    tabIndex(val) {
      this.init()
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getCustomerComplaintDataCZ().then((_) => {
        this.initData()
      })
    },
    async getCustomerComplaintDataCZ() {
      const data = {
        dateType: this.dateKeyList[this.tabIndex],
        levelCode: 39,
        [this.key_level_code]: this.zoneCode,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getCustomerComplaintDataCZ(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'customerComplaintDataCZ',
        data: obj || []
      })
      this.complaintDataSource = obj
    },
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
    },

    async btnOpenDia() {
      const data = {
        dateType: this.dateKeyList[this.tabIndex],
        levelCode: this.level_next_value,
        [this.key_level_code]: this.zoneCode
        // [this.key_date_value[this.tabIndex]]: this.$mount(this.dateValue).format('YYYY-MM-DD')
      }
      this.visible = true
      this.diaTableColumns = [...diaYundanTableColumns(this.level_next_name)]
      const { obj } = await this._getWaybillDurationData(data)
      this.diaTableDataSource = obj.map(item => {
        return {
          ...item,
          ccRate: item.ccRate || 0
        }
      })
    },
    diaClose() {
      this.visible = false
    },
    initData() {
      this.legendDataSource = []
      const datas = (this.complaintDataSource || []).map(item => {
        return {
          ...item,
          ccRate: item.ccRate
        }
      })
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: datas.map(item => {
              if (this.tabIndex === 0) {
                return this.$dateFormat(item.ccDate, this.tabIndex)
              } else if (this.tabIndex === 1) {
                return item.ccDate
              } else {
                return `${this.$moment(item.ccDate).format('MM')}月`
              }
            })
          }
        ],
        yAxis: {
          axisLabel: {
            formatter: value => this.$numToInteger(value || 0, 0)
          }
        },
        series: [
          {
            type: 'line',
            // data: this.complaintDataSource.map(item => item.ccRate)
            data: datas.map(item => {
              return {
                ...item,
                ccRate: item.ccRate,
                value: item.ccRate || 0
              }
            })
            // data: [{ccRate: 0.4}, {ccRate: 0.1}, {ccRate: 0.2}, {ccRate: 0.5}, {ccRate: 0.2}, {ccRate: 0.2}, {ccRate: 0.2}, {ccRate: 0.2}]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'], { isOneLine: true })
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 80% !important;
}
.chart_btn_wrap {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  // position: absolute;
  // right: 0;
  // top: 0;
}
</style>
