<!--
 * @Author: shigl
 * @Date: 2022-11-20 18:07:55
 * @LastEditTime: 2023-03-17 14:32:10
 * @Description: 预警指标
-->

<template>
  <div class="mt24">
    <CardList title="预警指标">
      <div v-for="(item,index) in warningIndicators[level_index].items" :key="index">
        <WarmDataList :dataSource="getTableList(item.key)" :options="item.options" :warmTitle="item.name" @openDia="btnOpenDia" :sortIndex="item.sortIndex" :typeKey="item.key"></WarmDataList>
      </div>
    </CardList>
    <KyDataDrawer
      :visible="visible"
      :title="diaTitle"
      height="80%"
      @close="diaClose"
      @drawerHeight="mixinsDrawerHeight"
    >
      <div class="mt32"></div>
      <NormalTable :columns="diaTableColumns" :dataSource="diaTableDataSource" :maxHeight="mixinsTableMaxHeight" />
    </KyDataDrawer>
  </div>
</template>
<script>
import request from './commonMixins/request'
import mixins from './commonMixins/mixins'
import { diaJiaohuoTableColumns } from './commonMixins/constant'
import { warningIndicators } from './commonMixins/constant'
import { mapMutations } from 'vuex'
import WarmDataList from '../components/warmDataList'
export default {
  components: {
    WarmDataList
  },
  mixins: [request, mixins],
  props: [],
  data() {
    return {
      dataSource: {},
      options: {},
      visible: false,
      diaTitle: '预警指标-交货及时率',
      diaTableColumns: [],
      diaTableDataSource: [],
      warningIndicators,
      datas: [
        {
          key: 'jhjsl',
          name: '交货及时率'
        },
        {
          key: 'zzjsl',
          name: '中转及时率'
        },
        {
          key: 'yxhgl',
          name: '运行合格率'
        },
        {
          key: 'psjsh',
          name: '派送及时率'
        },
        {
          key: 'bzhlcl',
          name: '必走货留仓率'
        },
        {
          key: 'ydsc',
          name: '运单时长'
        },
        {
          key: 'ghdc',
          name: '规划时效达成率'
        }
      ]
    }
  },
  computed: {
  },
  watch: {
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getEarlyWarningData().then(res => {
        this.setDataSource()
      })
    },
    async getEarlyWarningData() {
      const data = {
        dateType: 'month',
        levelCode: this.level_next_value,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateValue).format('YYYY-MM-DD')
      }
      this.options = {
        label: this.level_next_name,
        dataIndex: 'pickupRate',
        int: [0]
      }
      const { obj } = await this._getEarlyWarningData(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'earlyWarningData',
        data: obj
      })
    },
    setDataSource() {
      this.dataSource = this.earlyWarningData
    },
    btnOpenDia(data) {
      console.log(data)
      this.visible = true
      this.diaTitle = `预警指标-${data.warmTitle}`
      this.diaTableColumns = [...diaJiaohuoTableColumns(this.level_next_name, data.type)]
      // this.diaTableDataSource = this.earlyWarningData[data.type]
      this.diaTableDataSource = this.earlyWarningData[data.type].map(item => {
        if (item.levelCode === "39" && this.zoneLevel === '30') {
          return {
            ...item,
            provinceAreaName: item.deptName
          }
        } else if (item.levelCode === "39" && this.zoneLevel === '32') {
          return {
            ...item,
            areaName: item.deptName
          }
        } else {
          return item
        }
      })
    },
    diaClose() {
      this.visible = false
    },
    getTableList(id) {
      return this.earlyWarningData[id]
      // if (this.zoneLevel === '32') {
      //   return this.earlyWarningData[id].filter(item => item.levelCode !== "39")
      // } else {
      //   return this.earlyWarningData[id]
      // }
    }
  },
  mounted() {
    this.init()
    this.options = {
      label: this.level_next_name,
      dataIndex: 'pickupRate',
      int: [0]
    }
  }
}
</script>
<style lang="less" scoped></style>
