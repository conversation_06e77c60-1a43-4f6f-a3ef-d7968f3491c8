<!--
 * @Author: g<PERSON><PERSON>
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description:时效静态符合率情况
-->
<template>
  <div>
    <CardList :title="'时效静态环节符合情况'">
      <!-- <Tabs slot="nav" :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" /> -->
      <div class="pd_lr20">
        <BtnTabs class="mt32" :options="dataTypeList" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3"></BtnTabs>
        <div class="chart_wrap_content">
          <div class="chart_btn_wrap" v-if="zoneLevel<33">
            <div class="right_btn_wrap" @click="btnOpenDia">
              展开{{ level_next_name_zh }} <i class="iconfont icon-dayuhao fs20"></i>
            </div>
          </div>
          <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>
      </div>
    </CardList>
    <TabsDrawer :show="visible" :handleClose="handleClose" :title="drawerTitle" :tabs="tabOptions"
      :request="getTimeResultDataTable"></TabsDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import request from './commonMixins/request'
import { timeResultDataTab } from './commonMixins/config'
import { drawScrollChart } from 'common/charts/chartOption'
import TabsDrawer from './tabsDrawer'
import { mapMutations } from 'vuex'
const typeData = [
  {
    label: '静态环节符合率',
    serise: [
      {
        label: '静态环节符合率',
        dataIndex: 'staticAgreementRate',
        per: [1],
        min: 0.6,
        max: 0.9
      },
      {
        label: '目标完成率',
        dataIndex: 'staticAgreementRateCompletionRatio',
        per: [1],
        min: 0.7,
        max: 1.2
      }
    ]
    // key1: 'staticAgreementRate',
    // key2: 'staticAgreementRateCompletionRatio'
  },
  {
    label: '交货符合率',
    serise: [
      {
        label: '交货符合率',
        dataIndex: 'jhAgreementRate',
        per: [1],
        min: 0.8,
        max: 1
      },
      {
        label: '目标完成率',
        dataIndex: 'dintimeRateCompletionRatio',
        per: [1],
        min: 0.7,
        max: 1.2
      }
    ]
    // key1: 'jhAgreementRate',
    // key2: 'dintimeRateCompletionRatio'
  },
  {
    label: '转运符合率',
    serise: [
      {
        label: '转运符合率',
        dataIndex: 'zyAgreementRate',
        per: [1],
        min: 0.5,
        max: 0.8
      },
      {
        label: '目标完成率',
        dataIndex: 'tintimeRateCompletionRatio',
        per: [1],
        min: 0.7,
        max: 1.2
      }
    ]
    // key1: 'zyAgreementRate',
    // key2: 'tintimeRateCompletionRatio'
  },
  {
    label: '提货符合率',
    serise: [
      {
        label: '提货符合率',
        dataIndex: 'thAgreementRate',
        per: [1],
        min: 0.8,
        max: 1
      },
      {
        label: '目标完成率',
        dataIndex: 'pintimeRateCompletionRatio',
        per: [1],
        min: 0.7,
        max: 1.2
      }
    ]
    // key1: 'thAgreementRate',
    // key2: 'pintimeRateCompletionRatio'
  },
  {
    label: '配送符合率',
    serise: [
      {
        label: '配送符合率',
        dataIndex: 'psAgreementRate',
        per: [1],
        min: 0.8,
        max: 1
      },
      {
        label: '目标完成率',
        dataIndex: 'sintimeRateCompletionRatio',
        per: [1],
        min: 0.7,
        max: 1.2
      }
    ]
    // key1: 'psAgreementRate',
    // key2: 'sintimeRateCompletionRatio'
  }
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [mixins, request],
  components: { TabsDrawer },
  data() {
    return {
      dataTypeList: [...dataTypeList],
      tableWidth: '100%',
      tableData: {},
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日'],
      dateKeyList: ['day', 'week', 'month'],
      visible: false,
      drawerTitle: '时效静态环节符合情况',
      timeResultDataTab
    }
  },
  computed: {
    tabOptions() {
      return this.timeResultDataTab(this.level_next_name)
    },
    legendOption() {
      const currentSerise = typeData[this.btnIndex]?.serise
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: currentSerise.map((item, index) => {
          return {
            seriesIndex: index,
            ...item
          }
        }),
        // options: currentTypeData?.key2 ? [
        //   { label: currentTypeData.label, dataIndex: currentTypeData.key1, per: [1], seriesIndex: 0 },
        //   { label: '目标完成率', dataIndex: currentTypeData.key2, per: [1], seriesIndex: 1 }
        // ] : [
        //   { label: currentTypeData.label, dataIndex: currentTypeData.key1, per: [1], seriesIndex: 0 }
        //   // { label: '目标完成率', per: [1], seriesIndex: 1 }
        // ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    timeResultData(val) {
      this.initData(val)
    },
    btnIndex(val) {
      this.initData()
    },
    tabIndex(val) {
      this.initData()
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getTimeResultData().then(res => {
        this.initData()
      })
    },
    async getTimeResultData() {
      const data = {
        levelCode: this.zoneLevel,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getTimeResultData(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'timeResultData',
        data: obj
      })
    },
    btnConfirm({ index }) {
      this.btnIndex = index
      this.initData()
    },
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
    },
    async getTimeResultDataTable() {
      const dataType = this.dateKeyList[this.tabIndex]
      const data = {
        dateType: dataType,
        levelCode: this.level_next_value,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getTimeResultData(data)
      return obj[dataType]
    },
    btnOpenDia() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    initData() {
      const dateType = this.dateKeyList[this.tabIndex]
      const result = this.timeResultData[dateType]
      const dateValue = this.date_value_key[this.tabIndex]
      const dataSerise = typeData[this.btnIndex].serise
      const xData = []
      const sData = [[], []]
      if (result && result.length > 0) {
        const fliterList = JSON.parse(JSON.stringify(result))
        fliterList.reverse()
        fliterList.forEach(item => {
          xData.push(item[dateValue])
          dataSerise.forEach((items, i) => {
            sData[i].push({
              ...item,
              value: item[items.dataIndex] || 0
            })
          })
          // sData[0].push({
          //   ...item,
          //   value: item[typeData[this.btnIndex].key1] || 0
          // })
          // sData[1].push({
          //   ...item,
          //   value: item[typeData[this.btnIndex].key2] || 0
          // })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => {
                if (this.tabIndex === 0) {
                  return this.$dateFormat(value, this.tabIndex)
                } else if (this.tabIndex === 1) {
                  return `第${value.slice(-2)}周`
                } else {
                  return `${this.$moment(value).format('MM')}月`
                }
              }
            }
          }
        ],
        yAxis: typeData[this.btnIndex].serise.map((item, index) => {
          const colorList = ['#4c83f9', '#ff8066']
          return {
            scale: false,
            axisLine: {
              show: true, // 显示y轴线
              lineStyle: {
                  color: colorList[index] // 设置第二个Y轴的颜色为蓝色
              }
            },
            splitLine: {
              lineStyle: {
                  type: 'dashed'
              }
            },
            axisLabel: {
              formatter: (value, ...reset) => {
                return this.$perFormat(value)
              }
            },
            min: (value) => {
              // const num = Math.floor((value.min - 0.01) / 10)
              return value.min - value.min / 100
            }
          }
        }),
        // yAxis: [
        //   {
        //     axisLabel: {
        //       formatter: value => {
        //         return this.$perFormat(value)
        //       }
        //     }
        //   }
        // ],
        series: typeData[this.btnIndex]?.serise.map((item, i) => {
          return {
            type: 'line',
            data: sData[i],
            smooth: false,
            yAxisIndex: i
          }
        })
        // series: [
        //   {
        //     type: 'line',
        //     data: sData[0],
        //     smooth: false
        //   },
        //   {
        //     type: 'line',
        //     data: sData[1],
        //     smooth: false
        //   }
        // ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 80% !important;
}
.chart_wrap_content {
  position: relative;
}
.chart_btn_wrap {
  position: absolute;
  right: 0.08rem;
  top: -0.10rem;
}
.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  color: #333333;
}
</style>
