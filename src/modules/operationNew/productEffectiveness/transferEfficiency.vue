<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description:中转效率
-->
<template>
  <div>
    <CardList :title="'中转效率'">
      <Tabs slot="nav" :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" />
      <div class="pd_lr20">
        <BtnTabs class="mt32" :options="dataTypeList" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3"></BtnTabs>
        <div class="chart_wrap_content">
          <div class="chart_btn_wrap" v-if="zoneLevel<33">
            <div class="right_btn_wrap" @click="btnOpenDia">
              展开{{ level_next_name_zh }} <i class="iconfont icon-dayuhao fs20"></i>
            </div>
          </div>
          <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>
      </div>
    </CardList>
    <TabsDrawer :show="visible" :handleClose="handleClose" :title="drawerTitle" :tabs="tabOptions"
      :request="getTransferEffectDataTable"></TabsDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import request from './commonMixins/request'
import { transferEffectTab } from './commonMixins/config'
import { drawScrollChart } from 'common/charts/chartOption'
import TabsDrawer from './tabsDrawer'
import { mapMutations } from 'vuex'
const typeData = [
  {
    label: '中转及时率',
    serise: [
      {
        label: '中转及时率',
        dataIndex: 'tfIntimeRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'tfIntimeCompletionRatio',
        per: [1]
      }
    ]
    // key1: 'tfIntimeRate',
    // key2: 'tfIntimeCompletionRatio'
  },
  {
    label: '发车准点率',
    serise: [
      {
        label: '发车准点率',
        dataIndex: 'carStartIntimeRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'carStartIntimeCompletionRatio',
        per: [1]
      }
    ]
    // key1: 'carStartIntimeRate',
    // key2: 'carStartIntimeCompletionRatio'
  },
  {
    label: '移动及时率',
    serise: [
      {
        label: '移动及时率',
        dataIndex: 'mtRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'mtRateComRate',
        per: [1]
      }
    ]
    // key1: 'mtRate',
    // key2: 'mtRateComRate'
  },
  {
    label: '干线卸车及时率',
    serise: [
      {
        label: '干线卸车及时率',
        dataIndex: 'tuRateOfMv',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'tuRateComOfMv',
        per: [1]
      }
    ]
    // key1: 'tuRateOfMv',
    // key2: 'tuRateComOfMv'
  },
  {
    label: '干线卸车合格率',
    serise: [
      {
        label: '干线卸车合格率',
        dataIndex: 'qfRateOfMainline',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'qfRateOfComMainline',
        per: [1]
      }
    ]
    // key1: 'qfRateOfMainline',
    // key2: 'qfRateOfComMainline'
  },
  {
    label: '必走货留仓率',
    serise: [
      {
        label: '必走货留仓率',
        dataIndex: 'mgRetRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'mgRetComRate',
        per: [1]
      }
    ]
    // key1: 'mgRetRate',
    // key2: 'mgRetComRate'
  }
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [mixins, request],
  components: { TabsDrawer },
  data() {
    return {
      dataTypeList: [...dataTypeList],
      tableWidth: '100%',
      tableData: {},
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日', '周', '月'],
      dateKeyList: ['day', 'week', 'month'],
      visible: false,
      drawerTitle: '中转效率',
      transferEffectTab
    }
  },
  computed: {
    tabOptions() {
      return this.transferEffectTab(this.level_next_name, this.tabIndex)
    },
    legendOption() {
      const currentSerise = typeData[this.btnIndex]?.serise
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: currentSerise.map((item, index) => {
          return {
            seriesIndex: index,
            ...item
          }
        }),
        // options: [
        //   { label: typeData[this.btnIndex].label, seriesIndex: 0, dataIndex: typeData[this.btnIndex].key1, per: [1] },
        //   { label: '目标完成率', seriesIndex: 1, dataIndex: typeData[this.btnIndex].key2, per: [1] }
        // ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    transferEffectData(val) {
      this.initData(val)
    },
    btnIndex(val) {
      this.initData()
    },
    tabIndex(val) {
      this.initData()
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getTransferEffectData().then(res => {
        this.initData()
      })
    },
    async getTransferEffectData() {
      const data = {
        levelCode: this.zoneLevel,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getTransferEffectData(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'transferEffectData',
        data: obj
      })
    },
    btnConfirm({ index }) {
      this.btnIndex = index
      this.initData()
    },
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
    },
    async getTransferEffectDataTable() {
      const dataType = this.dateKeyList[this.tabIndex]
      const data = {
        dateType: dataType,
        levelCode: this.level_next_value,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getTransferEffectData(data)
      return obj[dataType]
    },
    btnOpenDia() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    initData() {
      const dataType = this.dateKeyList[this.tabIndex]
      const result = this.transferEffectData[dataType]
      const dateValue = this.date_value_key[this.tabIndex]
      const dataSerise = typeData[this.btnIndex].serise
      const xData = []
      const sData = [[], []]
      if (result && result.length > 0) {
        const fliterList = JSON.parse(JSON.stringify(result))
        fliterList.reverse()
        fliterList.forEach(item => {
          xData.push(item[dateValue])
          dataSerise.forEach((items, i) => {
            sData[i].push({
              ...item,
              value: item[items.dataIndex] || 0
            })
          })
          // sData[0].push({
          //   ...item,
          //   value: item[typeData[this.btnIndex].key1] || 0
          // })
          // sData[1].push({
          //   ...item,
          //   value: item[typeData[this.btnIndex].key2] || 0
          // })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              interval: 0,
              formatter: value => {
                if (this.tabIndex === 0) {
                  return this.$dateFormat(value, this.tabIndex)
                } else if (this.tabIndex === 1) {
                  return `第${value.slice(-2)}周`
                } else {
                  return `${this.$moment(value).format('MM')}月`
                }
              }
            }
          }
        ],
        yAxis: typeData[this.btnIndex].serise.map((item, index) => {
          const colorList = ['#4c83f9', '#ff8066']
          return {
            scale: false,
            axisLine: {
              show: true, // 显示y轴线
              lineStyle: {
                  color: colorList[index] // 设置第二个Y轴的颜色为蓝色
              }
            },
            splitLine: {
              lineStyle: {
                  type: 'dashed'
              }
            },
            axisLabel: {
              formatter: (value, ...reset) => {
                return this.$perFormat(value)
              }
            },
            min: (value) => {
              // const num = Math.floor((value.min - 0.01) / 10)
              return value.min - value.min / 100
            }
          }
        }),
        // yAxis: [
        //   {
        //     axisLabel: {
        //       formatter: (value, ...reset) => {
        //         return this.$perFormat(value)
        //       }
        //     },
        //     min: (value) => {
        //       const num = Math.floor((value.min - 0.01) / 10)
        //       return parseInt(num)
        //     }
        //   }
        // ],
        series: typeData[this.btnIndex]?.serise.map((item, i) => {
          return {
            type: 'line',
            data: sData[i],
            smooth: false,
            yAxisIndex: i
          }
        })
        // series: [
        //   {
        //     type: 'line',
        //     data: sData[0],
        //     smooth: false
        //   },
        //   {
        //     type: 'line',
        //     data: sData[1],
        //     smooth: false
        //   }
        // ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 80% !important;
}
/deep/ .tab-list {
  display: flex;
  justify-content: space-around !important;
  align-items: center;
}
.chart_wrap_content {
  position: relative;
}
.chart_btn_wrap {
  position: absolute;
  right: 0.08rem;
  top: -0.10rem;
}
.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  color: #333333;
}
</style>
