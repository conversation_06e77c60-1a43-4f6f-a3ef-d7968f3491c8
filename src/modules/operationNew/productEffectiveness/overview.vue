<!--
 * @Author: shigl
 * @Date: 2022-11-20 18:07:55
 * @LastEditTime: 2023-03-17 14:32:10
 * @Description: 运营品效总览
-->
<template>
  <CardList :title="is_station_level ? '场站品效总览' : '运营品效总览'">
    <!-- <KydSummaryDataList class="pd_lr20 mt24" :columns="columns" :dataSource="dataSource"></KydSummaryDataList> -->
    <OverviewDataList :overviewOption="columns" :dataSource="dataSource"></OverviewDataList>
  </CardList>
</template>
<script>
import request from './commonMixins/request'
import mixins from './commonMixins/mixins'
import { overviewOptionCZ, overviewOption } from './commonMixins/config'
import OverviewDataList from '../components/overviewDataList'
import { mapMutations } from 'vuex'
export default {
  components: {
    OverviewDataList
  },
  mixins: [request, mixins],
  props: [],
  data() {
    return {
      dataSource: {},
      overviewOptionCZ,
      overviewOption
    }
  },
  computed: {
    columns() {
      return this.is_station_level ? this.overviewOptionCZ : this.overviewOption
    }
  },
  watch: {
    operatorQualityData(val) {
      this.setDataSource()
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getOperatorQualityData().then(res => {
        this.setDataSource()
      })
    },
    async getOperatorQualityData() {
      const data = {
        dateType: 'month',
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateValue).format('YYYY-MM-DD')
      }
      let res = []
      if (this.zoneLevel === '34') {
        res = await this._getQueryTotalDataCZ({
          ...data,
          levelCode: 39,
          [this.key_level_code]: this.zoneCode
        })
      } else {
        res = await this._getOperatorQualityData({
          ...data,
          levelCode: this.zoneLevel
        })
      }
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'operatorQualityData',
        data: res?.obj || []
      })
    },
    setDataSource() {
      const data = this.operatorQualityData
      this.dataSource = data
    }
  },
  mounted() {
    this.init()
  }
}
</script>
<style lang='less' scoped>
</style>
