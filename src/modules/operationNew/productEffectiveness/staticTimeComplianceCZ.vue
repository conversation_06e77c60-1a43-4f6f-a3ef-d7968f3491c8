<!--
 * @Author: gilshi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description:时效静态环节符合率
-->
<template>
  <div>
    <CardList title="时效静态环节符合情况">
      <Tabs slot="nav" :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" />
      <div class="pd_lr20">
        <BtnTabs class="mt32" :options="dataTypeList" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3"></BtnTabs>
        <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
    </CardList>
    <TabsDrawer :show="visible" :handleClose="handleClose" :title="drawerTitle" :tabs="tabOptions"
      :request="getPlanTimeResultDataTable"></TabsDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import request from './commonMixins/request'
import { overAllProfit, planTimeReachTab } from './commonMixins/config'
import { drawScrollChart } from 'common/charts/chartOption'
import TabsDrawer from './tabsDrawer'
import { mapMutations } from 'vuex'
const typeData = [
  // {
  //   label: '盟商等待时长',
  //   key1: 'conformToRate',
  //   key2: 'conformToComRate',
  //   formatType: 'int',
  //   formatValue: [1]
  // },
  {
    label: '卸车同票同位率',
    serise: [
      {
        label: '卸车同票同位率',
        dataIndex: 'conformToRate',
        per: [1]
      }
    ]
    // key1: 'conformToRate',
    // // key2: 'conformToComRate',
    // formatType: 'per',
    // formatValue: [1]
  },
  {
    label: '清仓准确率',
    serise: [
      {
        label: '清仓准确率',
        dataIndex: 'conformToRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'conformToComRate',
        per: [1]
      }
    ]
    // key1: 'conformToRate',
    // key2: 'conformToComRate',
    // formatType: 'per',
    // formatValue: [1]
  },
  {
    label: '清仓覆盖率',
    serise: [
      {
        label: '清仓覆盖率',
        dataIndex: 'conformToRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'conformToComRate',
        per: [1]
      }
    ]
    // key1: 'conformToRate',
    // key2: 'conformToComRate',
    // formatType: 'per',
    // formatValue: [1]
  },
  // {
  //   label: '叉车错分率',
  //   key1: 'conformToRate',
  //   key2: 'conformToComRate'
  // },
  {
    label: '万票货损率',
    serise: [
      {
        label: '万票货损率',
        dataIndex: 'conformToRate',
        int: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'conformToComRate',
        int: [1]
      }
    ]
    // key1: 'conformToRate',
    // key2: 'conformToComRate',
    // formatType: 'int',
    // formatValue: [1]
  },
  {
    label: '百万件遗失率',
    serise: [
      {
        label: '百万件遗失率',
        dataIndex: 'conformToRate',
        int: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'conformToComRate',
        int: [1]
      }
    ]
    // key1: 'conformToRate',
    // key2: 'conformToComRate',
    // formatType: 'int',
    // formatValue: [1]
  },
  {
    label: '子母件分离率',
    serise: [
      {
        label: '子母件分离率',
        dataIndex: 'conformToRate',
        per: [1]
      },
      {
        label: '目标完成率',
        dataIndex: 'conformToComRate',
        per: [1]
      }
    ]
    // key1: 'conformToRate',
    // key2: 'conformToComRate',
    // formatType: 'per',
    // formatValue: [1]
  }
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [mixins, request],
  components: { TabsDrawer },
  data() {
    return {
      dataTypeList: [...dataTypeList],
      tableWidth: '100%',
      tableData: {},
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日', '周', '月'],
      dateKeyList: ['day', 'week', 'month'],
      visible: false,
      drawerTitle: '时效静态环节符合情况',
      planTimeReachTab
    }
  },
  computed: {
    columns() {
      return _.defaultsDeep([], overAllProfit[this.btnIndex][this.dateIndex])
    },
    tabOptions() {
      return this.planTimeReachTab(this.level_next_name)
    },
    isShowInt() {
      return typeData[this.btnIndex].label === '万票货损率' || typeData[this.btnIndex].label === '百万件遗失率' || typeData[this.btnIndex].label === '盟商等待时长'
    },
    legendOption() {
      // 当前选中的tab项
      const currentSerise = typeData[this.btnIndex]?.serise
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: currentSerise.map((item, index) => {
          return {
            seriesIndex: index,
            ...item
          }
        }),
        // options: this.isShowInt ? [
        //   { label: typeData[this.btnIndex].label, dataIndex: typeData[this.btnIndex].key1, int: [1], seriesIndex: 0 },
        //   { label: '目标完成率', dataIndex: typeData[this.btnIndex].key2, int: [1], seriesIndex: 1 }
        // ] : [
        //   { label: typeData[this.btnIndex].label, dataIndex: typeData[this.btnIndex].key1, per: [1], seriesIndex: 0 },
        //   { label: '目标完成率', dataIndex: typeData[this.btnIndex].key2, per: [1], seriesIndex: 1 }
        // ],
        // options: [
        //   { label: currentTarget.label, dataIndex: currentTarget.key1, [currentTarget.formatType]: currentTarget.formatValue, seriesIndex: 0 },
        //   { label: '目标完成率', dataIndex: currentTarget.key2, [currentTarget.formatType]: currentTarget.formatValue, seriesIndex: 1 }
        // ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    conformToSituationDataCZ(val) {
      this.initData(val)
    },
    btnIndex(val) {
      this.initData([this.conformToSituationDataCZ, this.trendChangeMon][val])
    },
    tabIndex(val) {
      this.init()
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getConformToSituationCZ().then(res => {
        this.initData()
      })
    },
    async getConformToSituationCZ() {
      const data = {
        dateType: this.dateKeyList[this.tabIndex],
        levelCode: 39,
        [this.key_level_code]: this.zoneCode,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      // const data = {
      //   "statisticalDate": "2024-09-19",
      //   "dateType": this.dateKeyList[this.tabIndex],
      //   "levelCode": "39",
      //   "provinceAreaCode": "S551Y022",
      //   "areaCode": "S512Y077",
      //   "deptCode": "025WH"
      // }
      const { obj } = await this._getConformToSituationCZ(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'conformToSituationDataCZ',
        data: obj || []
      })
    },
    btnConfirm({ index }) {
      this.btnIndex = index
      this.initData()
    },
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
    },
    async getPlanTimeResultDataTable() {
      const data = {
        dateType: this.dateKeyList[this.tabIndex],
        levelCode: 39,
        [this.key_level_code]: this.zoneCode
      }
      // const dataType = this.dateKeyList[this.tabIndex]
      const { obj } = await this._getConformToSituationCZ(data)
      return obj
    },
    btnOpenDia() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    initData() {
      const tabNames = this.dataTypeList[this.btnIndex]
      const targetObj = this.conformToSituationDataCZ.find((item) => item?.indicatorName === tabNames)
      const targetList = targetObj?.indicatorData || []
      this.legendDataSource = []
      // const currentTarget = typeData[this.btnIndex] || {}
      const option = {
        tooltip: {
          formatter: params => {
            console.log("fewf--21", params)
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: targetList.map(item => {
              if (this.tabIndex === 0) {
                return this.$dateFormat(item.conformToDate, this.tabIndex)
              } else if (this.tabIndex === 1) {
                return item.conformToDate
              } else {
                return `${this.$moment(item.conformToDate).format('MM')}月`
              }
            })
          }
        ],
        yAxis: typeData[this.btnIndex].serise.map((item, index) => {
          const colorList = ['#4c83f9', '#ff8066']
          return {
            scale: false,
            axisLine: {
              show: true, // 显示y轴线
              lineStyle: {
                  color: colorList[index] // 设置第二个Y轴的颜色为蓝色
              }
            },
            splitLine: {
              lineStyle: {
                  type: 'dashed'
              }
            },
            axisLabel: {
              formatter: (value, ...reset) => {
                if (item?.per) {
                  return this.$perFormat(value)
                }
                return value
              }
            },
            min: (value) => {
              if (item?.per) {
                return value.min - value.min / 100
              }
              // const num = Math.floor((value.min - 0.01) / 10)
              return parseInt(value.min - value.min / 100)
            }
          }
        }),
        // yAxis: [
        //   {
        //     axisLabel: {
        //       // formatter: value => this.$perFormat(value)
        //       // formatter: value => {
        //       //   if (this.isShowInt) {
        //       //     return this.$numToInteger(value, 0)
        //       //   } else {
        //       //     return this.$perFormat(value)
        //       //   }
        //       // }
        //       formatter: value => {
        //         if (currentTarget.formatType === 'int') {
        //           return this.$numToInteger(value, 0)
        //         } else {
        //           return this.$perFormat(value)
        //         }
        //       }
        //     }
        //   }
        // ],
        series: [
          {
            type: 'line',
            yAxisIndex: 0,
            // data: [0.4, 0.1, 0.2, 0.5, 0.2, 0.4, 0.1, 0.3],
            data: targetList.map(item => {
              return {
                ...item,
                value: item.conformToRate || 0,
                conformToRate: item.conformToRate || 0
              }
            }),
            smooth: false
          },
          {
            type: 'line',
            yAxisIndex: 1,
            // data: [0.2, 0.3, 0.4, 0.5, 0.6, 0.2, 0.1, 1.2],
            data: targetList.map(item => {
              return {
                ...item,
                value: item.conformToComRate || 0,
                conformToComRate: item.conformToComRate || 0
              }
            }),
            smooth: false
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 85% !important;
}
.chart_btn_wrap {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  // position: absolute;
  // right: 0;
  // top: 0;
}
</style>
