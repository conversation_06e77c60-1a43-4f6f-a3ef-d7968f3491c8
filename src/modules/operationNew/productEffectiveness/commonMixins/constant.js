import { numToPercent, numToInFloat } from 'common/js/numFormat'
// 预警指标
export const warningIndicators = [
  {
    value: '30',
    label: '总部',
    items: [
      {
        name: '运单时长',
        key: 'ydsc',
        sortIndex: 'qcTransHCompletionRatio',
        options: {
          label: 'provinceAreaName',
          dataIndex: 'qcWbTransH',
          int: [0]
        }
      },
      {
        name: '规划时效达成率',
        key: 'ghdc',
        sortIndex: 'qcPlanReachCompletionRatio',
        options: {
          label: 'provinceAreaName',
          dataIndex: 'qcPlanReachRate',
          per: [0]
        }
      },
      {
        name: '交货及时率',
        key: 'jhjsl',
        sortIndex: 'pickupCompletionRatio',
        options: {
          label: 'provinceAreaName',
          dataIndex: 'pickupRate',
          per: [0]
        }
      },
      {
        name: '中转及时率',
        key: 'zzjsl',
        sortIndex: 'tfIntimeCompletionRatio',
        options: {
          label: 'provinceAreaName',
          dataIndex: 'tfIntimeRate',
          per: [0]
        }
      },
      {
        name: '运行合格率',
        key: 'yxhgl',
        sortIndex: 'transHgCompletionRatio',
        options: {
          label: 'provinceAreaName',
          dataIndex: 'transHgRate',
          per: [0]
        }
      },
      {
        name: '派送及时率',
        key: 'psjsh',
        sortIndex: 'transHgCompletionRatio',
        options: {
          label: 'provinceAreaName',
          dataIndex: 'deliveryRate',
          per: [0]
        }
      }
    ]
  },
  {
    value: '32',
    label: '省区',
    items: [
      {
        name: '交货及时率',
        key: 'jhjsl',
        sortIndex: 'pickupCompletionRatio',
        options: {
          label: 'areaName',
          dataIndex: 'pickupRate',
          per: [0]
        }
      },
      {
        name: '中转及时率',
        key: 'zzjsl',
        sortIndex: 'tfIntimeCompletionRatio',
        options: {
          label: 'areaName',
          dataIndex: 'tfIntimeRate',
          per: [0]
        }
      },
      {
        name: '运行合格率',
        key: 'yxhgl',
        sortIndex: 'transHgCompletionRatio',
        options: {
          label: 'areaName',
          dataIndex: 'transHgRate',
          per: [0]
        }
      },
      {
        name: '派送及时率',
        key: 'psjsh',
        sortIndex: 'deliveryRate',
        options: {
          label: 'areaName',
          dataIndex: 'deliveryRate',
          per: [0]
        }
      }
    ]
  }
]
// 预警指标--详情列表列
export const diaJiaohuoTableColumns = (levelKey, typeKey) => {
  const warmTypeObj = {
    jhjsl: {
      key: 'jhjsl',
      name: '交货及时率',
      dataIndex: 'pickupRate',
      completeIndex: 'pickupCompletionRatio',
      targetIndex: 'pickupTarget'
    },
    zzjsl: {
      key: 'zzjsl',
      name: '中转及时率',
      dataIndex: 'tfIntimeRate',
      completeIndex: 'tfIntimeCompletionRatio',
      targetIndex: 'tfIntimeTarget'
    },
    yxhgl: {
      key: 'yxhgl',
      name: '运行合格率',
      dataIndex: 'transHgRate',
      completeIndex: 'transHgCompletionRatio',
      targetIndex: 'transHgTarget'
    },
    psjsh: {
      key: 'psjsh',
      name: '派送及时率',
      dataIndex: 'deliveryRate',
      completeIndex: 'deliveryCompletionRatio',
      targetIndex: 'deliveryTarget'
    },
    bzhlcl: {
      key: 'bzhlcl',
      name: '必走货留仓率',
      dataIndex: '暂时没有',
      completeIndex: '暂时没有',
      targetIndex: '暂时没有'
    },
    ydsc: {
      key: 'ydsc',
      name: '运单时长',
      dataIndex: 'qcWbTransH',
      completeIndex: 'qcTransHCompletionRatio',
      targetIndex: 'qcTransHTarget'
    },
    ghdc: {
      key: 'ghdc',
      name: '规划时效达成率',
      dataIndex: 'qcPlanReachRate',
      completeIndex: 'qcPlanReachCompletionRatio',
      targetIndex: 'qcPlanReachTarget'
    }
  }
  const columns = [
    {
      label: '排名',
      fixed: 'left',
      render: (h, val) => {
        return <div class="default-rank">{val + 1}</div>
      }
    },
    {
      label: '组织',
      maxWidth: '2.3rem',
      fixed: 'left',
      dataIndex: levelKey
    },
    {
      label: '当月完成比',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: warmTypeObj[typeKey].completeIndex,
      render: (h, val) => numToPercent(val)
    },
    {
      label: '指标',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: warmTypeObj[typeKey].targetIndex,
      render: (h, val) => numToInFloat(val)
      // render: (h, val) => numToPercent(val)
    }
  ]
  return [...columns, {
    label: warmTypeObj[typeKey].name, // 取率值
    dataIndex: warmTypeObj[typeKey].dataIndex,
    align: 'right',
    render: typeKey === 'ydsc' ? (h, val) => numToInFloat(val) : (h, val) => numToPercent(val)
  }]
}

