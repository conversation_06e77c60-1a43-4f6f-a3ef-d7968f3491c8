/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-07-05 18:43:55
 * @Description:
 */
import mixins from './mixins'
export default {
  mixins: [mixins],
  data() {
    return {
      defaultParmas: {
        // "statisticalDate": this.$moment('2024-09-25').format('YYYY-MM-DD')
        // "dateType": "day",
        // "levelCode": this.zoneLevel,
        // [this.key_level_code]: this.zoneCode
      },
      testParms: {
        // "statisticalDate": this.$moment('2024-09-25').format('YYYY-MM-DD')
      }
    }
  },
  computed: {
    defaultData() {
      let levelMap = {}
      if (this.dateIndex) {
        levelMap = {
          level_code: this.zoneLevel
        }
      }
      return {
        [this.key_star]: this.incDate,
        [this.key_end]: this.dateValue,
        // zone_level: String(this.zoneLevel),
        zone_code: this.zoneCode === '001' ? 'SFC015' : this.zoneCode, // 收入页面各层级都是zone_code
        // zone_code: 'SFC015'
        ...levelMap
      }
    }
  },
  methods: {
    // 【品效】【预警指标】总部
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/qualityOverview/getEarlyWarning
    _getEarlyWarningData(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/qualityOverview/getEarlyWarning',
        data: {
          ...this.defaultParmas,
          ...parmas,
          ...this.testParms
        }
      })
    },
    // 【品效】【运营品效总览】总部
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/qualityOverview/getOperatorQuality
    _getOperatorQualityData(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/qualityOverview/getOperatorQuality',
        data: {
          ...this.defaultParmas,
          ...parmas,
          ...this.testParms
        }
      })
    },
    // 【品效】【规划时效达成情况】总部
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/qualityOverview/getPlanTimeResult
    _getPlanTimeResultData(data) {
      return this.sendJavaRequest({
        url: '/cockpit/qualityOverview/getPlanTimeResult',
        data: {
          ...this.defaultParmas,
          ...data
        }
      })
    },
    // 【品效】【时效动态环节达成情况】总部
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/qualityOverview/getTimeResultDyn
    _getTimeResultDynData(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/qualityOverview/getTimeResultDyn',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    },
    // 【品效】【时效静态环节符合情况】总部
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/qualityOverview/getTimeResult
    _getTimeResultData(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/qualityOverview/getTimeResult',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    },
    // 【品效】【中转效率】总部
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/qualityOverview/getTransferEffect
    _getTransferEffectData(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/qualityOverview/getTransferEffect',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    },
    // 【品效】【中转品效】总部
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/qualityOverview/getTransferQuality
    _getTransferQualityData(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/qualityOverview/getTransferQuality',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    },
    // 【品效】【运单时长查询】总部
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/qualityOverview/getWaybillDuration
    _getWaybillDurationData(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/qualityOverview/getWaybillDuration',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    },
    // 【品效】【时效静态环节符合情况】场站
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/operationaleff/provinceregion/conformToSituation
    _getConformToSituationCZ(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/operationaleff/provinceregion/conformToSituation',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    },
    // 【品效】【客诉率】场站
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/operationaleff/provinceregion/customerComplaint
    _getCustomerComplaintDataCZ(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/operationaleff/provinceregion/customerComplaint',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    },
    // 【品效】【场站品效】场站
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/operationaleff/provinceregion/queryTotal
    _getQueryTotalDataCZ(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/operationaleff/provinceregion/queryTotal',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    },
    // 【品效】【中转效率】场站
    // http://yapi.sit.sf-express.com/mock/4230/cockpit/operationaleff/provinceregion/transferEff
    _getTransferEffDataCZ(parmas = {}) {
      return this.sendJavaRequest({
        url: '/cockpit/operationaleff/provinceregion/transferEff',
        data: {
          ...this.defaultParmas,
          ...parmas
        }
      })
    }
  }
}
