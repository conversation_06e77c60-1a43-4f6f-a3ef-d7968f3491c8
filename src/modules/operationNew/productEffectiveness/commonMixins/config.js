import { numToInteger, numToPercent } from 'common/js/numFormat'
const DateType = ['日同比', '周环比', '月环比']

export const overAllProfitKd = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'kd_weight_d',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'kd_weight_d_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'kd_weight_m',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'kd_weight_m_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'kd_weight_m',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'kd_weight_m_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'kd_weight_y',
    //       int: [0, 1000]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成率',
    //       dataIndex: 'kd_weight_y_rate',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]
export const overAllProfitQs = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'od_weight_d',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'od_weight_d_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'od_weight_m',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'od_weight_m_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'od_weight_m',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'od_weight_m_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'od_weight_y',
    //       int: [0, 1000]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成率',
    //       dataIndex: 'od_weight_y_rate',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]
export const tableColumns = [
  [
    {
      label: '排名',
      dataIndex: '',
      align: 'center',
      fixed: 'left',
      width: '0.8rem',
      render: (h, value) => {
        return (
          <div class={'flex_center'}>
            <div class="normal-rank">{value <= 2 ? '' : value + 1}</div>
          </div>
        )
      }
    },
    { label: '省区', dataIndex: 'zone_name', width: '1.4rem', fixed: 'left' },

    {
      label: '当日值',
      dataIndex: 'day_value',
      render: (h, value) => numToInteger(value, 1, 10000)
    },
    {
      label: '日环比',
      dataIndex: 'day_value_hb',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '同比上周',
      dataIndex: 'week_value_tb',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '月累计(万)',
      dataIndex: 'day_value_cum',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '预算值',
      dataIndex: 'target_value',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '完成比',
      dataIndex: 'complete_rate',
      render: (h, value) => numToPercent(value)
    },
    {
      label: '差额',
      dataIndex: 'balance',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '负责人',
      dataIndex: 'person_liable'
    }
  ],
  [
    {
      label: '排名',
      dataIndex: '',
      align: 'center',
      fixed: 'left',
      width: '0.8rem',
      render: (h, value) => {
        return (
          <div class={'flex_center'}>
            <div class="normal-rank">{value <= 2 ? '' : value + 1}</div>
          </div>
        )
      }
    },
    { label: '省区', dataIndex: 'zone_name', width: '1.4rem', fixed: 'left' },
    {
      label: '当月值',
      dataIndex: 'inc_value',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '月环比',
      dataIndex: 'hb_rate',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '月完成比',
      dataIndex: 'm_target_com',
      render: (h, value) => numToPercent(value)
    },
    // {
    //   label: '年累计',
    //   dataIndex: 'acc_value',
    //   render: (h, value) => numToInteger(value, 0, 10000)
    // },
    // {
    //   label: '年完成比',
    //   dataIndex: 'y_target_com',
    //   render: (h, value) => numToPercent(value)
    // },
    {
      label: '负责人',
      dataIndex: 'person_liable'
    }
  ]
]
export const overAllProfit = [
  // 件均
  [
    [
      {
        parent: [
          {
            label: '当日值',
            dataIndex: 'od_quantity_weight_d',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '日环比',
            dataIndex: 'od_quantity_weight_d_hb',
            int: [3, 1, 3],
            indexType: 'up'
          }
        ]
      },
      {
        parent: [
          {
            label: '月累计',
            dataIndex: 'od_quantity_weight_m',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_quantity_weight_m_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      }
    ],
    [
      {
        parent: [
          {
            label: '当月值',
            dataIndex: 'od_quantity_weight_m',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_quantity_weight_m_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      }
      // {
      //   parent: [
      //     {
      //       label: '年累计',
      //       dataIndex: 'od_quantity_weight_y',
      //       int: [0, 1]
      //     }
      //   ],
      //   child: [
      //     {
      //       label: '年环比',
      //       dataIndex: 'od_quantity_weight_y_hb',
      //       int: [3, 1, 3],
      //       indexType: 'up'
      //     }
      //   ]
      // }
    ]
  ],
  [
    [
      {
        parent: [
          {
            label: '当日值',
            dataIndex: 'od_ticket_weight_d',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '日环比',
            dataIndex: 'od_ticket_weight_d_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      },
      {
        parent: [
          {
            label: '月累计',
            dataIndex: 'od_ticket_weight_m',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_ticket_weight_m_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      }
    ],
    [
      {
        parent: [
          {
            label: '当月值',
            dataIndex: 'od_ticket_weight_m',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_ticket_weight_m_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      }
      // {
      //   parent: [
      //     {
      //       label: '年累计',
      //       dataIndex: 'od_ticket_weight_y',
      //       int: [0, 1]
      //     }
      //   ],
      //   child: [
      //     {
      //       label: '年环比',
      //       dataIndex: 'od_ticket_weight_y_hb',
      //       int: [3, 1, 3],

      //       indexType: 'up'
      //     }
      //   ]
      // }
    ]
  ],
  [
    [
      {
        parent: [
          {
            label: '当日值',
            dataIndex: 'od_ticket_quantity_d',
            int: [1, 1, 1]
          }
        ],
        child: [
          {
            label: '日环比',
            dataIndex: 'od_ticket_quantity_d_hb',
            int: [3, 1, 3],
            indexType: 'up'
          }
        ]
      },
      {
        parent: [
          {
            label: '月累计',
            dataIndex: 'od_ticket_quantity_m',
            int: [1, 1, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_ticket_quantity_m_hb',
            int: [3, 1, 3],
            indexType: 'up'
          }
        ]
      }
    ],
    [
      {
        parent: [
          {
            label: '当月值',
            dataIndex: 'od_ticket_quantity_m',
            int: [1, 1, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_ticket_quantity_m_hb',
            int: [3, 1, 3],
            indexType: 'up'
          }
        ]
      }
      // {
      //   parent: [
      //     {
      //       label: '年累计',
      //       dataIndex: 'od_ticket_quantity_y',
      //       int: [1, 1, 1]
      //     }
      //   ],
      //   child: [
      //     {
      //       label: '年环比',
      //       dataIndex: 'od_ticket_quantity_y_hb',
      //       int: [3, 1, 3],

      //       indexType: 'up'
      //     }
      //   ]
      // }
    ]
  ]
]
export const optionList = [
  [
    { label: '规划时效达成', int: [0, 1000] },
    { label: '目标完成率', int: [0] }
  ],
  [
    { label: '票数', int: [0, 1] },
    { label: '件均重量(kg/件)', int: [0] }
  ],
  [
    { label: '件数', int: [0, 1] },
    { label: '单联件数(件/票)', int: [1, 1, 1] }
  ]
]
// 运单时长
export const diaYundanTableColumns = (levelKey, dateTypeIndex) => {
  const columns = [
    {
      label: '排名',
      fixed: 'left',
      render: (h, val) => {
        return <div class="default-rank">{val + 1}</div>
      }
    },
    {
      label: '组织',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: levelKey
    },
    {
      label: '运单时长',
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'qcWbTransH'
    },
    {
      label: DateType[dateTypeIndex],
      maxWidth: '2.2rem',
      fixed: 'left',
      dataIndex: 'qcTransHHc',
      render: (h, val) => numToPercent(val)
    }
  ]
  return columns
}
// 规划时效达成
export const planTimeReachTab = (levelKey, dateTypeIndex) => [
  {
    value: '1',
    label: '规划时效总体达成',
    sortKey: 'qcPlanReachRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'qcPlanReachRate',
        label: '规划时效总体达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qcPlanReachCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qcPlanReachHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '2',
    label: '三日内占比动态',
    sortKey: 'qcRealWithin3dRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'qcRealWithin3dRate',
        label: '三日内占比动态',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qcWithin3dRateCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qcWithin3dHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '3',
    label: '次日达动态',
    sortKey: 'qcRealWithin1dRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'qcRealWithin1dRate',
        label: '次日达动态',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qcWithin1dRateCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qcWithin1dHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '4',
    label: '次日达经济圈动态',
    sortKey: 'qcEconomicRealWithin1dRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'qcEconomicRealWithin1dRate',
        label: '次日达经济圈动态',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qcEconomicWithin1dRateCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qcEconomicWithin1dHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  }
]
// 品效概览(场站)
export const overviewOptionCZ = [
  {
    parent: [
      {
        label: '百万票客诉率',
        dataIndex: 'ccRate',
        int: [1]
        // per: [0],
        // cus: true
      }
    ],
    child: [
      {
        label: '月环比',
        dataIndex: 'ccRateMonthOnMonth',
        per: [0]
      }
    ],
    footer: [
      {
        label: '月完成',
        dataIndex: 'ccMonthTargetRate',
        per: [0]
      }
    ]
  },
  {
    parent: [
      {
        label: '中转及时率',
        dataIndex: 'tfIntimeRate',
        per: [0],
        cus: true
      }
    ],
    child: [
      {
        label: '月环比',
        dataIndex: 'tfIntimeHc',
        per: [0]
      }
    ],
    footer: [
      {
        label: '月完成',
        dataIndex: 'tfIntimeCompletionRatio',
        per: [0]
      }
    ]
  },
  {
    parent: [
      {
        label: '万票货损率',
        dataIndex: 'dmRate',
        int: [1]
        // per: [0],
        // cus: true
      }
    ],
    child: [
      {
        label: '月环比',
        dataIndex: 'dmRateMonthOnMonth',
        per: [0]
      }
    ],
    footer: [
      {
        label: '月完成',
        dataIndex: 'dmMonthTargetRate',
        int: [0]
      }
    ]
  }
]

// 品效概览(总部)
export const overviewOption = [
  {
    parent: [
      {
        label: '运单时长（h）',
        dataIndex: 'qcWbTransH',
        // int: [0]
        cus: false
      }
    ],
    child: [
      {
        label: '月环比',
        dataIndex: 'qcTransHHc',
        per: [0]
      }
    ],
    footer: [
      {
        label: '月完成',
        dataIndex: 'qcTransHCompletionRatio',
        per: [0]
      }
    ]
  },
  {
    parent: [
      {
        label: '三日内占比',
        dataIndex: 'qcRealWithin3dRate',
        per: [0],
        cus: true
      }
    ],
    child: [
      {
        label: '月环比',
        dataIndex: 'qcWithin3dHc',
        per: [0]
      }
    ],
    footer: [
      // {
      //   label: '月目标达成率',
      //   dataIndex: 'qcWithin3dRateCompletionRatio',
      //   per: [0]
      // }
    ]
  },
  {
    parent: [
      {
        label: '规划时效达成率',
        dataIndex: 'qcPlanReachRate',
        per: [0],
        cus: true
      }
    ],
    child: [
      {
        label: '月环比',
        dataIndex: 'qcPlanReachHc',
        per: [0]
      }
    ],
    footer: [
      {
        label: '月完成',
        dataIndex: 'qcPlanReachCompletionRatio',
        per: [0]
      }
    ]
  }
]
// 时效静态符合
export const timeResultDataTab = (levelKey) => [
  {
    value: '1',
    label: '静态环节符合率',
    sortKey: 'staticAgreementRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'staticAgreementRate',
        label: '静态环节符合率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'staticAgreementRateCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      }
      // {
      //   dataIndex: 'dynHc',
      //   label: '周环比',
      //   render(_, i) {
      //     return numToPercent(i)
      //   }
      // }
    ]
  },
  {
    value: '2',
    label: '交货符合率',
    sortKey: 'jhAgreementRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'jhAgreementRate',
        label: '交货符合率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'dIntimeRateCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      }
      // {
      //   dataIndex: 'pickupHc',
      //   label: '周环比',
      //   render(_, i) {
      //     return numToPercent(i)
      //   }
      // }
    ]
  },
  {
    value: '3',
    label: '中转符合率',
    sortKey: 'zyAgreementRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'zyAgreementRate',
        label: '中转符合率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'tIntimeRateCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      }
      // {
      //   dataIndex: 'tfIntimeHc',
      //   label: '周环比',
      //   render(_, i) {
      //     return numToPercent(i)
      //   }
      // }
    ]
  },
  {
    value: '4',
    label: '提货符合率',
    sortKey: 'thAgreementRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'thAgreementRate',
        label: '提货符合率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'pIntimeRateCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      }
      // {
      //   dataIndex: 'transHgHc',
      //   label: '周环比',
      //   render(_, i) {
      //     return numToPercent(i)
      //   }
      // }
    ]
  },
  {
    value: '5',
    label: '配送符合率',
    sortKey: 'psAgreementRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'psAgreementRate',
        label: '配送符合率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'sIntimeRateCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      }
      // {
      //   dataIndex: 'deliveryIntimeRateHc',
      //   label: '周环比',
      //   render(_, i) {
      //     return numToPercent(i)
      //   }
      // }
    ]
  }
]
export const dynamicTimeReachTab = (levelKey, dateTypeIndex) => [
  {
    value: '1',
    label: '动态环节达成率',
    sortKey: 'dynRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'dynRate',
        label: '动态环节达成率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'dynCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'dynHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '2',
    label: '交货及时率',
    sortKey: 'pickupRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'pickupRate',
        label: '交货及时率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'pickupCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'pickupHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '3',
    label: '中转及时率',
    sortKey: 'tfIntimeRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'tfIntimeRate',
        label: '中转及时率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'tfIntimeCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'tfIntimeHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '4',
    label: '运行合格率',
    sortKey: 'transHgRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'transHgRate',
        label: '运行合格率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'transHgCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'transHgHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '5',
    label: '派送成功率',
    sortKey: 'deliveryRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'deliveryRate',
        label: '派送成功率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'deliveryCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'deliveryIntimeRateHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  }
]
// 中转效率
export const transferEffectTab = (levelKey, dateTypeIndex) => [
  {
    value: '1',
    label: '中转及时率',
    sortKey: 'tfIntimeRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'tfIntimeRate',
        label: '中转及时率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'tfIntimeCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'tfIntimeHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '2',
    label: '发车准点率',
    sortKey: 'carStartIntimeRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'carStartIntimeRate',
        label: '发车准点率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'carStartIntimeCompletionRatio',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'carStartIntimeHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '3',
    label: '移动及时率',
    sortKey: 'mtRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'mtRate',
        label: '移动及时率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'mtRateComRate',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'mtRateHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '4',
    label: '干线卸车及时率',
    sortKey: 'tuRateOfMv',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'tuRateOfMv',
        label: '干线卸车及时率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'tuRateComOfMv',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'tuRateHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '5',
    label: '干线卸车合格率',
    sortKey: 'qfRateOfMainline',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'qfRateOfMainline',
        label: '干线卸车合格率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qfRateOfComMainline',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'qfRateOfHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '6',
    label: '必走货留仓率',
    sortKey: 'mgRetRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'mgRetRate',
        label: '必走货留仓率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'mgRetComRate',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'mgRetHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  }
]
// 中转品效
export const transferQualityTab = (levelKey, dateTypeIndex) => [
  {
    value: '1',
    label: '卸车同位同票率',
    sortKey: 'jtsxUstStsSameRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'jtsxUstStsSameRate',
        label: '卸车同位同票率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'jtsxUstStsSameComRate',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'jtsxUstStsSameHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '2',
    label: '清仓准确率',
    sortKey: 'jtsxClearAccRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'jtsxClearAccRate',
        label: '清仓准确率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'jtsxClearAccComRate',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'jtsxClearAccHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  },
  {
    value: '3',
    label: '清仓覆盖率',
    sortKey: 'jtsxClearCovRate',
    keys: [
      {
        label: '排名',
        fixed: 'left',
        render: (h, val) => {
          return <div class="default-rank">{val + 1}</div>
        }
      },
      {
        dataIndex: levelKey,
        label: '组织'
      },
      {
        dataIndex: 'jtsxClearCovRate',
        label: '清仓覆盖率',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'jtsxClearCovComRate',
        label: '目标达成',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'jtsxClearCovHc',
        label: DateType[dateTypeIndex],
        render(_, i) {
          return numToPercent(i)
        }
      }
    ]
  }
]

