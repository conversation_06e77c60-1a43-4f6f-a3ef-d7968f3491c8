/*
 * @Author: shigl
 * @Date: 2022-07-19 17:25:34
 * @LastEditTime: 2022-07-19 17:35:52
 * @Description:
 */
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
import baseMixins from '../../baseMixins'

export default {
  mixins: [requestMixins, baseMixins],

  data() {
    return {
      dateTypes: [
        {
          label: '日',
          value: 'day'
        },
        {
          label: '周',
          value: 'week'
        },
        {
          label: '月',
          value: 'month'
        }
      ],
      mixinsTableMaxHeight: '8rem',
      date_value_key: ['statisticalDate', 'weekWid', 'monthWid']
    }
  },
  computed: {
    incDate() {
      if (!this.dateIndex) {
        return this.$moment(this.dateValue).subtract(15, 'days').format('YYYYMMDD')
      }
      return this.$moment(this.dateValue).subtract(5, 'month').format('YYYYMM')
    },

    key_unit() {
      return ['d', 'm'][this.dateIndex]
    },
    key_dateunit() {
      return ['statisticalDate', 'weekWid', 'monthWid'][this.dateIndex]
    },
    // key_dateunit() {
    //   return ['inc_day', 'inc_month'][this.dateIndex]
    // },
    key_value() {
      return ['this_day_value', 'this_month_value'][this.dateIndex]
    },
    key_rateCar() {
      return ['kd_weight_rate', 'kd_weight_m_rate'][this.dateIndex]
    },
    key_rateOd() {
      return ['od_weight_rate', 'od_weight_m_rate'][this.dateIndex]
    },
    key_star() {
      return ['star_day', 'star_mon'][this.dateIndex]
    },
    key_end() {
      return ['end_day', 'end_mon'][this.dateIndex]
    },
    level_name() {
      return {
        30: 'deptName',
        32: 'provinceAreaName',
        33: 'areaName',
        34: 'deptName'
      }[+this.zoneLevel]
    },
    level_index() {
      return {
        30: 0,
        32: 1,
        33: 2,
        34: 3
      }[+this.zoneLevel]
    },
    level_next_value() {
      return {
        30: 32,
        32: 33,
        33: 39,
        34: null
      }[+this.zoneLevel]
    },
    level_next_name() {
      return {
        30: 'provinceAreaName',
        32: 'areaName',
        33: 'deptName',
        34: null
      }[+this.zoneLevel]
    },
    level_next_name_zh() {
      return {
        30: '省区',
        32: '区域',
        33: '场站',
        34: null
      }[+this.zoneLevel]
    },
    key_level_code() {
      return {
        30: null,
        32: 'provinceAreaCode',
        33: 'areaCode',
        34: 'deptCode'
      }[+this.zoneLevel]
    },
    key_level_next_code() {
      return {
        30: 'provinceAreaCode',
        32: 'areaCode',
        33: 'deptCode',
        34: null
      }[+this.zoneLevel]
    },
    key_date_value() {
      return ['statisticalDate', 'weekWid', 'monthWid']
    },
    // 场站
    is_station_level() {
      return this.zoneLevel + '' === '34'
    },
    // 规划时效达成
    key_gh_target() {
      return ['qcPlanReachRate', 'qcRealWithin3dRate', 'qcRealWithin1dRate', 'qcEconomicRealWithin1dRate']
    },
    key_gh_complete() {
      return ['qcPlanReachCompletionRatio', 'qcWithin3dRateCompletionRatio', 'qcWithin1dRateCompletionRatio', 'qcEconomicWithin1dRateCompletionRatio']
    },
     // 动态时效达成
     key_dt_target() {
      return ['dynHc', 'pickupRate', 'tfIntimeRate', 'transHgCompletionRatio', 'deliveryRate']
    },
    key_dt_complete() {
      return ['dynCompletionRatio', 'pickupCompletionRatio', 'tfIntimeCompletionRatio', 'transHgCompletionRatio', 'deliveryCompletionRatio']
    },
     // 静态态时效达成
     key_jt_target() {
      return ['qcPlanReachRate', 'qcRealWithin3dRate', 'qcRealWithin1dRate', 'qcEconomicRealWithin1dRate']
    },
    key_jt_complete() {
      return ['qcPlanReachCompletionRatio', 'qcWithin3dRateCompletionRatio', 'qcWithin1dRateCompletionRatio', 'qcEconomicWithin1dRateCompletionRatio']
    },
    // 中转品效
    key_px_target() {
      return ['qcPlanReachRate', 'qcRealWithin3dRate', 'qcRealWithin1dRate', 'qcEconomicRealWithin1dRate']
    },
    key_px_complete() {
      return ['qcPlanReachCompletionRatio', 'qcWithin3dRateCompletionRatio', 'qcWithin1dRateCompletionRatio', 'qcEconomicWithin1dRateCompletionRatio']
    },
    // 中转效率
    key_xl_target() {
      return ['tfIntimeRate', 'carStartIntimeRate', 'qcRealWithin1dRate', 'qcEconomicRealWithin1dRate']
    },
    key_xl_complete() {
      return ['tfIntimeCompletionRatio', 'carStartIntimeCompletionRatio', 'qcWithin1dRateCompletionRatio', 'qcEconomicWithin1dRateCompletionRatio']
    },
    ...mapGetters('operationNew', {
      dateValue: 'dateValue'
    }),
    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('operationNew', {
      dateIndex: state => state.all.dateIndex,
      dateDay: state => state.all.dateDay,
      planTimeResultData: state => state.productEffectiveness.planTimeResultData, // 规划时效
      operatorQualityData: state => state.productEffectiveness.operatorQualityData, // 运行品效
      earlyWarningData: state => state.productEffectiveness.earlyWarningData, // 预警指标
      timeResultData: state => state.productEffectiveness.timeResultData, // 时效静态
      timeResultDynData: state => state.productEffectiveness.timeResultDynData, // 时效动态
      transferEffectData: state => state.productEffectiveness.transferEffectData, // 中转效率
      transferQualityData: state => state.productEffectiveness.transferQualityData, // 中转[品效]
      waybillDurationData: state => state.productEffectiveness.waybillDurationData, // 运单时效
      conformToSituationDataCZ: state => state.productEffectiveness.conformToSituationDataCZ, // 静态-场站
      customerComplaintDataCZ: state => state.productEffectiveness.customerComplaintDataCZ, // 客诉-场站
      queryTotalDataCZ: state => state.productEffectiveness.queryTotalDataCZ, // 中站品效-场站
      transferEffDataCZ: state => state.productEffectiveness.transferEffDataCZ // 中转效率-场站
    })
  },
  methods: {
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    }
  }
}
