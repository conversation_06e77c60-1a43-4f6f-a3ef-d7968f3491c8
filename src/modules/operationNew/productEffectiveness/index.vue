<!--
 * @Author: shigl
 * @Date: 2024-09-19 18:17:37
 * @LastEditTime: 2024-09-19 18:17:37
 * @Description: 运营--品效
-->
<template>
  <div>
    <div class="date_container">
      <KyDatePicker
        :type="dateType"
        :dateValue="dateValue"
        @onChange="dateChange"
        :holidayData="holidayData"
      ></KyDatePicker>
    </div>
    <PageContent>
      <components
        v-for="item in filterComponentList"
        :key="item"
        :is="item"
        @btnOpenDia="btnOpenDia"
      >
      </components>
      <div style="height: 0.5rem"></div>
      <KyDataDrawer
        :visible="visible"
        :title="diaTitle"
        height="80%"
        @close="diaClose"
      >
      </KyDataDrawer>
    </PageContent>
  </div>
</template>
<script>
import request from './request'
import mixins from './commonMixins/mixins'
import flowLayout from 'common/mixins/flowLayout.js'
import OverView from './overview'
// import WarmTarget from './warmTarget'
import WaybillDuration from './waybillDuration'
import PlanTimeReach from './planTimeReach'
import DynamicTimeReach from './dynamicTimeReach'
import StaticTimeCompliance from "./staticTimeCompliance"
import TransferEfficiency from "./transferEfficiency"
import TransferProEfficiency from "./transferProEfficiency"
import ComplaintRateCZ from "./complaintRate"
import TransferEfficiencyCZ from "./transferEfficiencyCZ"
import StaticTimeComplianceCZ from "./staticTimeComplianceCZ"
import { mapMutations, mapState } from 'vuex'
export default {
  mixins: [request, flowLayout, mixins],
  components: {
    OverView,
    // WarmTarget,
    WaybillDuration,
    PlanTimeReach,
    DynamicTimeReach,
    StaticTimeCompliance,
    TransferEfficiency,
    TransferProEfficiency,
    ComplaintRateCZ,
    TransferEfficiencyCZ,
    StaticTimeComplianceCZ
  },
  data() {
    return {
      componentList: ['OverView', 'WaybillDuration', 'PlanTimeReach', 'DynamicTimeReach', 'TransferEfficiency', 'TransferProEfficiency'],
      // componentList: ['OverView', 'WarmTarget', 'WaybillDuration', 'PlanTimeReach', 'DynamicTimeReach', 'StaticTimeCompliance', 'TransferEfficiency', 'TransferProEfficiency', 'ComplaintRate'],
      diaTitle: '标题',
      visible: false
    }
  },
  computed: {
    filterComponentList() {
      console.log('this.zoneCode', this.zoneCode, this.zoneLevel)
      if (this.zoneLevel === '33') {
        return ['OverView', 'WaybillDuration', 'PlanTimeReach', 'DynamicTimeReach', 'TransferEfficiency', 'TransferProEfficiency']
      } else if (this.zoneLevel === '34') {
        return ['OverView', 'ComplaintRateCZ', 'TransferEfficiencyCZ', 'staticTimeComplianceCZ']
      } else {
        return ['OverView', 'WaybillDuration', 'PlanTimeReach', 'DynamicTimeReach', 'StaticTimeCompliance', 'TransferEfficiency', 'TransferProEfficiency']
      }
    },
    dateType() {
      return 'day'
      // return ['day', 'week', 'month'][this.dateIndex]
    },
    ...mapState({
      holidayData: 'holidayData'
    })
  },
  watch: {
    zoneCode() {
      this.initOptions()
    }
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    dateChange(date) {
      this.setDate({
        type: 'all',
        key: 'dateDay',
        // key: ['dateDay', 'dateMon'][this.dateIndex],
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
      this.initOptions()
    },
    initOptions() {},
    diaClose() {
      this.visible = false
    },
    btnOpenDia(data) {
      this.title = data
      this.visible = true
    }
  },
  mounted() {
    this.setDateIndex({
      type: 'all',
      index: 0
    })
  }
}
</script>
<style lang='less' scoped>
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
