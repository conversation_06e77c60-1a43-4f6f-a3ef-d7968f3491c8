<!--
 * @Author: g<PERSON><PERSON>
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description:时效动态环节达成情况-总部/省区/区域
-->
<template>
  <div>
    <CardList :title="'时效动态环节达成情况'">
      <Tabs slot="nav" :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" />
      <div class="pd_lr20">
        <BtnTabs class="mt32" :options="dataTypeList" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3"></BtnTabs>
        <div class="chart_wrap_content">
          <div class="chart_btn_wrap" v-if="zoneLevel<33">
            <div class="right_btn_wrap" @click="btnOpenDia">
              展开{{ level_next_name_zh }} <i class="iconfont icon-dayuhao fs20"></i>
            </div>
          </div>
          <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>
      </div>
    </CardList>
    <TabsDrawer :show="visible" :handleClose="handleClose" :title="drawerTitle" :tabs="tabOptions"
      :request="getTimeResultDynDataTable"></TabsDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import request from './commonMixins/request'
import { dynamicTimeReachTab } from './commonMixins/config'
import { drawScrollChart } from 'common/charts/chartOption'
import TabsDrawer from './tabsDrawer'
import { mapMutations } from 'vuex'
const typeData = [
  {
    label: '动态环节达成率',
    serise: [
      {
        label: '动态环节达成率',
        dataIndex: 'dynRate',
        per: [1],
        min: 0.5,
        max: 0.95
      },
      {
        label: '目标完成率',
        dataIndex: 'dynCompletionRatio',
        per: [1],
        min: 0.5,
        max: 1.2
      }
    ]
    // key1: 'dynRate',
    // key2: 'dynCompletionRatio'
  },
  {
    label: '交货及时率',
    serise: [
      {
        label: '交货及时率',
        dataIndex: 'pickupRate',
        per: [1],
        min: 0.55,
        max: 0.95
      },
      {
        label: '目标完成率',
        dataIndex: 'pickupCompletionRatio',
        per: [1],
        min: 0.5,
        max: 1.5
      }
    ]
    // key1: 'pickupRate',
    // key2: 'pickupCompletionRatio'
  },
  {
    label: '中转及时率',
    serise: [
      {
        label: '中转及时率',
        dataIndex: 'tfIntimeRate',
        per: [1],
        min: 0.3,
        max: 1
      },
      {
        label: '目标完成率',
        dataIndex: 'tfIntimeCompletionRatio',
        per: [1],
        min: 0.3,
        max: 1.2
      }
    ]
    // key1: 'tfIntimeRate',
    // key2: 'tfIntimeCompletionRatio'
  },
  {
    label: '运行合格率',
    serise: [
      {
        label: '运行合格率',
        dataIndex: 'transHgRate',
        per: [1],
        min: 0.4,
        max: 0.98
      },
      {
        label: '目标完成率',
        dataIndex: 'transHgCompletionRatio',
        per: [1],
        min: 0.4,
        max: 1.2
      }
    ]
    // key1: 'transHgRate',
    // key2: 'transHgCompletionRatio'
  },
  {
    label: '派送成功率',
    serise: [
      {
        label: '派送成功率',
        dataIndex: 'deliveryRate',
        per: [1],
        min: 0.55,
        max: 0.95
      },
      {
        label: '目标完成率',
        dataIndex: 'deliveryCompletionRatio',
        per: [1],
        min: 0.5,
        max: 1.5
      }
    ]
    // key1: 'deliveryRate',
    // key2: 'deliveryCompletionRatio'
  }
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [mixins, request],
  components: { TabsDrawer },
  data() {
    return {
      // btnData: ['规划时效总体达成', '三日内占比动态', '次日达动态', '次日达经济圈动态'],
      dataTypeList: [...dataTypeList],
      tableWidth: '100%',
      tableData: {},
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日', '周', '月'],
      dateKeyList: ['day', 'week', 'month'],
      visible: false,
      drawerTitle: '动态环节达成情况',
      dynamicTimeReachTab
    }
  },
  computed: {
    tabOptions() {
      return this.dynamicTimeReachTab(this.level_next_name, this.tabIndex)
    },
    legendOption() {
      const currentSerise = typeData[this.btnIndex]?.serise
      return {
        type: 'line',
        colNum: 4,
        isGrid: true,
        options: currentSerise.map((item, index) => {
          return {
            seriesIndex: index,
            ...item
          }
        }),
        // options: [
        //   { label: typeData[this.btnIndex].label, dataIndex: typeData[this.btnIndex].key1, per: [1], seriesIndex: 0 },
        //   { label: '目标完成率', per: [1], dataIndex: typeData[this.btnIndex].key2, seriesIndex: 1 }
        // ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    timeResultDynData(val) {
      this.initData(val)
    },
    btnIndex(val) {
      this.initData()
    },
    tabIndex(val) {
      this.initData()
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getTimeResultDynData().then(res => {
        this.initData()
      })
    },
    async getTimeResultDynData() {
      const data = {
        levelCode: this.zoneLevel,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getTimeResultDynData(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'timeResultDynData',
        data: obj
      })
    },
    btnConfirm({ index }) {
      this.btnIndex = index
      this.initData()
    },
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
    },
    async getTimeResultDynDataTable() {
      const dataType = this.dateKeyList[this.tabIndex]
      const data = {
        dateType: dataType,
        levelCode: this.level_next_value,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getTimeResultDynData(data)
      return obj[dataType]
    },
    btnOpenDia() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    initData() {
      const dateType = this.dateKeyList[this.tabIndex]
      const result = this.timeResultDynData[dateType]
      const dateValue = this.date_value_key[this.tabIndex]
      const dataSerise = typeData[this.btnIndex].serise
      const xData = []
      const sData = [[], []]
      if (result && result.length > 0) {
        const fliterList = JSON.parse(JSON.stringify(result))
        // this.$objectSortUp(fliterList, this.key_dateunit)
        fliterList.reverse()
        fliterList.forEach(item => {
          xData.push(item[dateValue])
          dataSerise.forEach((items, i) => {
            sData[i].push({
              ...item,
              value: item[items.dataIndex] || 0
            })
          })
          // sData[0].push({
          //   ...item,
          //   value: item[typeData[this.btnIndex].key1] || 0
          // })
          // sData[1].push({
          //   ...item,
          //   value: item[typeData[this.btnIndex].key2] || 0
          // })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              interval: 0,
              formatter: value => {
                if (this.tabIndex === 0) {
                  return this.$dateFormat(value, this.tabIndex)
                } else if (this.tabIndex === 1) {
                  return `第${value.slice(-2)}周`
                } else {
                  return `${this.$moment(value).format('MM')}月`
                }
              }
            }
          }
        ],
        yAxis: typeData[this.btnIndex].serise.map((item, index) => {
          const colorList = ['#4c83f9', '#ff8066']
          return {
            scale: false,
            axisLine: {
              show: true, // 显示y轴线
              lineStyle: {
                  color: colorList[index] // 设置第二个Y轴的颜色为蓝色
              }
            },
            splitLine: {
              lineStyle: {
                  type: 'dashed'
              }
            },
            axisLabel: {
              formatter: (value, ...reset) => {
                return this.$perFormat(value)
              }
            },
            min: (value) => {
              // const num = Math.floor((value.min - 0.01) / 10)
              return value.min - value.min / 100
            }
          }
        }),
        // yAxis: [
        //   {
        //     axisLabel: {
        //       formatter: (value, ...reset) => {
        //         return this.$perFormat(value)
        //       }
        //     }
        //   }
        // ],
        series: typeData[this.btnIndex]?.serise.map((item, i) => {
          return {
            type: 'line',
            data: sData[i],
            smooth: false,
            yAxisIndex: i
          }
        })
        // series: [
        //   {
        //     type: 'line',
        //     data: sData[0],
        //     smooth: false
        //   },
        //   {
        //     type: 'line',
        //     data: sData[1],
        //     smooth: false
        //   }
        // ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 80% !important;
}
.chart_wrap_content {
  position: relative;
}
.chart_btn_wrap {
  position: absolute;
  right: 0.08rem;
  top: -0.10rem;
}
.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  color: #333333;
}
</style>
