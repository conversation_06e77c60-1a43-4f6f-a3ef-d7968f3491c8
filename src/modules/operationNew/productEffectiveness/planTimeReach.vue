<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description:规划时效达成情况
-->
<template>
  <div>
    <CardList title="规划时效达成情况">
      <Tabs slot="nav" :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" />
      <div class="pd_lr20">
        <BtnTabs class="mt32 ft28" :options="dataTypeList" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3"></BtnTabs>
        <div class="chart_wrap_content">
          <div class="chart_btn_wrap" v-if="zoneLevel<33">
            <div class="right_btn_wrap" @click="btnOpenDia">
              展开{{ level_next_name_zh }} <i class="iconfont icon-dayuhao fs20"></i>
            </div>
          </div>
          <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>
      </div>
    </CardList>
    <TabsDrawer :show="visible" :handleClose="handleClose" :title="drawerTitle" :tabs="tabOptions"
      :request="getPlanTimeResultDataTable"></TabsDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import request from './commonMixins/request'
import { overAllProfit, planTimeReachTab } from './commonMixins/config'
import { drawScrollChart } from 'common/charts/chartOption'
import TabsDrawer from './tabsDrawer'
import { mapMutations } from 'vuex'
const typeData = [
  {
    label: '规划时效总体达成',
    serise: [
      {
        label: '规划时效总体达成',
        dataIndex: 'qcPlanReachRate',
        per: [1],
        min: 0.3,
        max: 1
      },
      {
        label: '目标完成率',
        dataIndex: 'qcPlanReachCompletionRatio',
        per: [1],
        min: 0.6,
        max: 1.3
      }
    ]
  },
  {
    label: '三日内占比动态',
    serise: [
      {
        label: '三日内占比动态',
        dataIndex: 'qcRealWithin3dRate',
        per: [1],
        min: 0.3,
        max: 0.9
      }
    ]
  },
  {
    label: '次日达动态',
    serise: [
      {
        label: '次日达动态',
        dataIndex: 'qcRealWithin1dRate',
        per: [1],
        min: 0,
        max: 0.2
      }
    ]
  },
  {
    label: '次日达经济圈动态',
    serise: [
      {
        label: '次日达经济圈动态',
        dataIndex: 'qcEconomicRealWithin1dRate',
        per: [1],
        min: 0,
        max: 0.7
      }
    ]
  }
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [mixins, request],
  components: { TabsDrawer },
  data() {
    return {
      // btnData: ['规划时效总体达成', '三日内占比动态', '次日达动态', '次日达经济圈动态'],
      dataTypeList: [...dataTypeList],
      tableWidth: '100%',
      tableData: {},
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日', '周', '月'],
      dateKeyList: ['day', 'week', 'month'],
      visible: false,
      drawerTitle: '规划时效达成情况',
      planTimeReachTab // https://blog.csdn.net/m0_52252555/article/details/140273000
    }
  },
  computed: {
    columns() {
      return _.defaultsDeep([], overAllProfit[this.btnIndex][this.dateIndex])
    },
    tabOptions() {
      return this.planTimeReachTab(this.level_next_name, this.tabIndex)
    },
    legendOption() {
      const currentSerise = typeData[this.btnIndex]?.serise
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: currentSerise.map((item, index) => {
          return {
            seriesIndex: index,
            ...item
          }
        }),
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    planTimeResultData(val) {
      this.initData(val)
    },
    btnIndex(val) {
      this.initData([this.planTimeResultData, this.trendChangeMon][val])
    },
    tabIndex(val) {
      this.initData([this.planTimeResultData, this.trendChangeMon][val])
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getPlanTimeResultData().then(res => {
        this.initData()
      })
    },
    async getPlanTimeResultData() {
      const data = {
        levelCode: this.zoneLevel,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getPlanTimeResultData(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'planTimeResultData',
        data: obj
      })
    },
    btnConfirm({ index }) {
      this.btnIndex = index
      this.initData()
    },
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
    },
    async getPlanTimeResultDataTable() {
      const data = {
        dateType: this.dateKeyList[this.tabIndex],
        levelCode: this.level_next_value,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const dataType = this.dateKeyList[this.tabIndex]
      const { obj } = await this._getPlanTimeResultData(data)
      return obj[dataType]
    },
    btnOpenDia() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    initData() {
      const dataType = this.dateKeyList[this.tabIndex]
      const result = this.planTimeResultData[dataType]
      const dateValue = this.date_value_key[this.tabIndex]
      const dataSerise = typeData[this.btnIndex].serise
      const xData = []
      const sData = [[], []]
      if (result && result.length > 0) {
        const fliterList = JSON.parse(JSON.stringify(result))
        // this.$objectSortUp(fliterList, this.key_dateunit)
        fliterList.reverse()
        fliterList.forEach(item => {
          xData.push(item[dateValue])
          dataSerise.forEach((items, i) => {
            sData[i].push({
              ...item,
              value: item[items.dataIndex] || 0
            })
          })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              interval: 0, // 每个标签都显示
              formatter: value => {
                if (this.tabIndex === 0) {
                  return this.$dateFormat(value, this.tabIndex)
                } else if (this.tabIndex === 1) {
                  return `第${value.slice(-2)}周`
                } else {
                  return `${this.$moment(value).format('MM')}月`
                }
              }
            }
          }
        ],
        yAxis: typeData[this.btnIndex].serise.map((item, index) => {
          const colorList = ['#4c83f9', '#ff8066']
          return {
            scale: false,
            axisLine: {
              show: true, // 显示y轴线
              lineStyle: {
                  color: colorList[index] // 设置第二个Y轴的颜色为蓝色
              }
            },
            splitLine: {
              lineStyle: {
                  type: 'dashed'
              }
            },
            axisLabel: {
              formatter: (value, ...reset) => {
                return this.$perFormat(value)
              }
            },
            min: (value) => {
              // const num = Math.floor((value.min - 0.01) / 10)
              return value.min - value.min / 100
            }
          }
        }),
        series: typeData[this.btnIndex]?.serise.map((item, i) => {
          return {
            type: 'line',
            data: sData[i],
            smooth: false,
            yAxisIndex: i
          }
        })
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 85% !important;
}
.chart_wrap_content {
  position: relative;
}
.chart_btn_wrap {
  position: absolute;
  right: 0.08rem;
  top: -0.10rem;
}
.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  color: #333333;
}
</style>
