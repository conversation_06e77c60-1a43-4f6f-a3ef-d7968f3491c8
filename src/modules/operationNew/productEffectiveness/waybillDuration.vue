<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description: 运单时长
-->
<template>
  <div>
    <CardList title="运单时长">
      <Tabs slot="nav" :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" />
      <div class="pd_lr20">
        <!-- <BtnTabs class="mt32" :options="dataTypeList" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3"></BtnTabs> -->
        <div class="chart_wrap_content">
          <div class="chart_btn_wrap" v-if="zoneLevel<33">
            <div class="right_btn_wrap" @click="btnOpenDia">
              展开{{ level_next_name_zh }} <i class="iconfont icon-dayuhao fs20"></i>
            </div>
          </div>
          <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>
      </div>
    </CardList>
    <KyDataDrawer
      :visible="visible"
      :title="diaTitle"
      height="80%"
      @close="diaClose"
      @drawerHeight="mixinsDrawerHeight"
    >
      <div class="mt32"></div>
      <NormalTable :columns="diaTableColumns" :dataSource="diaTableDataSource" :maxHeight="mixinsTableMaxHeight" />
    </KyDataDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import request from './commonMixins/request'
import { drawScrollChart } from 'common/charts/chartOption'
import { overAllProfit, diaYundanTableColumns } from './commonMixins/config'
import { mapMutations } from 'vuex'
export default {
  mixins: [mixins, request],
  data() {
    return {
      // btnData: ['规划时效总体达成', '三日内占比动态', '次日达动态', '次日达经济圈动态'],
      tableWidth: '100%',
      tableData: {},
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日', '周', '月'],
      dateKeyList: ['day', 'week', 'month'],
      dateTypeKeyList: ['statisticalDate', 'weekWid', 'monthWid'],
      visible: false,
      diaTitle: '运单时长',
      diaTableColumns: [],
      diaTableDataSource: []
    }
  },
  computed: {
    columns() {
      return _.defaultsDeep([], overAllProfit[this.btnIndex][this.dateIndex])
    },
    legendOption() {
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: [
          { label: '运单时长', int: [1], seriesIndex: 0 }
          // { label: '目标完成率', per: [1], seriesIndex: 1 }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    waybillDurationData(val) {
      this.initData(val)
    },
    tabIndex(val) {
      this.initData([this.waybillDurationData, this.trendChangeMon][val])
    },
    zoneCode(val) {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      this.getWaybillDurationData().then((_) => {
        this.initData()
      })
    },
    async getWaybillDurationData() {
      const data = {
        // dateType: this.dateKeyList[this.tabIndex],
        levelCode: this.zoneLevel,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      const { obj } = await this._getWaybillDurationData(data)
      this.setPageData({
        type: 'productEffectiveness',
        dataType: 'waybillDurationData',
        data: obj
      })
    },
    tabSelect(index) {
      this.tabIndex = index
      this.initData()
    },

    async btnOpenDia() {
      const dataParm = this.key_date_value[this.tabIndex]
      console.log("this.key_date_value[this.tabIndex]", dataParm)
      const data = {
        dateType: this.dateKeyList[this.tabIndex],
        levelCode: this.level_next_value,
        [this.key_level_code]: this.zoneCode,
        statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      }
      this.visible = true
      this.diaTableColumns = [...diaYundanTableColumns(this.level_next_name, this.tabIndex)]
      const { obj } = await this._getWaybillDurationData(data)
      const dataType = this.dateKeyList[this.tabIndex]
      const newList = [...obj[dataType]]
      this.$objectSortDown(newList, 'qcWbTransH')
      this.diaTableDataSource = newList
    },
    diaClose() {
      this.visible = false
    },
    initData() {
      const dataType = this.dateKeyList[this.tabIndex]
      const result = this.waybillDurationData[dataType]
      const dateValue = this.date_value_key[this.tabIndex]
      const xData = []
      const sData = [[], []]
      if (result && result.length > 0) {
        const fliterList = JSON.parse(JSON.stringify(result))
        // this.$objectSortUp(fliterList, this.dateTypeKeyList[this.tabIndex])
        fliterList.reverse()
        fliterList.forEach(item => {
          xData.push(item[dateValue])
          sData[0].push({
            ...item,
            value: item['qcWbTransH'] || 0
          })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => {
                if (this.tabIndex === 0) {
                  return this.$dateFormat(value, this.tabIndex)
                } else if (this.tabIndex === 1) {
                  return `第${value.slice(-2)}周`
                } else {
                  return `${this.$moment(value).format('MM')}月`
                }
              }
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 1)
            },
            min: (value) => {
              const num = Math.floor((value.min - 2) / 10)
              return parseInt(num * 10)
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: sData[0]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 80% !important;
}
.chart_wrap_content {
  position: relative;
}
.chart_btn_wrap {
  position: absolute;
  right: 0.08rem;
  top: -0.10rem;
}
.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  color: #333333;
}
</style>
