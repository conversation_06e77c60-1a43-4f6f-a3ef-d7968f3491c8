/*
 * @Author: shigl
 * @Date: 2022-07-19 17:25:34
 * @LastEditTime: 2022-07-20 18:46:28
 * @Description:
 */
import moment from 'moment'
export default {
  name: 'operationNew',
  namespaced: true,
  state: {
    all: {
      dateIndex: 0,
      dateDay: '',
      dateWeek: '',
      dateMon: ''
    },
    // 品效（new）
    productEffectiveness: {
      planTimeResultData: {}, // 规划
      operatorQualityData: {}, // 运行品效
      earlyWarningData: {}, // 预警指标
      timeResultData: [], // 时效静态
      timeResultDynData: {}, // 时效动态
      transferEffectData: {}, // 中转效率
      transferQualityData: {}, // 中转[品效]
      waybillDurationData: {}, // 运单时长
      conformToSituationData: [], // 运单时长--场站
      conformToSituationDataCZ: [], // 静态-场站
      customerComplaintDataCZ: [], // 客诉-场站
      queryTotalDataCZ: [], // 中站品效-场站
      transferEffDataCZ: [] // 中转效率-场站
    },
    // 成本模块
    cost: {
      planTimeReachData: {}, // 环节成本单价趋势
      // 其他成本相关数据...
    }
  },
  getters: {
    dateValue(state, getters) {
      const dateIndex = state.all.dateIndex
      const dateDay = state.all.dateDay
      const dateMon = state.all.dateMon
      if (!dateIndex) {
        return dateDay
      }
      return dateMon
    }
  },
  mutations: {
    setDate(state, { type, key, date }) {
      state[type][key] = date
    },
    setDateIndex(state, { type, index }) {
      state[type].dateIndex = index
    },
    // 数据赋值,type:页面,dataType:变量
    setPageData(state, { type, dataType, data }) {
      state[type][dataType] = data
    }
  },
  actions: {
    initDate({ state }) {
      state.all.dateDay = moment().add(-1, 'days').format('YYYYMMDD')
      const tody = moment().format('DD')
      state.all.dateMon =
        tody < 15 ? moment().add(-2, 'month').format('YYYYMM') : moment().add(-1, 'month').format('YYYYMM')
    }
  }
}
