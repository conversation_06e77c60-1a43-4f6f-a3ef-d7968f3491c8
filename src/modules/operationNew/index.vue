<!--
 * @Author: shigl
 * @Date: 2022-10-31 18:53:53
 * @LastEditTime: 2023-03-20 16:12:14
 * @Description: 质控
-->
<template>
  <div class="page_bgc">
    <TitleTabs
      class="bgc_white"
      :tabData="pageData"
      :tabValue="pageIndex"
      :sp="true"
      color="black"
      @changeTab="changeTab"
    ></TitleTabs>

    <components :is="componentList[pageIndex]"></components>
  </div>
</template>
<script>
import baseMixins from './baseMixins'
import { mapActions } from 'vuex'
import ProductEffectiveness from './productEffectiveness'
import Cost from './cost'
export default {
  mixins: [baseMixins],
  components: { ProductEffectiveness, Cost },
  data() {
    return {
      pageData: ['品效', '成本'],
      pageIndex: 0,
      componentList: ['ProductEffectiveness', 'Cost']
    }
  },
  computed: {
    dateType() {
      return ['day', 'month', 'year'][this.dateIndex]
    }
  },
  methods: {
    ...mapActions('operationNew', ['initDate']),
    changeTab({ index }) {
      this.pageIndex = index
      this.$sensors.pageview('运营-' + this.pageData[this.pageIndex])
    }
  },
  mounted() {
    this.$sensors.pageview('运营-' + this.pageData[this.pageIndex])
  },
  created() {
    this.initDate()
  }
}
</script>
<style lang="less" scoped></style>
