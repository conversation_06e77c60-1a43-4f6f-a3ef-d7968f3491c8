<!--
 * @Author: shigl
 * @Date: 2022-11-19 19:16:55
 * @LastEditTime: 2022-11-19 19:37:46
 * @Description: 查看数据按钮
-->
<template>
  <div class="btn-detail-container" @click="btnChange">
    <span class="label">{{ value }}</span>
    <i class="iconfont icon-kyd-more font-custom"></i>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: String,
      default: '展开更多'
    }
  },
  methods: {
    btnChange() {
      this.$emit('btnChange')
    }
  }
}
</script>
<style lang='less' scoped>
.btn-detail-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 0.65rem;
  background: #f8f9fc;
  .label {
    font-family: PingFangSC-Medium;
    font-size: 0.24rem;
    color: #666666;
    text-align: right;
    font-weight: 700;
  }
  .font-custom {
    color: #666;
    font-size: 0.24rem;
    margin-left: 0.08rem;
  }
}
</style>
