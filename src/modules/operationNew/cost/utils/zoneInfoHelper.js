/*
 * @Author: AI Assistant
 * @Date: 2025-01-22
 * @Description: 区域信息获取工具类
 */

/**
 * 获取选中区域的上级信息
 * @param {Object} zoneParams - 当前选中的区域参数
 * @param {String} zoneLevel - 当前区域级别
 * @returns {Object} 包含上级信息的对象
 */
export function getParentZoneInfo(zoneParams, zoneLevel) {
  const level = +zoneLevel
  
  const result = {
    currentLevel: level,
    currentCode: zoneParams.zoneCode,
    currentName: zoneParams.zoneName,
    provinceAreaCode: null,
    provinceAreaName: null,
    areaCode: null,
    areaName: null,
    levelName: getLevelName(level),
    hasParentProvince: false,
    hasParentArea: false
  }

  switch (level) {
    case 30: // 全网级别
      result.hasParentProvince = false
      result.hasParentArea = false
      break
      
    case 32: // 省区级别 - 没有上级省区和区域
      result.hasParentProvince = false
      result.hasParentArea = false
      break
      
    case 33: // 区域级别 - 上级是省区
      result.provinceAreaCode = zoneParams.province_area_code
      result.provinceAreaName = zoneParams.province_area_name
      result.hasParentProvince = !!result.provinceAreaCode
      result.hasParentArea = false
      break
      
    case 34: // 场站级别 - 上级是区域，上上级是省区
      result.provinceAreaCode = zoneParams.province_area_code
      result.provinceAreaName = zoneParams.province_area_name
      result.areaCode = zoneParams.area_code
      result.areaName = zoneParams.area_name
      result.hasParentProvince = !!result.provinceAreaCode
      result.hasParentArea = !!result.areaCode
      break
  }

  return result
}

/**
 * 获取级别名称
 * @param {Number} level - 级别代码
 * @returns {String} 级别名称
 */
export function getLevelName(level) {
  const levelMap = {
    30: '全网',
    32: '省区',
    33: '区域', 
    34: '场站'
  }
  return levelMap[level] || '未知'
}

/**
 * 构建API请求参数，根据当前级别自动添加上级code参数
 * @param {Object} zoneParams - 区域参数
 * @param {String} zoneLevel - 当前级别
 * @param {Object} baseParams - 基础参数
 * @returns {Object} 完整的请求参数
 */
export function buildApiParams(zoneParams, zoneLevel, baseParams = {}) {
  const level = +zoneLevel
  const params = { ...baseParams }
  
  switch (level) {
    case 32: // 省区级别
      params.provinceAreaCode = zoneParams.zoneCode
      break
      
    case 33: // 区域级别
      params.provinceAreaCode = zoneParams.province_area_code
      params.areaCode = zoneParams.zoneCode
      break
      
    case 34: // 场站级别
      params.provinceAreaCode = zoneParams.province_area_code
      params.areaCode = zoneParams.area_code
      params.deptCode = zoneParams.zoneCode
      break
  }
  
  return params
}

/**
 * 获取面包屑导航数据
 * @param {Object} zoneParams - 区域参数
 * @param {String} zoneLevel - 当前级别
 * @returns {Array} 面包屑数组
 */
export function getBreadcrumbData(zoneParams, zoneLevel) {
  const level = +zoneLevel
  const breadcrumbs = []
  
  // 总是包含全网
  breadcrumbs.push({
    level: 30,
    code: '001',
    name: '顺心捷达',
    levelName: '全网'
  })
  
  switch (level) {
    case 34: // 场站级别 - 显示：全网 > 省区 > 区域 > 场站
      if (zoneParams.province_area_code) {
        breadcrumbs.push({
          level: 32,
          code: zoneParams.province_area_code,
          name: zoneParams.province_area_name,
          levelName: '省区'
        })
      }
      if (zoneParams.area_code) {
        breadcrumbs.push({
          level: 33,
          code: zoneParams.area_code,
          name: zoneParams.area_name,
          levelName: '区域'
        })
      }
      breadcrumbs.push({
        level: 34,
        code: zoneParams.zoneCode,
        name: zoneParams.zoneName,
        levelName: '场站'
      })
      break
      
    case 33: // 区域级别 - 显示：全网 > 省区 > 区域
      if (zoneParams.province_area_code) {
        breadcrumbs.push({
          level: 32,
          code: zoneParams.province_area_code,
          name: zoneParams.province_area_name,
          levelName: '省区'
        })
      }
      breadcrumbs.push({
        level: 33,
        code: zoneParams.zoneCode,
        name: zoneParams.zoneName,
        levelName: '区域'
      })
      break
      
    case 32: // 省区级别 - 显示：全网 > 省区
      breadcrumbs.push({
        level: 32,
        code: zoneParams.zoneCode,
        name: zoneParams.zoneName,
        levelName: '省区'
      })
      break
  }
  
  return breadcrumbs
}

export default {
  getParentZoneInfo,
  getLevelName,
  buildApiParams,
  getBreadcrumbData
}
