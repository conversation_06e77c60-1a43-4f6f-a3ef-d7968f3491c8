import BtnDetail from '../cpns/BtnDetail.vue'
import baseMixins from '../baseMixins'
import { mapState, mapMutations } from 'vuex'
export default {
  mixins: [baseMixins],
  components: {
    BtnDetail
  },
  data() {
    return {}
  },
  computed: {
    btnValue() {
      return `展开${this.zoneData.dLevelName}数据`
    },
    startDate() {
      if (!this.dateIndex) {
        return this.$moment(this.dateValue).subtract(7, 'days').format('YYYYMMDD')
      }
      if (this.dateIndex === 1) {
        return this.$moment(this.dateValue).subtract(11, 'month').format('YYYYMM')
      }
      return this.$moment(this.dateValue).subtract(2, 'year').format('YYYY')
    },
    key_date() {
      return ['inc_day', 'data_month', 'inc_year'][this.dateIndex]
    },
    levelMap() {
      return {
        32: { province_area_code: this.zoneCode },
        33: { area_code: this.zoneCode },
        34: { dept_code: this.zoneCode }
      }
    },
    deptType() {
      return {
        30: { deptType: 1 },
        32: { deptType: 2 },
        33: { deptType: 3 },
        34: { deptType: 4 }
      }
    },
    level_next_code() {
      return {
        30: 'province_area_code',
        32: 'area_code',
        33: 'dept_code',
        34: 'dept_code'
      }[+this.zoneLevel]
    },
    level_next_name() {
      return {
        30: 'province_area_name',
        32: 'area_name',
        33: 'dept_name',
        34: 'dept_name'
      }[+this.zoneLevel]
    },
    // 取下钻
    // nextLevelMap() {
    //   return {
    //     32: { area_code: this.zoneCode },
    //     33: { dept_code: this.zoneCode },
    //     34: { dept_code: this.zoneCode }
    //   }
    // },
    ...mapState('qualityBord', {
      // qualityData: state => state.service.qualityData
    })
  },
  methods: {
    ...mapMutations('qualityBord', ['setServiceState']),
    drawerHeight(height) {
      this.drawerTableMaxHeight = `${height - 120}px`
    }
  }
}
