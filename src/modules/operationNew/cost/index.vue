<!--
 * @Author: shigl
 * @Date: 2024-09-19 18:17:37
 * @LastEditTime: 2024-09-19 18:17:37
 * @Description: 运营--品效
-->
<template>
  <div>
    <div class="date_container">
      <KyDatePicker :type="dateType" :dateValue="dateValue" @onChange="dateChange" :holidayData="holidayData">
      </KyDatePicker>
    </div>
    <PageContent>
      <OverView :areaCode="apiParams.areaCode" :dataLevel="apiParams.dataLevel"
        :provinceAreaCode="apiParams.provinceAreaCode"></OverView>
      <PlanTimeReach :areaCode="apiParams.areaCode" :dataLevel="apiParams.dataLevel"
        :provinceAreaCode="apiParams.provinceAreaCode"></PlanTimeReach>
      <div style="height: 0.5rem"></div>
      <KyDataDrawer :visible="visible" :title="diaTitle" height="80%" @close="diaClose">
      </KyDataDrawer>
    </PageContent>
  </div>
</template>
<script>
import request from './request'
import mixins from './commonMixins/mixins'
import flowLayout from 'common/mixins/flowLayout.js'
import OverView from './overview'
import PlanTimeReach from './planTimeReach'
import { mapMutations, mapState, mapGetters } from 'vuex'
export default {
  mixins: [request, flowLayout, mixins],
  components: {
    OverView,
    PlanTimeReach,
  },
  data() {
    return {
      diaTitle: '标题',
      visible: false,
      apiParams: {
        areaCode: '',
        dataLevel: 30,
        provinceAreaCode: ''
      }
    }
  },
  computed: {
    dateType() {
      return 'day'
      // return ['day', 'week', 'month'][this.dateIndex]
    },
    ...mapState({
      holidayData: 'holidayData'
    }),

    // 从 zoneInfo store 中获取区域信息
    ...mapGetters('zoneInfo', [
      'currentZoneInfo',           // 当前区域基本信息
      'parentProvinceAreaCode',    // 上级省区code
      'parentProvinceAreaName',    // 上级省区名称
      'parentAreaCode',            // 上级区域code
      'parentAreaName',            // 上级区域名称
      'parentZoneInfo',            // 完整的上级信息对象
      'breadcrumbData',            // 面包屑导航数据
      'isStationLevel'             // 是否为场站级别
    ])
  },
  watch: {
    zoneCode() {
      this.initOptions()
      this.buildApiParams()
    },
    dateDay() {
      this.buildApiParams()
    },
    // 监听 zoneInfo store 的变化
    parentZoneInfo: {
      handler(newVal, oldVal) {
        console.log('=== index.vue: zoneInfo 数据变化 ===')
        console.log('新的上级信息:', newVal)
        console.log('旧的上级信息:', oldVal)

        // 当区域信息变化时，重新构建API参数
        if (newVal && newVal.currentCode !== (oldVal && oldVal.currentCode)) {
          console.log('区域发生变化，重新构建API参数')
          this.buildApiParams()
        }
      },
      deep: true,
      immediate: false
    }
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    dateChange(date) {
      this.setDate({
        type: 'all',
        key: 'dateDay',
        // key: ['dateDay', 'dateMon'][this.dateIndex],
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
      this.initOptions()
    },
    initOptions() { },
    diaClose() {
      this.visible = false
    },
    btnOpenDia(data) {
      this.title = data
      this.visible = true
    },

    // 构建API请求参数
    buildApiParams() {
      // 数据层级 30顺心 32省区 33区域
      const dataLevelMap = {
        provinceAreaCode: 32,
        areaCode: 33,
        default: 30
      }

      const params = {
        // 数据层级 30顺心 32省区 33区域 - 根据当前选中层级判断
        dataLevel: dataLevelMap[this.key_level_code] || dataLevelMap.default,
        areaCode: '',
        provinceAreaCode: ''
      }

      // 根据当前级别设置 provinceAreaCode 和 areaCode
      if (dataLevelMap[this.key_level_code] === 33) {
        params.provinceAreaCode = this.parentProvinceAreaCode
        params.areaCode = this.zoneCode
      }
      if (dataLevelMap[this.key_level_code] === 32) {
        params.provinceAreaCode = this.zoneCode
      }

      this.apiParams = params
      console.log('父组件构建的API参数:', this.apiParams)
    }
  },
  mounted() {
    this.setDateIndex({
      type: 'all',
      index: 0
    })
    // 初始化API参数
    this.buildApiParams()
  }
}
</script>
<style lang='less' scoped>
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
