
<template>
  <div class="pd_lr20">
    <div class="flex_between mt24">
      <div class="fs28 fw700">{{ warmTitle }}</div>
      <div class="round_btn_warp grey333" @click="btnOpenDia">
        查看详情 <i class="iconfont icon-dayuhao fs20"></i>
      </div>
    </div>
    <div class="mt32">
      <div class="warm_warp">
        <div class="warm_title">排名后两位</div>
        <div class="warm_content" v-show="(dataSource || []).length>0">
          <div class="warm_list_item" v-for="(item,i) in dataSource" :key="i">
            <span class="warm_list_item_label">{{ item.levelCode === '39' ? item.deptName : item[options.label] }}</span>
            <span class="warm_list_item_value">{{ fomaDatas(item[options.dataIndex], options.per) }}</span>
          </div>
        </div>
        <div class="mb32 flex_around grey666" v-show="(dataSource || []).length<=0">暂无数据</div>
      </div>
    </div>
  </div>
</template>
<script>
import { numToPercent, numToInFloat } from 'common/js/numFormat'
export default {
  props: ['dataSource', 'options', 'warmTitle', 'sortIndex', 'typeKey'],
  data() {
    return {
      // dataSource,
      // options,
      // warmTitle
    }
  },
  computed: {
    // dataSources() {
    //   const newDataSource = this.dataSource || []
    //   console.log("wwewgbfdfb", newDataSource)
    //   // return newDataSource
    //   if (this.zoneLevel === '32') {
    //     console.log("wwewgbfdfb1111", this.zoneLevel,  newDataSource)
    //     return newDataSource.filter((item, index) => item?.levelCode !== '39')
    //   } else {
    //     console.log("wwewgbfdfb2222", newDataSource)
    //     return newDataSource
    //   }
    // }
  },
  watch: {
  },
  methods: {
    btnOpenDia() {
      this.$emit('openDia', {
        type: this.typeKey,
        warmTitle: this.warmTitle
      })
    },
    fomaDatas(value, per) {
      if (per) {
        return value === null ? '_' : numToPercent(value, 1)
      } else {
        return value === null ? '_' : numToInFloat(value, 1)
      }
    }
  }
}
</script>
<style lang='less' scoped>
.warm_warp {
  width: 100%;
  border-radius: 0.08rem;
  background: #F8F9FC;
  overflow: hidden;
}
.warm_title {
  font-size: 0.24rem;
  font-weight: 600;
  line-height: 0.45rem;
  color: #CC1B23;
  background: linear-gradient(90deg, #FFF8F8 0%, #FFEBEB 87%);
  width: fit-content;
  padding: 0 0.24rem;
  border-radius: 0% 0% 0.12rem 0%;
}
.warm_content {
  display: flex;
  grid-template-columns: repeat(2, 1fr);
  gap: 0;
  padding-bottom: 0.32rem;
  flex-wrap: wrap;
}
.warm_list_item {
  flex: 1 1 45%;
  white-space: nowrap;
  box-sizing: border-box;
  margin-top: 0.24rem;
  margin-left: 0.24rem;
}
.warm_list_item_label {
  color: #666666;
  font-size: 0.24rem;
}
.warm_list_item_value {
  color: #333333;
  font-size: 0.24rem;
  font-size: 0.32rem;
  font-weight: bold;
  margin-left: 0.24rem;
}
</style>
