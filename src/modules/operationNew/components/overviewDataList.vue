
<template>
  <div class="overview_warp grid3 pd_lr20">
    <div class="overview_list_item mr12" v-for="(item,index) in overviewOption" :key="index">
      <div class="row_list fs24">{{ item.parent[0].label }}</div>
      <div class="row_list fs48 fw700 row_lh_58 row_h_58">
        {{ formatDatas(data[item.parent[0].dataIndex], item.parent[0])}}<span class="fs36" v-show="item.parent[0].cus">%</span>
      </div>
      <div class="row_list">
        <span class="row_label fs20">{{ item.child[0].label }}</span>
        <span class="row_value fs20">{{ formatDatas(data[item.child[0].dataIndex], item.child[0])}}</span>
      </div>
      <div class="row_h_56">
        <div class="row_list row_list_bg pd_l20" v-if="item?.footer && item?.footer.length>0">
          <span class="row_label fs20">{{ item.footer[0].label }}</span>
          <span class="row_value fs20">{{ formatDatas(data[item.footer[0].dataIndex], item.footer[0])}}</span>
        </div>
      </div>
    </div>
    <!-- <div class="overview_list_item mr12">
      <div class="row_list fs24">运单时长（h）</div>
      <div class="row_list fs48 fw700 row_lh_58">32</div>
      <div class="row_list">
        <span class="row_label fs20">月环比</span>
        <span class="row_value fs20">86%</span>
      </div>
      <div class="row_list row_list_bg pd_l20">
        <span class="row_label fs20">月目标达成率</span>
        <span class="row_value fs20">86%</span>
      </div>
    </div>
    <div class="overview_list_item mr8 ml8">
      <div class="row_list fs24">运单时长（h）</div>
      <div class="row_list fs48 fw700 row_lh_58">32</div>
      <div class="row_list">
        <span class="row_label fs20">月环比</span>
        <span class="row_value fs20">86%</span>
      </div>
      <div class="row_list row_list_bg pd_l20">
        <span class="row_label fs20">月目标达成率</span>
        <span class="row_value fs20">86%</span>
      </div>
    </div>
    <div class="overview_list_item ml12">
      <div class="row_list fs24">运单时长（h）</div>
      <div class="row_list fs48 fw700 row_lh_58">32</div>
      <div class="row_list">
        <span class="row_label fs20">月环比</span>
        <span class="row_value fs20">86%</span>
      </div>
      <div class="row_list row_list_bg pd_l20">
        <span class="row_label fs20">月目标达成率</span>
        <span class="row_value fs20">86%</span>
      </div>
    </div> -->
  </div>
</template>
<script>
import { numToPercent, numToInFloat } from 'common/js/numFormat'
export default {
  props: ['overviewOption', 'dataSource'],
  data() {
    return {
      option: [],
      data: {}
    }
  },
  computed: {},
  watch: {
    dataSource(val) {
      this.data = val
    },
    overviewOption(val) {
      this.option = val
    }
  },
  methods: {
    formatDatas(value, option) {
      if (option?.per) {
        // return value === null ? '_' : numToPercent(value, 1)
        if (option?.cus) {
          return value === null || value === undefined ? '_' : numToPercent(value, 1).replace(/%/, '')
        } else {
          return value === null ? '_' : numToPercent(value, 1)
        }
      } else if (option?.int) {
        return value === null ? '_' : numToInFloat(value, 1)
      } else {
        return value === null ? '_' : value
      }
    }
  }
}
</script>
<style lang='less' scoped>
.overview_warp {
  color: #ffffff;
  margin-top: 0.24rem;
}
.overview_list_item {
  border-radius: 0.08rem;
  background: linear-gradient(174deg, #4F699F 0%, #2E3F63 93%);
  box-shadow: 0px 8px 18px 0px rgba(34, 48, 77, 0.3);
  padding-top: 0.08rem;
}
.row_list {
  margin-top: 0.12rem;
  margin-left: 0.2rem;
}
.row_list_bg {
  line-height: 0.56rem;
  background: rgba(255, 255, 255, 0.05);
  margin-left: 0;
}
.row_label {
  color: rgba(255, 255, 255, 0.7);
}
.row_value{
  font-weight: 500;
  padding-left: 0.16rem;
}
.row_lh_58 {
  line-height: 0.58rem;
}
.row_h_58 {
  height: 0.58rem;
}
.row_h_56 {
  height: 0.56rem;
}
</style>
