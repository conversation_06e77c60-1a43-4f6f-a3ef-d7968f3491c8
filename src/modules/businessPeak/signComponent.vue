<template>
  <div>
    <img class="imgTitle" :src="sxTitle" alt="" />
    <KydChartModel
      class="mt32 pd_lr20"
      :legendOption="legendOption"
      :tableOption="tableData"
      tableLength="5"
      :isTable="false"
    >
      <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
    </KydChartModel>
    <div class="parentBtn flex_center">
      <div class="btn_check" @click="buttonClick">查看{{ nextLevelName }}</div>
    </div>
    <!-- 弹框组件 -->
    <KyDataDrawer :visible="visible" height="70%" @close="visible = false" class="modal_drawer_content">
      <div class="content_header flex_around">
        <div class="tabs_btn" v-for="(item, index) in tabs" :key="index" @click="clickHandler(item, index)">
          <div :class="tabsIndex === index ? 'active baseFont' : 'baseFont'">
            {{ item }}
          </div>
        </div>
      </div>
      <div class="table_modal">
        <div v-if="tabsIndex === 0">
          <NormalTable
            size="small"
            maxHeight="9rem"
            :width="moreTableWidth"
            :dataSource="deliveryDataSource"
            :columns="deliveryColumns"
            :isRowBgc="false"
            :border="false"
          >
          </NormalTable>
        </div>
        <div v-if="tabsIndex === 1">
          <NormalTable
            size="small"
            maxHeight="9rem"
            :width="moreTableWidth"
            :dataSource="warnDataSource"
            :columns="warnTableColumns"
            :isRowBgc="false"
            :border="false"
          >
          </NormalTable>
        </div>
      </div>
    </KyDataDrawer>
  </div>
</template>

<script>
import baseMixins from './baseMixins'

import { drawMoreBarChart } from 'common/charts/chartOption'

import sxTitle from './img/qianshou.png'

export default {
  mixins: [baseMixins],
  props: ['datePicker'],
  components: {},
  data() {
    return {
      sxTitle,
      tableData: {},
      visible: false,
      tabs: ['概览'],
      legendDataSource: [],
      tabsIndex: 0,
      moreTableWidth: '100%',
      deliveryColumns: [], // 交货-概览
      deliveryDataSource: [], // 交货-概览
      warnTableColumns: [], // 交货-预警情况
      warnDataSource: [] // 交货-预警情况
    }
  },
  computed: {
    nextLevelName() {
      if (+this.zoneLevel === 34) {
        return '网点'
      }
      return this.zoneData.dLevelName === '网点' ? '网管' : this.zoneData.dLevelName
    },
    legendOption() {
      return {
        type: 'column',
        options: [
          { label: '总签收票数', color: '#4C83F9', seriesIndex: 0, dataIndex: 'value', int: [0] },
          { label: '延误票数', color: '#DC1E32', dataIndex: 'value', seriesIndex: 1, int: [0] },
          { label: '实时签收率', color: '#22F0AF', seriesIndex: 2, dataIndex: 'value', per: [1] }
          // { label: '留仓率环比昨日', seriesIndex: 0, dataIndex: 'index_value', color: '#0a142e', per: [1] }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    zoneCode() {
      this.init()
    },
    datePicker() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getDatas()
    },

    async getDatas() {
      const { obj } = await this._getSignData()
      const { mainInfo, extendInfo } = obj || {}
      const { overview } = extendInfo || { extendInfo }
      this.setTable(overview)

      this.initModelChart(mainInfo)
    },
    setTable(result) {
      this.tableWidth = '100%'
      this.deliveryDataSource = result

      this.deliveryColumns = [
        { label: this.nextLevelName, dataIndex: 'siteName' },
        { label: '总签收票数', dataIndex: 'totalSign', render: (h, val) => this.$numToInteger(val, 0, 1, 0) },
        { label: '延误票数', dataIndex: 'delaySign', render: (h, val) => this.$numToInteger(val, 0, 1, 0) },
        {
          label: '实时签收率',
          dataIndex: 'realTimeSignRate',
          render: (h, val) =>
            val && Number(val) < 70 ? (
              <span style={{ color: 'red' }} >{this.$numToPercent(val / 100)}</span>
            ) : (
              this.$numToPercent(val / 100)
            )
        }
      ]
      this.warnTableColumns = [
        { label: '月累计实时签收率', dataIndex: 'realTimeSignRate' },
        { label: '预警标识', dataIndex: 'dept_name' },
        { label: '预警网点数', dataIndex: 'cal_weight_mtd' }
      ]
    },
    initModelChart(mainInfo) {
      const { trend } = mainInfo || {}
      const xData = []
      const sData = [[], [], []]
      if (trend && trend.length > 0) {
        this.$objectSortUp(trend, 'signTimeDate')
        const newList = trend.slice(-8)
        newList.forEach(item => {
          xData.push(item.signTimeDate)
          sData[0].push({
            value: item.totalSign,
            index_value: ''
          })
          sData[1].push(item.delaySign)
          sData[2].push(item.realTimeSignRate / 100)
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$moment(value).format('M.DD')
            },
            axisLine: {
              show: false
            }
          },
          {
            axisLine: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: value => this.$intFormat(value)
            }
          },
          {
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: value => this.$perFormat(value)
            }
          }
        ],
        series: [
          {
            data: sData[0],
            stack: '占比',
            itemStyle: {
              color: '#4C83F9'
            }
          },
          {
            data: sData[1],
            stack: '占比',
            itemStyle: {
              color: '#DC1E32'
            }
          },
          {
            type: 'line',
            lineStyle: {
              color: '#22F0AF'
            },
            smooth: true,
            yAxisIndex: 1,
            data: sData[2]
          }
        ]
      }
      drawMoreBarChart(option, this.$refs['chart-model-trend'], true)
      this.tableData = {
        options: option
      }
    },

    clickHandler(item, index) {
      this.tabsIndex = index
    },
    buttonClick(e) {
      e.preventDefault()
      this.visible = true
      this.$sensors.webClick('实时-签收-详情')
    }
  }
}
</script>

<style lang="less" scoped>
.content-wrapper {
  background-color: #000a1c !important;
}
.imgTitle {
  width: 100%;
}
.parentBtn {
  width: 100%;
  height: 0.64rem;
  margin-top: 0.32rem;
  .btn_check {
    width: 7.1rem;
    height: 0.64rem;
    line-height: 0.64rem;
    text-align: center;
    font-size: 0.24rem;
    color: #999999;
    font-weight: 500;
    background-color: #132041;
  }
}
// 弹框改色
.modal_drawer_content {
  .table_modal {
    /deep/.kyd-table-outer-wrap-container {
      background-color: #132041 !important;
      .table-outer-show {
        background-color: #132041;
        color: #fff;
      }
      th {
        background-color: #132041 !important;
        color: #fff !important;
        box-shadow: inset 0 -1px 0 0 rgba(153, 153, 153, 0.5);
      }
      td {
        background-color: #132041;
        color: #fff !important;
        box-shadow: inset 0 -1px 0 0 rgba(153, 153, 153, 0.5);
      }
    }
  }

  /deep/.drawer-content {
    background-color: #132041 !important;

    .header {
      height: 0.72rem;
    }
    .content_header {
      margin-bottom: 0.5rem;
      .tabs_btn {
        // opacity: 0.5;
        font-size: 0.4rem;
        letter-spacing: 0.08rem;
        text-align: center;
        color: #ffffff;
        text-shadow: 0 2px 27px #41a6f3;
      }
    }
  }
}

.baseFont {
  font-family: YouSheBiaoTiHei;
  opacity: 0.5;
  font-size: 0.4rem;
  color: #6bb5ff;
  letter-spacing: 0.08rem;
  text-align: center;
}
.active {
  color: #ffffff;
  opacity: 1;
}
</style>
