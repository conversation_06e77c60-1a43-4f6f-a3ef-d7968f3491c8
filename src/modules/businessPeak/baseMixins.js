/*
 * @Author: shigl
 * @Date: 2022-06-07 15:03:45
 * @LastEditTime: 2022-12-01 17:23:05
 * @Description:
 */
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
export default {
  mixins: [requestMixins],
  computed: {
    zoneLevel() {
      return this.zoneData.zoneLevel
    },
    zoneCode() {
      return this.zoneData.zoneCode
    },
    zoneName() {
      return this.zoneData.zoneName
    },
    // 下专
    dLevelName() {
      return this.zoneData.dLevelName
    },
    // 下专zoneLevel
    dZoneLevel() {
      return this.zoneData.dZoneLevel
    },
    ...mapState({
      // isDev: state => state.isDev,
    }),
    ...mapGetters({
      zoneData: 'zoneData'
    }),
    isWangguan() {
      const code = Number(this.zoneCode)
      return +this.zoneLevel === 34 && !isNaN(code)
    }
  },

  methods: {
    // 顺心战况-实时-签收
    _getSignData() {
      let initData = {}
      if (+this.zoneLevel === 34) {
        initData = {
          level: this.isWangguan ? '37' : '39',
          zoneCode: this.zoneCode
        }
      } else {
        initData = {
          level: this.zoneLevel,
          zoneCode: this.zoneCode
        }
      }
      return this.sendJavaRequest({
        url: '/resourceServices/signTimely/signQuery',
        data: initData
      })
    },
    // 顺心战况-实时-交货
    _getDeliveryData(datePicker) {
      const levelMap = {
        30: {},
        32: { province_area_code: this.zoneCode, province_area_name: this.zoneName },
        33: { area_code: this.zoneCode, area_name: this.zoneName },
        34: { manager_id: this.zoneCode, manager_name: this.zoneName }
      }[+this.zoneLevel]
      const conditonList = {
        cal_date: datePicker,
        ...levelMap
      }
      return this.sendJavaRequest({
        url: '/resourceServices/bdpRequest/transfer',
        data: {
          apiPath: '/dsp-search/common/758/sx/zk/last_7days_delivery_rate',
          params: conditonList
        }
      })
    },
    // 顺心战况-实时-交货
    _getDeliveryDataDetail(datePicker) {
      const levelMap = {
        30: { groupId: 'province_area_code', groupName: 'province_area_name' },
        32: { groupId: 'area_code', groupName: 'area_name', province_area_code: this.zoneCode },
        33: { groupId: 'manager_id', groupName: 'manager_name', area_code: this.zoneCode },
        34: { groupId: 'consignSiteCode', groupName: 'consignSiteName', manager_id: this.zoneCode }
      }[+this.zoneLevel]
      const conditonList = {
        cal_date: datePicker,
        ...levelMap
      }
      return this.sendJavaRequest({
        url: '/resourceServices/bdpRequest/transfer',
        data: {
          apiPath: '/dsp-search/common/758/sx/zk/delivery_intime_rate',
          params: conditonList
        }
      })
    },
    _getsxRealTimeIncome() {
      const initData = {
        incDay: '20210630'
      }
      return this.sendJavaRequest({
        // url: '/resourceServices/sxRealTimeIncomeAndWeight/query',
        url: '/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query',
        data: initData
      })
    },
    // 顺心实时战况-提货-七天数据
    _getPickData(datePicker) {
      const levelMap = {
        30: {},
        32: { province_area_code: this.zoneCode },
        33: { area_code: this.zoneCode },
        34: { manager_id: this.zoneCode }
      }[+this.zoneLevel]
      const tableName = 'sx_pick_standard_rate_his'
      const conditonList = {
        cal_date: datePicker,
        ...levelMap
      }
      return this.sendTwoDimenRequest(tableName, this.forMapData(conditonList))
    },
    // 顺心实时战况-提货-展开明细
    _getPickDataDetail(datePicker) {
      const levelMap = {
        30: { groupId: 'province_area_code', groupName: 'province_area_name' },
        32: { groupId: 'area_code', groupName: 'area_name', province_area_code: this.zoneCode },
        33: { groupId: 'manager_id', groupName: 'manager_name', area_code: this.zoneCode },
        34: { groupId: 'site_code', groupName: 'site_name', manager_id: this.zoneCode }
      }[+this.zoneLevel]
      const tableName = 'sx_pick_standard_rate_list'
      const conditonList = {
        cal_date: datePicker,
        ...levelMap
      }
      return this.sendTwoDimenRequest(tableName, this.forMapData(conditonList))
    },

    // 顺心实时战况-派送-七天数据
    _getSendData(datePicker) {
      const levelMap = {
        30: {},
        32: { province_area_code: this.zoneCode },
        33: { area_code: this.zoneCode },
        34: { manager_id: this.zoneCode }
      }[+this.zoneLevel]
      const tableName = 'sx_delivery_standard_rate_his'
      const conditonList = {
        cal_date: datePicker,
        ...levelMap
      }
      return this.sendTwoDimenRequest(tableName, this.forMapData(conditonList))
    },
    // 顺心实时战况-派送-展开明细
    _getSendDataDetail(datePicker) {
      const levelMap = {
        30: { groupId: 'province_area_code', groupName: 'province_area_name' },
        32: { groupId: 'area_code', groupName: 'area_name', province_area_code: this.zoneCode },
        33: { groupId: 'manager_id', groupName: 'manager_name', area_code: this.zoneCode },
        34: { groupId: 'site_code', groupName: 'site_name', manager_id: this.zoneCode }
      }[+this.zoneLevel]
      const tableName = 'sx_delivery_standard_rate_list'
      const conditonList = {
        cal_date: datePicker,
        ...levelMap
      }
      return this.sendTwoDimenRequest(tableName, this.forMapData(conditonList))
    }
  }
}
