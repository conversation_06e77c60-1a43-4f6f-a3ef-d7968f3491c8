<template>
  <div>
    <img class="img-sxTitle" :src="sxTitle" alt="" />
    <KydChartModel
      class="mt32 pd_lr20"
      :legendOption="legendOption"
      :tableOption="tableData"
      tableLength="5"
      :isTable="false"
    >
      <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
    </KydChartModel>
    <div class="parentBtn flex_center">
      <div class="btn_check" @click="buttonClick">查看{{ nextLevelName }}</div>
    </div>
    <KyDataDrawer :visible="visible" height="70%" @close="visible = false" class="modal_drawer_content">
      <div class="content_header flex_around">
        <div class="tabs_btn" v-for="(item, index) in tabs" :key="index" @click="clickHandler(item, index)">
          <div :class="tabsIndex === index ? 'active baseFont' : 'baseFont'">
            {{ item }}
          </div>
        </div>
      </div>
      <div v-if="tabsIndex === 0">
        <NormalTable
          size="small"
          maxHeight="9rem"
          :width="moreTableWidth"
          :dataSource="dataSource"
          :columns="tableColumns"
          :isRowBgc="false"
          :border="false"
        >
        </NormalTable>
      </div>
    </KyDataDrawer>
  </div>
</template>

<script>
import { drawMoreBarChart } from 'common/charts/chartOption'
import baseMixins from './baseMixins'

import sxTitle from './img/jiaohuo.png'

export default {
  mixins: [baseMixins],
  props: ['datePicker'],

  components: {},
  data() {
    return {
      sxTitle,
      tableData: {},
      visible: false,
      tabs: ['概览'],
      tabsIndex: 0,
      moreTableWidth: '100%',
      dataSource: [], // 交货-概览
      legendDataSource: []
    }
  },
  computed: {
    nextLevelName() {
      if (+this.zoneLevel === 34) {
        return '网点'
      }
      return this.zoneData.dLevelName === '网点' ? '网管' : this.zoneData.dLevelName
    },
    legendOption() {
      return {
        type: 'column',
        options: [
          {
            label: '应交票数',
            color: '#4C83F9',
            seriesIndex: 0,
            dataIndex: 'value',
            int: [0]
          },
          {
            label: '延误票数',
            color: '#DC1E32',
            dataIndex: 'value',
            seriesIndex: 1,
            int: [0]
          },
          {
            label: '交货及时率',
            color: '#22F0AF',
            seriesIndex: 2,
            dataIndex: 'value',
            per: [1]
          }
        ],
        dataSource: this.legendDataSource
      }
    },
    tableColumns() {
      return [
        { label: this.nextLevelName, dataIndex: 'group_name' },
        {
          label: '应交票数',
          dataIndex: 'total_ewbno'
        },
        {
          label: '延误票数',
          dataIndex: 'delay_ewbno'
        },
        {
          label: '交货率',
          dataIndex: 'intime_rate',
          render: (h, val) => this.$numToPercent(val, 1)
        }
      ]
    }
  },
  watch: {
    zoneCode() {
      this.init()
    },
    datePicker() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getData()
    },
    async getData() {
      const { obj } = await this._getDeliveryData(this.datePicker)
      this.initModelChart(obj)
    },
    async getSendDataDetail() {
      this.dataSource = []
      const { obj } = await this._getDeliveryDataDetail(this.datePicker)
      this.dataSource = obj
    },
    initModelChart(objList) {
      const xData = []
      const sData = [[], [], []]

      if (objList && objList.length > 0) {
        this.$objectSortUp(objList, 'rcalculate_date')
        const newList = objList.slice(-8)
        newList.forEach(item => {
          xData.push(item.calculate_date)
          sData[0].push(item.total_ewbno)
          sData[1].push(item.delay_ewbno)
          sData[2].push(item.intime_rate)
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$moment(value).format('M.DD')
            },
            textStyle: {
              color: '#999999'
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value)
            },
            textStyle: {
              color: '#999999'
            },
            axisLine: {
              show: false
            }
          },
          {
            textStyle: {
              color: '#999999'
            },
            axisLabel: {
              formatter: value => this.$perFormat(value)
            },
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: sData[0],
            stack: '占比',
            itemStyle: {
              color: '#4C83F9'
            }
          },
          {
            type: 'bar',
            data: sData[1],
            stack: '占比',
            itemStyle: {
              color: '#DC1E32'
            }
          },
          {
            type: 'line',
            lineStyle: {
              color: '#22F0AF'
            },
            smooth: true,
            yAxisIndex: 1,
            data: sData[2]
          }
        ]
      }
      drawMoreBarChart(option, this.$refs['chart-model-trend'], true)
      this.tableData = {
        options: option
      }
    },
    clickHandler(item, index) {
      this.tabsIndex = index
    },
    buttonClick() {
      this.visible = true
      this.$sensors.webClick('实时-交货-详情')
      this.getSendDataDetail()
    }
  }
}
</script>

<style lang="less" scoped>
@import url('./css/common.less');
.content-wrapper {
  background-color: #000a1c !important;
}
.img-sxTitle {
  width: 100%;
}
.parentBtn {
  width: 100%;
  height: 0.64rem;
  margin-top: 0.32rem;
  .btn_check {
    width: 7.1rem;
    height: 0.64rem;
    line-height: 0.64rem;
    text-align: center;
    font-size: 0.24rem;
    color: #999999;
    font-weight: 500;
    background-color: #132041;
  }
}
// 弹框改色
.modal_drawer_content {
  /deep/.table_modal {
    .table-outer-show {
      background-color: #132041;
    }
    tr {
      background-color: #132041;
    }
    th {
      background-color: #132041;
    }
    td {
      background-color: #132041;
    }
  }
  /deep/.drawer-content {
    background-color: #132041 !important;

    .header {
      height: 0.72rem;
    }
    .content_header {
      margin-bottom: 0.5rem;
      .tabs_btn {
        // opacity: 0.5;
        font-size: 0.4rem;
        letter-spacing: 0.08rem;
        text-align: center;
        color: #ffffff;
        text-shadow: 0 2px 27px #41a6f3;
      }
    }
  }
}
// 图表改色
/deep/.kyd_chart_model_container {
  background-color: #000a1c;
  .chart_legend_box {
    .grey333 {
      color: #fff !important;
    }
    .grey666 {
      color: #999999;
      font-size: 0.24rem;
    }
  }
}
.baseFont {
  font-family: YouSheBiaoTiHei;
  opacity: 0.5;
  font-size: 0.4rem;
  color: #6bb5ff;
  letter-spacing: 0.08rem;
  text-align: center;
}
.active {
  color: #ffffff;
  opacity: 1;
}
</style>
