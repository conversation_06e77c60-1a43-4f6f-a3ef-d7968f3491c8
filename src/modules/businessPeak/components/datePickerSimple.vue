<template>
  <div class="datePicker-businessPeak" :class="{ 'datePicker-wrap-peak': isPeak }">
    <div class="flex_start">
      <div
        v-for="(item, index) in dayList"
        :key="index"
        class="date-content flex_center"
        @click="switchDay(item, index)"
      >
        <div class="date-bgc" :class="{ 'active-date': selected == index }">
          <span>{{ item.week }}</span>
          <p>{{ item.day }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
const daysOfWeek = ['日', '一', '二', '三', '四', '五', '六']
export default {
  name: 'DatePickerSimple',
  props: {
    // 需要显示多少个日期，默认是7天
    preNum: {
      type: Number,
      default: 7
    },
    // 默认时间格式，采用momentJs的格式
    format: {
      type: String,
      default: 'YYYYMMDD'
    },
    // 最后一天显示的日期
    endDay: {
      default: ''
    },
    // 默认点亮哪一个，以最后一个日期索引为0，往前一天-1，依次类推
    activeIndex: {
      type: Number,
      default: 0
    },
    isPeak: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selected: '',
      dayList: [],
      preClickTime: new Date().getTime()
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    init() {
      const endDay = this.$moment(this.endDay || new Date(), this.format)
      this.dayList = []
      let tempDay
      for (let i = this.preNum - 1; i >= 0; i--) {
        tempDay = this.$moment(endDay).subtract(i, 'days')
        this.dayList.push({
          date: tempDay.format(this.format),
          week: daysOfWeek[tempDay.format('d')],
          day: tempDay.format('DD')
        })
      }
      this.upDateIndex()
    },
    upDateIndex() {
      this.selected = this.activeIndex - 1 + this.preNum
    },
    switchDay(item, index) {
      if (index === this.selected) return
      const curTime = new Date().getTime()
      if (curTime - this.preClickTime < 1000) {
        this.$toast({
          duration: 1000,
          message: '操作频繁！'
        })
        return
      }
      this.preClickTime = curTime
      this.selected = index

      // 返回参数,选择的日期和索引
      this.$emit('change', {
        date: item.date,
        week: item.week,
        index: index - this.preNum + 1
      })
    }
  },
  watch: {
    activeIndex() {
      this.upDateIndex()
    },
    endDay() {
      this.init()
    }
  }
}
</script>
<style lang="less" scoped>
.datePicker-businessPeak {
  position: relative;
  height: 1.1rem;
  background-color: #101732;

  .date-content {
    display: flex;
    flex: 1;
    line-height: 0.5rem;
    font-size: 0.22rem;
    flex-direction: column;
    align-items: center;
    padding-top: 0.02rem;
    .date-bgc {
      width: 0.56rem;

      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      span {
        color: #fff;
      }
      p {
        height: 0.5rem;
        width: 0.5rem;
        font-size: 0.3rem;
        text-align: center;
        color: #fff;
      }
    }
  }
  .active-date {
    background-image: linear-gradient(178deg, rgba(5, 22, 45, 0) 0%, #67b0fa 100%);
    border-radius: 4px;
  }
}
</style>
