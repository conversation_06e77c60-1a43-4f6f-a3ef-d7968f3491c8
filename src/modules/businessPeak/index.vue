<template>
  <div class="page_container">
    <PageContent>
      <div>
        <img class="imgTitle" :src="sxTitle" alt="" />
      </div>
     <div class="datePicker_container">
       <DatePickerSimple
        class="datePicker-wrap-peak"
        :endDay="$moment().format('YYYYMMDD')"
        :activeIndex="dateIndex"
        :preNum="8"
        :isPeak="true"
        @change="changeDay"
      />
     </div>

      <!-- 实时收入货量 -->
      <RealTimeCargoComponent :datePicker="datePicker" />
      <!-- 交货 -->
      <DeliveryComponent :datePicker="datePicker" />
      <!-- 提货 -->
      <PickUpComponent :datePicker="datePicker" />
      <!-- 派送 -->
      <SendComponent :datePicker="datePicker" />
      <!-- 签收 -->
      <SignComponent :datePicker='datePicker'/>
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>
<script>
import RealTimeCargoComponent from './realTimeCargoComponent.vue'
import DeliveryComponent from './deliveryComponent'
import SignComponent from './signComponent.vue'
import SendComponent from './sendComponent.vue'
import PickUpComponent from './pickUpComponent.vue'
import sxTitle from './img/sxTitle.png'
import { mapState } from 'vuex'
export default {
  components: {
    // DatePickerSimple,
    DeliveryComponent,
    SendComponent,
    RealTimeCargoComponent,
    SignComponent,
    PickUpComponent
  },
  data() {
    return {
      moreTableWidth: '100%',
      dateIndex: 0,
      sxTitle,
      datePicker: this.$moment().format('YYYYMMDD')
    }
  },
  watch: {},
  computed: {
    ...mapState({})
  },

  methods: {
    changeDay(obj) {
      this.datePicker = obj.date
    }
  },
  mounted() {},
  activated() {
    this.$setWebViewBackgroundColor('#020e22')
  },
  created() {
    this.$setWebViewBackgroundColor('#020e22')
  },
  beforeDestroy() {
    this.$setWebViewBackgroundColor('#ffffff')
  }
}
</script>
<style lang="less" scoped>
@import url('./css/common.less');
.page_container{
   position: relative;
}
.datePicker_container{
  position: sticky;
  top:0;
  z-index: 999;
}
/deep/.datePicker-wrap-peak {
  background-color: #132041 !important;
  color: #fff;
  border: none !important;
  ul {
    li {
      &.active {
        background-image: linear-gradient(178deg, rgba(5, 22, 45, 0) 0%, #67b0fa 100%);
        border-radius: 4px;
        width: 0.56rem;
        height: 1.09rem;
      }
    }
  }
}
.content-wrapper {
  background-color: #000a1c !important;
}

.realTime_title {
  color: #999999;
  text-align: center;
  margin-bottom: 0.08rem;
}
.realTime_momony {
  font-size: 0.48rem;
  color: #ffffff !important;
  text-align: center;
  line-height: 0.67rem;
  text-shadow: 0 0 0.16rem #66b1fa;
}
.realTime_unit {
  color: #ffffff;
  text-align: center;
  line-height: 67rem;
  text-shadow: 0 0 0.16rem #66b1fa;
}
.realTime_dayPer {
  color: #999999;
  text-align: center;
}
.realTime_history {
  color: #999999;
  text-align: center;
}
</style>
