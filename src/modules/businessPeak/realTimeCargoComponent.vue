<template>
  <div>
    <img class="imgTitle" :src="sxTitle" alt="" />
    <div class="flex_around">
      <MultiDataList :columns="incomeColumns" colNum="1" :dataSource="webDataSource"></MultiDataList>
      <img class="img-cuOffLine" :src="cuOffLine" alt="" />
      <MultiDataList :columns="cargoColumns" colNum="1" :dataSource="webDataSource"></MultiDataList>
    </div>
    <NormalTable
      v-if="this.isWangguan"
      class="mt64"
      size="small"
      maxHeight="7rem"
      :isRowBgc="false"
      :border="false"
      :width="moreTableWidth"
      :dataSource="tableDataSource"
      :columns="tableColumns"
    >
    </NormalTable>
  </div>
</template>

<script>
import baseMixins from './baseMixins'
import VueCountUp from 'common/components/vue-count-to/vue-countTo.vue'
import sxTitle from './img/huoliang.png'
import cuOffLine from './img/cutOff_Line.png'

export default {
  mixins: [baseMixins],
  props: ['datePicker'],

  components: {},
  data() {
    return {
      sxTitle,
      cuOffLine,
      tableColumns: [],
      tableDataSource: [],
      moreTableWidth: '100%',
      webDataSource: {},
      timeId: ''
    }
  },

  computed: {
    todayBool() {
      return this.$moment(this.datePicker).format('YYYYMMDD') === this.$moment().format('YYYYMMDD')
    },
    incomeColumns() {
      return [
        {
          parent: [
            {
              label: '实时收入',
              dataIndex: this.todayBool ? 'income' : this.isWangguan ? 'old_income' : 'last_income',
              render: (h, v) => {
                return (
                  <div class={'flex_center'}>
                    <VueCountUp class="white fs40" endVal={v} decimals={1} duration={500} />
                    <div style={{ color: '#FFF', fontSize: '0.24rem', paddingTop: '0.1rem' }}>万元</div>
                  </div>
                )
              }
            }
          ],
          child: [
            {
              label: '日基比',
              dataIndex: this.todayBool ? 'inc_income' : this.isWangguan ? 'kibi_income' : 'yesterday_inc_income',
              indexType: 'up',
              render: (h, v) => (
                <span style={{ color: v > 0 ? '#22F0AF' : '#EE7335', fontWeight: '700' }}>
                  {this.$numToPercent(v, 1)}
                </span>
              )
            },
            {
              label: '历史峰值',
              dataIndex: 'max_income',
              render: (h, v) => <VueCountUp class="white" endVal={v} decimals={0} duration={500} />
            }
          ]
        }
      ]
    },
    cargoColumns() {
      return [
        {
          parent: [
            {
              label: '实时货量',
              dataIndex: this.todayBool ? 'weight' : this.isWangguan ? 'old_weight' : 'last_weight_qty',
              render: (h, v) => (
                <div class={'flex_center'}>
                  <VueCountUp class="white fs40" endVal={v} decimals={1} duration={500} />
                  <div style={{ color: '#FFF', fontSize: '0.24rem', paddingTop: '0.1rem' }}>吨</div>
                </div>
              )
            }
          ],
          child: [
            {
              label: '日基比',
              dataIndex: this.todayBool ? 'inc_weight' : this.isWangguan ? 'kibi_weight' : 'yesterday_inc_weight',
              indexType: 'up',
              render: (h, v) => (
                <span style={{ color: v > 0 ? '#22F0AF' : '#EE7335', fontWeight: '700' }}>
                  {this.$numToPercent(v, 1)}
                </span>
              )
            },
            {
              label: '历史峰值',
              dataIndex: 'max_weight',
              render: (h, v) => <VueCountUp class="white" endVal={v} decimals={0} duration={500} />
            }
          ]
        }
      ]
    }
  },
  watch: {
    zoneCode() {
      this.initOptions()
    },
    datePicker() {
      this.initOptions()
    }
  },
  mounted() {
    this.initOptions()
  },
  methods: {
    async getIncomeIndexData() {
      const [{ obj: data1 }, { obj: data2 }] = await Promise.all([this.getNewRlData(), this.getOldRlData()])
      data1.inc_income = data1.income / data2[0]?.real_income_jb - 1
      data1.inc_weight = data1.weight / data2[0]?.real_weight_jb - 1
      this.webDataSource = [{ ...data1, ...data2[0] }]
      this.setTable()
    },

    getNewRlData() {
      const levelMap = {
        30: {},
        32: { provinceAreaCode: this.zoneCode },
        33: { areaCode: this.zoneCode },
        34: { deptCode: this.zoneCode }
      }[+this.zoneLevel]

      return this.sendJavaRequest({
        // url: '/resourceServices/sxRealTimeIncomeAndWeight/query',
        url: '/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query',
        data: {
          incDay: this.$moment(this.datePicker).format('YYYYMMDD'),
          ...levelMap
        }
      })
    },
    getOldRlData() {
      const levelMap = {
        30: {
          groupByCode: "'001'",
          groupByName: "'全网'"
        },
        32: {
          province_area_code: this.zoneCode,
          groupByCode: 'province_area_code',
          groupByName: 'province_area_name'
        },
        33: {
          area_code: this.zoneCode,
          groupByCode: 'area_code',
          groupByName: 'area_name'
        },
        34: {
          dept_code: this.zoneCode,
          groupByCode: 'dept_code',
          groupByName: 'dept_name'
        }
      }[+this.zoneLevel]
      const data = {
        incDay: this.$moment(this.datePicker).subtract(1, 'days').format('YYYYMMDD'),
        zoneLevel: this.zoneData.level,
        type: '总体',
        ...levelMap
      }
      return this.sendTwoDimenRequest('sx_manage_monit_info_2022', this.forMapData(data))
    },

    initOptions() {
      if (this.isWangguan) {
        this.getWgRealtimeData()
        this.getWeightData()
      } else {
        this.getIncomeIndexData()
      }
    },

    getWangGuanData() {
      const date = this.todayBool
        ? this.$moment().subtract(1, 'days').format('YYYYMMDD')
        : this.$moment(this.datePicker).format('YYYYMMDD')
      // const tableName = 'manager_income_weight'
      const url = '/cockpit/reportrest/report/twoDimen/manager_income_weight'
      const conditionList = [
        { key: 'manager_id', value: this.zoneCode },
        { key: 'inc_day', value: date }
      ]
      return this.sendTwoDimenRequestNew(url, conditionList).then(res => {
        const data = res.obj[0] || {}
        data.old_income = data.income / 10000
        data.old_weight = data.weight / 1000
        delete data.income
        delete data.weight
        return data
      })
    },
    async getWgRealtimeData() {
      const [data1, data2, data3] = await Promise.all([
        this.getWgIncomeData(),
        this.getWgJbData(),
        this.getWangGuanData()
      ])
      data2.inc_income = data1.income / data2.real_income_jb - 1
      data2.inc_weight = data1.weight / data2.real_weight_jb - 1
      this.webDataSource = { ...data1, ...data2, ...data3 }
    },

    getWgIncomeData() {
      return this.sendJavaRequest({
        method: 'GET',
        // url: `/resourceServices/sxRealTimeIncomeAndWeight/query/manager?managerId=${this.zoneCode}`
        url: `/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query/manager?managerId=${this.zoneCode}`
      }).then(res => {
        return res.obj
      })
    },

    getWgJbData() {
      // const tableName = 'sx_manage_kb_real_time'
      const url = '/cockpit/reportrest/report/twoDimen/sx_manage_kb_real_time'
      return this.sendTwoDimenRequestNew(url, [{ key: 'manager_id', value: this.zoneCode }]).then(
        res => {
          return res.obj[0] || {}
        }
      )
    },

    async getWeightData() {
      const result = await this.sendJavaRequest({
        method: 'GET',
        // url: `/resourceServices/sxRealTimeIncomeAndWeight/query/deptByManager?managerId=${this.zoneCode}`
        url: `/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query/deptByManager?managerId=${this.zoneCode}`
      })
      this.setTable(result.obj)
    },

    setTable(result) {
      this.tableDataSource = result
      this.tableColumns = [
        { label: '网点', dataIndex: 'deptName', width: '2.2rem' },
        { label: '收入(万)', dataIndex: 'income', render: (h, val) => this.$numToInteger(val, 2) },
        {
          label: '基比收入',
          dataIndex: 'jbIncome',
          render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val)}</div>
        },
        { label: '货量(吨)', dataIndex: 'weight', render: (h, val) => this.$numToInteger(val, 2) },
        {
          label: '基比货量',
          dataIndex: 'jbWeight',
          render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val)}</div>
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
@import url('./css/common.less');

/deep/.multi_data_list_wrapper {
  width: 2.36rem;
  background-color: #000a1c !important;
  background-image: linear-gradient(180deg, rgba(0, 10, 28) 0%, #285777 100%);
  border-radius: 0 0 0.12rem 0.12rem;
  border-width: 0 0.01rem 0.02rem 0.01rem;

  .multi_data_list_parent {
    .list_parent_label {
      color: #999999 !important;
    }

    .list_parent_text {
      .num-card {
        display: flex;
      }
    }
  }

  .multi_data_list_child_box {
    .list_child_conent {
      justify-content: center;

      .list_child_label {
        color: #999;
      }

      .list_child_text {
        color: #fff;

        .num-card {
          display: flex;
        }
      }
    }
  }
}

.img-cuOffLine {
  width: 0.28rem;
  height: 1.36rem;
}
</style>
