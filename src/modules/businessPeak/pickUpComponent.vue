<template>
  <div>
    <!-- 到货预警 -->
    <img class="imgTitle" :src="sxTitle" alt="" />
    <div class="flex_around">
      <div class="tabs_btn" v-for="(item, index) in pickUpList" :key="index" @click="tabSelectClick(item, index)">
        <div :class="tabsIndex === index ? 'active baseFont' : 'baseFont'">
          {{ item }}
        </div>
      </div>
    </div>

      <!-- 今天&&明天&&后天 -->
      <div class="flex_around timeContnet pd_lr20">
        <MultiDataList
          colNum="1"
          :dataSource="pickupDataSource"
          :columns="pickUpCardColumns[tabsIndex][index]"
          v-for="(item, index) in 3"
          :key="index"
        ></MultiDataList>
      </div>
      <!-- 库存&&在途  -->
      <div class="flex_around pd_lr20">
        <MultiDataList
          class="polygon"
          colNum="1"
          :dataSource="pickupDataSource"
          :columns="pickUpCardColumns[tabsIndex][index + 3]"
          v-for="(item, index) in [1, 2]"
          :key="index"
        ></MultiDataList>
      </div>

      <div class="flex_around">
        <div class="subTitle flex_center" v-for="(item, index) in pickUpLabelList[tabsIndex]" :key="index">
          <img :src="index === 0 ? homeIcon : carIcon" alt="" class="imgIcon" />
          <div class="words fw500">{{ item }}</div>
        </div>
      </div>

    <div class="parentBtn flex_center">
      <div class="btn_check" @click="warnClick">查看{{ nextLevelName }}</div>
    </div>
    <!-- 到货预警-弹框  -->
    <KyDataDrawer :visible="warnVisible" height="75%" @close="warnVisible = false">
      <div class="content_header flex_around">
        <div class="tabs_btn" v-for="(item, index) in pickUpList" :key="index" @click="clickHandlerWarn(item, index)">
          <div :class="warnTabIndex === index ? 'active baseFont' : 'baseFont'">
            {{ item }}
          </div>
        </div>
      </div>
      <NormalTable
        maxHeight="9rem"
        size="small"
        :width="moreTableWidth"
        :dataSource="warmDataSource"
        :columns="warmTableColumns[warnTabIndex]"
        :isRowBgc="false"
        :border="false"
      >
      </NormalTable>
    </KyDataDrawer>
    <!-- 提货 -->
    <KydChartModel
      class="mt32 pd_lr20 mt64"
      :legendOption="legendOption"
      :tableOption="tableData"
      tableLength="5"
      :isTable="false"
    >
      <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
    </KydChartModel>
    <div class="parentBtn flex_center">
      <div class="btn_check" @click="pickUpClick">查看{{ nextLevelName }}</div>
    </div>

    <!-- 提货-弹框  -->
    <KyDataDrawer :visible="pickUpVisible" height="75%" @close="pickUpVisible = false">
      <div class="content_header flex_around">
        <div
          class="tabs_btn"
          v-for="(item, index) in pickUpListB"
          :key="index"
          @click="clickHandlerPickUp(item, index)"
        >
          <div :class="picjupTabIndex === index ? 'active baseFont' : 'baseFont'">
            {{ item }}
          </div>
        </div>
      </div>
      <div v-if="picjupTabIndex === 0">
        <NormalTable
          size="small"
          :maxHeight="maxHeight"
          :width="moreTableWidth"
          :dataSource="dataSource"
          :columns="pickTableColumns"
          :isRowBgc="false"
          :border="false"
        >
        </NormalTable>
      </div>
    </KyDataDrawer>
  </div>
</template>

<script>
import { drawMoreBarChart } from 'common/charts/chartOption'

import VueCountUp from 'common/components/vue-count-to/vue-countTo.vue'
import baseMixins from './baseMixins'
import sxTitle from './img/tihuo.png'
import homeIcon from './img/home_icon.png'
import carIcon from './img/car_icon.png'
import cuOffLine from './img/cutOff_Line.png'

export default {
  mixins: [baseMixins],
  props: ['datePicker'],

  data() {
    return {
      unit: '吨',
      isLoading: false,
      carIcon,
      homeIcon,
      sxTitle,
      cuOffLine,
      maxHeight: '9rem',
      pickUpList: ['货量', '票数', '体积'],
      tableData: {},
      pickUpVisible: false,
      warnVisible: false,
      tabs: ['签收票数', '延误票数', '实时签收率'],
      warnTabIndex: 0,
      tabsIndex: 0,
      moreTableWidth: '100%',
      pickupDataSource: [],
      pickUpLabelList: [
        ['库存货量', '在途货量'],
        ['库存票数', '在途票数'],
        ['库存体积', '在库体积']
      ],
      pickUpVisibleB: false,
      // 提货
      dataSource: [],
      warmDataSource: [],
      // warmTableColumns: [],
      picjupTabIndex: 0,
      pickUpListB: ['概览'],
      legendDataSource: [],
      pickupDataTable: []
    }
  },
  computed: {
    nextLevelName() {
      if (+this.zoneLevel === 34) {
        return '网点'
      }
      return this.zoneData.dLevelName === '网点' ? '网管' : this.zoneData.dLevelName
    },
    legendOption() {
      return {
        type: 'column',
        options: [
          { label: '应提票数', color: '#4C83F9', int: [0] },
          { label: '留仓票数', color: '#DC1E32', int: [0] },
          { label: '提货留仓率', color: '#22F0AF', per: [1] }
        ],
        dataSource: this.legendDataSource
      }
    },
    // 今天&&明天&&后天&&库存&&在途
    pickUpCardColumns() {
      return [
        [
          [
            {
              parent: [
                {
                  label: '预计今天货量',
                  dataIndex: 'today_plan_weight',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '预计明日货量',
                  dataIndex: 'tomorrow_plan_weight',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '预计后天货量',
                  dataIndex: 'after_tomorrow_plan_weight',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '',
                  dataIndex: 'arrive_weight',
                  render: (h, v) => this.countUpSpan(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '',
                  dataIndex: 'onway_weight',
                  render: (h, v) => this.countUpSpan(v)
                }
              ]
            }
          ]
        ],
        [
          [
            {
              parent: [
                {
                  label: '预计今天票数',
                  dataIndex: 'today_plan_votes',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '预计明日票数',
                  dataIndex: 'tomorrow_plan_votes',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '预计后天票数',
                  dataIndex: 'after_tomorrow_plan_votes',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '',
                  dataIndex: 'arrive_votes',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '',
                  dataIndex: 'onway_votes',
                  render: (h, v) => this.countUpSpan(v)
                }
              ]
            }
          ]
        ],
        [
          [
            {
              parent: [
                {
                  label: '预计今天体积',
                  dataIndex: 'today_plan_vol',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '预计明日体积',
                  dataIndex: 'tomorrow_plan_vol',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '预计后天体积',
                  dataIndex: 'after_tomorrow_plan_vol',
                  render: (h, v) => this.componentDiv(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '',
                  dataIndex: 'arrive_vol',
                  render: (h, v) => this.countUpSpan(v)
                }
              ]
            }
          ],
          [
            {
              parent: [
                {
                  label: '',
                  dataIndex: 'onway_vol',
                  render: (h, v) => this.countUpSpan(v)
                }
              ]
            }
          ]
        ]
      ]
    },
    pickTableColumns() {
      return [
        { label: this.nextLevelName, dataIndex: 'group_name' },
        {
          label: '应提票数',
          dataIndex: 'total_ewbno'
        },
        {
          label: '留仓票数',
          dataIndex: 'delay_ewbno'
        },
        {
          label: '提货留仓率',
          dataIndex: 'intime_rate',
          render: (h, val) => this.$numToPercent(val, 1)
        }
      ]
    },
    warmTableColumns() {
      return [
        [
          { label: this.nextLevelName, dataIndex: 'zone_name' },
          { label: '预计到货量', dataIndex: 'today_plan_weight' },
          { label: '库存货量', dataIndex: 'arrive_weight' },
          { label: '在途货量', dataIndex: 'onway_weight' }
        ],
        [
          { label: this.nextLevelName, dataIndex: 'zone_name' },
          {
            label: '预计到达票数',
            dataIndex: 'today_plan_votes',
            render: (h, val) => this.$numToInteger(val, 0, 1, 0)
          },
          { label: '库存票数', dataIndex: 'arrive_votes', render: (h, val) => this.$numToInteger(val, 0, 1, 0) },
          { label: '在途票数', dataIndex: 'onway_votes', render: (h, val) => this.$numToInteger(val, 0, 1, 0) }
        ],
        [
          { label: this.nextLevelName, dataIndex: 'zone_name' },
          { label: '预计到达体积', dataIndex: 'today_plan_vol' },
          { label: '库存体积', dataIndex: 'arrive_vol' },
          { label: '在途体积', dataIndex: 'onway_vol' }
        ]
      ]
    }
  },
  watch: {
    zoneCode() {
      this.init()
    },
    tabsIndex() {
      this.unit = this.getNumHandle(this.tabsIndex)
    },
    datePicker() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async getDatas() {
      const code = this.zoneCode === '001' ? '0' : this.zoneCode
      const conditonList = {
        zone_code: code
      }
      const tableName = 'sx_net_cargo_alarm_sum_rt'
      this.isLoading = true
      const { obj } = await this.sendTwoDimenRequest(tableName, this.forMapData(conditonList))
      this.isLoading = false
      this.pickupDataSource = obj
      this.setTableA(obj)
    },
    init() {
      this.getDatas()
      this.getChartDatas()
    },
    async getChartDatas() {
      const { obj } = await this._getPickData(this.datePicker)
      this.initModelChart(obj)
    },
    async getPictDataDetail() {
      this.dataSource = []
      const { obj } = await this._getPickDataDetail(this.datePicker)
      this.setTable(obj)
    },
    setTableA(list) {
      this.warmDataSource = list
    },
    setTable(objList) {
      this.dataSource = objList
    },
    initModelChart(objList) {
      const xData = []
      const sData = [[], [], []]
      if (objList && objList.length > 0) {
        this.$objectSortUp(objList, 'cal_date')
        const newList = objList.slice(-8)
        newList.forEach(item => {
          xData.push(item.cal_date)
          sData[0].push(item.total_ewbno)
          sData[1].push(item.delay_ewbno)
          sData[2].push(item.intime_rate)
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$moment(value).format('M.DD')
            },
            textStyle: {
              color: '#999999'
            }
          }
        ],
        yAxis: [
          {
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: value => this.$intFormat(value)
            },
            textStyle: {
              color: '#999999'
            }
          },
          {
            textStyle: {
              color: '#999999'
            },
            axisLabel: {
              formatter: value => this.$perFormat(value)
            },
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: sData[0],
            stack: '占比',
            itemStyle: {
              color: '#4C83F9'
            }
          },
          {
            type: 'bar',
            data: sData[1],
            stack: '占比',
            itemStyle: {
              color: '#DC1E32'
            }
          },
          {
            type: 'line',
            lineStyle: {
              color: '#22F0AF'
            },
            smooth: true,
            yAxisIndex: 1,
            data: sData[2]
          }
        ]
      }
      drawMoreBarChart(option, this.$refs['chart-model-trend'], true)

      this.tableData = {
        options: option
      }
    },
    getNumHandle(num) {
      switch (num) {
        case 0:
          return '吨'
        case 1:
          return '票'
        case 2:
          return '方'
        default:
          return ''
      }
    },
    componentDiv(v) {
      return (
        <div class={'flex_center'}>
          <VueCountUp
            style={{
              fontSize: '0.4rem',
              color: '#ffffff',
              textAlign: 'center',
              lineHeight: '0.56rem',
              fontWeight: 700
            }}
            endVal={v}
            decimals={0}
            duration={500}
          />

          <div style={{ color: '#FFF', fontSize: '0.24rem', paddingTop: '0.1rem' }}>{this.unit}</div>
        </div>
      )
    },
    countUpSpan(v) {
      return (
        <div class={'flex_center'}>
          <VueCountUp
            style={{
              fontSize: '0.4rem',
              color: '#ffffff'
            }}
            endVal={v}
            decimals={0}
            duration={500}
          />

          <div style={{ color: '#FFF', fontSize: '0.24rem', paddingTop: '0.1rem' }}>{this.unit}</div>
        </div>
      )
    },

    tabSelectClick(item, index) {
      this.tabsIndex = index
    },
    clickHandlerWarn(item, index) {
      this.warnTabIndex = index
    },
    warnClick() {
      this.warnVisible = true
      this.$sensors.webClick('实时-提货-概览详情')
    },
    clickHandlerPickUp(item, index) {
      this.picjupTabIndex = index
    },
    pickUpClick() {
      this.pickUpVisible = true
      this.$sensors.webClick('实时-提货-应提详情')
      this.getPictDataDetail()
    }
  }
}
</script>

<style lang="less" scoped>
@import url('./css/common.less');

.timeContnet {
  margin-bottom: 0.64rem;
  margin-top: 0.6rem;

  /deep/.multi_data_list_wrapper {
    width: 2.23rem;
    height: 1.66rem;
    background-color: #000a1c !important;
    background-image: linear-gradient(180deg, rgba(5, 22, 45, 0) 0%, #162e52 100%);
    border-radius: 0 0 12px 12px;
    border-bottom-color: #419bf9;

    .multi_data_list_body {
      .multi_data_list_parent {
        .list_parent_label {
          color: #999;
        }
      }
    }
  }
}
.polygon {
  background-image: url(./img/polygon.png);
  width: 3.26rem;
  height: 1.28rem;
  background-size: 3.26rem 1.28rem;
  background-color: #000a1c;
}

.subTitle {
  width: 3.26rem;
  height: 0.64rem;
  color: #fff;
  background-image: linear-gradient(270deg, rgba(4, 22, 45, 0) 0%, #0d233f 50%, rgba(22, 48, 81, 0) 100%);

  .imgIcon {
    width: 0.4rem;
    height: 0.4rem;
    margin-right: 0.16rem;
  }
}
// 图表改色

.baseFont {
  font-family: YouSheBiaoTiHei;
  opacity: 0.5;
  font-size: 0.4rem;
  color: #6bb5ff;
  letter-spacing: 0.08rem;
  text-align: center;
}
.active {
  color: #ffffff;
  opacity: 1;
}
.fontStyle {
  font-size: 0.4rem;
  color: #ffffff;
  text-align: center;
  line-height: 0.56rem;
  text-shadow: 0 0 0.16rem #66b1fa;
  font-weight: 700;
}
/deep/.num-card {
  display: flex;
  font-family: Roboto-Medium;
  font-size: 0.4rem;
  line-height: 0.4rem;
  font-weight: 700;
}
</style>
