.imgTitle {
  width: 100%;
}
// table样式
/deep/.kyd-table-outer-wrap-container {
  background-color: #132041 !important;
  .table-outer-show {
    background-color: #132041;
    color: #fff;
  }
  th {
    background-color: #132041 !important;
    color: #fff !important;
    box-shadow: inset 0 -1px 0 0 rgba(153, 153, 153, 0.5);
  }
  td {
    background-color: #132041;
    color: #fff !important;
    box-shadow: inset 0 -1px 0 0 rgba(153, 153, 153, 0.5);
  }
}

// 弹层中样式
/deep/.drawer-content {
  background-color: #132041 !important;

  .header {
    height: 0.72rem;
  }
  .content_header {
    margin-bottom: 0.5rem;
    .tabs_btn {
      font-size: 0.4rem;
      letter-spacing: 0.08rem;
      text-align: center;
      color: #ffffff;
      text-shadow: 0 2px 27px #41a6f3;
    }
  }
  .kyd-table-outer-wrap-container {
    background-color: #132041;
    .table-outer-show {
      background-color: #132041;
      color: #fff;
    }
    th {
      background-color: #132041 !important;
      color: #fff !important;
      box-shadow: inset 0 -1px 0 0 rgba(153, 153, 153, 0.5);
    }
    td {
      background-color: #132041;
      color: #fff !important;
      box-shadow: inset 0 -1px 0 0 rgba(153, 153, 153, 0.5);
    }
  }
}

// 图表改色--tooltip
/deep/.kyd_chart_model_container {
  background-color: #000a1c;
  .chart_legend_box {
    .grey333 {
      color: #fff !important;
    }
    .grey666 {
      color: #999999;
      font-size: 0.24rem;
    }
  }
}

// 查看战区
.parentBtn {
  width: 100%;
  height: 0.64rem;
  margin-top: 0.32rem;
  .btn_check {
    width: 7.1rem;
    height: 0.64rem;
    line-height: 0.64rem;
    text-align: center;
    font-size: 0.24rem;
    color: #999999;
    font-weight: 500;
    background-color: #132041;
  }
}
