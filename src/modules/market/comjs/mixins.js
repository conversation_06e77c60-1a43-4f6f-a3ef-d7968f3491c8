/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-12-27 17:00:53
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-03-22 16:23:51
 * @Description:
 */
import requestMixins from 'common/mixins/requestMixins'
import { mapState, mapMutations, mapGetters } from 'vuex'

export default {
  mixins: [requestMixins],
  computed: {
    ...mapState({
      zoneLevel: state => +state.zoneParams.zoneLevel,
      zoneCode: state => state.zoneParams.zoneCode,
      zoneName: state => state.zoneParams.zoneName,
      provinceCode: state => state.zoneParams.province_area_code,
      moduleProblems: state => state.moduleProblem,
      holidayData: state => state.holidayData,
      themeArray: state => state.themeArray,
      zoneParams: state => state.zoneParams
    }),
    ...mapState('market', {
      firstTabIndex: state => state.firstTabIndex,
      secondTabIndex: state => state.secondTabIndex
    }),
    ...mapGetters({
      zoneData: 'zoneData'
    }),
    isWangguan() {
      const code = Number(this.zoneCode)
      if (+this.zoneLevel === 4) {
        return !isNaN(code) || this.zoneName.includes('加盟区')
      }
      // if (+this.zoneLevel === 4) {
      //   return true
      // }
      return false
    },
    dateMin() {
      return this.$moment()
        .subtract(1, 'year')
        .toDate()
    },
    dateMax() {
      return this.$moment().toDate()
    }
  },
  methods: {
    ...mapMutations('market', ['setFirstTabIndex', 'setSecondTabIndex']),
    // 格式化参数
    formatParams(param = {}) {
      return Object.keys(param).map(key => ({ key, value: param[key] }))
    }
  }
}
