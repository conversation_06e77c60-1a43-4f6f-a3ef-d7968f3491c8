/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-12-27 17:00:53
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-07-20 18:47:18
 * @Description:
 */
export default {
  namespaced: true,
  name: 'market',
  state: {
    scrolltop: 0,
    firstTabIndex: 0,
    secondTabIndex: 0
  },
  mutations: {
    setScrollTop(state, data) {
      state.scrolltop = data
    },
    setFirstTabIndex(state, val) {
      state.firstTabIndex = val
    },
    setSecondTabIndex(state, val) {
      state.secondTabIndex = val
    }
  }
}
