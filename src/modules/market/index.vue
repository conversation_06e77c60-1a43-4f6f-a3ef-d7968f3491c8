<!--
 * @Author: Jie<PERSON>w
 * @Date: 2021-09-02 17:13:49
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-04-25 09:52:25
 * @Description:
-->
<template>
  <div class="page_bgc">
    <MAlertWarning v-if="marketProblem.length" :isScroll="true" type="blue">
      {{ marketText }}
    </MAlertWarning>
    <ScrollTabBar
      v-show="!isWangguan && tabData.length"
      :tabData="tabData"
      color="black"
      :tabValue="firstTabIndex"
      @change="tabChange"
    />
    <ScrollTabBar
      v-show="!isWangguan && tabDataTop.length"
      :showShadow="false"
      :showBar="false"
      :zIndex="0"
      size="md"
      color="black"
      :tabData="tabDataTop"
      :tabValue="secondTabIndex"
      @change="tabChange2"
    />
    <div class="date-container" v-if="isKgDiscount && !isWangguan">
      <KyDatePicker type="day" :dateValue="selectDate" @onChange="dateChange" :holidayData="holidayData"></KyDatePicker>
    </div>
    <!-- 网管 -->
    <Wangguan v-if="isWangguan" />
    <!-- 电商 -->
    <Ecommerce v-else-if="isEcommerce" />
    <!-- 单价折扣 -->
    <DiscountPrice v-else-if="isKgDiscount" :selectDate="selectDate" />
    <!-- 收入&货量 -->
    <PublicPage v-else-if="isPage" />
    <div v-else class="page_bgc" style="height: 100%">
      <PageContent>
        <MAlertWarning type="error">当前模块您暂无权限...</MAlertWarning>
        <KydEmtyData text="" />
      </PageContent>
    </div>
  </div>
</template>

<script>
import mixins from './comjs/mixins'

import DiscountPrice from './discountPrice'
import PublicPage from './publicPage'
// 网管
import Wangguan from './wangguan'
import Ecommerce from './ecommerce'
// 菜单权限控制,value是为了兼容以前的下标
const firstMenusList = [
  {
    label: '总体',
    value: 0,
    code: 'sxzk-market-total',
    children: [
      { label: '收入', value: 0, code: 'sxzk-market-total-shouru' },
      { label: '货量', value: 1, code: 'sxzk-market-total-cargo' }
    ]
  },
  {
    label: '加盟',
    value: 1,
    code: 'sxzk-market-jiameng',
    children: [
      { label: '收入', value: 0, code: 'sxzk-market-jiameng-shouru' },
      { label: '货量', value: 1, code: 'sxzk-market-jiameng-cargo' },
      { label: '单价折扣', value: 2, code: 'sxzk-market-jiameng-zhekou' }
    ]
  },
  {
    label: '渠道',
    value: 2,
    code: 'sxzk-market-qudao',
    children: [
      { label: '收入', value: 0, code: 'sxzk-market-qudao-shouru' },
      { label: '货量', value: 1, code: 'sxzk-market-qudao-cargo' },
      { label: '电商', value: 2, code: 'sxzk-market-qudao-dianshang' }
    ]
  }
]
export default {
  mixins: [mixins],
  components: {
    PublicPage,
    Wangguan,
    Ecommerce,
    DiscountPrice
  },
  data() {
    return {
      selectDate: this.$moment().format('YYYY-MM-DD')
    }
  },
  computed: {
    tabData() {
      const list = []
      firstMenusList.forEach(item => {
        const obj = this.themeArray.find(data => data['themeCode'] === item.code)
        if (obj) {
          list.push(item)
        }
      })
      if (list.length) {
        this.setFirstTabIndex(list[0].value)
      }
      return list
    },
    tabDataTop() {
      const list = []
      if (this.tabData) {
        const base = this.tabData.find(item => item.value === this.firstTabIndex)
        if (base) {
          base.children.forEach(item => {
            const obj = this.themeArray.find(data => data['themeCode'] === item.code)
            if (obj) {
              list.push(item)
            }
          })
        }
      }
      if (list.length) {
        this.setSecondTabIndex(list[0].value)
      }
      return list
    },
    isKgDiscount() {
      return this.firstTabIndex === 1 && this.secondTabIndex === 2 && this.tabDataTop.length
    },
    isEcommerce() {
      return this.firstTabIndex === 2 && this.secondTabIndex === 2 && this.tabDataTop.length
    },
    isPage() {
      return !this.isWangguan && !this.isEcommerce && !this.isKgDiscount && this.tabDataTop.length
    },
    marketProblem() {
      return this.moduleProblems.filter(item => item.module_code === 'sxzk-sc')
    },
    marketText() {
      return (this.moduleProblems.find(item => item.module_code === 'sxzk-sc') || {}).content
    }
  },
  watch: {
    tabData(val) {
      if (val.length) {
        this.setFirstTabIndex(val[0].value)
      }
    },
    tabDataTop(val) {
      if (val.length) {
        this.setSecondTabIndex(val[0].value)
      }
    }
  },
  created() {},
  methods: {
    tabChange({ value: val }) {
      this.setFirstTabIndex(val)
      if (this.tabDataTop.length) {
        this.setSecondTabIndex(this.tabDataTop[0].value)
      }
    },
    tabChange2({ value: val }) {
      this.setSecondTabIndex(val)
    },
    dateChange(date) {
      this.selectDate = date
    }
  },
  activated() {}
}
</script>
<style lang="less" scoped>
.date-container {
  width: 100vw;
  height: 0.8rem;
  padding-left: 0.2rem;
}
</style>
