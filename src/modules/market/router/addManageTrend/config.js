/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-06-09 14:28:10
 * @Description:
 */
import { numToPercent, numToInteger } from 'common/js/numFormat'
export default {
  settlement_unit: {
    label: '结算单价',
    unit: '元/kg',
    render: val => numToInteger(val, 3, 1, 3)
  },
  transit_unit: {
    label: '中转费单价',
    unit: '元/kg',
    tip: '含包仓与操作',
    render: val => numToInteger(val, 3, 1, 3)
  },
  discounts_unit: {
    label: '优惠中转费单价',
    unit: '元/kg',
    render: val => numToInteger(val, 3, 1, 3)
  },
  oper_unit: {
    label: '操作费单价',
    unit: '元/kg',
    render: val => numToInteger(val, 3, 1, 3)
  },
  house_unit: {
    label: '包仓单价',
    unit: '元/kg',
    render: val => numToInteger(val, 3, 1, 3)
  },
  synth_discount: {
    label: '综合折扣',
    tip: '含包仓与操作',
    render: val => numToPercent(val, 1)
  },
  transit_discount: {
    label: '中转折扣',
    tip: '含包仓与不含操作',
    render: val => numToPercent(val, 1)
  },

  sys_discount: {
    label: '系统折扣',
    tip: '不含包仓与操作',
    render: val => numToPercent(val, 1)
  },
  votes: {
    label: '开单票数',
    unit: '票',
    render: val => numToInteger(val, 0, 1)
  },
  quantity: {
    label: '开单件数',
    unit: '件',
    render: val => numToInteger(val, 0, 1)
  },
  quantity_per_votes: {
    label: '票均件数',
    unit: '件/票',
    render: val => numToInteger(val, 2, 1)
  },
  meterage_wt_per_votes: {
    label: '票均重量',
    unit: 'kg/票',
    render: val => numToInteger(val, 0, 1)
  },
  settlement_fee_per_votes: {
    label: '票均结算收入',
    unit: '元/票',
    render: val => numToInteger(val, 0, 1)
  },
  real_rate: {
    label: '实际泡比',
    render: val => numToInteger(val, 0, 1)
  },
  fee_rate: {
    label: '计费泡比',
    render: val => numToInteger(val, 0, 1)
  },
  in_center_meterage_wt_rate: {
    label: '同分拨货量占比',
    render: val => numToPercent(val, 1)
  },
  in_prov_meterage_wt_rate: {
    label: '省内货量占比',
    render: val => numToPercent(val, 1)
  },
  out_prov_meterage_wt_rate: {
    label: '跨省货量占比',
    render: val => numToPercent(val, 1)
  },
  month_res_adj_unit: {
    label: '资源调节费',
    tip: '元/kg',
    render: val => numToInteger(val, 3, 1, 3)
  }
}
