<template>
  <div class="add-manage-trend-wrap">
    <HeadBar :showPublicSelect="false" title="加盟管理" :rightName="rightName"></HeadBar>
    <TitleTabs style="background: #fff" :tabData="tabData" :tabValue="tabValue" @change="tabChange"></TitleTabs>
    <div class="pd20" style="background: #fff">
      <RadiusButton :buttonData="option" :activeIndex="activeIndex" @click="btnConfirm" />
    </div>

    <div class="whole-net">
      <new-header :sp="false" :title="`${spRightName}趋势`">
        <div class="tip">{{ tip }}</div>
      </new-header>
      <!-- <common-legend :num="1" style="margin-top: 0.1rem" :inline="true" :columns="columns" :dataSource="dataSource" /> -->
      <div class="chart-box" ref="recent-chart-box"></div>
      <new-header
        :title="`${name}排名情况(${month}月累计值)`"
        :sp="false"
        v-show="zoneLevel <= 2"
        style="margin-top: 0.55rem"
      />
      <hScroll :width="nextZoneWidth">
        <div v-show="zoneLevel <= 2" class="chart-box" ref="next-zone-chart-box"></div>
      </hScroll>
    </div>
  </div>
</template>

<script>
import newHeader from '../../components/newHeader'
import HeadBar from 'common/components/head'
import config from './config'
// import commonLegend from 'common/components/commonLegend'
import requestMixins from 'common/mixins/requestMixins'

import { drawLineChart, drawBarChart } from 'common/charts/chartOption'
export default {
  mixins: [requestMixins],
  components: {
    HeadBar,
    newHeader
    // commonLegend
  },
  created() {
    this.tabValue = this.$route.query.tabValue
    this.fieldCode = this.$route.query.value
    this.zoneCode = this.$route.query.zoneCode
    this.zoneLevel = this.$route.query.zoneLevel
    this.rightName = this.$route.query.rightName
    const target = config[this.fieldCode]
    const label = target.label
    this.activeIndex = this.option.indexOf(label)
  },
  computed: {
    option() {
      switch (this.tabValue) {
        case '单价':
          return this.djOption
        case '折扣率':
          return this.zkOption
        case '泡比':
          return this.pbOption
        case '票件比':
          return this.pjOption
        case '长短线':
          return this.cdxOption
        default:
          return []
      }
    },
    tip() {
      let tip
      const label = this.option[this.activeIndex]
      for (const key in config) {
        if (config[key].label === label) {
          tip = config[key].tip ? `注：${config[key].tip}` : ''
        }
      }
      return tip
    },
    columns() {
      let render
      let dataIndex
      let unit
      const label = this.option[this.activeIndex]
      for (const key in config) {
        if (config[key].label === label) {
          dataIndex = `current_${key}`
          render = config[key].render
          unit = config[key].unit ? `(${config[key].unit || ''})` : ''
        }
      }
      return [
        {
          icon: <div class="blue-rect"></div>,
          label: `${label}${unit}`,
          dataIndex,
          render
        }
      ]
    },
    month() {
      return this.$moment()
        .subtract(1, 'days')
        .format('M')
    },
    spRightName() {
      if (!this.zoneLevel) {
        return '全网'
      } else {
        return (this.rightName || '').replace(/SX/g, '')
      }
    },
    name() {
      switch (+this.zoneLevel) {
        case 0:
          return '省区'
        case 2:
          return '区域'
        default:
          return ''
      }
    }
  },
  data() {
    return {
      tabData: [
        { label: '单价', value: '单价' },
        { label: '折扣率', value: '折扣率' },
        { label: '票件比', value: '票件比' },
        { label: '泡比', value: '泡比' },
        { label: '长短线', value: '长短线' }
      ],
      djOption: ['结算单价', '中转费单价', '优惠中转费单价', '操作费单价', '包仓单价', '资源调节费'],
      zkOption: ['综合折扣', '中转折扣', '系统折扣'],
      pjOption: ['开单票数', '开单件数', '票均件数', '票均重量', '票均结算收入'],
      pbOption: ['实际泡比', '计费泡比'],
      cdxOption: ['同分拨货量占比', '省内货量占比', '跨省货量占比'],
      tabValue: '',
      activeIndex: 0,
      fieldCode: '',
      dataSource: {},
      rencentData: [],
      zoneCode: '',
      zoneLevel: '',
      rightName: '',
      nextZoneData: [],
      nextZoneWidth: '100vw'
    }
  },
  mounted() {
    this.getRencentData()
    this.zoneLevel <= 2 && this.getNextZoneData()
    // 神策 页面浏览
    this.$sensors.pageview('加盟管理')
  },
  methods: {
    getRencentData() {
      const tableName = 'sx_manage_monit_info_22'
      const endDay = this.$moment()
        .subtract(1, 'days')
        .format('YYYYMMDD')
      const startDay = this.$moment(endDay)
        .subtract(8, 'days')
        .format('YYYYMMDD')
      const zoneLevel = '3' + this.zoneLevel
      const fzbData = this.zoneLevel ? [{ key: this.getKey(zoneLevel), value: this.zoneCode }] : []
      const conditionList = [
        { key: 'startDay', value: startDay },
        { key: 'endDay', value: endDay },
        { key: 'zone_level', value: zoneLevel },
        ...fzbData
      ]
      this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        this.rencentData = res.obj.sort((a, b) => a.bus_date - b.bus_date)
        this.handleData()
        console.log(JSON.parse(JSON.stringify(this.rencentData)), 'rencentData0---------')
      })
    },
    getNextZoneData() {
      const tableName = 'sx_manage_monit_info_22'
      const endDay = this.$moment()
        .subtract(1, 'days')
        .format('YYYYMMDD')
      const startDay = endDay
      const zoneLevel = '3' + this.getNextLevel(this.zoneLevel)
      const fzbData = this.zoneLevel ? [{ key: this.getKey('3' + this.zoneLevel), value: this.zoneCode }] : []
      const conditionList = [
        { key: 'startDay', value: startDay },
        { key: 'endDay', value: endDay },
        { key: 'zone_level', value: zoneLevel },
        ...fzbData
      ]
      this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        this.nextZoneData = res.obj
        this.handleNextZoneData()
      })
    },
    handleNextZoneData() {
      const zone_code = (this.getKey('3' + (Number(this.zoneLevel) + 1)) || '').replace('code', 'name')
      let dataIndex
      let render
      for (const key in config) {
        if (config[key].label === this.option[this.activeIndex]) {
          dataIndex = `current_${key}`
          render = config[key].render
        }
      }
      this.nextZoneData.sort((a, b) => b[dataIndex] - a[dataIndex])
      const axisData = this.nextZoneData.map(item => (item[zone_code] || '').replace('SX', ''))
      const barData = this.nextZoneData.map(item => item[dataIndex])
      this.nextZoneWidth = barData.length <= 6 ? '100%' : (7.5 / 6) * barData.length + 'rem'
      const option = {
        xAxis: [
          {
            data: axisData
          },
          {
            data: barData,
            axisLabel: {
              formatter: val => render(val)
            }
          }
        ],
        series: [
          {
            data: barData
          }
        ]
      }
      drawBarChart(option, this.$refs['next-zone-chart-box'])
    },
    getNextLevel(level) {
      switch (level) {
        case 0:
          return '2'
        case 2:
          return '3'
        default:
          return '3'
      }
    },
    getKey(val) {
      switch (val) {
        case '31':
          return 'big_area_code'
        case '32':
          return 'province_area_code'
        case '33':
          return 'area_code'
        case '34':
          return 'dept_code'
        default:
          return ''
      }
    },
    handleData() {
      this.dataSource = this.rencentData[this.rencentData.length - 1] || {}
      let dataIndex
      let render
      for (const key in config) {
        if (config[key].label === this.option[this.activeIndex]) {
          dataIndex = key
          render = config[key].render
        }
      }
      const axisData = this.rencentData.map(item => this.$moment(item.bus_date).format('M.D'))
      const lineData = this.rencentData.map(item => item[dataIndex])

      const option = {
        tooltip: {
          formatter: params => {
            this.dataSource = {
              [dataIndex]: params[0].value
            }
          }
        },
        xAxis: [
          {
            data: axisData
          }
        ],
        grid: {
          containLabel: false,
          top: 25,
          bottom: 20
        },
        series: [
          {
            showSymbol: true,
            data: lineData,
            label: {
              show: true,
              formatter: val => render(val.data),
              textStyle: {
                color: '#333'
              }
            },
            areaStyle: {
              show: true,
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#2E55EC'
                },
                {
                  offset: 1,
                  color: '#FFFFFF'
                }
              ])
            }
          }
        ]
      }
      drawLineChart(option, this.$refs['recent-chart-box'])
    },
    tabChange(val) {
      this.tabValue = val
      this.activeIndex = 0
      this.handleData()
      this.zoneLevel <= 2 && this.handleNextZoneData()
      // 神策点击
      this.$sensors.webClick(`加盟管理-${val}`)
    },
    btnConfirm(index, item) {
      this.activeIndex = index
      this.handleData()
      this.zoneLevel <= 2 && this.handleNextZoneData()
      // 神策点击
      this.$sensors.webClick(`加盟管理-${this.tabValue}-${item}`)
    }
  }
}
</script>

<style lang="less" scoped>
@import url('./index.less');
.add-manage-trend-wrap {
  background: #f5f5f5;
  .whole-net {
    margin-top: 0.2rem;
    background: #fff;
    padding: 0 0.2rem 0.4rem;
    .tip {
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      color: #828a99;
    }
  }
  .chart-box {
    height: 3rem;
    width: 100%;
    // margin-top: 0.24rem;
  }
}
</style>
