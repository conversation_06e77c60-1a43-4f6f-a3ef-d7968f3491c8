<template>
  <CardList title="业务类型维度收入分析" class="mt20">
    <Tabs slot="nav" :options="['日', '月']" :tabIndex="tabIndex" @tabSelect="tabSelect" />
    <TitleTabs class="mt32" :tabData="tabData" :tabValue="tabValue" @change="tabChange" />
    <div class="pd_lr20 mt32">
      <CardTabs :activeIndex="activeIndex" :tabTitle="[`加盟(${unit})`, `新业务(${unit})`]" :columns="columns" :dataSource="dataSource" @change="cardTabChange" />
    </div>
    <TitleTabs class="mt40" :tabData="tabData2" :tabValue="tabValue2" @change="tabChange2" />
    <div class="pd_lr20">
      <div v-show="!tabValue2">
        <ProgressPie :columns="columns2" :dataSource="[dataSource]" />
      </div>
      <div v-show="tabValue2">
        <CommonLegend class="mt24" :columns="columns3" :dataSource="dataSource3" inline :num="2" />
        <div class="ht_chart" ref="income-chart-box"></div>
        <BtnShowMore>
          <NormalTable
            :dataSource="tableDataSource"
            :columns="tableColumns"
            :width="tableWidth"
            size="little"
            minHeight="1rem"
            isIndicator
          ></NormalTable>
        </BtnShowMore>
      </div>
    </div>
  </CardList>
</template>

<script>
import mixins from '../comjs/mixins'
import { drawScrollChart } from 'common/charts/chartOption'
import CardTabs from 'common/components/cardTabs'
import ProgressPie from 'common/components/progressPie'
export default {
  mixins: [mixins],
  components: {
    CardTabs,
    ProgressPie
  },
  data() {
    return {
      tabIndex: 0,
      tabValue: 0,
      tabData: [
        { label: '收入', value: 0 },
        { label: '货量', value: 1 }
      ],
      activeIndex: 0,
      tabValue2: 0,
      dataSource: {},
      wangguanMonthData: [],
      wangguanDayData: [],
      tableColumns: [],
      tableDataSource: [],
      tableWidth: '100%',
      dataSource3: {}
    }
  },
  computed: {
    unit() {
      return ['万', '吨'][this.tabValue]
    },
    columns() {
      const text = ['income', 'weight'][this.tabValue]
      return {
        parent: {
          dataIndex: [`league_${text}`, `new_business_${text}`],
          render: val => this.$numToInteger(val, 2, this.tabValue ? 1000 : 10000)
        }
      }
    },
    tabData2() {
      const text = ['日', '月'][this.tabIndex]
      return [
        { label: `当${text}占比`, value: 0 },
        { label: `${text}度趋势`, value: 1 }
      ]
    },
    columns2() {
      const text = ['income', 'weight'][this.tabValue]
      return {
        labelDataIndex: 'label',
        zbDataIndex: [`league_${text}_rate`, `new_business_${text}_rate`][this.activeIndex],
        zbRender: val => this.$numToPercent(val, 1),
        valueDataIndex: [`league_${text}`, `new_business_${text}`][this.activeIndex],
        valueRender: val => `${this.$numToInteger(val, 0, 1)}${['元', 'kg'][this.tabValue]}`
      }
    },
    columns3() {
      const text = ['income', 'weight'][this.tabValue]
      return [
        {
          icon: <div class='rect bgblue'></div>,
          label: ['收入(万元)', '货量(吨)'][this.tabValue],
          dataIndex: [`league_${text}`, `new_business_${text}`][this.activeIndex],
          render: val => this.$numToInteger(val, 2, this.tabValue ? 1000 : 10000)
        },
        {
          icon: <div class='rect bgorange'></div>,
          label: '占比',
          dataIndex: [`league_${text}_rate`, `new_business_${text}_rate`][this.activeIndex],
          render: val => this.$numToPercent(val, 1)
        }
      ]
    }
  },
  watch: {
    zoneCode() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      await Promise.all([this.getWangguanDayData(), this.getWangGuanMonthData()])
      this.handlerData()
    },
    getWangguanDayData() {
      const endDate = this.$moment().subtract(1, 'days').format('YYYYMMDD')
      const startDate = this.$moment(endDate).subtract(60, 'days').format('YYYYMMDD')
      // const tableName = 'manager_business_analyse_day'
      const url = '/cockpit/reportrest/report/twoDimen/manager_business_analyse_day'
      const conditionList = [
        { key: 'manager_id', value: this.zoneCode },
        { key: 'start_date', value: startDate },
        { key: 'end_date', value: endDate }
      ]
      return this.sendTwoDimenRequestNew(url, conditionList).then(res => {
        this.wangguanDayData = res.obj
        this.$objectSortUp(this.wangguanDayData, 'inc_day')
      })
    },
    getWangGuanMonthData() {
      const endDate = this.$moment().subtract(1, 'days').format('YYYYMM')
      const startDate = this.$moment(endDate).subtract(12, 'months').format('YYYYMM')
      // const tableName = 'manager_business_analyse_month'
      const url = '/cockpit/reportrest/report/twoDimen/manager_business_analyse_month'
      const conditionList = [
        { key: 'manager_id', value: this.zoneCode },
        { key: 'start_date', value: startDate },
        { key: 'end_date', value: endDate }
      ]
      return this.sendTwoDimenRequestNew(url, conditionList).then(res => {
        this.wangguanMonthData = res.obj
        this.$objectSortUp(this.wangguanMonthData, 'inc_day')
      })
    },
    handlerData() {
      const target = this.tabIndex ? this.wangguanMonthData : this.wangguanDayData
      this.dataSource = target[target.length - 1] || {}
      this.tabValue2 ? this.handlerTrendData() : this.handlerProgressData()
    },
    handlerProgressData() {
      this.dataSource.label = ['加盟', '新业务'][this.activeIndex]
    },
    handlerTrendData() {
      const text = ['income', 'weight'][this.tabValue]
      const target = this.tabIndex ? this.wangguanMonthData : this.wangguanDayData
      this.dataSource3 = target[target.length - 1] || {}
      const barData = target.map(item => ({
        value: item[[`league_${text}`, `new_business_${text}`][this.activeIndex]],
        ...item
      }))
      const lineData = target.map(item => ({
        value: item[[`league_${text}_rate`, `new_business_${text}_rate`][this.activeIndex]],
        ...item
      }))
      const xAxisData = target.map(item => item.inc_day)
      const res = this.$setTable(target, this.columns3, 'inc_day', ['day', 'month'][this.tabIndex])
      this.tableColumns = res[0]
      this.tableDataSource = res[1]
      this.tableWidth = res[2]
      const option = {
        tooltip: {
          formatter: params => {
            this.dataSource3 = (params[0] || {}).data || {}
          }
        },
        xAxis: [
          {
            data: xAxisData,
            axisLabel: {
              formatter: val => this.$dateFormat(val, this.tabIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: val => this.$numToInteger(val, 0, this.tabValue ? 1000 : 10000)
            }
          },
          {
            axisLabel: {
              formatter: val => this.$numToPercent(val, 0)
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: barData,
            yAxisIndex: 0
          },
          {
            type: 'line',
            data: lineData,
            yAxisIndex: 1
          }
        ]
      }
      drawScrollChart(option, this.$refs['income-chart-box'])
    },
    tabSelect(val) {
      this.tabIndex = val
      this.handlerData()
    },
    tabChange(val) {
      this.tabValue = val
      this.handlerData()
    },
    tabChange2(val) {
      this.tabValue2 = val
      this.handlerData()
    },
    cardTabChange(val) {
      this.activeIndex = val
      this.handlerData()
    }
  }
}
</script>

<style lang="less" scoped>

</style>
