<template>
  <div class="modal-data-wrappper">
    <div class="flex_start top">
      <div class="left-box">
        <div class="main-value" v-if="columns.dataIndex1">
          <span class="fs24">{{ columns.label1 }}</span>
          <div>
            <span class="fs48">{{ columns.render1(dataSource[columns.dataIndex1]) }}</span>
            <span v-if="columns.dataIndex12">{{ columns.label12 }}</span>
          </div>
        </div>
        <div class="per-value">
          <span v-if="columns.dataIndex3" class="fs20 fw400">{{ columns.label3 }}</span>
          <span
            v-if="columns.dataIndex3"
            :class="dataSource[columns.dataIndex3] >= 0 ? 'fontsuccess' : 'fontfail'"
            class="fs20 fw500"
            >{{ columns.render3(dataSource[columns.dataIndex3]) }}</span
          >
          <span v-if="columns.dataIndex4" class="fs20 fw400">{{ columns.label4 }}</span>
          <span v-if="columns.dataIndex4" class="fs20 fw500">{{
            columns.render4(dataSource[columns.dataIndex4])
          }}</span>
        </div>
        <div class="max-value">
          <span class="fs24" v-if="columns.dataIndex7">{{ columns.label7 }}</span>
          <span class="fs24" v-if="columns.dataIndex7">{{ columns.render7(dataSource[columns.dataIndex7]) }}</span>
          <span class="date fs20" v-if="columns.dataIndex8">{{ columns.render8(dataSource[columns.dataIndex8]) }}</span>
        </div>
        <div style="height: 0.33rem; margin-top: 0.16rem;" v-if="columns.dataIndex11">
        </div>
      </div>
      <div class="right-box">
        <div class="main-value" v-if="columns.dataIndex2">
          <span class="fs24">{{ columns.label2 }}</span>
          <span class="fs48">{{ columns.render2(dataSource[columns.dataIndex2]) }}</span>
        </div>
        <div class="per-value">
          <span v-if="columns.dataIndex5" class="fs20 fw400">{{ columns.label5 }}</span>
          <span
            v-if="columns.dataIndex5"
            :class="dataSource[columns.dataIndex5] >= 0 ? 'fontsuccess' : 'fontfail'"
            class="fs20 fw500"
            >{{ columns.render5(dataSource[columns.dataIndex5]) }}</span
          >
          <span v-if="columns.dataIndex6" class="fs20 fw400">{{ columns.label6 }}</span>
          <span v-if="columns.dataIndex6" class="fs20 fw500">{{
            columns.render6(dataSource[columns.dataIndex6])
          }}</span>
        </div>
        <div class="max-value">
          <span class="fs24" v-if="columns.dataIndex9">{{ columns.label9 }}</span>
          <span class="fs24" v-if="columns.dataIndex9">{{ columns.render9(dataSource[columns.dataIndex9]) }}</span>
          <span class="date fs20" v-if="columns.dataIndex10">{{ columns.render10(dataSource[columns.dataIndex10]) }}</span>
        </div>
        <div class="today-forecast" v-if="columns.dataIndex11">
          <span class="fs24">{{ columns.label11 }}</span>
          <span class="fs24 ml8">{{ columns.render9(dataSource[columns.dataIndex11]) }}</span>
        </div>
      </div>
    </div>
    <div v-if="showIcon" class="dia_text" @click="showIconVisi">
      {{ `统计口径` }} <i class="iconfont icon-help" style="font-size:0.24rem"></i>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    columns: Object,
    dataSource: Object,
    tabValue: Number,
    showIcon: {
      type: Boolean,
      default: false
    }
  },
  computed: {},
  methods: {
    showIconVisi() {
      this.$emit('showTip')
    }
  }
}
</script>

<style lang="less" scoped>
.modal-data-wrappper {
  position: relative;
  transition: 100ms;
  width: 100%;
  background-color: #2e3f63;
  color: #fff;
  border-radius: 0.1rem;
  box-shadow: 0px 3px 4px 3px rgba(0, 0, 0, 0.05);

  .left-box,
  .right-box {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    &:last-child {
      padding-left: 0.28rem;
    }
  }

  .top,
  .bottom {
    padding: 0.28rem;
  }
  .bottom {
    border-top: 0.01rem solid rgba(255, 255, 255, 0.2);
  }

  .main-value {
    display: flex;
    flex-direction: column;
    > span:first-child {
      font-family: PingFangSC-Regular;
      color: #ffffff;
      font-weight: 400;
      line-height: 0.33rem;
    }
    > span:last-child {
      font-family: Roboto-Medium;
      color: #ffffff;
      font-weight: 400;
      line-height: 0.57rem;
      margin: 0.16rem 0;
    }
    > div:last-child {
      display: flex;

      > span:first-child {
        font-family: Roboto-Medium;
        color: #ffffff;
        font-weight: 400;
        line-height: 0.57rem;
        margin: 0.16rem 0;
      }
      > span:last-child {
        margin-left: 0.4rem;
        font-family: PingFangSC-Regular;
        font-size: 0.24rem;
        color: #ddd;
        padding-top: 0.38rem;
      }
    }
  }
  .main-value2 {
    margin: 0 0 0.16rem;
    span:first-child {
      font-family: PingFangSC-Regular;
      color: #ffffff;
      font-weight: 400;
      line-height: 0.33rem;
      margin-right: 0.16rem;
    }
    span:last-child {
      font-family: Roboto-Medium;
      color: #ffffff;
      font-weight: 400;
      line-height: 0.33rem;
    }
  }
  .per-value {
    margin-bottom: 0.16rem;
    span:first-child,
    span:nth-child(3) {
      font-family: PingFangSC-Regular;
      color: #ddd;
      line-height: 0.28rem;
    }
    span:last-child,
    span:nth-child(2) {
      font-family: PingFangSC-Medium;
      line-height: 0.28rem;
      margin-left: 0.08rem;
    }
    span:nth-child(2) {
      margin-right: 0.24rem;
    }
  }
  .max-value {
    display: flex;
    align-items: center;
    span:first-child {
      font-family: PingFangSC-Regular;
      color: #ddd;
      line-height: 0.33rem;
    }
    span:nth-child(2) {
      font-family: PingFangSC-Medium;
      color: #fff;
      line-height: 0.33rem;
      margin: 0 0.16rem 0 0.08rem;
    }
    .date {
      height: 0.32rem;
      width: 0.82rem;
      border-radius: 0.16rem;
      background: rgba(190, 190, 206, 0.5);
      text-align: center;
      line-height: 0.32rem;
    }
  }

  .today-forecast {
    margin-top: 0.16rem;

    span:first-child {
      font-family: PingFangSC-Regular;
      font-size: 0.24rem;
      color: #ddd;
      line-height: 0.33rem;
    }
  }

  .dia_text {
    background: url('../images/realtime_bg.png') no-repeat;
    background-size: cover;
    height: 0.48rem;
    width: 1.56rem;
    position: absolute;
    color: #fff;
    top: 0rem;
    right: 0rem;
    text-align: center;
    line-height: 0.48rem;
  }
}
.customise-lunar {
  border-radius: 0.08rem;
  background: #233252 !important;
  /deep/.content {
    &::before {
      background: rgba(213, 216, 223, 0.1);
    }
    > div {
      > div {
        color: rgba(255, 255, 255, 0.8);
      }
      span {
        color: #fff !important;
      }
    }
  }
}
</style>
