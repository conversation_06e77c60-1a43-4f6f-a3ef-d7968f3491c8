<template>
  <div class="realtime-container">
    <div class="date-container">
      <KyDatePicker type="day" :dateValue="selectDate" @onChange="dateChange" :holidayData="holidayData"></KyDatePicker>
    </div>
    <div class="realtime-box-wrapper">
      <TitleTabs v-show="todayBool" :tabData="tabData" :tabValue="tabValue" @change="tabChange" />
      <div class="pd_lr20 pdt24">
        <Card :showIcon="true" :tabValue="1" :columns="columns2" :dataSource="realtimeData" @showTip="showTip" />
      </div>
    </div>
    <KyDataCommentsModel
      :isShow="tipVisible"
      @close="tipVisible = false"
      :isFeedback="isFeedback"
      @feedback="btnFeedback"
    >
      <ExplainContent :detail="explaindDetail"></ExplainContent>
    </KyDataCommentsModel>
  </div>
</template>

<script>
import Card from './card'
import mixins from '../comjs/mixins'
export default {
  mixins: [mixins],
  props: {
    baseData: Object
  },
  components: {
    Card
  },
  data() {
    return {
      selectDate: this.$moment().format('YYYY-MM-DD'),
      realtimeData: {},
      pasttimeData: {},
      tabData: [
        { label: '实时战况', value: 0 },
        { label: '昨日战况', value: 1 }
      ],
      tabValue: 0,
      tipVisible: false
    }
  },
  computed: {
    dateMin() {
      return this.$moment()
        .subtract(1, 'year')
        .toDate()
    },
    dateMax() {
      return this.$moment()
        .subtract(0, 'month')
        .toDate()
    },
    todayBool() {
      return this.$moment(this.selectDate).format('YYYYMMDD') === this.$moment().format('YYYYMMDD')
    },
    realtimeBool() {
      return this.todayBool && !this.tabValue
    },
    columns2() {
      const col = {
        label1: `${!this.todayBool ? '' : ['实时', '昨日'][this.tabValue]}收入(万元)`,
        dataIndex1: this.realtimeBool ? 'income' : 'old_income',
        render1: val => this.$numToInteger(val, 2, 1),
        label2: `${!this.todayBool ? '' : ['实时', '昨日'][this.tabValue]}货量(吨)`,
        dataIndex2: this.realtimeBool ? 'weight' : 'old_weight',
        render2: val => this.$numToInteger(val, 1, 1),
        label12: '',
        dataIndex12: 'notContainer'
      }
      const forecast = {
        label3: '周环比',
        dataIndex3: this.realtimeBool ? 'incomeIncRate' : 'income_inc_rate',
        render3: val => this.$numToPercent(val, 1),
        label5: '周环比',
        dataIndex5: this.realtimeBool ? 'weightIncRate' : 'weight_inc_rate',
        render5: val => this.$numToPercent(val, 1),
        label7: '历史峰值',
        dataIndex7: 'max_income',
        render7: val => this.$numToInteger(val, 2, 10000),
        label8: '',
        dataIndex8: 'max_income_day',
        render8: val => (val ? this.$moment(val).format('MM.DD') : '-'),
        label9: '历史峰值',
        dataIndex9: 'max_weight',
        render9: val => this.$numToInteger(val, 1, 1000),
        label10: '',
        dataIndex10: 'max_weight_day',
        render10: val => (val ? this.$moment(val).format('MM.DD') : '-')
      }
      return { ...col, ...forecast }
    }
  },
  watch: {
    baseData(val) {
      this.realtimeData = val
    },
    selectDate(val) {
      this.$nextTick(() => {
        this.$refs.slide && this.$refs.slide.refresh()
        this.$refs.slide && this.$refs.slide._goToPage(this.tabValue)
      })
    }
  },
  mounted() {},
  activated() {
    this.$nextTick(() => {
      this.$refs.slide && this.$refs.slide.refresh()
      this.$refs.slide && this.$refs.slide._goToPage(this.tabValue)
    })
  },
  methods: {
    dateChange(date) {
      this.selectDate = date
      this.$emit('dateChange', date)
    },
    changeSlide(val) {
      this.tabValue = val
      this.$refs.slide && this.$refs.slide._goToPage(val)
    },
    tabChange(val) {
      this.tabValue = val
      this.$refs.slide && this.$refs.slide._goToPage(val)
    },
    showTip() {
      this.tipVisible = true
      // 名词解释反馈接口
      this.getExplainData(this.realtimeBool ? 'sxzk-sc-sszk-wg' : 'sxzk-sc-zrzk-wg')
    }
  }
}
</script>

<style lang="less" scoped>
.realtime-container {
  .date-container {
    width: 100vw;
    height: 0.8rem;
    padding-left: 0.2rem;
    background-color: #fff;
    margin-top: 0.16rem;
  }

  .realtime-box-wrapper {
    background-color: #fff;
    padding-bottom: 0.2rem;
  }
}
.model-dia-content {
  margin-top: 0.2rem;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  line-height: 0.4rem;
}
</style>
