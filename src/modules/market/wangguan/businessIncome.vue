<template>
  <CardList title="整体业务收入分析" class="mt20">
    <Tabs slot="nav" :options="['日', '月']" :tabIndex="tabIndex" @tabSelect="tabSelect" />
    <div class="pd_lr20">
      <CommonLegend class="mt24" :columns="incomeColumns" :dataSource="incomeData" inline :num="2" />
      <div class="ht_chart" ref="income-chart-box"></div>
      <BtnShowMore class="pd_lr20">
        <normalTable
          :dataSource="incomeTableSource"
          :columns="incomeTableColumns"
          :width="incomeWidth"
          size="little"
          minHeight="1rem"
          isIndicator
        ></normalTable>
      </BtnShowMore>
    </div>
  </CardList>
</template>

<script>
import { drawScrollChart } from 'common/charts/chartOption'
import mixins from '../comjs/mixins'
export default {
  mixins: [mixins],
  computed: {
    roleKeyMap() {
      return {
        0: 'qwCode',
        31: 'bigAreaCode',
        2: 'provinceAreaCode',
        3: 'areaCode',
        4: 'deptCode'
      }
    },
    roleKeyMapM() {
      return {
        0: 'qwCode',
        31: 'zoneCode',
        2: 'zoneCode',
        3: 'zoneCode',
        4: 'zoneCode'
      }
    },
    incomeColumns() {
      return [
        {
          icon: 'blue',
          label: '收入(万元)',
          dataIndex: 'income',
          render: val => this.$numToInteger(val, 2, 10000)
        },
        {
          icon: 'orange',
          label: '货量(吨)',
          dataIndex: 'weight',
          render: val => this.$numToInteger(val, 2, 1000)
        }
      ]
    }
  },
  data() {
    return {
      tabData: [
        { label: '日', value: 'day' },
        { label: '月', value: 'month' }
      ],
      tabIndex: 0,
      incomeData: {},
      incomeTableSource: [],
      incomeTableColumns: [],
      incomeWidth: '100%',
      dayData: [],
      monthData: [],
      wangguanDayData: [],
      wangguanMonthData: []
    }
  },
  watch: {
    zoneCode() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      await Promise.all([this.getWangguanDayData(), this.getWangGuanMonthData()])
      this.handlerWangguanData()
    },
    getWangguanDayData() {
      const endDate = this.$moment().subtract(1, 'days').format('YYYYMMDD')
      const startDate = this.$moment(endDate).subtract(60, 'days').format('YYYYMMDD')
      // const tableName = 'manager_business_analyse_day'
      const url = '/cockpit/reportrest/report/twoDimen/manager_business_analyse_day'
      const conditionList = [
        { key: 'manager_id', value: this.zoneCode },
        { key: 'start_date', value: startDate },
        { key: 'end_date', value: endDate }
      ]
      // return this.sendTwoDimenRequest(tableName, conditionList).then(res => {
      //   this.wangguanDayData = res.obj
      // })
      return this.sendTwoDimenRequestNew(url, conditionList).then(res => {
        this.wangguanDayData = res.obj
      })
    },
    getWangGuanMonthData() {
      const endDate = this.$moment().subtract(1, 'days').format('YYYYMM')
      const startDate = this.$moment(endDate).subtract(12, 'months').format('YYYYMM')
      // const tableName = 'manager_business_analyse_month'
      const url = '/cockpit/reportrest/report/twoDimen/manager_business_analyse_month'
      const conditionList = [
        { key: 'manager_id', value: this.zoneCode },
        { key: 'start_date', value: startDate },
        { key: 'end_date', value: endDate }
      ]
      return this.sendTwoDimenRequestNew(url, conditionList).then(res => {
        this.wangguanMonthData = res.obj
      })
    },
    handlerWangguanData() {
      const target = this.tabIndex ? this.wangguanMonthData : this.wangguanDayData
      this.incomeData = target[target.length - 1] || {}
      const barData = target.map(item => ({
        value: item.income,
        ...item
      }))
      const lineData = target.map(item => ({
        value: item.weight,
        ...item
      }))
      const xAxisData = target.map(item => item.inc_day)
      const res = this.$setTable(target, this.incomeColumns, 'inc_day', ['day', 'month'][this.tabIndex])
      this.incomeTableColumns = res[0]
      this.incomeTableSource = res[1]
      this.incomeWidth = res[2]
      const option = {
        tooltip: {
          formatter: params => {
            this.incomeData = (params[0] || {}).data || {}
          }
        },
        xAxis: [
          {
            data: xAxisData,
            axisLabel: {
              formatter: val => this.$dateFormat(val, this.tabIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: val => this.$numToInteger(val, 0, 10000)
            }
          },
          {
            axisLabel: {
              formatter: val => this.$numToInteger(val, 0, 1000)
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: barData,
            yAxisIndex: 0
          },
          {
            type: 'line',
            data: lineData,
            yAxisIndex: 1
          }
        ]
      }
      drawScrollChart(option, this.$refs['income-chart-box'])
    },
    tabSelect(val) {
      this.tabIndex = val
      this.handlerWangguanData()
    }
  }
}
</script>

<style lang="less" scoped>
@import url('./index.less');
.business-income-wrap {
  background: #fff;
  margin-top: 0.24rem;
  padding-bottom: 0.24rem;

  .chart-box {
    height: 3rem;
    width: 100%;
    margin-top: 0.32rem;
    padding: 0 0.2rem;
  }
}
</style>
