<template>
  <div class="new-index-wrap page_bgc">
    <PageContent ref="contentWrapper">
      <RealtimeBoxNew :baseData="baseData" @dateChange="dateChange" :zoneLevel="zoneLevel" />
      <NetRank />
      <CompleteRatio :baseData="gaugeData" :indexDate="indexDate" />
      <BusinessIncome />
      <!-- <BusinessTypeAnalyze /> -->
      <NetWeightRank />
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>

<script>
import BusinessIncome from './businessIncome'
// import BusinessTypeAnalyze from './businessTypeAnalyze.vue'
import RealtimeBoxNew from './realtimeBoxNew'
import CompleteRatio from './completeRatio'
import NetWeightRank from './netWeightRank'
import flowLayout from 'common/mixins/flowLayout.js'
import NetRank from './netRank'
import mixins from '../comjs/mixins'
import { mapState, mapMutations } from 'vuex'
export default {
  mixins: [flowLayout, mixins],
  props: {
    defaultDate: {
      type: String
    }
  },
  data() {
    return {
      indexDate: '',
      baseData: {},
      gaugeData: {},
      timeId: ''
    }
  },
  computed: {
    isToday() {
      return this.indexDate === this.$moment().format('YYYYMMDD')
    },
    ...mapState('market', {
      scrollTops: state => state.scrolltop
    })
  },
  components: {
    BusinessIncome,
    RealtimeBoxNew,
    CompleteRatio,
    NetWeightRank,
    // BusinessTypeAnalyze,
    NetRank
  },
  watch: {
    zoneCode() {
      clearInterval(this.timeId)
      if (this.isToday) {
        this.timeId = setInterval(() => {
          this.getWgRealtimeData(false)
        }, 60000)
      }
      this.getWgRealtimeData()
      this.getWangguanGaugeData()
    },
    indexDate(newVal, oldVal) {
      if (oldVal) {
        clearInterval(this.timeId)
        if (this.isToday) {
          this.timeId = setInterval(() => {
            this.getWgRealtimeData(false)
          }, 60000)
        }
        this.getWgRealtimeData()
        this.getWangguanGaugeData()
      }
    }
  },
  mounted() {
    this.indexDate = this.$moment().format('YYYYMMDD')
    // window.addEventListener('scroll', this.handleScroll, true)
    this.init()
  },
  methods: {
    ...mapMutations('market', ['setScrollTop']),
    // handleScroll() {
    //   const top = this.$refs['contentWrapper'].scrollTop
    //   this.setScrollTop(top)
    // },
    init() {
      if (this.isToday) {
        this.timeId = setInterval(() => {
          this.getWgRealtimeData(false)
        }, 60000)
      }
      this.getWgRealtimeData()
      // this.getWangGuanData()
      this.getWangguanGaugeData()
    },
    async getWgRealtimeData() {
      const [data1, data2, data3] = await Promise.all([
        this.getWgIncomeData(),
        this.getWgJbData(),
        this.getWangGuanData()
      ])
      // data2.inc_income = data1.income / data2.real_income_jb - 1
      // data2.inc_weight = data1.weight / data2.real_weight_jb - 1
      this.baseData = { ...data1, ...data2, ...data3 }
    },
    getWgIncomeData() {
      return this.sendJavaRequest({
        method: 'GET',
        // url: `/resourceServices/sxRealTimeIncomeAndWeight/query/manager?managerId=${this.zoneCode}`
        url: `/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query/manager?managerId=${this.zoneCode}`
      }).then(res => {
        return res.obj
      })
    },
    getWgJbData() {
      // const tableName = 'sx_manage_kb_real_time'
      const url = '/cockpit/reportrest/report/twoDimen/sx_manage_kb_real_time'
      return this.sendTwoDimenRequestNew(url, [{ key: 'manager_id', value: this.zoneCode }]).then(
        res => {
          return res.obj[0] || {}
        }
      )
    },
    // 网管数据
    getWangGuanData() {
      const date = this.isToday
        ? this.$moment()
            .subtract(1, 'days')
            .format('YYYYMMDD')
        : this.$moment(this.indexDate).format('YYYYMMDD')
      // const tableName = 'manager_income_weight'
      const url = '/resourceServices/report/twoDimen/manager_income_weight'
      const conditionList = [
        { key: 'manager_id', value: this.zoneCode },
        { key: 'inc_day', value: date }
      ]
      return this.sendTwoDimenRequestNew(url, conditionList).then(res => {
        const data = res.obj[0] || {}
        data.old_income = data.income / 10000
        data.old_weight = data.weight / 1000
        delete data.income
        delete data.weight
        return data
      })
    },
    getWangguanGaugeData() {
      const date = this.isToday
        ? this.$moment()
            .subtract(1, 'days')
            .format('YYYYMMDD')
        : this.$moment(this.indexDate).format('YYYYMMDD')
      // const tableName = 'manager_weight_completion_rate'
      const url = '/cockpit/reportrest/report/twoDimen/manager_weight_completion_rate'
      const conditionList = [
        { key: 'manager_id', value: this.zoneCode },
        { key: 'inc_day', value: date }
      ]
      this.sendTwoDimenRequestNew(url, conditionList).then(res => {
        this.gaugeData = res.obj[0] || {}
      })
    },
    dateChange(res) {
      this.indexDate = this.$moment(res).format('YYYYMMDD')
    },
    // 格式化参数
    formatParams(param = {}) {
      return Object.keys(param).map(key => ({ key, value: param[key] }))
    }
  },
  activated() {
    if (this.timeId) {
      return
    } else {
      this.init()
    }
  },
  deactivated() {
    clearInterval(this.timeId)
    this.timeId = null
  },
  destroyed() {},
  beforeDestroy() {
    clearInterval(this.timeId)
  }
}
</script>

<style lang="less" scoped>
@import url('./index.less');
</style>
