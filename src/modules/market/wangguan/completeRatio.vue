<template>
  <div class="bgc_white complete-ratio">
    <div class="flex_start">
      <div class="pd_lr20 fs28 fw700 flex_start" style="line-height: 0.4rem">
        累计货量完成比(T-1)
        <ShowTipIcon
          class="ml8"
          @btnShowTip="btnShowTip($event, 'sxzk-sc-ljsrwcbwg')"
          :isFeedback="isFeedback"
          :explaindDetail="explaindDetail"
          @feedback="btnFeedback"
        >
        </ShowTipIcon>
      </div>
    </div>
    <div class="gauge-content">
      <div class="left-box">
        <SubHead title="月累计货量完成比" :percentValue="finishRate" />
        <KyDataGauge :value="finishPer" :date="defaultDate" :type="'month'" class="canvas-box mt12" />
        <!-- <canvas id="circle-charts" class="mt10"></canvas>
        <LeftDate style="margin-top: 0.24rem" :date="defaultDate" type="month" /> -->
      </div>
      <div class="right-box">
        <div>
          <span>月累计货量</span>
          <span class="grey333">{{ $numToInteger(baseData.day_mon_weight, 2, 1000) }}</span>
        </div>
        <div>
          <span>月目标差值</span>
          <span :class="textStyle(baseData.income_diff)">{{ $numToInteger(baseData.gap_value, 2, 1000) }}</span>
        </div>
        <div>
          <span>当月环比</span>
          <span class="grey333">{{ $numToPercent(baseData.mon_chain, 1) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SubHead from '../components/subHead'
// import LeftDate from '../share/leftDate'
import KyDataGauge from 'common/components/kyDataGauge'
// import { drawGaugeByCanvas } from 'common/js/drawGaugeByCanvas'
import mixins from '../comjs/mixins'
export default {
  mixins: [mixins],
  props: {
    baseData: Object,
    indexDate: String
  },
  components: {
    SubHead,
    KyDataGauge
  },
  watch: {
    baseData(val) {
      this.finishRate = String(val.month_acc_rate)
      this.finishPer = val.month_rate || 0
    }
  },
  data() {
    return {
      finishPer: '',
      finishRate: '',
      defaultDate: this.$moment()
        .subtract(1, 'days')
        .format('YYYYMMDD')
    }
  },
  created() {
    // this.$nextTick(() => {
    //   drawGaugeByCanvas('circle-charts', 0, 0)
    // })
  },
  methods: {
    formatterTheDateOfThisMonth(thisDate) {
      const date = this.$moment(thisDate)
      const y = date.year()
      const m = date.month()
      const d = date.date()
      return Math.ceil((new Date(y, m, d) - new Date(y, m, 0)) / (24 * 60 * 60 * 1000))
    },
    textStyle(v) {
      return parseFloat(v) > 0 ? 'green' : 'orange'
    }
  }
}
</script>

<style lang="less" scoped>
.complete-ratio {
  padding: 0.24rem 0.24rem;
}
.gauge-content {
  display: flex;
  margin: 0.3rem 0;
  .canvas-box {
    height: 1.76rem;
    width: 96%;
  }
  & > div:first-child {
    min-width: 54.4%;
    padding: 0 0.2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  & > div:last-child {
    min-width: 45.6%;
    padding-right: 0.2rem;
  }

  .right-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0.4rem 0 0.2rem 0.04rem;

    div {
      font-style: 0.24rem;
      display: flex;
      > span:first-child {
        color: #666;
        font-family: PingFangSC-Regular;
        flex: 2.3;
      }

      > span:last-child {
        font-family: PingFangSC-Regular;
        font-weight: 700;
        flex: 3;
      }
    }
  }
}
</style>
