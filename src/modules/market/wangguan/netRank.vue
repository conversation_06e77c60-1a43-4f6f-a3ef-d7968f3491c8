<!--
 * @Author: JieJw
 * @Date: 2021-07-27 18:56:50
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-31 15:18:26
 * @Description:
-->
<template>
  <CardList title="网点收入与货量排名（当日实时）" class="mt20">
    <NormalTable
      class="mt24"
      width="120%"
      :columns="columns"
      :dataSource="dataSource"
      size="small"
      maxHeight="8rem"
      isIndicator
      :onSelect="onSelect"
    />
    <KyDataDrawer
      :visible="visible"
      @close="visible = false"
      :title="drawerTitle"
      @drawerHeight="drawerHeight"
      height="80%"
    >
      <div class="fs28 fw700 mt24">{{ subTitle }}</div>
      <NormalTable
        class="mt24"
        width="100%"
        :columns="drawerColumns"
        :dataSource="drawerDataSource"
        :maxHeight="maxHeight"
      ></NormalTable>
    </KyDataDrawer>
  </CardList>
</template>

<script>
import mixins from '../comjs/mixins'
export default {
  mixins: [mixins],
  data() {
    return {
      dataSource: [],
      visible: false,
      drawerTitle: '',
      subTitle: '',
      drawerDataSource: [],
      drawerColumns: [],
      maxHeight: '8rem'
    }
  },
  computed: {
    columns() {
      return [
        {
          label: '排名',
          width: '0.8rem',
          fixed: 'left',
          render: (h, val) => <div class="normal-rank">{val > 2 ? val + 1 : ''}</div>
        },
        {
          label: '网点',
          dataIndex: 'deptName',
          width: '2rem',
          fixed: 'left',
          isMoreIcon: true
        },
        {
          label: '收入(万)',
          dataIndex: 'income',
          render: (h, val) => this.$numToInteger(val, 2)
        },
        {
          label: '基比收入',
          dataIndex: 'jbIncome',
          render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val)}</div>
        },
        {
          label: '货量(吨)',
          dataIndex: 'weight',
          render: (h, val) => this.$numToInteger(val, 2)
        },
        {
          label: '基比货量',
          dataIndex: 'jbWeight',
          render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val)}</div>
        }
      ]
    }
  },
  watch: {
    zoneCode() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getWeightData()
    },
    drawerHeight(height) {
      this.maxHeight = `${height - 100}px`
    },
    async getWeightData() {
      const result = await this.sendJavaRequest({
        method: 'GET',
        // url: `/resourceServices/sxRealTimeIncomeAndWeight/query/deptByManager?managerId=${this.zoneCode}`
        url: `/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query/deptByManager?managerId=${this.zoneCode}`
      })
      this.dataSource = result.obj
      this.$objectSortDown(this.dataSource, 'income')
    },
    async onSelect(item) {
      // if (true) return
      this.visible = true
      this.drawerTitle = '网点收入与货量排名详情'
      this.subTitle = item['deptName']
      const result = await this.sendJavaRequest({
        method: 'GET',
        // url: `/resourceServices/sxRealTimeIncomeAndWeight/query/deptByManager?managerId=${item['deptCode']}`
        url: `/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query/deptByManager?managerId=${item['deptCode']}`,
        notMatchJm: true // 此接口不匹配加盟区逻辑
      })
      this.drawerDataSource = result.obj
      console.log(this.drawerDataSource)
      this.$objectSortDown(this.drawerDataSource, 'income')
      this.drawerColumns = [
        {
          label: '排名',
          width: '0.8rem',
          fixed: 'left',
          render: (h, val) => <div class="normal-rank">{val > 2 ? val + 1 : ''}</div>
        },
        {
          label: '网点',
          dataIndex: 'deptName',
          width: '2rem',
          fixed: 'left'
        },
        {
          label: '收入(万)',
          dataIndex: 'income',
          render: (h, val) => this.$numToInteger(val, 2)
        },
        {
          label: '基比收入',
          dataIndex: 'jbIncome',
          render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val)}</div>
        },

        {
          label: '货量(吨)',
          dataIndex: 'weight',
          render: (h, val) => this.$numToInteger(val, 2)
        },
        {
          label: '基比货量',
          dataIndex: 'jbWeight',
          render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val)}</div>
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped></style>
