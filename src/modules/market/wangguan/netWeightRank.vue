<!--
 * @Author: JieJw
 * @Date: 2021-07-27 18:56:50
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-02-10 10:15:39
 * @Description:
-->
<template>
  <div>
    <CardList title="网点货量排名" class="mt20">
      <Tabs slot="nav" :options="['日', '月']" :tabIndex="tabIndex" @tabSelect="tabSelect" />

      <NormalTable
        class="mt24"
        :columns="columns"
        :dataSource="dataSource"
        size="small"
        :onSelect="onSelect"
        maxHeight="9rem"
        :width="tableWidth"
      />
    </CardList>
    <KyDataDrawer
      :visible="visible"
      @close="visible = false"
      :title="drawerTitle"
      @drawerHeight="drawerHeight"
      height="80%"
    >
      <div class="fs28 fw700 mt24">{{ subTitle }}</div>
      <NormalTable
        class="mt24"
        width="110%"
        :columns="drawerColumns"
        :dataSource="drawerDataSource"
        :maxHeight="maxHeight"
      ></NormalTable>
    </KyDataDrawer>
  </div>
</template>

<script>
import mixins from '../comjs/mixins'
export default {
  mixins: [mixins],
  data() {
    return {
      tabIndex: 0,
      dataSource: [],
      visible: false,
      drawerTitle: '',
      subTitle: '',
      drawerDataSource: [],
      drawerColumns: [],
      maxHeight: '8rem'
    }
  },
  computed: {
    tableWidth() {
      if (!this.tabIndex) {
        return '100%'
      }
      return '130%'
    },
    columns() {
      const data = [
        {
          label: '排名',
          width: '0.8rem',
          fixed: 'left',
          render: (h, val) => <div class="normal-rank">{val > 2 ? val + 1 : ''}</div>
        },
        {
          label: '网点',
          dataIndex: 'dept_name',
          width: '2.4rem',
          fixed: 'left',
          isMoreIcon: !this.visible
          // isMoreIcon: false
        },
        {
          label: '收入(万)',
          dataIndex: 'income',
          render: (h, val) => this.$numToInteger(val, 2, 10000)
        },
        {
          label: '目标值(吨)',
          dataIndex: 'weight_target',
          render: (h, val) => this.$numToInteger(val, 2, 1000)
        },
        {
          label: '货量(吨)',
          dataIndex: 'weight',
          render: (h, val) => this.$numToInteger(val, 2, 1000)
        },
        {
          label: '完成比',
          dataIndex: '',
          render: (h, val, item) => {
            if (item['weight_target']) {
              return this.$numToPercent(item['weight'] / item['weight_target'], 1)
            }
            return '-'
          }
        },
        {
          label: '环比',
          dataIndex: 'weight_chain',
          render: (h, val) => this.$numToPercent(val, 1)
        }
      ]
      if (!this.tabIndex) {
        data.splice(3, 1)
        data.splice(4, 1)
      }
      return data
    }
  },
  watch: {
    zoneCode() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getRankData()
    },
    drawerHeight(height) {
      this.maxHeight = `${height - 100}px`
    },
    getRankData() {
      // const tableName = 'manager_dept_weight_rank'
      const url = '/cockpit/reportrest/report/twoDimen/manager_dept_weight_rank'
      const conditionList = [
        {
          key: 'inc_day',
          value: this.$moment()
            .subtract(1, 'days')
            .format(this.tabIndex ? 'YYYYMM' : 'YYYYMMDD')
        },
        { key: 'manager_id', value: this.zoneCode }
      ]
      this.sendTwoDimenRequestNew(url, conditionList).then(res => {
        this.dataSource = res.obj
      })
    },
    tabSelect(val) {
      this.tabIndex = val
      this.getRankData()
    },
    onSelect(item) {
      // if (true) return
      this.visible = true
      this.drawerTitle = '网点货量排名详情'
      // this.subTitle = item['deptName']
      const code = item['dept_code']
      this.sendTwoDimenRequest(
        'two_dept_weight_rank',
        this.forMapData({
          // inc_day: '20230103',
          // upper_dept_code: 'S010AAEP',
          inc_day: this.$moment()
            .subtract(1, 'days')
            .format(this.tabIndex ? 'YYYYMM' : 'YYYYMMDD'),
          upper_dept_code: code
        })
      ).then(res => {
        this.drawerDataSource = res.obj
        this.drawerColumns = [
          {
            label: '排名',
            width: '0.8rem',
            render: (h, val) => <div class="normal-rank">{val > 2 ? val + 1 : ''}</div>
          },
          {
            label: '网点',
            dataIndex: 'dept_name',
            width: '2.6rem'
          },
          {
            label: '收入(万)',
            dataIndex: 'income',
            render: (h, val) => this.$numToInteger(val, 2)
          },
          {
            label: '货量(吨)',
            dataIndex: 'weight',
            render: (h, val) => this.$numToInteger(val, 2, 1000)
          },
          {
            label: '上月货量',
            dataIndex: 'last_month_weight',
            render: (h, val) => this.$numToInteger(val, 2, 1000)
          },
          {
            label: '环比',
            dataIndex: 'weight_chain',
            render: (h, val) => this.$numToPercent(val, 1)
          }
        ]
      })
    }
  }
}
</script>

<style lang="less" scoped></style>
