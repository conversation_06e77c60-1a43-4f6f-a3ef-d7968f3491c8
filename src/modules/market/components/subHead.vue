<template>
  <div class="flex0" style="width: 100%;">
    <div :class="['flex_center', 'content-box', bgColor]">
      <div :class="['icon-bg', bgColor]">
        <i :class="['iconfont', 'icon-profit-target', bgColor]"></i>
      </div>
      <span class="ml8">{{ title }}</span>
      <span class="percent fw700">{{ $numToPercent(percentValue, 1) }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String
    },
    percentValue: {
      type: String
    }
  },
  computed: {
    bgColor() {
      return this.percentValue >= 1 ? 'bgGreen' : 'bgRed'
    }
  }
}
</script>

<style lang="less" scoped>
.content-box {
  border-radius: 0.04rem;
  height: 0.48rem;
  width: 100%;
  color: #dc1e32;

  .icon-bg {
    height: 0.32rem;
    width: 0.32rem;
    border-radius: 50%;
    text-align: center;

    i {
      font-size: 0.2rem;
      line-height: 0.32rem;
    }
  }

  .percent {
    margin-left: 0.08rem;
    font-weight: 700;
  }
}

.bgRed {
  background: rgba(255, 128, 102, 0.1);
  color: #dc1e32;
}

.bgGreen {
  background: rgba(8, 188, 160, 0.1);
  color: #08bca0;
}
</style>
