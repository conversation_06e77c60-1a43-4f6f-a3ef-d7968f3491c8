.real-time-datalist-container {
  position: relative;
  transition: 100ms;
  width: 100%;
  background-color: #2e3f63;
  color: #fff;
  border-radius: 0.1rem;
  box-shadow: 0px 3px 4px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  padding: 0.24rem;
  &.bgc-holiday {
    background: url('./spring_bgc.png') no-repeat bottom right, #2e3f63;
    background-size: 2.7rem 2.2rem;
  }
  &.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 0.5rem 0;
  }
  &.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 0.5rem 0;
  }
  .content-container {
    position: relative;
    .cut-line {
      width: 100%;
      height: 1px;
      background-color: #f2f2f2;
      position: absolute;
      top: -0.25rem;
      left: 0;
      opacity: 0.1;
    }
    .content-wrap {
      .first-wrap {
        &.wrap-line {
          display: flex;
          align-items: center;
          flex-direction: row;
          .label {
            font-family: PingFangSC-Regular;
            color: #ffffff;
          }
          .value {
            font-size: 0.28rem;
            font-family: PingFangSC-Medium;
            color: #ffffff;
            margin-left: 0.08rem;
          }
        }
        &.wrap-column {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          .label {
            font-family: PingFangSC-Regular;
            color: #ffffff;
          }
          .value {
            font-size: 0.48rem;
            font-family: Roboto-Medium;
            color: #ffffff;
            margin-top: 0.14rem;
          }
        }
      }
      .other-wrap {
        display: flex;
        align-items: center;
        margin-top: 0.14rem;
        .label {
          font-family: PingFangSC-Regular;
          color: #ddd;
          font-size: 0.24rem;
        }
        .value {
          font-size: 0.24rem;
          font-family: Roboto-Medium;
          color: #ffffff;
          margin-left: 0.08rem;
        }
        .row-box {
          display: flex;
          align-items: center;
          .label {
            font-family: PingFangSC-Regular;
            color: #ddd;
            font-size: 0.2rem;
          }
          .value {
            font-size: 0.2rem;
            font-family: Roboto-Medium;
            color: #ffffff;
            margin-left: 0.08rem;
          }
        }
      }
    }
    .tag-circle {
      height: 0.32rem;
      padding: 0 0.12rem;
      border-radius: 0.16rem;
      background: rgba(190, 190, 206, 0.5);
      text-align: center;
      line-height: 0.32rem;
      font-family: PingFangSC-Medium;
      color: #fff;
      margin-left: 0.08rem;
      font-size: 0.2rem;
    }
  }
  .tip-wrap {
    background: url('~common/img/real_time_bg.svg') no-repeat;
    background-size: cover;
    height: 0.48rem;
    width: 1.56rem;
    position: absolute;
    color: #fff;
    top: 0rem;
    right: 0rem;
    text-align: right;
    line-height: 0.48rem;
  }
}
