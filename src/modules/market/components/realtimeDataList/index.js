import classNames from 'classnames'
import './index.less'
import LunarBox from 'common/components/lunarBox'
import { mapState } from 'vuex'
// import { mapState } from 'vuex'
export default {
  props: {
    showIcon: {
      type: Boolean,
      default: true
    },
    tipName: {
      type: String,
      default: '统计口径'
    },
    colNum: {
      type: Number,
      default: 2
    },
    columns: {
      type: Array,
      default: () => [
        // [
        //   {
        //     label: '昨日收入',
        //     type: 'column',
        //     dataIndex: ''
        //   },
        //   [
        //     {
        //       label: '日基比',
        //       dataIndex: ''
        //     },
        //     {
        //       label: '日环比',
        //       dataIndex: ''
        //     }
        //   ],
        //   {
        //     label: '历史峰值',
        //     tag: '01.01',
        //     dataIndex: ''
        //   }
        // ],
        // [
        //   {
        //     label: '昨日收入',
        //     type: 'column',
        //     dataIndex: ''
        //   },
        //   [
        //     {
        //       label: '日基比',
        //       dataIndex: ''
        //     },
        //     {
        //       label: '日环比',
        //       dataIndex: ''
        //     }
        //   ],
        //   {
        //     label: '历史峰值',
        //     tag: '01.01',
        //     dataIndex: ''
        //   }
        // ]
      ]
    },
    dataSource: {
      type: [Array, Object],
      default: () => []
    },
    lunarShow: {
      type: Boolean,
      default: false
    },
    lunarDate: {
      type: String,
      default: ''
    },
    lunarType: {
      type: String,
      default: 'lunar'
    },
    lunarColumnsLeft: {
      type: Array,
      default: () => []
    },
    lunarColumnsRight: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState({
      showThemeTab: state => state.showThemeTab
    })
  },
  methods: {
    showIconVisi() {
      this.$emit('showTip')
    }
  },
  render(h) {
    const {
      showThemeTab,
      showIcon,
      tipName,
      colNum,
      columns,
      dataSource,
      showIconVisi,
      lunarShow,
      lunarDate,
      lunarType,
      lunarColumnsLeft,
      lunarColumnsRight
    } = this
    // dataSource处理
    const dataFilter = index => {
      if (Array.isArray(dataSource)) {
        return dataSource.length && dataSource[index]
      }
      return dataSource
    }
    // label处理
    const labelFilter = (data, index) => {
      const { label } = data
      if (label && typeof label === 'function') {
        return label(h, index, dataFilter(index))
      }
      return label
    }
    const tagFilter = (data, index) => {
      const { tag, tagIndex } = data
      if (tag && typeof tag === 'function') {
        return tag(h, dataFilter(index)[tagIndex], dataFilter(index))
      }
      return tag
    }
    const valueFilter = (data, index) => {
      const { render, dataIndex = '' } = data
      if (render && typeof render === 'function') {
        return render(h, dataFilter(index)[dataIndex], dataFilter(index))
      }
      return dataFilter(index)[dataIndex]
    }
    //   <LunarBox
    //   v-if="lunarShow"
    //   class="customise-lunar mt20"
    //   date={lunarDate}
    //   type={lunarType}
    //   value={lunarDataLeft}
    // />

    const contentDom = (colItem, colIndex) => {
      const listDom = (item, index) => {
        const { type = 'line', tag } = item
        if (!index) {
          return (
            <div
              class={classNames({
                'first-wrap': true,
                'wrap-column': type === 'column',
                'wrap-line': type === 'line'
              })}
            >
              <div class={'label'}>{labelFilter(item, index)}</div>
              <div class={'value'}>{valueFilter(item, index)}</div>
            </div>
          )
        }
        if (Array.isArray(item)) {
          return (
            <div class={'other-wrap'}>
              {item.map((data, i) => {
                return (
                  <div class={`row-box ${i ? 'ml20' : ''}`}>
                    <div class={'label'}>{labelFilter(data, index)}</div>
                    <div class={'value'}>{valueFilter(data, index)}</div>
                  </div>
                )
              })}
            </div>
          )
        }
        return (
          <div class={'other-wrap'}>
            <div class={'label'}>{labelFilter(item, index)}</div>
            <div class={'value'}>{valueFilter(item, index)}</div>
            {tag && <div class={'tag-circle'}>{tagFilter(item, index)}</div>}
          </div>
        )
      }
      return (
        <div class="content-container">
          <div class={{ 'cut-line': colIndex === 2 || colIndex === 3 }}></div>
          {colItem.map((item, index) => {
            return <div class={'content-wrap'}>{listDom(item, index)}</div>
          })}
          {lunarShow && colIndex === 0 ? (
            <LunarBox
              class={classNames({
                mt20: true,
                mr20: colIndex === 0
              })}
              isShow={lunarShow}
              lunarDate={lunarDate}
              type={lunarType}
              columns={lunarColumnsLeft}
              dataSource={dataSource}
            />
          ) : (
            ''
          )}
          {lunarShow && colIndex === 1 ? (
            <LunarBox
              class={classNames({
                mt20: true
              })}
              isShow={lunarShow}
              lunarDate={lunarDate}
              type={lunarType}
              columns={lunarColumnsRight}
              dataSource={dataSource}
            />
          ) : (
            ''
          )}
        </div>
      )
    }
    const TipIcon = () => {
      if (showIcon) {
        return (
          <div class="tip-wrap" onClick={showIconVisi}>
            {tipName}
            <i class="iconfont icon-help ml4 mr4" style="font-size: 0.24rem"></i>
          </div>
        )
      }
    }
    return (
      <div class={`real-time-datalist-container grid-${colNum} ${showThemeTab ? 'bgc-holiday' : ''}`}>
        {columns.map((item, index) => {
          return contentDom(item, index)
        })}
        <TipIcon />
      </div>
    )
  }
}
