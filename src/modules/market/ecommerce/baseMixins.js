/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-01-11 11:06:00
 * @Description:
 */
import { mapState, mapGetters } from 'vuex'
export default {
  computed: {
    zoneLevel() {
      return this.zoneData.zoneLevel
    },

    zoneCode() {
      return this.zoneData.zoneCode
    },
    zoneName() {
      return this.zoneData.zoneName
    },
    // 下专
    dLevelName() {
      return this.zoneData.dLevelName
    },
    // 下专zoneLevel
    dZoneLevel() {
      return this.zoneData.dZoneLevel
    },
    ...mapState({
      isDev: state => state.isDev,
      holidayData: state => state.holidayData
    }),
    ...mapGetters({
      zoneData: 'zoneData'
    })
  },
  methods: {}
}
