import mixins from './mixins'

export default {
  mixins: [mixins],
  data() {
    return {}
  },
  computed: {
    defaultData() {
      return {
        inc_day: this.dateValue,
        zone_code: this.zoneCode
      }
    }
  },
  methods: {
    // 接口
    _getOverviewData(data) {
      return this.sendTwoDimenRequest('ads_sx_mkt_ec_key_index_sum_di', this.forMapData(data))
    },
    _getStructureData(data) {
      return this.sendTwoDimenRequest('ads_sx_mkt_ec_weight_structure_sum_di', this.forMapData(data))
    },
    _getChannelData(data) {
      return this.sendTwoDimenRequest('ads_sx_mkt_ec_channel_classify_sum_di', this.forMapData(data))
    },
    getZoneCodeParam(zoneLevel) {
      switch (+zoneLevel) {
        case 30:
          return { name: '', code: '' }
        case 31:
          return { name: 'big_area_name_src', code: 'big_area_code_src' }
        case 32:
          return { name: 'province_area_name_src', code: 'province_area_code_src' }
        case 33:
          return { name: 'area_name_src', code: 'area_code_src' }
        case 34:
          return { name: 'dept_name_src', code: 'dept_code_src' }
        default:
          return { name: '', code: '' }
      }
    }
  }
}
