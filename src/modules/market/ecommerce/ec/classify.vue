<template>
  <div class="mt24">
    <CardList title="电商渠道分类">
      <NormalTable class="mt32" size="small" isIndicator :dataSource="tableDataSource" :columns="tableColumns" width="140%" :onSelect="tableOnSelect">
      </NormalTable>
      <KyDataDrawer
        :visible="drawerVisible"
        @close="drawerVisible = false"
        height="58%"
        :title="`电商渠道三级分类`"
      >
        <NormalTable
          class="mt24"
          size="small"
          isIndicator
          width="120%"
          maxHeight="6.0rem"
          :dataSource="modalResources"
          :columns="modalColumns"
        />
      </KyDataDrawer>
    </CardList>
  </div>
</template>
<script>
import request from './request'
export default {
  mixins: [request],
  data() {
    return {
      tableDataSource: [],
      tableColumns: [],
      drawerVisible: false,
      modalResources: [],
      modalColumns: []
    }
  },
  methods: {
    init() {
      this.getChannelData()
    },
    async getChannelData() {
      const params = {
        start_day: this.dateValue,
        end_day: this.dateValue,
        zone_level: this.zoneLevel,
        channel_level: '二级渠道'
      }
      if (this.getZoneCodeParam(this.zoneLevel).code) {
        params[`${this.getZoneCodeParam(this.zoneLevel).code}`] = this.zoneCode
      }
      const result = await this._getChannelData(params)
      this.tableDataSource = []
      if (result.obj.length) {
        this.tableDataSource = result.obj
      }
      this.setTable()
    },

    setTable() {
      const dataSource = this.tableDataSource
      this.tableDataSource = []
      const items = []
      for (const a of dataSource) {
        if (items.indexOf(a.channel_level_one) === -1) {
          items.push(a.channel_level_one)
        }
      }
      const channels = []
      for (const item of items) {
        const r = dataSource.filter(data => data.channel_level_one === item)
        r[0].isMerge = true
        channels.push(r)
        this.tableDataSource = this.tableDataSource.concat([...r])
      }
      this.tableColumns = [
        {
          label: '一级分类',
          dataIndex: 'channel_level_one',
          fixed: 'left',
          render: (h, value, res, index) => {
            const itemIndex = items.indexOf(value)
            const len = channels[itemIndex].length
            let count = 0
            for (let i = 0; i < itemIndex; i++) {
              count += channels[i].length
            }
            const start = count
            if (index === start) {
              return {
                component: value,
                props: { rowSpan: len }
              }
            } else {
              return {
                component: value,
                props: { rowSpan: 0 }
              }
            }
          }
        },
        {
          label: '二级分类',
          dataIndex: 'channel_level_two',
          fixed: 'left',
          render: (h, val, data) => {
            const arr = <i class={'iconfont icon-dayuhao fw700 fs20'}></i>
            return (
              <div class={'flex_start'}>
                {val}
                {arr}
              </div>
            )
          }
        },
        {
          label: '当月货量(吨)',
          dataIndex: 'weight_mtd',
          render: (h, value) => this.$numToInteger(value, 2, 1000)
        },
        {
          label: '日均货量环比',
          dataIndex: 'weight_avg_rate',
          render: (h, value) => this.$numToPercent(value, 2)
        },
        {
          label: '当月票数(万票)',
          dataIndex: 'tickets_mtd',
          render: (h, value) => this.$numToInteger(value, 2, 10000)
        },
        {
          label: '日均票数环比',
          dataIndex: 'tickets_avg_rate',
          render: (h, value) => this.$numToPercent(value, 2)
        }
      ]
    },
    tableOnSelect(item) {
      this.drawerVisible = true
      this.modalItem = item
      this.getModalData(item)
      // 神策点击
      // this.$sensors.webClick(`市场-累计收入分析弹框`)
    },
    async getModalData(item) {
      const params = {
        start_day: this.dateValue,
        end_day: this.dateValue,
        zone_level: this.zoneLevel,
        channel_level: '三级渠道',
        channel_level_one: item.channel_level_one,
        channel_level_two: item.channel_level_two
      }
      if (this.getZoneCodeParam(this.zoneLevel).code) {
        params[`${this.getZoneCodeParam(this.zoneLevel).code}`] = this.zoneCode
      }
      const result = await this._getChannelData(params)
      this.modalResources = []
      if (result.obj.length) {
        this.modalResources = result.obj
      }
      this.setModalTable()
    },
    setModalTable() {
      this.modalColumns = [
        {
          label: '三级分类',
          dataIndex: 'channel_level_three',
          fixed: 'left'
        },
        {
          label: '当月货量(吨)',
          dataIndex: 'weight_mtd',
          render: (h, value) => this.$numToInteger(value, 2, 1000)
        },
        {
          label: '日均货量环比',
          dataIndex: 'weight_avg_rate',
          render: (h, value) => this.$numToPercent(value, 2)
        },
        {
          label: '当月票数(万票)',
          dataIndex: 'tickets_mtd',
          render: (h, value) => this.$numToInteger(value, 2, 10000)
        },
        {
          label: '日均票数环比',
          dataIndex: 'tickets_avg_rate',
          render: (h, value) => this.$numToPercent(value, 2)
        }
      ]
    }
  },
  mounted() {
    this.init()
  }
}
</script>
<style lang='less' scoped>
</style>
