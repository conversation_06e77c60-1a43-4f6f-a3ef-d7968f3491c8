<template>
  <div class="mt24">
    <CardList title="电商货源结构">
      <NormalTable width="130%" class="mt32" size="small" isIndicator :dataSource="tableDataSource" :columns="tableColumns">
      </NormalTable>
      <div class="dshy_flag">*30-130kg为电商小票占比，计算公式：30-130kg电商货量/电商货量</div>
    </CardList>
  </div>
</template>
<script>
import request from './request'
export default {
  mixins: [request],
  data() {
    return {
      tableDataSource: [],
      tableColumns: []
    }
  },
  methods: {
    init() {
      this.getStuctureData()
    },
    async getStuctureData() {
      const params = {
        start_day: this.dateValue,
        end_day: this.dateValue,
        zone_level: this.zoneLevel
      }
      if (this.getZoneCodeParam(this.zoneLevel).code) {
        params[`${this.getZoneCodeParam(this.zoneLevel).code}`] = this.zoneCode
      }
      const result = await this._getStructureData(params)
      this.tableDataSource = []
      if (result.obj.length > 0) {
        this.tableDataSource = result.obj.sort((a, b) => {
          return a.weight_rk - b.weight_rk
        })
        this.tableDataSource[0].isWeight = true
      }
      this.setTable()
    },

    setTable() {
      this.tableColumns = [
        {
          label: '公斤段',
          dataIndex: 'weight_seg',
          fixed: 'left'
        },
        {
          label: '当日电商货量(吨)',
          dataIndex: 'weight',
          render: (h, value) => this.$numToInteger(value, 2, 1000)
        },
        {
          label: '当日货量占比',
          dataIndex: 'weight_rate',
          render: (h, value) => this.$numToPercent(value, 2)
        },
        {
          label: '当月电商货量(吨)',
          dataIndex: 'weight_mtd',
          render: (h, value) => this.$numToInteger(value, 2, 1000)
        },
        {
          label: '当月货量占比',
          dataIndex: 'weight_mtd_rate',
          render: (h, value) => this.$numToPercent(value, 2)
        }
      ]
    }
  },
  mounted() {
    this.init()
  }
}
</script>
<style lang='less' scoped>
.dshy_flag {
  padding: .1rem .15rem;
  font-family: PingFangSC-Regular;
  font-size: .2rem;
  color: #999;
  //text-align: center;
  font-weight: 400;
}
</style>
