<!--
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-01-11 11:25:24
 * @Description:
-->
<template>
  <div class="page_bgc">
    <div class="date_container pd_lr20">
      <KyDatePicker
        type="day"
        :dateValue="dateValue"
        @onChange="dateChange"
        :holidayData="holidayData"
      ></KyDatePicker>
    </div>
    <PageContent>
      <Target ref="target"></Target>
      <Structure ref="structure"></Structure>
      <Classify ref="classify"></Classify>
      <div style="height: 1rem"></div>
    </PageContent>
  </div>
</template>

<script>
import flowLayout from 'common/mixins/flowLayout.js'
import { mapMutations } from 'vuex'
import Target from './target.vue'
import Structure from './structure.vue'
import Classify from './classify.vue'
import mixins from './mixins'
export default {
  mixins: [flowLayout, mixins],
  components: {
    Target,
    Structure,
    Classify
  },
  props: {},

  data() {
    return {}
  },
  watch: {
    zoneCode() {
      this.$refs.target.init()
      this.$refs.structure.init()
      this.$refs.classify.init()
    },
    dateValue() {
      this.$refs.target.init()
      this.$refs.structure.init()
      this.$refs.classify.init()
    }
  },
  computed: {

  },

  methods: {
    ...mapMutations('ecommerce', ['setEcDateValue']),
    dateChange(date) {
      this.setEcDateValue(this.$moment(date).format('YYYYMMDD'))
    }
  },
  mounted() {}
}
</script>

<style lang="less" scoped>
.profit_page {
  color: #333;
}
.date_container {
  width: 100vw;
  height: 0.8rem;
  color: #999;
}
</style>
