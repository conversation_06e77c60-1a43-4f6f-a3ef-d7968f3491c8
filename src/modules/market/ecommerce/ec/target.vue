<template>
  <div>
    <CardList title="电商关键指标">
      <div class="pd_lr20 page_bgf" style="overflow: hidden">
        <MultiDataList class="mt24" :colNum="2" :columns="columns" :dataSource="overviewResult"></MultiDataList>
        <div class="flex_between mt64">
          <p class="fs28 fw700">近{{ this.dateIndex ? '六个月' : '七日' }}电商货量占比</p>
          <Tabs :options="['日', '月']" :tabIndex="dateIndex" @tabSelect="tabSelect" />
        </div>
        <KydChartModel
          class="mt32"
          isScroll
          :legendOption="ecommerceTrendLegend"
          :tableOption="ecommerceTrendTableData"
          position="right"
          tableLength="5"
          chartLength="6"
        >
          <div class="mt24" ref="chart-model-trend" style="height: 4rem"></div>
        </KydChartModel>
        <div class="fs28 fw700 mt64">{{ titleName }}</div>
      </div>
      <NormalTable
        class="mt32"
        size="small"
        isIndicator
        width="120%"
        maxHeight="8.0rem"
        :dataSource="tableDataSource"
        :columns="tableColumns"
        :onSelect="tableOnSelect"
      >
      </NormalTable>
    </CardList>
    <KyDataDrawer :visible="drawerVisible" @close="drawerVisible = false" height="58%" :title="drawetTitle">
      <NormalTable
        class="mt24"
        size="small"
        isIndicator
        width="120%"
        maxHeight="6.0rem"
        :dataSource="modalResources"
        :columns="modalColumns"
      />
    </KyDataDrawer>
  </div>
</template>
<script>
import request from './request'
import { drawMoreBarChart } from 'common/charts/chartOption'
export default {
  mixins: [request],
  data() {
    return {
      overviewResources: [],
      overviewResourcesMon: [],
      dillOverviewResources: [],
      dateIndex: 0,
      columns: [],
      pitSources: {},
      ecommerceDataSource: [],
      ecommerceTrendTableData: {},
      tableColumns: [],
      tableDataSource: [],
      modalResources: [],
      modalColumns: [],
      drawerVisible: false
    }
  },
  computed: {
    titleName() {
      return `各${this.formatLabel(this.zoneLevel)}货量${this.zoneLevel <= 32 ? '占比' : ''}排名`
    },
    drawetTitle() {
      return `各${this.formatLabel(this.dZoneLevel)}货量${this.dZoneLevel <= 32 ? '占比' : ''}排名`
    },
    overviewResult() {
      let result = {}
      if (this.overviewResources.length) {
        result = this.overviewResources[this.overviewResources.length - 1]
      }
      return result
    },
    ecommerceTrendLegend() {
      return {
        options: [
          {
            label: '货量(吨)',
            width: '2.6rem',
            int: [2, 1000]
          },
          {
            label: '电商占比',
            per: '2'
          }
        ],
        dataSource: this.ecommerceDataSource
      }
    }
  },
  methods: {
    init() {
      this.getOverviewData()
    },
    async getOverviewData() {
      const cParams = {
        start_day: this.$moment(this.dateValue).subtract(6, 'days').format('YYYYMMDD'),
        end_day: this.dateValue,
        zone_level: this.zoneLevel
      }
      if (this.getZoneCodeParam(this.zoneLevel).code) {
        cParams[`${this.getZoneCodeParam(this.zoneLevel).code}`] = this.zoneCode
      }
      const cParamsMon = {
        start_day: this.$moment(this.dateValue).subtract(5, 'months').format('YYYYMMDD'),
        end_day: this.$moment(this.dateValue).format('YYYYMMDD'),
        zone_level: this.zoneLevel,
        mon_rk: 1
      }
      if (this.getZoneCodeParam(this.zoneLevel).code) {
        cParamsMon[`${this.getZoneCodeParam(this.zoneLevel).code}`] = this.zoneCode
      }
      const dParams = {
        start_day: this.dateValue,
        end_day: this.dateValue,
        zone_level: this.dZoneLevel
      }
      if (this.getZoneCodeParam(this.zoneLevel).code) {
        dParams[`${this.getZoneCodeParam(this.zoneLevel).code}`] = this.zoneCode
      }
      const results = await Promise.all([
        this._getOverviewData(cParams),
        this._getOverviewData(cParamsMon),
        this._getOverviewData(dParams)
      ])
      const cOverviewData = results[0]
      const cOverviewDataMon = results[1]
      const dOverviewData = results[2]
      this.overviewResources = []
      this.dillOverviewResources = []
      this.overviewResourcesMon = []
      if (cOverviewData.obj.length > 0) {
        cOverviewData.obj.sort((a, b) => {
          return a.inc_day - b.inc_day
        })
        this.overviewResources = cOverviewData.obj
      }
      if (cOverviewDataMon.obj.length > 0) {
        cOverviewDataMon.obj.sort((a, b) => {
          return a.inc_month - b.inc_month
        })
        this.overviewResourcesMon = cOverviewDataMon.obj
      }
      if (dOverviewData.obj.length > 0) {
        this.dillOverviewResources = dOverviewData.obj
      }
      this.handleMainKWResult()
      this.setEcommerceChart()
      this.setTable()
    },
    tabSelect(index) {
      if (this.dateIndex === index) return
      this.dateIndex = index
      this.setEcommerceChart()
    },
    handleMainKWResult() {
      this.columns = [
        {
          parent: [
            {
              label: '本月电商货量占比',
              dataIndex: 'weight_mtd_rate',
              per: '2'
            }
          ],
          child: [
            {
              label: '昨日货量占比',
              dataIndex: 'weight_rate',
              per: '2'
            },
            {
              label: '上月货量占比',
              dataIndex: 'weight_ma_mtd_rate',
              per: '2'
            },
            {
              label: '环比上月',
              dataIndex: 'weight_zb_mom_rate',
              per: '2',
              indexType: 'down'
            }
          ]
        },
        {
          parent: [
            {
              label: '本月电商货量(吨)',
              int: [2, 1000, 0],
              dataIndex: 'weight_mtd'
            }
          ],
          child: [
            {
              label: '昨日货量',
              int: [2, 1000, 0],
              dataIndex: 'weight'
            },
            {
              label: '上月货量',
              int: [2, 1000, 0],
              dataIndex: 'weight_ma_mtd'
            },
            {
              label: '环比上月',
              dataIndex: 'weight_mom_rate',
              per: '2',
              indexType: 'down'
            }
          ]
        },
        {
          parent: [
            {
              label: '本月电商票数(万票)',
              int: [2, 10000, 0],
              dataIndex: 'tickets_mtd'
            }
          ],
          child: [
            {
              label: '昨日票数',
              int: [2, 10000, 0],
              dataIndex: 'tickets'
            },
            {
              label: '上月票数',
              int: [2, 10000, 0],
              dataIndex: 'tickets_ma_mtd'
            },
            {
              label: '环比上月',
              dataIndex: 'tickets_mom_rate',
              per: '2',
              indexType: 'down'
            }
          ]
        },
        {
          parent: [
            {
              label: '本月电商收入(万)',
              int: [2, 10000, 0],
              dataIndex: 'income_mtd'
            }
          ],
          child: [
            {
              label: '昨日收入',
              int: [2, 10000, 0],
              dataIndex: 'income'
            },
            {
              label: '上月收入',
              int: [2, 10000, 0],
              dataIndex: 'income_ma_mtd'
            },
            {
              label: '环比上月',
              dataIndex: 'income_mom_rate',
              per: '2',
              indexType: 'down'
            }
          ]
        }
      ]
    },
    setEcommerceChart() {
      const xData = []
      const sData = [[], []]
      let resources = []
      if (!this.dateIndex) {
        resources = this.overviewResources
      } else {
        resources = this.overviewResourcesMon
      }
      resources.map(item => {
        xData.push(item.inc_day)
        sData[0].push(!this.dateIndex ? item.weight : item.weight_mtd)
        sData[1].push(!this.dateIndex ? item.weight_rate : item.weight_mtd_rate)
      })
      this.ecommerceDataSource = []
      const options = {
        tooltip: {
          formatter: params => {
            this.ecommerceDataSource = params
          }
        },
        grid: {
          top: '10%',
          left: '0',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          },
          {
            data: sData[1],
            axisLabel: {
              formatter: val => ['{a|电商货\n量占比}', `{b|${this.$numToPercent(val, 2)}}`].join('\n'),
              borderWidth: 1,
              borderColor: 'rgba(221,221,221,0.70)',
              backgroundColor: '#f8f9fc',
              padding: 2,
              rich: {
                a: {
                  color: '#666',
                  fontSize: 9,
                  lineHeight: 12
                },
                b: {
                  color: '#333',
                  fontSize: 9,
                  lineHeight: 15
                }
              }
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 1000)
            }
          },
          {
            axisLabel: {
              formatter: value => this.$perFormat(value)
            }
          }
        ],
        series: [
          {
            data: sData[0]
          },
          {
            type: 'line',
            yAxisIndex: 1,
            data: sData[1]
          }
        ]
      }
      drawMoreBarChart(options, this.$refs['chart-model-trend'], true)
      this.ecommerceTrendTableData = {
        options
      }
    },
    setTable() {
      this.tableDataSource = this.dillOverviewResources
      this.tableDataSource.sort((a, b) => {
        if (this.zoneLevel <= 32) {
          return b.weight_mtd_rate - b.ec_weight_rate - (a.weight_mtd_rate - a.ec_weight_rate)
        }
        return b.weight_mtd - a.weight_mtd
      })
      this.tableDataSource.splice(0, 0, this.overviewResult)
      this.tableDataSource[0].isWeight = true
      let columns = []
      if (this.zoneLevel <= 32) {
        columns = [
          {
            label: '排名',
            width: '0.8rem',
            fixed: 'left',
            render: (h, value) => {
              if (!value) return
              return <div class='normal-rank-next'>{value <= 3 ? '' : value}</div>
            }
          },
          {
            label: this.dLevelName,
            dataIndex: this.getZoneCodeParam(this.dZoneLevel).name,
            fixed: 'left',
            width: '1.6rem',
            // render: (h, value) => this.$valueFormat(value)
            render: (h, val, data) => {
              const arr =
                this.zoneLevel !== data.zone_level && this.zoneLevel <= 32 ? (
                  <i class={'iconfont icon-dayuhao fw700 fs20'}></i>
                ) : (
                  ''
                )
              return (
                <div class={'flex_start'}>
                  {this.$valueFormat(val)}
                  {arr}
                </div>
              )
            }
          },
          {
            label: '目标',
            dataIndex: 'ec_weight_rate',
            render: (h, value) => this.$numToPercent(value, 2)
          },
          {
            label: '电商占比',
            dataIndex: 'weight_mtd_rate',
            render: (h, value) => this.$numToPercent(value, 2)
          },
          {
            label: '完成差值',
            dataIndex: '',
            render: (h, value, data) => {
              value = data.weight_mtd_rate - data.ec_weight_rate
              return this.$numToPercent(value, 2)
            }
          },
          {
            label: '上月电商件占比',
            dataIndex: 'weight_ma_mtd_rate_all',
            render: (h, value) => this.$numToPercent(value, 2)
          },
          {
            label: '环比',
            dataIndex: 'weight_zb_mom_rate_all',
            render: (h, value) => this.$numToPercent(value, 2)
          }
        ]
      } else {
        columns = [
          {
            label: '排名',
            width: '0.8rem',
            fixed: 'left',
            render: (h, value) => {
              if (!value) return
              return <div class='normal-rank-next'>{value <= 3 ? '' : value}</div>
            }
          },
          {
            label: this.dLevelName,
            dataIndex: this.getZoneCodeParam(this.dZoneLevel).name,
            fixed: 'left',
            width: '1.6rem',
            render: (h, val, data) => this.$valueFormat(val)
          },
          {
            label: '电商货量',
            dataIndex: 'weight_mtd',
            render: (h, val, data) => this.$numToInteger(val, 1, 1000)
          },
          {
            label: '电商占比',
            dataIndex: 'weight_mtd_rate',
            render: (h, val, data) => this.$numToPercent(val)
          },
          {
            label: '上月电商货量',
            dataIndex: 'weight_ma_mtd',
            render: (h, val, data) => this.$numToInteger(val, 1, 1000)
          },
          {
            label: '货量环比',
            dataIndex: 'weight_mom_rate',
            render: (h, val, data) => this.$numToPercent(val)
          }
        ]
      }

      this.tableColumns = columns
    },
    setModalTable(modalItem) {
      this.modalResources.sort((a, b) => {
        if (this.dZoneLevel <= 32) {
          return b.weight_mtd_rate - b.ec_weight_rate - (a.weight_mtd_rate - a.ec_weight_rate)
        }
        return b.weight_mtd - a.weight_mtd
      })
      this.modalResources.splice(0, 0, modalItem)
      this.modalResources[0].isWeight = true
      let columns = []
      if (this.dZoneLevel <= 32) {
        columns = [
          {
            label: '排名',
            width: '0.8rem',
            fixed: 'left',
            render: (h, value) => {
              if (!value) return
              return <div class='normal-rank-next'>{value <= 3 ? '' : value}</div>
            }
          },
          {
            label: this.formatLabel(this.dZoneLevel),
            dataIndex: this.getZoneCodeParam(parseInt(modalItem.zone_level) + 1 + '').name,
            fixed: 'left',
            width: '1.6rem',
            render: (h, value) => this.$valueFormat(value)
          },
          {
            label: '目标',
            dataIndex: 'ec_weight_rate',
            render: (h, value) => this.$numToPercent(value, 2)
          },
          {
            label: '电商占比',
            dataIndex: 'weight_mtd_rate',
            render: (h, value) => this.$numToPercent(value, 2)
          },
          {
            label: '完成差值',
            dataIndex: '',
            render: (h, value, data) => {
              value = data.weight_mtd_rate - data.ec_weight_rate
              return this.$numToPercent(value, 2)
            }
          },
          {
            label: '上月电商件占比',
            dataIndex: 'weight_ma_mtd_rate_all',
            render: (h, value) => this.$numToPercent(value, 2)
          },
          {
            label: '环比',
            dataIndex: 'weight_zb_mom_rate_all',
            render: (h, value) => this.$numToPercent(value, 2)
          }
        ]
      } else {
        columns = [
          {
            label: '排名',
            width: '0.8rem',
            fixed: 'left',
            render: (h, value) => {
              if (!value) return
              return <div class='normal-rank-next'>{value <= 3 ? '' : value}</div>
            }
          },
          {
            label: this.formatLabel(this.dZoneLevel),
            dataIndex: this.getZoneCodeParam(parseInt(modalItem.zone_level) + 1 + '').name,
            fixed: 'left',
            width: '1.6rem',
            render: (h, value) => this.$valueFormat(value)
          },
          {
            label: '电商货量',
            dataIndex: 'weight_mtd',
            render: (h, val, data) => this.$numToInteger(val, 1, 1000)
          },
          {
            label: '电商占比',
            dataIndex: 'weight_mtd_rate',
            render: (h, val, data) => this.$numToPercent(val)
          },
          {
            label: '上月电商货量',
            dataIndex: 'weight_ma_mtd',
            render: (h, val, data) => this.$numToInteger(val, 1, 1000)
          },
          {
            label: '货量环比',
            dataIndex: 'weight_mom_rate',
            render: (h, val, data) => this.$numToPercent(val)
          }
        ]
      }
      this.modalColumns = columns
    },
    tableOnSelect(item) {
      if (this.zoneLevel > 32 || this.zoneLevel === item.zone_level) {
        return
      }
      this.drawerVisible = true
      this.modalItem = item
      this.getModalData(item)
      // 神策点击
      // this.$sensors.webClick(`市场-累计收入分析弹框`)
    },
    async getModalData(item) {
      const dParams = {
        start_day: this.dateValue,
        end_day: this.dateValue,
        zone_level: parseInt(item.zone_level) + 1 + ''
      }
      dParams[`${this.getZoneCodeParam(item.zone_level).code}`] = item[this.getZoneCodeParam(item.zone_level).code]
      const result = await this._getOverviewData(dParams)
      this.modalResources = []
      if (result.obj.length) this.modalResources = result.obj
      this.setModalTable(item)
    },
    formatLabel(level) {
      switch (+level) {
        case 30:
          return '战区'
        case 31:
          return '省区'
        case 32:
          return '区域'
        case 33:
          return '网点'
        default:
          break
      }
    }
  },
  mounted() {
    this.init()
  }
}
</script>
<style lang='less' scoped>
</style>
