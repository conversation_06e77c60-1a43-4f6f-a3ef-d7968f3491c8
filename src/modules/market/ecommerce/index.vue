<!--
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-13 16:16:52
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-07-20 18:48:02
-->
<template>
  <div class="chanel-wrap">
    <Ec></Ec>
  </div>
</template>

<script>
// 总览
import Ec from './ec'

export default {
  components: { Ec },
  props: {},
  data() {
    return {}
  },
  mounted() {},
  activated() {
    this.$sensors.webClick(`市场-渠道-电商`)
  },

  methods: {}
}
</script>

<style lang="less" scoped>
</style>
