/*
 * @Author: shigl
 * @Date: 2022-07-18 15:48:45
 * @LastEditTime: 2022-07-20 18:47:55
 * @Description:
 */
import moment from 'moment'

export default {
  namespaced: true,
  name: 'ecommerce',
  state: {
    ec: {
      dateValue: process.env.NODE_ENV === 'development' ? '20220518' : moment().subtract(1, 'days').format('YYYYMMDD')
    }
  },
  getters: {},
  mutations: {
    setEcDateValue(state, date) {
      state.ec.dateValue = date
    }
  },
  actions: {}
}
