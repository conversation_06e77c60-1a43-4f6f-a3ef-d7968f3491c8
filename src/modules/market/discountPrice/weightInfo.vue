<!--
 * @Author: shigl
 * @Date: 2023-06-09 11:02:37
 * @LastEditTime: 2023-06-09 11:02:38
 * @Description:
-->
<!--
 * @Author: JieJw
 * @Date: 2022-04-22 18:25:57
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-06-02 15:26:02
 * @Description:
-->
<template>
  <CardList title="公斤段情况(T-1)" class="mt24">
    <NormalTable
      class="mt24"
      size="small"
      width="220%"
      :columns="tableColumns"
      :dataSource="tableDataSource"
      headBorder
    />
  </CardList>
</template>

<script>
import mixins from '../comjs/mixins'
export default {
  props: {
    selectDate: [Object, String]
  },
  mixins: [mixins],
  data() {
    return {
      tableDataSource: []
    }
  },
  computed: {
    tableColumns() {
      return [
        {
          label: '公斤段',
          dataIndex: 'type',
          width: '2rem',
          fixed: 'left'
        },
        {
          label: '货量',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'month_weight',
              render: (h, val) => this.$numToInteger(val, 0, 1)
            },
            {
              label: '上月整体',
              dataIndex: 'last_month_weight',
              render: (h, val) => this.$numToInteger(val, 0, 1)
            },
            {
              label: '日均环比',
              dataIndex: 'avg_weight_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToPercent(val)}</span>
            }
            // {
            //   label: '昨天',
            //   dataIndex: 'one_day_weight',
            //   render: (h, val) => this.$numToInteger(val, 0, 1)
            // },
            // {
            //   label: '前天',
            //   dataIndex: 'two_day_weight',
            //   render: (h, val) => this.$numToInteger(val, 0, 1)
            // },
            // {
            //   label: '大前天',
            //   dataIndex: 'three_day_weight',
            //   render: (h, val) => this.$numToInteger(val, 0, 1)
            // }
          ]
        },
        {
          label: '货量占比',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'month_weight_rate',
              render: (h, val) => this.$numToPercent(val)
            },
            {
              label: '上月整体',
              dataIndex: 'last_month_weight_rate',
              render: (h, val) => this.$numToPercent(val)
            },
            {
              label: '月环比',
              dataIndex: 'month_weight_rate_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToPercent(val)}</span>
            }
            // {
            //   label: '昨天',
            //   dataIndex: 'one_day_weight_rate',
            //   render: (h, val) => this.$numToPercent(val)
            // },
            // {
            //   label: '前天',
            //   dataIndex: 'two_day_weight_rate',
            //   render: (h, val) => this.$numToPercent(val)
            // },
            // {
            //   label: '大前天',
            //   dataIndex: 'three_day_weight_rate',
            //   render: (h, val) => this.$numToPercent(val)
            // }
          ]
        },
        {
          label: '计费泡比',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'month_pb',
              render: (h, val) => this.$numToInteger(val, 0, 1)
            },
            {
              label: '上月整体',
              dataIndex: 'last_month_pb',
              render: (h, val) => this.$numToInteger(val, 0, 1)
            },
            {
              label: '月环比',
              dataIndex: 'month_pb_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToInteger(val, 0, 1)}</span>
            }
            // {
            //   label: '昨天',
            //   dataIndex: 'one_day_pb',
            //   render: (h, val) => this.$numToInteger(val, 0, 1)
            // },
            // {
            //   label: '前天',
            //   dataIndex: 'two_day_pb',
            //   render: (h, val) => this.$numToInteger(val, 0, 1)
            // },
            // {
            //   label: '大前天',
            //   dataIndex: 'three_day_pb',
            //   render: (h, val) => this.$numToInteger(val, 0, 1)
            // }
          ]
        }
      ]
    }
  },
  watch: {
    zoneCode() {
      this.init()
    },
    selectDate() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getTableData()
    },
    getTableData() {
      const zoneCodeQuery = this.zoneLevel
        ? [{ key: ['', 'big_area_code', 'province_area_code', 'area_code'][this.zoneLevel], value: this.zoneCode }]
        : []
      const tableName = 'sx_kg_segment'
      const conditionList = [
        { key: 'incDay', value: this.$moment(this.selectDate).format('YYYYMMDD') },
        { key: 'zoneLevel', value: ['0', '31', '2', '3'][this.zoneLevel] },
        ...zoneCodeQuery
      ]
      this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        this.tableDataSource = res.obj
        this.$objectSortDown(this.tableDataSource, 'type', [
          '(0-40]',
          '(40-70]',
          '(70-130]',
          '(130-300]',
          '(300-500]',
          '(500-1000]',
          '(1000-3000]',
          '(3000-5000]',
          '5000以上'
        ])
      })
    }
  }
}
</script>

<style lang="less" scoped>
.customize-table {
  /deep/ tbody tr td:nth-child(5) {
    padding: 0 !important;
    >div {
      width: 100%;
      height: 100%;
      flex: 1;

    }
    .three-div {
      flex: 1;
      display: flex;
      justify-content: space-between;
      height: 100%;
      >span {
        flex: 1;
        text-align: center;
        height: 100%;
        &:not(:last-child) {
          border-right: 0.01rem solid #e5e5e5;
        }
      }
    }
  }
}
.three-div {
  display: flex;
  justify-content: space-between;
}
</style>
