<!--
 * @Author: shigl
 * @Date: 2023-06-09 10:51:00
 * @LastEditTime: 2023-06-09 11:07:05
 * @Description:
-->
<template>
  <PageContent>
    <component
      v-for="(item, index) in componentList"
      :key="index"
      :is="componentList[index]"
      :selectDate="selectDate"
    ></component>
    <div style="height: 0.5rem"></div>
  </PageContent>
</template>
<script>
import addManage from './addManage.vue'
import weightInfo from './weightInfo.vue'
import nextLevelInfo from './nextLevelInfo.vue'
import policies from './policies.vue'
export default {
  components: { addManage, weightInfo, nextLevelInfo, policies },
  props: {
    selectDate: [Object, String]
  },
  data() {
    return {
      componentList: ['addManage', 'weightInfo', 'nextLevelInfo', 'policies']
    }
  }
}
</script>
<style lang="less" scoped></style>
