<!--
 * @Author: shigl
 * @Date: 2023-06-09 11:04:00
 * @LastEditTime: 2023-06-09 11:55:16
 * @Description:
-->

<template>
  <div class="mt24">
    <CardList title="政策有效性(T-1)">
      <ShowTipIcon
        slot="tip"
        @btnShowTip="btnShowTip($event, 'jm-djzk-zzyxx')"
        :isFeedback="isFeedback"
        :explaindDetail="explaindDetail"
        @feedback="btnFeedback"
      >
      </ShowTipIcon>
      <NormalTable
        class="mt24"
        size="small"
        :columns="tbaleColumns"
        :dataSource="tableDataSource"
        :onSelect="onSelect"
      ></NormalTable>
    </CardList>
    <KyDataDrawer :visible="visible" @close="visible = false" :title="drawerTitle" height="80%" ref="kyDataDrawerDom">
      <NormalTable
        class="mt24"
        headBorder
        :width="drawerWidth"
        :columns="drawerColumns"
        :dataSource="drawerDataSource"
        size="small"
        maxHeight="10.5rem"
        minHeight="4rem"
        isIndicator
      ></NormalTable>
    </KyDataDrawer>
  </div>
</template>
<script>
import mixins from '../comjs/mixins'
export default {
  mixins: [mixins],
  props: ['selectDate'],
  data() {
    return {
      tableDataSource: [],
      visible: false,
      drawerWidth: '250%',
      drawerTitle: '4折以下网点汇总明细',
      drawerDataSource: []
    }
  },
  computed: {
    tbaleColumns() {
      const formatRate = value => {
        if (/%/.test(value)) {
          return this.$numToPercent(value.replace('%', '') / 100)
        }
        return value
      }
      return [
        {
          label: '',
          dataIndex: 'zone_name',
          fixed: 'left',
          isMoreIcon: true,
          render: (h, value) => {
            if (/顺心总部/.test(value)) {
              return '总计'
            }
            if (/SX|省区|【|】/g.test(value)) {
              return value.replace(/SX|省区|【|】/g, '')
            }
            return value
          }
        },
        {
          label: '网点总数',
          dataIndex: 'dept_count',
          render: (h, value) => this.$numToInteger(value, 0, 1, 0)
        },
        {
          label: '参与方案网点数',
          dataIndex: 'subscribe_dept_count',
          render: (h, value) => this.$numToInteger(value, 0, 1, 0)
        },
        {
          label: '政策参与率',
          dataIndex: 'subscribe_per',
          render: (h, value) => formatRate(value)
        },
        {
          label: '环比',
          dataIndex: 'subscribe_per_hb',
          render: (h, value) => formatRate(value)
        },
        {
          label: '参与包仓网点数',
          dataIndex: 'bc_dept_count',
          render: (h, value) => this.$numToInteger(value, 0, 1, 0)
        },
        {
          label: '包仓参与率',
          dataIndex: 'bc_per',
          render: (h, value) => formatRate(value)
        },
        {
          label: '环比',
          dataIndex: 'bc_per_hb',
          render: (h, value) => formatRate(value)
        },
        {
          label: '收入提升网点数',
          dataIndex: 'income_up_dept_count',
          render: (h, value) => this.$numToInteger(value, 0, 1, 0)
        },
        {
          label: '政策有效性',
          dataIndex: 'income_up_per',
          render: (h, value) => formatRate(value)
        },
        {
          label: '环比',
          dataIndex: 'income_up_per_hb',
          render: (h, value) => formatRate(value)
        },
        {
          label: '4折以下网点数',
          dataIndex: 'low_discount_dept_count',
          render: (h, value) => this.$numToInteger(value, 0, 1, 0)
        }
      ]
    },
    drawerColumns() {
      return [
        {
          label: '省区',
          width: '1.2rem',
          fixed: 'left',
          dataIndex: 'src_area_name',
          render: (h, value) => {
            if (/顺心总部/.test(value)) {
              return '总计'
            }
            if (/SX|省区|【|】/g.test(value)) {
              return value.replace(/SX|省区|【|】/g, '')
            }
            return value
          }
        },
        {
          label: '网点名称',
          fixed: 'left',
          width: '2rem',
          dataIndex: 'src_division_name',
          render: (h, value) => {
            if (/顺心总部/.test(value)) {
              return '总计'
            }
            if (/SX|省区|【|】/g.test(value)) {
              return value.replace(/SX|省区|【|】/g, '')
            }
            return value
          }
        },
        {
          label: '收入(万)',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'income',
              render: (h, value) => this.$numToInteger(value, 1, 10000)
            },
            {
              label: '上月累计',
              dataIndex: 'income_sy',
              render: (h, value) => this.$numToInteger(value, 1, 10000)
            },
            {
              label: '日均环比',
              dataIndex: 'rjsr_hb',
              render: (h, value) => this.$numToPercent(value / 100)
            }
          ]
        },
        {
          label: '货量(吨)',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'meterage_weight_qty',
              render: (h, value) => this.$numToInteger(value, 1, 1000)
            },
            {
              label: '上月累计',
              dataIndex: 'meterage_weight_qty_sy',
              render: (h, value) => this.$numToInteger(value, 1, 1000)
            },
            {
              label: '日均环比',
              dataIndex: 'rjhl_hb',
              render: (h, value) => this.$numToPercent(value / 100)
            }
          ]
        },
        {
          label: '结算单价(元/kg)',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'jsdj',
              render: (h, value) => this.$numToInteger(value, 3, 1, 3)
            },
            {
              label: '上月累计',
              dataIndex: 'jsdj_sy',
              render: (h, value) => this.$numToInteger(value, 3, 1, 3)
            },
            {
              label: '环比',
              dataIndex: 'jsdj_hb',
              render: (h, value) => this.$numToInteger(value, 3, 1, 3)
            }
          ]
        },
        {
          label: '综合折扣',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'zhzk',
              render: (h, value) => this.$numToPercent(value / 100)
            },
            {
              label: '上月累计',
              dataIndex: 'zhzk_sy',
              render: (h, value) => this.$numToPercent(value / 100)
            },
            {
              label: '环比',
              dataIndex: 'zhzk_hb',
              render: (h, value) => this.$numToPercent(value / 100)
            }
          ]
        }
      ]
    }
  },
  watch: {
    selectDate() {
      this.initOptions()
    },
    zoneCode() {
      this.initOptions()
    }
  },
  methods: {
    async getEffectivenessData() {
      const params = {
        inc_day: this.$moment(this.selectDate).format('YYYYMMDD'),
        zone_code: this.zoneCode === '001' ? '0' : this.zoneCode
      }
      const { obj } = await this.sendTwoDimenRequest(
        'ads_sx_waybill_policy_effectiveness_day_sum_di',
        this.forMapData(params)
      )
      this.$objectSortDown(obj, 'subscribe_per')
      this.tableDataSource = obj
    },
    async onSelect(item, index) {
      this.visible = true
      const code = item['zone_code']
      let levelMap = {}
      if (!+this.zoneLevel && !index) {
        levelMap = {}
      } else {
        levelMap = {
          s_area_code: code,
          src_area_code: code
        }
      }

      const params = {
        inc_day: this.$moment(this.selectDate).format('YYYYMMDD'),
        ...levelMap
      }
      const { obj } = await this.sendTwoDimenRequest(
        'ads_sx_waybill_policy_effectiveness_month_sum_di',
        this.forMapData(params)
      )
      this.$objectSortDown(obj, 'meterage_weight_qty_sy')
      this.drawerDataSource = obj
    },
    initOptions() {
      this.getEffectivenessData()
    }
  },
  mounted() {
    this.initOptions()
  }
}
</script>
<style lang="less" scoped></style>
