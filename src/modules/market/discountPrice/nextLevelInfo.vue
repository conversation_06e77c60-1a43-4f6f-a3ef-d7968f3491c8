<!--
 * @Author: shigl
 * @Date: 2023-06-09 11:03:29
 * @LastEditTime: 2023-06-09 11:03:30
 * @Description:
-->
<!--
 * @Author: JieJw
 * @Date: 2022-04-22 18:25:57
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-06-02 15:24:14
 * @Description:
-->
<template>
  <CardList :title="`${name}情况(T-1)`" class="mt24">
    <NormalTable
      class="mt24"
      size="small"
      width="280%"
      :columns="tableColumns"
      :dataSource="tableDataSource"
      headBorder
    />
  </CardList>
</template>

<script>
import mixins from '../comjs/mixins'
export default {
  props: {
    selectDate: [Object, String]
  },
  mixins: [mixins],
  data() {
    return {
      tableDataSource: []
    }
  },
  computed: {
    tableColumns() {
      return [
        {
          label: this.name,
          dataIndex: ['province_area_name', 'province_area_name', 'area_name', 'area_name'][this.zoneLevel],
          width: '1.4rem',
          fixed: 'left',
          render: (h, val) => {
            return (
              <div class='flex_start'>
                {val.replace(/SX|省区|区域/g, '')}
              </div>
            )
          }
        },
        {
          label: '收入',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'income',
              render: (h, val) => this.$numToInteger(val, 0, 1)
            },
            {
              label: '日均环比',
              dataIndex: 'income_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToPercent(val)}</span>
            }
          ]
        },
        {
          label: '货量',
          align: 'center',
          children: [
            {
              label: '本月累计',
              dataIndex: 'meterage',
              render: (h, val) => this.$numToInteger(val, 0, 1)
            },
            {
              label: '日均环比',
              dataIndex: 'weight_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToPercent(val)}</span>
            }
          ]
        },
        {
          label: '结算单价',
          align: 'center',
          children: [
            {
              label: '结算单价',
              dataIndex: 'settlement_unit',
              render: (h, val) => this.$numToInteger(val, 3, 1, 3)
            },
            {
              label: '月累计环比',
              dataIndex: 'settlement_unit_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToInteger(val, 3, 1, 3)}</span>
            }
          ]
        },
        {
          label: '包仓单价',
          align: 'center',
          children: [
            {
              label: '包仓单价',
              dataIndex: 'house_unit',
              render: (h, val) => this.$numToInteger(val, 3, 1, 3)
            },
            {
              label: '月累计环比',
              dataIndex: 'house_unit_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToInteger(val, 3, 1, 3)}</span>
            }
          ]
        },
        {
          label: '综合折扣',
          align: 'center',
          children: [
            {
              label: '综合折扣',
              dataIndex: 'synth_discount',
              render: (h, val) => this.$numToPercent(val)
            },
            {
              label: '月累计环比',
              dataIndex: 'synth_discount_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToPercent(val)}</span>
            }
          ]
        },
        {
          label: '小票占比',
          align: 'center',
          children: [
            {
              label: '小票占比',
              dataIndex: 'small_rate',
              render: (h, val) => this.$numToPercent(val)
            },
            {
              label: '月累计环比',
              dataIndex: 'small_rate_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToPercent(val)}</span>
            }
          ]
        },
        {
          label: '计费泡比',
          align: 'center',
          children: [
            {
              label: '计费泡比',
              dataIndex: 'fee_rate',
              render: (h, val) => this.$numToInteger(val, 0, 1)
            },
            {
              label: '月累计环比',
              dataIndex: 'fee_rate_chain',
              render: (h, val) => <span class={val >= 0 ? 'fontsuccess' : 'fontfail'}>{this.$numToInteger(val, 0, 1)}</span>
            }
          ]
        }
      ]
    },
    name() {
      return ['省区', '省区', '区域', '区域'][this.zoneLevel]
    }
  },
  watch: {
    zoneCode() {
      this.init()
    },
    selectDate() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getTableData()
    },
    getTableData() {
      const zoneCodeQuery = this.zoneLevel
        ? [{ key: ['', 'big_area_code', 'province_area_code', 'area_code'][this.zoneLevel], value: this.zoneCode }]
        : []
      const tableName = 'sx_market_join_data'
      const conditionList = [
        { key: 'incDay', value: this.$moment(this.selectDate).format('YYYYMMDD') },
        { key: 'zoneLevel', value: ['32', '32', '33', '33'][this.zoneLevel] },
        ...zoneCodeQuery
      ]
      this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        this.tableDataSource = res.obj
        this.$objectSortDown(this.tableDataSource, 'income')
      })
    }
  }
}
</script>

<style lang="less" scoped>
</style>
