<!--
 * @Author: JieJw
 * @Date: 2022-04-22 18:01:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-06-09 14:38:42
 * @Description:
-->
<template>
  <div class="add-manage-wrap">
    <CardList :title="`费用项情况(${month}月累计值T-1)`">
      <ShowTipIcon
        slot="tip"
        @btnShowTip="btnShowTip($event, 'jm-djzk-fyxxq')"
        :isFeedback="isFeedback"
        :explaindDetail="explaindDetail"
        @feedback="btnFeedback"
      >
      </ShowTipIcon>
    </CardList>
    <NormalTable
      :onSelect="onSelect"
      :columns="tableColumns"
      :dataSource="tableDataSource"
      maxHeight="8rem"
      minHeight="2rem"
      size="small"
      :childColumns="childColumns"
    />
  </div>
</template>

<script>
import mixins from '../comjs/mixins'
export default {
  props: {
    selectDate: [Object, String]
  },
  mixins: [mixins],
  components: {},
  data() {
    return {
      tabData: [
        { label: '单价', value: '单价' },
        { label: '折扣率', value: '折扣率' },
        { label: '票件比', value: '票件比' },
        { label: '泡比', value: '泡比' },
        { label: '长短线', value: '长短线' }
      ],
      tabValue: '单价',
      djColumn: [
        {
          label: '结算单价(元/kg)',
          dataIndex: 'current_settlement_unit',
          render: val => this.$numToInteger(val, 3, 1, 3),
          last: 'last_mon_settlement_unit',
          hbIndex: 'month_settlement_unit_rate'
        },
        {
          label: '中转费单价(元/kg)',
          dataIndex: 'current_transit_unit',
          last: 'last_mon_transit_unit',
          render: val => this.$numToInteger(val, 3, 1, 3),
          hbIndex: 'month_transit_unit_rate'
        },
        {
          label: '优惠中转费单价(元/kg)',
          dataIndex: 'current_discounts_unit',
          last: 'last_mon_discounts_unit',
          render: val => this.$numToInteger(val, 3, 1, 3),
          hbIndex: 'month_discounts_unit_rate'
        },
        {
          label: '操作费单价(元/kg)',
          dataIndex: 'current_oper_unit',
          render: val => this.$numToInteger(val, 3, 1, 3),
          hbIndex: 'month_oper_unit_rate'
        },
        {
          label: '包仓单价(元/kg)',
          dataIndex: 'current_house_unit',
          render: val => this.$numToInteger(val, 3, 1, 3),
          hbIndex: 'month_house_unit_rate'
        },
        {
          label: '资源调节费(元/kg)',
          dataIndex: 'current_month_res_adj_unit',
          render: val => this.$numToInteger(val, 3, 1, 3),
          last: 'pre_month_res_adj_unit',
          hbIndex: 'comp_pre_res_adj_unit'
        }
      ],
      zkColumn: [
        {
          label: '综合折扣',
          dataIndex: 'current_synth_discount',
          render: val => this.$numToPercent(val, 1),
          hbIndex: 'month_synth_discount_rate'
        },
        {
          label: '中转折扣',
          dataIndex: 'current_transit_discount',
          render: val => this.$numToPercent(val, 1),
          hbIndex: 'month_transit_discount_rate'
        },
        {
          label: '系统折扣',
          dataIndex: 'current_sys_discount',
          render: val => this.$numToPercent(val, 1),
          hbIndex: 'month_sys_discount_rate'
        }
      ],
      pbColumns: [
        {
          label: '实际泡比',
          dataIndex: 'current_real_rate',
          render: val => this.$numToInteger(val, 0, 1),
          hbIndex: 'month_real_rate_rate'
        },
        {
          label: '计费泡比',
          dataIndex: 'current_fee_rate',
          render: val => this.$numToInteger(val, 0, 1),
          hbIndex: 'month_fee_rate_rate'
        }
      ],
      cdxColumn: [
        {
          label: '同分拨货量占比',
          dataIndex: 'current_in_center_meterage_wt_rate',
          render: val => this.$numToPercent(val, 1),
          hbIndex: 'month_in_center_meterage_wt_rate_rate'
        },
        {
          label: '省内货量占比',
          dataIndex: 'current_in_prov_meterage_wt_rate',
          render: val => this.$numToPercent(val, 1),
          hbIndex: 'month_in_prov_meterage_wt_rate_rate'
        },
        {
          label: '跨省货量占比',
          dataIndex: 'current_out_prov_meterage_wt_rate',
          render: val => this.$numToPercent(val, 1),
          hbIndex: 'month_out_prov_meterage_wt_rate_rate'
        }
      ],
      addManageData: {},
      tableDataSource: []
    }
  },
  computed: {
    pjColumn() {
      return [
        {
          label: this.zoneLevel === 0 ? '开单票数(万票)' : '开单票数(票)',
          dataIndex: 'current_votes',
          render: val => (this.zoneLevel === 0 ? this.$numToInteger(val, 1, 10000) : this.$numToInteger(val, 0, 1)),
          hbIndex: 'month_votes_rate'
        },
        {
          label: this.zoneLevel === 0 ? '开单件数(万件)' : '开单件数(件)',
          dataIndex: 'current_quantity',
          render: val => (this.zoneLevel === 0 ? this.$numToInteger(val, 1, 10000) : this.$numToInteger(val, 0, 1)),
          hbIndex: 'month_quantity_rate'
        },
        {
          label: '票均件数(件/票)',
          dataIndex: 'current_quantity_per_votes',
          render: val => this.$numToInteger(val, 2, 1),
          hbIndex: 'month_quantity_per_votes_rate'
        },
        {
          label: '票均重量(kg/票)',
          dataIndex: 'current_meterage_wt_per_votes',
          render: val => this.$numToInteger(val, 0, 1),
          hbIndex: 'month_meterage_wt_per_votes_rate'
        },
        {
          label: '票均结算收入(元/票)',
          dataIndex: 'current_settlement_fee_per_votes',
          render: val => this.$numToInteger(val, 0, 1),
          hbIndex: 'month_settlement_fee_per_votes_rate'
        }
      ]
    },
    childColumnsList() {
      return [this.djColumn, this.zkColumn, this.pjColumn, this.pbColumns, this.cdxColumn]
    },
    month() {
      return this.$moment().subtract(1, 'days').format('M')
    },
    tableColumns() {
      return [
        {
          label: '指标项',
          dataIndex: 'label',
          render: (h, val) => <span class='fw700 grey333'>{this.$noChange(val)}</span>,
          isExpandIcon: true
        },
        {
          label: '本月累计',
          dataIndex: 'add',
          render: (h, val) => ''
        },
        {
          label: '上月整体',
          dataIndex: 'last',
          render: (h, val) => ''
        },
        {
          label: '环比',
          dataIndex: 'hb',
          render: (h, val) => ''
        }
      ]
    },
    childColumns() {
      return [
        {
          label: '指标项',
          dataIndex: 'label',
          render: (h, val) => this.$noChange(val),
          isExpandIcon: true
        },
        {
          label: '本月累计',
          dataIndex: 'add',
          render: (h, val) => this.$noChange(val)
        },
        {
          label: '上月整体',
          dataIndex: 'last',
          render: (h, val) => this.$noChange(val)
        },
        {
          label: '环比',
          dataIndex: 'hb',
          render: (h, val, res, d) => (
            <span>
              {this.$noChange(val)}
              <i class='iconfont icon-dayuhao fw700 fs20 ml8'></i>
            </span>
          )
        }
      ]
    }
  },

  watch: {
    zoneCode() {
      this.getFieldData()
    },
    selectDate() {
      this.getFieldData()
    }
  },
  mounted() {
    this.getFieldData()
  },
  methods: {
    getFieldData() {
      const tableName = 'sx_manage_monit_info_22'
      const startDay = this.$moment(this.selectDate).subtract(1, 'days').format('YYYYMMDD')
      const endDay = startDay
      const zoneLevel = '3' + this.zoneLevel
      const fzbData = this.zoneLevel ? [{ key: this.getKey(zoneLevel), value: this.zoneCode }] : []
      const conditionList = [
        { key: 'startDay', value: startDay },
        { key: 'endDay', value: endDay },
        { key: 'zone_level', value: zoneLevel },
        ...fzbData
      ]
      this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        this.addManageData = res.obj[0] || {}
        // for (const key in this.addManageData) {
        //   if (Object.hasOwnProperty.call(this.addManageData, key)) {
        //     if (key === 'pre_month_res_adj_unit') {
        //       this.addManageData['last_mon_month_res_adj_unit'] = this.addManageData[key]
        //     }
        //   }
        // }
        this.handlerAddManageData()
      })
    },
    handlerAddManageData() {
      this.tableDataSource = this.tabData.map((item, index) => {
        return {
          label: item.label,
          isExpandIcon: true,
          expand: true,
          child: this.childColumnsList[index].map((_item, _index) => {
            return {
              label: _item.label,
              add: _item.render(this.addManageData[_item.dataIndex]),
              last: _item.render(this.addManageData[_item.dataIndex.replace('current', 'last_mon')]),
              hb: (
                <span class={this.addManageData[_item.hbIndex] > 0 ? 'fontsuccess' : 'fontfail'}>
                  {['current_votes', 'current_quantity'].includes(_item.dataIndex)
                    ? this.$numToPercent(this.addManageData[_item.hbIndex], 1)
                    : _item.render(this.addManageData[_item.hbIndex])}
                </span>
              )
            }
          })
        }
      })
    },
    onSelect(res, index) {
      if (!res.hasOwnProperty('expand')) {
        let dataIndex
        const label = res.label
        const target = this.djColumn.find(item => item.label === label)
        if (target) {
          dataIndex = target.dataIndex
          this.tabValue = '单价'
        }
        const target2 = this.zkColumn.find(item => item.label === label)
        if (target2) {
          dataIndex = target2.dataIndex
          this.tabValue = '折扣率'
        }
        const target3 = this.pjColumn.find(item => item.label === label)
        if (target3) {
          dataIndex = target3.dataIndex
          this.tabValue = '票件比'
        }
        const target4 = this.pbColumns.find(item => item.label === label)
        if (target4) {
          dataIndex = target4.dataIndex
          this.tabValue = '泡比'
        }
        const target5 = this.cdxColumn.find(item => item.label === label)
        if (target5) {
          dataIndex = target5.dataIndex
          this.tabValue = '长短线'
        }
        this.$router.push({
          name: 'addManageTrend',
          query: {
            tabValue: this.tabValue,
            value: (dataIndex || '').replace(/current_/g, ''),
            zoneCode: this.zoneCode,
            zoneLevel: this.zoneLevel,
            rightName: this.zoneName
          }
        })
      } else {
        res.expand = !res.expand
      }
    },
    getKey(val) {
      switch (val) {
        case '31':
          return 'big_area_code'
        case '32':
          return 'province_area_code'
        case '33':
          return 'area_code'
        case '34':
          return 'dept_code'
        default:
          return ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.add-manage-wrap {
  background: #fff;
  padding-bottom: 0.2rem;
  .data-box {
    padding: 0 0.2rem;
    display: flex;
    flex-wrap: wrap;
    > div {
      max-width: calc((100% - 0.4rem) / 3);
      min-width: calc((100% - 0.4rem) / 3);
      height: 1.45rem;
      background: #fff;
      padding: 0.2rem 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 0.08rem 0.18rem 0 rgba(196, 202, 210, 0.3);
      margin-right: 0.2rem;
      margin-top: 0.2rem;
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
    .label {
      font-family: PingFangSC-Medium;
      font-size: 0.2rem;
      color: #333;
      font-weight: 500;
      line-height: 0.28rem;
    }
    .hb {
      font-family: PingFangSC-Regular;
      font-size: 0.2rem;
      line-height: 0.28rem;
      color: #808285;
    }
    .value {
      font-family: Roboto-Medium;
      font-size: 0.28rem;
      line-height: 0.33rem;
      color: #333;
      font-weight: 500;
      margin: 0.04rem 0 0.08rem;
    }
    .hb > span {
      font-size: 0.2rem;
      line-height: 0.28rem;
      margin-left: 0.1rem;
      font-weight: 500;
    }
  }
}
</style>
