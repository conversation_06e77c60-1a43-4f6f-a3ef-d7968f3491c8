<template>
  <div class="realtime-container ">
    <div class="date-container">
      <KyDatePicker type="day" :dateValue="selectDate" @onChange="dateChange" :holidayData="holidayData"></KyDatePicker>
    </div>
    <div class="realtime-box-wrapper">
      <TitleTabs v-if="todayBool" :tabData="tabData" :tabValue="tabValue" @change="tabChange" />
      <div style="padding: 0.28rem 0">
        <div v-if="todayBool">
          <van-swipe ref="realtimeSwipe" :loop="false" :show-indicators="false" @change="changeSlide">
            <van-swipe-item class="pd_lr20">
              <RealtimeDataList :columns="realtimeColumns" :dataSource="realTimeData" @showTip="showTip" />
              <!-- <BtnShowMore class="show-wrap" :textList="textList"></BtnShowMore> -->
              <div v-if="zoneData.levelKey !== 'dept'" class="show-wrap" @click="btnShowDital('realtime')">
                {{ detailText }}<i class="iconfont icon-pack-right"></i>
              </div>
            </van-swipe-item>
            <van-swipe-item class="pd_lr20">
              <RealtimeDataList :columns="pastColumns" :dataSource="baseData" @showTip="showTip" />
            </van-swipe-item>
          </van-swipe>
        </div>
        <div class="pd_lr20" v-show="!todayBool">
          <RealtimeDataList :columns="pastColumns" :dataSource="baseData" @showTip="showTip" />
        </div>
      </div>
    </div>

    <KyDataCommentsModel
      :isShow="tipVisible"
      @close="tipVisible = false"
      :isFeedback="isFeedback"
      @feedback="btnFeedback"
    >
      <ExplainContent :detail="explaindDetail"></ExplainContent>
    </KyDataCommentsModel>
    <KyDataDrawer
      :visible="visible"
      :title="diaTitle"
      height="80%"
      @close="visible = false"
      @drawerHeight="drawerHeight"
    >
      <TitleTabs :tabData="['收入', '货量']" :tabValue="diaTabIndex" @changeTab="diaTabChange" />
      <NormalTable
        class="mt20"
        :columns="diaTableColumns"
        :dataSource="diaTableDataSource"
        :maxHeight="diaTableMaxHeight"
      />
    </KyDataDrawer>
  </div>
</template>

<script>
import mixins from '../comjs/mixins'
import RealtimeDataList from '../components/realtimeDataList'
export default {
  mixins: [mixins],
  components: {
    RealtimeDataList
  },
  data() {
    return {
      baseData: {},
      baseData1: {}, // 兼容之前的baseData数据
      selectDate: this.$moment().format('YYYY-MM-DD'),
      pasttimeData: {},
      tabData: [
        { label: '实时战况', value: 0 },
        { label: '昨日战况', value: 1 }
      ],
      tabValue: 0,
      tipVisible: false,
      timeId: '',
      realtimeDitalData: [],
      visible: false,
      diaTitle: '',
      diaTableMaxHeight: '9rem',
      diaTableColumns: [],
      diaTableDataSource: [],
      diaTabIndex: 0
    }
  },
  computed: {
    detailText() {
      return `查看${this.zoneData.dLevelName}详情`
    },
    todayBool() {
      return this.$moment(this.selectDate).format('YYYYMMDD') === this.$moment().format('YYYYMMDD')
    },
    realtimeBool() {
      return this.todayBool && !this.tabValue
    },
    realtimeColumns() {
      const createJb = val => {
        return <div class={val > 0 ? 'green fs20' : 'orange fs20'}>{this.$numToPercent(val, 1)}</div>
      }
      // const createHb = val => {
      //   return <div class={'fs20'}>{this.$numToPercent(val, 1)}</div>
      // }

      const createDate = val => {
        return <div>{val ? this.$moment(val).format('YY.MM.DD') : '--'}</div>
      }
      return [
        [
          {
            label: '实时收入(万元)',
            dataIndex: 'inc_income',
            // dataIndex: ['income', 'incomeJm', 'incomeQd'][this.firstTabIndex],
            type: 'column',
            render: (h, val) => {
              if (this.zoneLevel <= 2 && this.firstTabIndex !== 2) {
                return (
                  <div>
                    {this.$numToInteger(val, 0)} <span class="fs24 fw_normal ml4">(不含包仓)</span>
                  </div>
                )
              }
              return this.$numToInteger(val, 0)
            }
          },
          [
            {
              label: () => <div class="fs20">周环比</div>,
              dataIndex: 'inc_income_hb',
              render: (h, val) => createJb(val)
            }
          ],
          {
            label: '历史峰值',
            tag: (h, val, item) => createDate(item['max_income_date']),
            dataIndex: 'max_income',
            render: (h, val) => this.$numToInteger(val, 0)
          }
        ],
        [
          {
            label: '实时货量(吨)',
            dataIndex: 'inc_weight',
            // dataIndex: ['weight', 'weightJm', 'weightQd'][this.firstTabIndex],
            type: 'column',
            render: (h, val) => this.$numToInteger(val, 0)
          },
          [
            {
              label: () => <div class="fs20">周环比</div>,
              dataIndex: 'inc_weight_hb',
              render: (h, val) => createJb(val)
            }
          ],
          {
            label: '历史峰值',
            tag: (h, val, item) => createDate(item['max_weight_date']),
            dataIndex: 'max_weight',
            render: (h, val) => this.$numToInteger(val, 0)
          }
        ]
      ]
    },
    pastColumns() {
      const createJb = val => {
        return <div class={val > 0 ? 'green fs20' : 'orange fs20'}>{this.$numToPercent(val, 1)}</div>
      }
      // const createHb = val => {
      //   return <div class={'fs20'}>{this.$numToPercent(val, 1)}</div>
      // }

      const createDate = val => {
        return <div>{val ? this.$moment(val).format('YY.MM.DD') : '--'}</div>
      }
      const zlabel = () => {
        if (
          this.$moment()
            .subtract(1, 'days')
            .format('YYYYMMDD') < this.selectDate
        ) {
          return '昨日'
        }
        return ''
      }
      return [
        [
          {
            label: zlabel() + '收入(万元)',
            dataIndex: 'last_income',
            type: 'column',
            render: (h, val) => this.$numToInteger(val, 0)
          },
          [
            {
              label: () => <div class="fs20">周环比</div>,
              dataIndex: 'last_weekday_inc_income',
              render: (h, val) => createJb(val)
            }
          ],
          {
            label: '历史峰值',
            tag: (h, val, item) => createDate(item['max_income_date']),
            dataIndex: 'max_income',
            render: (h, val) => this.$numToInteger(val, 0)
          }
        ],
        [
          {
            label: zlabel() + '货量(吨)',
            dataIndex: 'last_weight_qty',
            type: 'column',
            render: (h, val) => this.$numToInteger(val, 0)
          },
          [
            {
              label: () => <div class="fs20">周环比</div>,
              dataIndex: 'last_weekday_inc_weight',
              render: (h, val) => createJb(val)
            }
          ],
          {
            label: '历史峰值',
            tag: (h, val, item) => createDate(item['max_weight_date']),
            dataIndex: 'max_weight',
            render: (h, val) => this.$numToInteger(val, 0)
          }
        ]
      ]
    },
    realTimeData() {
      const { max_income, max_weight, max_weight_date, max_income_date } = this.baseData
      return {
        ...this.baseData1,
        max_income,
        max_weight,
        max_weight_date,
        max_income_date
      }
    }
  },
  watch: {
    selectDate(val) {
      this.$nextTick(() => {
        this.init()
      })
    },
    firstTabIndex() {
      this.init()
    },
    zoneCode() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    drawerHeight(height) {
      this.diaTableMaxHeight = `${height - 140}px`
    },
    init() {
      clearInterval(this.timeId)
      if (this.todayBool) {
        this.timeId = setInterval(() => {
          this.getIncomeIndexData(false)
          this.getRealtimeDitalData()
        }, 60000)
      }
      // 请求实时数据
      this.getIncomeIndexData()
      this.getRealtimeDitalData()
      setTimeout(() => {
        if (this.$refs.realtimeSwipe) {
          this.$refs.realtimeSwipe.resize()
          this.$refs.realtimeSwipe.swipeTo(this.tabValue)
        }
      }, 500)
    },

    async getIncomeIndexData() {
      const [data1, data2, data3] = await Promise.all([
        this.getNewRlData(),
        this.getOldRlData(),
        this.getNewLastWeekData()
      ])
      data2.inc_income = data2.real_income_jb ? data1[['income', 'incomeJm', 'incomeQd'][this.firstTabIndex]] / data2.real_income_jb - 1 : null
      data2.inc_weight = data2.real_weight_jb ? data1[['weight', 'weightJm', 'weightQd'][this.firstTabIndex]] / data2.real_weight_jb - 1 : null
      data1.inc_income_hb = data3[['income', 'incomeJm', 'incomeQd'][this.firstTabIndex]] ? data1[['income', 'incomeJm', 'incomeQd'][this.firstTabIndex]] / data3[['income', 'incomeJm', 'incomeQd'][this.firstTabIndex]] - 1 : null
      data1.inc_weight_hb = data3[['weight', 'weightJm', 'weightQd'][this.firstTabIndex]] ? data1[['weight', 'weightJm', 'weightQd'][this.firstTabIndex]] / data3[['weight', 'weightJm', 'weightQd'][this.firstTabIndex]] - 1 : null
      // 不要合并data3
      this.baseData = { ...data1, ...data2 }
    },

    getNewRlData() {
      if (!this.todayBool) return Promise.resolve({})
      const code = ['', 'bigAreaCode', 'provinceAreaCode', 'areaCode', 'deptCode'][this.zoneLevel]
      const codeObj = this.zoneLevel ? { [code]: this.zoneCode } : {}
      return this.sendJavaRequest({
        // url: '/resourceServices/sxRealTimeIncomeAndWeight/query',
        url: '/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query',
        data: {
          incDay: this.$moment(this.selectDate).format('YYYYMMDD'),
          ...codeObj
        }
      }).then(res => {
        return res.obj || {}
      })
    },
    getNewLastWeekData() {
      if (!this.todayBool) return Promise.resolve({})
      const code = ['', 'bigAreaCode', 'provinceAreaCode', 'areaCode', 'deptCode'][this.zoneLevel]
      const codeObj = this.zoneLevel ? { [code]: this.zoneCode } : {}
      return this.sendJavaRequest({
        // url: '/resourceServices/sxRealTimeIncomeAndWeight/query/lastWeek',
        url: '/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query/lastWeek',
        data: {
          incDay: this.$moment(this.selectDate).format('YYYYMMDD'),
          ...codeObj
        }
      }).then(res => {
        return res.obj || {}
      })
    },
    getOldRlData() {
      const incDay = this.todayBool
        ? this.$moment(this.selectDate)
            .add(-1, 'days')
            .format('YYYYMMDD')
        : this.$moment(this.selectDate).format('YYYYMMDD')
      const zoneCodeQuery = this.zoneLevel
        ? [
            {
              key: ['', 'big_area_code', 'province_area_code', 'area_code', 'dept_code'][this.zoneLevel],
              value: this.zoneCode
            }
          ]
        : []
      const conditionList = [
        {
          key: 'groupByCode',
          value: ["'001'", 'big_area_code', 'province_area_code', 'area_code', 'dept_code'][this.zoneLevel]
        },
        {
          key: 'groupByName',
          value: ["'全网'", 'big_area_name', 'province_area_name', 'area_name', 'dept_name'][this.zoneLevel]
        },
        { key: 'incDay', value: incDay },
        { key: 'zoneLevel', value: +this.zoneLevel === 1 ? '31' : this.zoneLevel },
        { key: 'type', value: ['总体', '加盟', '渠道'][this.firstTabIndex] },
        ...zoneCodeQuery
      ]
      return this.sendTwoDimenRequest('sx_manage_monit_info_2022', conditionList).then(res => {
        return res.obj[0] || {}
      })
    },

    dateChange(date) {
      this.selectDate = date
    },
    changeSlide(val) {
      this.tabValue = val
    },
    tabChange(val) {
      this.tabValue = val
      this.$nextTick(() => {
        if (this.$refs.realtimeSwipe) {
          this.$refs.realtimeSwipe.resize()
          this.$refs.realtimeSwipe.swipeTo(val)
        }
      })
    },
    showTip() {
      this.tipVisible = true
      // 名词解释反馈接口
      if (!this.isWangguan) {
        switch (this.firstTabIndex) {
          case 0:
            this.getExplainData(this.realtimeBool ? 'zt-srhl-sszk' : 'zt-srhl-zrzk')
            break
          case 1:
            this.getExplainData(this.realtimeBool ? 'jm-srhl-sszk' : 'jm-srhl-zrzk')
            break
          case 2:
            this.getExplainData(this.realtimeBool ? 'qd-srhl-sszk' : 'qd-srhl-zrzk')
            break
        }
      } else {
        this.getExplainData(this.realtimeBool ? 'sxzk-sc-sszk-wg' : 'sxzk-sc-zrzk-wg')
      }
    },
    btnShowDital(type) {
      this.visible = true
      this.diaTitle = `${this.zoneData.dLevelName}实时详情`
      // this.getRealtimeDitalData()
    },
    diaTabChange({ index }) {
      this.diaTabIndex = index
      this.setDiaTable(this.realtimeDitalData)
    },
    async getRealtimeDitalData() {
      const levelMap = {
        bu: { queryType: 1 },
        province: { provinceAreaCode: this.zoneCode, queryType: 2 },
        area: { provinceAreaCode: this.provinceCode, areaCode: this.zoneCode, queryType: 3 }
      }
      // http://yapi.sit.sf-express.com/project/734/interface/api/446646
      // queryType	number 必须 查询类型： 1：省区 2：区域 3：网管
      // countType	number 必须 统计类型： 1：收入 2：货量
      // provinceAreaCode	string 非必须 省区编码 选择省区或区域时传
      // areaCode	string 非必须区域编码 选择区域时传
      // incDay	string 非必须 日期
      const { obj } = await this.sendJavaRequest({
        // url: '/resourceServices/sxRealTimeIncomeAndWeight/query/list',
        url: '/cockpit/realtimeQuery/sxRealTimeIncomeAndWeight/query/list',
        data: {
          incDay: this.$moment(this.selectDate).format('YYYYMMDD'),

          ...levelMap[this.zoneData.levelKey]
        }
      })
      this.realtimeDitalData = obj
      this.setDiaTable(obj)
    },
    setDiaTable(res) {
      this.diaTableColumns = []
      this.diaTableDataSource = []
      const result = res[['totalList', 'jmList', 'qdList'][this.firstTabIndex]]
      if (result?.length) {
        const keyRank = ['incomePercent', 'weightPercent'][this.diaTabIndex]

        const levelName = {
          bu: 'provinceAreaName',
          province: 'areaName',
          area: 'managerName'
        }
        const obj1 = result.filter(item => item[levelName[this.zoneData.levelKey]] === '总计')
        if (obj1.length) {
          obj1[0]['isWeight'] = 1
          // 实时数据概览
          this.baseData1 = {
            inc_income: obj1[0].income,
            inc_weight: obj1[0].weight,
            inc_income_hb: obj1[0].lastWeekdayIncIncome,
            inc_weight_hb: obj1[0].lastWeekdayIncWeight
          }
        }
        const obj2 = result.filter(item => item[levelName[this.zoneData.levelKey]] !== '总计')
        this.$objectSortDown(obj2, keyRank)
        this.diaTableDataSource = [...obj1, ...obj2]
        if (!this.diaTabIndex) {
          this.diaTableColumns = [
            {
              label: '排名',
              dataIndex: '',
              render: (h, val) => {
                if (!val) return ''
                return <div class={'normal-rank-next'}>{val < 4 ? '' : val}</div>
              }
            },
            {
              label: this.zoneData.dLevelName,
              dataIndex: levelName[this.zoneData.levelKey]
            },
            {
              label: '当日目标',
              dataIndex: 'incomeTarget',
              align: 'right',
              render: (h, val) => this.$numToInteger(val, val < 100000 ? 2 : 0)
            },
            {
              label: '当日收入',
              dataIndex: 'income',
              align: 'right',
              render: (h, val) => this.$numToInteger(val, val < 100000 ? 2 : 0)
            },
            {
              label: '当日完成比',
              dataIndex: 'incomePercent',
              align: 'right',
              render: (h, val) => this.$numToPercent(val)
            },
            {
              label: '周环比',
              dataIndex: 'lastWeekdayIncIncome',
              align: 'right',
              render: (h, val) => <div class={val > 0 ? 'green ' : 'orange '}>{this.$numToPercent(val)}</div>
            }
          ]
          if (this.zoneData.levelKey === 'area') {
            this.diaTableColumns = this.diaTableColumns.filter(item => !/当日目标|当日完成比/.test(item.label))
          }
        } else {
          this.diaTableColumns = [
            {
              label: '排名',
              dataIndex: '',
              render: (h, val) => {
                if (!val) return ''
                return <div class={'normal-rank-next'}>{val < 4 ? '' : val}</div>
              }
            },
            {
              label: this.zoneData.dLevelName,
              dataIndex: levelName[this.zoneData.levelKey]
            },
            {
              label: '当日目标',
              dataIndex: 'weightTarget',
              align: 'right',
              render: (h, val) => this.$numToInteger(val, 0)
            },
            {
              label: '当日货量',
              dataIndex: 'weight',
              align: 'right',
              render: (h, val) => this.$numToInteger(val, 0)
            },
            {
              label: '当日完成比',
              dataIndex: 'weightPercent',
              align: 'right',
              render: (h, val) => this.$numToPercent(val)
            },
            {
              label: '周环比',
              dataIndex: 'lastWeekdayIncWeight',
              align: 'right',
              render: (h, val) => <div class={val > 0 ? 'green ' : 'orange '}>{this.$numToPercent(val)}</div>
            }
          ]
        }
      }
    }
  },

  activated() {
    setTimeout(() => {
      if (this.$refs.realtimeSwipe) {
        this.$refs.realtimeSwipe.resize()
        this.$refs.realtimeSwipe.swipeTo(this.tabValue)
      }
    }, 500)
  },
  beforeDestroy() {
    clearInterval(this.timeId)
  },
  deactivated() {
    clearInterval(this.timeId)
  }
}
</script>

<style lang="less" scoped>
.realtime-container {
  .date-container {
    width: 100vw;
    height: 0.8rem;
    padding-left: 0.2rem;
  }

  .realtime-box-wrapper {
    background-color: #fff;
  }
}
.model-dia-content {
  margin-top: 0.2rem;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  line-height: 0.4rem;
}
.show-wrap {
  border-radius: 0.12rem;
  height: 0.6rem;
  background-color: #596b98;
  color: #fff;
  margin-top: -0.14rem;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 0.12rem;
  .iconfont {
    font-size: 0.24rem;
    margin-left: 0.04rem;
  }
}
</style>
