<!--
 * @Author: Jie<PERSON>w
 * @Date: 2022-04-22 14:13:56
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-03-22 10:38:50
 * @Description:
-->
<template>
  <CardList title="总体完成比(T-1)" class="mt24">
    <ShowTipIcon
      slot="tip"
      class="ml8"
      @btnShowTip="btnShowTip"
      :isFeedback="isFeedback"
      :explaindDetail="explaindDetail"
      @feedback="btnFeedback"
    />
    <Tabs slot="nav" :options="['日', '月']" :tabIndex="tabIndex" @tabSelect="tabSelect" />
    <!-- <div class="pd_lr20 mt32" v-show="firstTabIndex === 2 && !secondTabIndex">
      <RadiusButton :buttonData="option" :activeIndex="activeIndex" @click="btnConfirm" />
    </div> -->
    <div class="gauge-content">
      <div class="left-box">
        <SubHead v-show="tabIndex" :title="`月累计${secondLabel}完成比`" :percentValue="finishRate" />
        <KyDataGauge
          :value="finishPer"
          :date="defaultDate"
          :type="['day', 'month'][tabIndex]"
          class="canvas-box mt12"
        />
      </div>
      <div class="right-box" :style="{ paddingTop: this.tabIndex ? '0.4rem' : 0 }">
        <div>
          <span>{{ `${['日', '月累计'][this.tabIndex]}${secondLabel}` }}</span>
          <span class="grey333">{{
            $numToInteger(baseData[[`last_${secondField}`, `sum_${secondField}`][this.tabIndex]], 0, 1)
          }}</span>
        </div>
        <div>
          <span>{{ `${['日', '月'][this.tabIndex]}目标差值` }}</span>
          <span :class="textStyle(baseData[`${this.tabIndex ? '' : 'day_'}${secondField2}_diff`])">{{
            $numToInteger(baseData[`${this.tabIndex ? '' : 'day_'}${secondField2}_diff`], 0, 1)
          }}</span>
        </div>
        <div>
          <span>{{ `当${['日', '月'][this.tabIndex]}环比` }}</span>
          <span class="grey333">{{
            $numToPercent(baseData[`seq_${this.tabIndex ? 'last' : 'day'}_${secondField}`], 1)
          }}</span>
        </div>
        <div v-show="tabIndex">
          <span>{{ `当${['日', '月'][this.tabIndex]}同比` }}</span>
          <span class="grey333">{{ $numToPercent(baseData[`comp_last_${secondField}`], 1) }}</span>
        </div>
      </div>
    </div>
    <IncomeTrend :tabIndex="tabIndex" />
  </CardList>
</template>

<script>
import SubHead from '../components/subHead'
import KyDataGauge from 'common/components/kyDataGauge'
import IncomeTrend from './incomeTrend.vue'
import mixins from '../comjs/mixins'
export default {
  mixins: [mixins],
  components: {
    SubHead,
    KyDataGauge,
    IncomeTrend
  },
  watch: {
    baseData(val) {
      this.initPerData()
    },
    secondTabIndex() {
      this.initPerData()
    },
    firstTabIndex() {
      this.init()
    },
    zoneCode() {
      this.init()
    }
  },
  data() {
    return {
      baseData: {},
      tabIndex: 1,
      activeIndex: 0,
      option: ['合计', '新业务', 'KA融通'],
      finishPer: '',
      finishRate: '',
      defaultDate: this.$moment()
        .subtract(1, 'days')
        .format('YYYYMMDD')
    }
  },
  computed: {
    secondLabel() {
      return ['收入', '货量'][this.secondTabIndex]
    },
    secondField() {
      return this.secondTabIndex ? 'weight_qty' : 'income'
    },
    secondField2() {
      return this.secondTabIndex ? 'weight' : 'income'
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    btnShowTip() {
      switch (this.firstTabIndex) {
        case 0:
          this.getExplainData(['zt-sr-ztwcb', 'zt-hl-ztwcb'][this.secondTabIndex])
          break
        case 1:
          this.getExplainData(['jm-sr-ztwcb', 'jm-hl-ztwcb'][this.secondTabIndex])
          break
        case 2:
          this.getExplainData(['qd-sr-ztwcb', 'qd-hl-ztwcb'][this.secondTabIndex])
          break
      }
    },
    init() {
      this.getBaseData()
    },
    initPerData() {
      this.finishRate = String(this.baseData[`current_month_${this.secondField2}_achive_rate`])
      this.finishPer = this.baseData[`${this.tabIndex ? 'month' : 'day'}_${this.secondField2}_achive_rate`] || 0
    },
    getBaseData(type) {
      const incDay = this.$moment().format('YYYYMMDD')
      const zoneCodeQuery = this.zoneLevel
        ? [
            {
              key: ['', 'big_area_code', 'province_area_code', 'area_code', 'dept_code'][this.zoneLevel],
              value: this.zoneCode
            }
          ]
        : []
      const tableName = 'sx_manage_monit_info_2022'
      const conditionList = [
        {
          key: 'groupByCode',
          value: ["'001'", 'big_area_code', 'province_area_code', 'area_code', 'dept_code'][this.zoneLevel]
        },
        {
          key: 'groupByName',
          value: ["'全网'", 'big_area_name', 'province_area_name', 'area_name', 'dept_name'][this.zoneLevel]
        },
        { key: 'incDay', value: incDay },
        { key: 'zoneLevel', value: +this.zoneLevel === 1 ? '31' : this.zoneLevel },
        { key: 'type', value: type || ['总体', '加盟', '渠道'][this.firstTabIndex] },
        ...zoneCodeQuery
      ]
      this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        this.baseData = res.obj[0] || {}
      })
    },
    formatterTheDateOfThisMonth(thisDate) {
      const date = this.$moment(thisDate)
      const y = date.year()
      const m = date.month()
      const d = date.date()
      return Math.ceil((new Date(y, m, d) - new Date(y, m, 0)) / (24 * 60 * 60 * 1000))
    },
    textStyle(v) {
      return parseFloat(v) > 0 ? 'green' : 'orange'
    },
    tabSelect(val) {
      this.tabIndex = val
      this.initPerData()
    },
    btnConfirm(index, item) {
      this.activeIndex = index
      this.getBaseData(['渠道', '新业务', 'KA融通'][this.activeIndex])
    }
  }
}
</script>

<style lang="less" scoped>
.gauge-content {
  display: flex;
  margin: 0.3rem 0;
  .canvas-box {
    height: 1.76rem;
    width: 96%;
  }
  & > div:first-child {
    min-width: 54.4%;
    padding: 0 0.2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  & > div:last-child {
    min-width: 45.6%;
    padding-right: 0.2rem;
  }

  .right-box {
    display: flex;
    flex-direction: column;
    padding: 0 0 0.2rem 0.04rem;

    > div {
      margin-top: 0.24rem;
    }

    div {
      font-style: 0.24rem;
      display: flex;
      > span:first-child {
        color: #666;
        font-family: PingFangSC-Regular;
        flex: 2.8;
      }

      > span:last-child {
        font-family: PingFangSC-Regular;
        font-weight: 700;
        flex: 3;
      }
    }
  }
}
</style>
