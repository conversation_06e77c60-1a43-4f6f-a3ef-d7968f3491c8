<!--
 * @Author: shigl
 * @Date: 2024-03-22 10:31:30
 * @LastEditTime: 2024-03-22 10:33:49
 * @Description: 收入&货量
-->
<template>
  <PageContent>
    <RealtimeBoxNew />
    <CompleteRatio />
    <AddIncome />
    <NetAddup />
    <div style="height: 0.5rem"></div>
  </PageContent>
</template>
<script>
import RealtimeBoxNew from './realtimeBoxNew.vue'
import CompleteRatio from './completeRatio.vue'
import AddIncome from './addIncome.vue'
import NetAddup from './netAddup.vue'
export default {
  components: {
    RealtimeBoxNew,
    CompleteRatio,
    AddIncome,
    NetAddup
  }
}
</script>
<style lang="less" scoped></style>
