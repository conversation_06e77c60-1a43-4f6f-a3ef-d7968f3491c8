<!--
 * @Author: Jie<PERSON>w
 * @Date: 2022-04-22 15:32:09
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-12-04 18:33:32
 * @Description:
-->
<template>
  <CardList
    v-show="zoneLevel < 3"
    :title="`${name}累计${['收入', '货量'][this.secondTabIndex]}完成比(T-1)`"
    class="add-income-wrap"
  >
    <div class="calender-bar">
      <KyDatePicker @onChange="dateChange" :dateValue="dateValue" :type="tabVal" :disabled="tabIndex === 2" isScroll />
      <Tabs :options="tabData" :tabIndex="tabIndex" @tabSelect="tabSelect" />
    </div>
    <NormalTable
      :dataSource="baseData"
      :columns="columns"
      size="small"
      isIndicator
      :onSelect="tableOnSelect"
      width="max-content"
    />

    <KyDataDrawer
      :visible="drawerVisible"
      @close="drawerVisible = false"
      height="75%"
      :title="`${formatLabel(zoneLevel + 2)}月累计${['收入', '货量'][this.secondTabIndex]}完成比`"
    >
      <div class="calender-bars" ref="calenderDom">
        <!-- <KyDatePicker @onChange="modalDateChange" :dateValue="modalDateValue" :type="modalTabVal"  :getPopupContainer="getPopupContainer" /> -->
        <div></div>
        <Tabs :options="tabData" :tabIndex="modalTabIndex" @tabSelect="modalTabSelect" />
      </div>

      <NormalTable
        style="margin-top: 0.24rem"
        :dataSource="modalBaseData"
        :columns="modalColumns"
        size="small"
        isIndicator
        maxHeight="8rem"
      />
    </KyDataDrawer>
  </CardList>
</template>

<script>
import { numToInteger, numToPercent } from 'common/js/numFormat'
import sortNormal from 'common/img/sortImg/sortNormal.png'
import sortAsc from 'common/img/sortImg//sortAsc.png'
import sortDesc from 'common/img/sortImg//sortDesc.png'
import mixins from '../comjs/mixins'
export default {
  mixins: [mixins],
  components: {},
  computed: {
    dateValue() {
      if (this.tabIndex === 0) {
        return this.dayValue
      } else if (this.tabIndex === 1) {
        return this.monthValue
      } else {
        return this.yearValue
      }
    },
    modalDateValue() {
      if (this.modalTabIndex === 0) {
        return this.modalDayValue
      } else if (this.modalTabIndex === 1) {
        return this.modalMonthValue
      } else {
        return this.yearValue
      }
    },
    lastMonthDate() {
      const today = this.$moment()
        .subtract(1, 'days')
        .format('YYYYMM')
      if (today === this.monthValue) {
        return this.$moment().format('YYYYMMDD')
      } else {
        return this.$moment(this.monthValue, 'YYYYMM')
          .endOf('month')
          .format('YYYYMMDD')
      }
    },
    modalLastMonthDate() {
      const today = this.$moment()
        .subtract(1, 'days')
        .format('YYYYMM')
      if (today === this.modalMonthValue) {
        return this.$moment().format('YYYYMMDD')
      } else {
        return this.$moment(this.modalMonthValue, 'YYYYMM')
          .endOf('month')
          .format('YYYYMMDD')
      }
    },
    name() {
      switch (+this.zoneLevel) {
        case 0:
          return '省区'
        case 2:
          return '区域'
        default:
          return ''
      }
    },
    secondField() {
      return this.secondTabIndex ? 'weight' : 'income'
    },
    secondField2() {
      return this.secondTabIndex ? 'weight_qty' : 'income'
    }
  },
  watch: {
    baseData(val) {
      this.setTable()
    },
    modalBaseData() {
      this.setModalTable()
    },
    dateValue(newVal, oldVal) {
      if (oldVal) {
        this.finishSort = this.sortDesc
        this.getAddIncomeListData()
      }
    },
    modalDateValue(newVal, oldVal) {
      if (oldVal) {
        this.modalFinishSort = this.sortDesc
        this.getModalData()
      }
    },
    zoneCode() {
      if (this.zoneLevel <= 2) {
        this.finishSort = this.sortDesc
        this.getAddIncomeListData()
      }
    },
    firstTabIndex() {
      if (this.zoneLevel <= 2) {
        this.finishSort = this.sortDesc
        this.getAddIncomeListData()
      }
    },
    secondTabIndex() {
      this.setTable()
    }
  },
  created() {
    this.dayValue = this.$moment()
      .subtract(1, 'days')
      .format('YYYYMMDD')
    this.monthValue = this.$moment()
      .subtract(1, 'days')
      .format('YYYYMM')
    this.monthValue2 = this.$moment()
      .subtract(1, 'days')
      .format('YYYYMM')
    this.yearValue = this.$moment()
      .subtract(1, 'days')
      .format('YYYYMM')
  },
  data() {
    return {
      tabData: [
        { label: '日', value: '日' },
        { label: '月', value: '月' }
      ],
      tabIndex: 1,
      modalTabIndex: 1,
      tabVal: 'month',
      modalTabVal: 'month',
      dayValue: '',
      monthValue: '',
      yearValue: '',
      modalMonthValue: '',
      modalDayValue: '',
      columns: [],
      modalColumns: [],
      baseData: [],
      modalBaseData: [],
      sortAsc,
      sortDesc,
      sortNormal,
      finishSort: sortDesc,
      modalFinishSort: sortDesc,
      modalItem: {},
      drawerVisible: false
    }
  },
  mounted() {
    this.zoneLevel <= 2 && this.getAddIncomeListData()
  },
  methods: {
    getPopupContainer() {
      this.$nextTick(() => {
        return this.$refs['calenderDom']
      })
    },
    async getAddIncomeListData() {
      let wholeNetRes = []
      if (this.zoneLevel === 0) {
        wholeNetRes = await this.getWholeNetData()
      }
      const nextOrgLevel = this.zoneLevel ? this.zoneLevel + 1 : this.zoneLevel + 2
      const incDay = this.tabIndex
        ? this.lastMonthDate
        : this.$moment(this.dayValue)
            .add(1, 'days')
            .format('YYYYMMDD')
      const common = {
        groupByCode: 'province_area_code',
        groupByName: 'province_area_name',
        incDay,
        zoneLevel: nextOrgLevel,
        type: ['总体', '加盟', '渠道'][this.firstTabIndex]
      }
      if (nextOrgLevel === 2) {
        Object.assign(common, {
          groupByCode: 'province_area_code',
          groupByName: 'province_area_name',
          zoneLevel: nextOrgLevel
        })
      }
      if (nextOrgLevel === 3) {
        Object.assign(common, {
          groupByCode: 'area_code',
          groupByName: 'area_name',
          province_area_code: this.zoneCode
        })
      }
      if (nextOrgLevel === 4) {
        Object.assign(common, {
          groupByCode: 'area_code',
          groupByName: 'area_name',
          zoneLevel: 3
        })
      }
      const conditionList = this.formatParams(common)
      this.sendTwoDimenRequest('sx_manage_monit_info_2022', conditionList).then(res => {
        const result = res.obj.filter(item => item.groupByCode)
        const sortKey = this.tabIndex ? 'current_month_income_achive_rate' : 'day_income_achive_rate'
        result.sort((a, b) => b[sortKey] - a[sortKey])
        if (wholeNetRes.length) {
          result.unshift(wholeNetRes[0])
        }
        this.baseData = result.map((item, index) => {
          if (this.zoneLevel === 0) {
            return {
              index: index > 3 ? index : '',
              isWeight: !index,
              ...item
            }
          } else {
            return {
              index: index > 2 ? index + 1 : '',
              ...item
            }
          }
        })
      })
    },

    getModalData() {
      const incDay = this.modalTabIndex
        ? this.modalLastMonthDate
        : this.$moment(this.modalDayValue)
            .add(1, 'days')
            .format('YYYYMMDD')
      const nextOrgLevel = 3
      const common = {
        groupByCode: 'area_code',
        groupByName: 'area_name',
        incDay,
        zoneLevel: nextOrgLevel,
        type: ['总体', '加盟', '渠道'][this.firstTabIndex]
      }
      Object.assign(common, {
        groupByCode: 'area_code',
        groupByName: 'area_name',
        province_area_code: this.modalItem.groupByCode
      })
      // if (nextOrgLevel === 3) {
      //   Object.assign(common, {
      //     groupByCode: 'area_code',
      //     groupByName: 'area_name',
      //     province_area_code: this.modalItem.groupByCode
      //   })
      // }
      // if (this.modalItem.groupByName === '全网') {
      //   Object.assign(common, {
      //     groupByCode: 'province_area_code',
      //     groupByName: 'province_area_name'
      //   })
      // }
      const conditionList = this.formatParams(common)
      this.sendTwoDimenRequest('sx_manage_monit_info_2022', conditionList, false).then(res => {
        const sortKey = this.modalTabIndex ? 'current_month_income_achive_rate' : 'day_income_achive_rate'
        res.obj.sort((a, b) => b[sortKey] - a[sortKey])
        this.modalBaseData = res.obj.map((item, index) => {
          return {
            index: index > 2 ? index + 1 : '',
            ...item
          }
        })
      })
    },

    getWholeNetData() {
      const tableName = 'sx_manage_monit_info_2022'
      const incDay = this.tabIndex
        ? this.lastMonthDate
        : this.$moment(this.dayValue)
            .add(1, 'days')
            .format('YYYYMMDD')
      const common = {
        groupByCode: "'001'",
        groupByName: "'全网'",
        incDay,
        zoneLevel: 0,
        type: ['总体', '加盟', '渠道'][this.firstTabIndex]
      }
      const conditionList = this.formatParams(common)
      return this.sendTwoDimenRequest(tableName, conditionList).then(res => {
        return res.obj
      })
    },

    tabSelect(val) {
      this.tabIndex = val
      this.tabVal = ['day', 'month'][val]
      // this.setTable()
      // 神策点击
      this.$sensors.webClick(`市场-累计收入分析-${['日', '月'][val]}`)
    },

    modalTabSelect(val) {
      this.modalTabIndex = val
      this.modalTabVal = ['day', 'month'][val]
      // this.setModalTable()
      // 神策点击
      this.$sensors.webClick(`市场-累计收入分析弹框-${['日', '月'][val]}`)
    },

    dateRange(min) {
      const year = this.$moment()
        .subtract(1, 'days')
        .subtract(1, 'years')
        .year()
      return min
        ? this.$moment(`${year}-01-01`).toDate()
        : this.$moment()
            .subtract(1, 'days')
            .toDate()
    },

    dateChange(res) {
      if (this.tabIndex === 0) {
        this.dayValue = res
      } else if (this.tabIndex === 1) {
        this.monthValue = res
      } else {
        this.yearValue = res
      }
    },

    modalDateChange(res) {
      if (this.modalTabIndex === 0) {
        this.modalDayValue = res
      } else if (this.modalTabIndex === 1) {
        this.modalMonthValue = res
      } else {
        this.yearValue = res
      }
    },

    // 格式化参数
    formatParams(param = {}) {
      return Object.keys(param).map(key => ({ key, value: param[key] }))
    },

    btnSort(res, key) {
      if (this.finishSort === this.sortNormal || this.finishSort === this.sortAsc) {
        this.finishSort = this.sortDesc
        const sp = res[0]
        const fsp = res.slice(1).sort((a, b) => a[key] - b[key])
        this.baseData = [sp, ...fsp].map((item, index) => {
          return {
            ...item,
            index: index > 3 ? index : ''
          }
        })
      } else {
        this.finishSort = this.sortAsc
        const sp = res[0]
        const fsp = res.slice(1).sort((a, b) => b[key] - a[key])
        this.baseData = [sp, ...fsp].map((item, index) => {
          return {
            ...item,
            index: index > 3 ? index : ''
          }
        })
      }
    },

    btnSort2(res, key) {
      if (this.modalFinishSort === this.sortNormal || this.modalFinishSort === this.sortAsc) {
        this.modalFinishSort = this.sortDesc
        res.sort((a, b) => a[key] - b[key])
        this.modalBaseData = res.map((item, index) => {
          return {
            ...item,
            index: index > 2 ? index + 1 : ''
          }
        })
      } else {
        this.modalFinishSort = this.sortAsc
        res.sort((a, b) => b[key] - a[key])
        this.modalBaseData = res.map((item, index) => {
          return {
            ...item,
            index: index > 2 ? index + 1 : ''
          }
        })
      }
    },

    tableOnSelect(item) {
      if (this.zoneLevel >= 2 || this.zoneCode === item.groupByCode) {
        return
      }
      this.drawerVisible = true
      this.modalTabIndex = this.tabIndex
      this.modalDayValue = this.dayValue
      this.modalMonthValue = this.monthValue
      this.modalTabVal = this.tabVal
      this.modalItem = item
      this.getModalData()
      // 神策点击
      this.$sensors.webClick(`市场-累计收入分析弹框`)
      this.getPopupContainer()
    },

    formatLabel(level) {
      switch (+level) {
        case 0:
          return '省区'
        case 2:
          return '区域'
        default:
          break
      }
    },

    setTable() {
      this.columns = [
        {
          label: '排名',
          render: (h, value) => {
            if (+this.zoneLevel === 0) {
              if (value === 0) {
                return ''
              } else {
                return <div class="normal-rank-next">{value <= 3 ? '' : value}</div>
              }
            } else {
              return <div class="normal-rank">{value <= 2 ? '' : value + 1}</div>
            }
          },
          fixed: 'left'
        },
        {
          label: this.formatLabel(this.zoneLevel),
          dataIndex: 'groupByName',
          render: (h, val, data) => {
            const name =
              this.zoneLevel === 0 && this.zoneCode !== data.groupByCode ? (
                <i class={'iconfont icon-dayuhao fs20 ml4'}></i>
              ) : (
                ''
              )
            return (
              <div class={'flex_start'}>
                {val.replace(/SX|省区|分拨区|快运|区域|中转场|（撤销）/g, '')}
                {name}
              </div>
            )
          },
          fixed: 'left'
        },
        {
          label: (
            <div
              class="fw700 flex_start"
              onclick={() =>
                this.btnSort(
                  this.baseData,
                  this.tabIndex
                    ? `current_month_${this.secondField}_achive_rate`
                    : `day_${this.secondField}_achive_rate`
                )
              }
            >
              完成比
              <img src={this.finishSort} />
            </div>
          ),
          dataIndex: this.tabIndex
            ? `current_month_${this.secondField}_achive_rate`
            : `day_${this.secondField}_achive_rate`,
          render(h, val) {
            return <div class={val >= 1 ? 'green' : 'orange'}>{numToPercent(val, 1)}</div>
          }
        },
        {
          label: '目标',
          dataIndex: this.tabIndex ? `current_${this.secondField}_index` : `${this.secondField}_data_cargo`,
          render(h, val) {
            return numToInteger(val, 0, 1)
          }
        },
        {
          label: this.secondTabIndex ? '货量(吨)' : '收入(万)',
          dataIndex: this.tabIndex ? `sum_${this.secondField2}` : `last_${this.secondField2}`,
          render(h, val) {
            return numToInteger(val, 0, 1)
          }
        },
        {
          label: '日均环比',
          dataIndex: `seq_avg_${this.secondField}`,
          render(h, val) {
            return numToPercent(val, 1)
          }
        },
        {
          label: '累计同比',
          dataIndex: `comp_last_${this.secondField2}`,
          render(h, val) {
            return numToPercent(val, 1)
          }
        }
      ]
      if (!this.tabIndex) {
        this.columns.splice(5, 2)
      }
    },

    setModalTable() {
      this.modalColumns = [
        {
          label: '排名',
          align: 'center',
          render: (h, value, res, index) => {
            return (
              <div class="flex_center">
                <span class="normal-rank">{value <= 2 ? '' : value + 1}</span>
              </div>
            )
          },
          fixed: 'left'
        },
        {
          label: this.formatLabel(+this.zoneLevel + 2),
          dataIndex: 'groupByName',
          render: (h, val) => {
            return <div>{(val || '').replace(/SX|省区|分拨区|快运|区域|中转场|（撤销）/g, '')}</div>
          },
          fixed: 'left'
        },
        {
          label: (
            <div
              class="flex_start fw700"
              onclick={() =>
                this.btnSort2(
                  this.modalBaseData,
                  this.modalTabIndex
                    ? `current_month_${this.secondField}_achive_rate`
                    : `day_${this.secondField}_achive_rate`
                )
              }
            >
              完成比
              <img src={this.modalFinishSort} />
            </div>
          ),
          dataIndex: this.modalTabIndex
            ? `current_month_${this.secondField}_achive_rate`
            : `day_${this.secondField}_achive_rate`,
          render(h, val) {
            return numToPercent(val, 1)
          }
        },
        {
          label: '目标',
          dataIndex: this.modalTabIndex ? `current_${this.secondField}_index` : `${this.secondField}_data_cargo`,
          render(h, val) {
            return numToInteger(val, 0, 1)
          }
        },
        {
          label: this.secondTabIndex ? '货量(吨)' : '收入(万)',
          dataIndex: this.modalTabIndex ? `sum_${this.secondField2}` : `last_${this.secondField2}`,
          render(h, val) {
            return numToInteger(val, 0, 1)
          }
        },
        {
          label: '日均环比',
          dataIndex: `seq_avg_${this.secondField}`,
          render(h, val) {
            return numToPercent(val, 1)
          }
        }
      ]
      if (!this.tabIndex) {
        this.modalColumns.splice(5, 1)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.calender-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.2rem;
  height: 0.9rem;
}
.calender-bars {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 0.6rem;
  padding: 0 0.2rem;
}
.add-income-wrap {
  margin-top: 0.24rem;
  background: #fff;
}

/deep/.drawer-content {
  z-index: 9998 !important;

  .title {
    font-weight: 700;
  }
}

/deep/.ky-data-drawer-mask {
  z-index: 9997 !important;
}

/deep/ th img {
  height: 0.24rem;
  width: 0.24rem;
  // margin-left: 0.08rem;
}
</style>
