<!--
 * @Author: Jie<PERSON>w
 * @Date: 2022-04-22 15:16:22
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-05-13 15:00:12
 * @Description:
-->
<template>
  <CardList :title="`${['收入', '货量'][this.secondTabIndex]}趋势`" type="sub">
    <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableOption">
      <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
    </KydChartModel>
  </CardList>
</template>

<script>
import { drawScrollChart } from 'common/charts/chartOption'
import mixins from '../comjs/mixins'
export default {
  props: {
    tabIndex: Number
  },
  mixins: [mixins],

  data() {
    return {
      legendDataSource: [],
      tableOption: {},
      dayData: [],
      monthData: []
    }
  },
  computed: {
    legendOption() {
      return {
        options: [
          {
            label: ['收入(万元)', '货量(吨)'][this.secondTabIndex],
            int: [1]
          }
        ],
        dataSource: this.legendDataSource
      }
    },
    roleKeyMap() {
      return {
        0: 'qwCode',
        31: 'bigAreaCode',
        2: 'provinceAreaCode',
        3: 'areaCode',
        4: 'deptCode'
      }
    },
    roleKeyMapM() {
      return {
        0: 'qwCode',
        31: 'zoneCode',
        2: 'zoneCode',
        3: 'zoneCode',
        4: 'zoneCode'
      }
    }
  },

  watch: {
    zoneCode() {
      this.init()
    },
    tabIndex() {
      this.handlerData()
    },
    firstTabIndex() {
      this.init()
    },
    secondTabIndex() {
      this.handlerData()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      await Promise.all([this.getRecentEightDaysIncomeData(), this.getMonthlyIncomeData()])
      this.handlerData()
    },
    getRecentEightDaysIncomeData() {
      const startDay = this.$moment()
        .subtract(8, 'days')
        .format('YYYYMMDD')
      const endDay = this.$moment()
        .subtract(1, 'days')
        .format('YYYYMMDD')
      const level = +this.zoneLevel === 1 ? '31' : this.zoneLevel
      const conditionList = [
        { key: 'startDay', value: startDay },
        { key: 'endDay', value: endDay },
        { key: 'zoneLevel', value: level },
        { key: this.roleKeyMap[level], value: this.zoneCode },
        { key: 'type', value: ['总体', '加盟', '渠道'][this.firstTabIndex] }
      ]
      return this.sendTwoDimenRequest('sx_manage_monit_eight_days_info_2022', conditionList).then(res => {
        const result = res.obj.sort((a, b) => {
          return (a.consigned_tm || '').replace(/-/g, '') - (b.consigned_tm || '').replace(/-/g, '')
        })
        this.dayData = result
      })
    },
    getMonthlyIncomeData() {
      const endMonth = this.$moment()
        .subtract(1, 'days')
        .format('YYYYMM')
      const startMonth = this.$moment()
        .subtract(1, 'days')
        .subtract(12, 'months')
        .format('YYYYMM')
      const level = +this.zoneLevel === 1 ? '31' : this.zoneLevel
      const conditionList = [
        { key: 'startDay', value: startMonth },
        { key: 'endDay', value: endMonth },
        { key: 'zoneLevel', value: level },
        { key: this.roleKeyMapM[level], value: this.zoneCode }
      ]
      return this.sendTwoDimenRequest('sx_manage_monit_august_info', conditionList).then(res => {
        const result = res.obj.sort((a, b) => {
          return this.$moment(a.consigned_tm).format('YYYYMM') - this.$moment(b.consigned_tm).format('YYYYMM')
        })
        this.monthData = result
      })
    },
    handlerData() {
      const target = this.tabIndex ? this.monthData : this.dayData
      const key = this.tabIndex
        ? `${['', 'join_', 'qd_'][this.firstTabIndex]}${
            ['income', this.firstTabIndex ? 'weight' : 'meterage_weight_qty'][this.secondTabIndex]
          }`
        : ['income', 'meterage_weight_qty'][this.secondTabIndex]
      const lineData = target.map(item => ({
        value: item[key],
        ...item
      }))
      const xAxisData = target.map(item => item.consigned_tm)

      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xAxisData,
            axisLabel: {
              formatter: val => this.$dateFormat((val || '').replace(/-/g, ''), this.tabIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: val => this.$intFormat(val)
            }
          }
        ],
        series: [
          {
            type: 'line',
            data: lineData
          }
        ]
      }
      this.$nextTick(() => {
        // drawOneLineChart(option, this.$refs['income-chart-box'])
        drawScrollChart(option, this.$refs['chart-model-trend'], { isOneLine: true })
        this.tableOption = { options: option }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.business-income-wrap {
  background: #fff;
  margin-top: 0.24rem;
  padding-bottom: 0.24rem;

  .chart-box {
    height: 3rem;
    width: 100%;
    margin-top: 0.32rem;
    padding: 0 0.2rem;
  }
}
</style>
