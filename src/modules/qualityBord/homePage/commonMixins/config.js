// import { numToInteger, numToPercent } from 'common/js/numFormat'
export const rankColumns = [
  {
    label: '排名',
    dataIndex: 'qualityStarRank',
    align: 'center',
    fixed: 'left',
    render: (h, value) => {
      return (
        <div class={'flex_center'}>
          <div class="normal-rank">{value <= 2 ? '' : value + 1}</div>
        </div>
      )
    }
  },
  { label: '省区',
    dataIndex: 'provinceAreaName',
    align: 'center'
  },
  {
    label: '指标操作',
    align: 'center',
    expandType: 'button'
    // render: (h, value) => {
    //   return <div style="color: #CC1B23;">展开<i class="iconfont icon-dayuhao fs20"></i></div>
    // }
  }
]
// 子表格columns
export const childColumns = [
  {
    label: '指标类型',
    dataIndex: 'targetType',
    align: 'center',
    fixed: 'left'
  },
  { label: '排名',
    dataIndex: 'rank',
    align: 'center'
  },
  { label: '达成值',
    dataIndex: 'target',
    align: 'center'
  },
  { label: '完成比',
    dataIndex: 'complete',
    align: 'center'
  }
]

// 测试数据
export const dataRankList = [
  {
    rank: 1,
    provinceName: '西北省区',
    total: 100,
    rate: 10,
    zone_name: '西北省区'
  },
  {
    rank: 2,
    provinceName: '粤东省区',
    total: 100,
    rate: 10,
    zone_name: '西北省区'
  },
  {
    rank: 3,
    provinceName: '湘粤省区',
    total: 100,
    rate: 10,
    zone_name: '西北省区'
  },
  {
    rank: 4,
    provinceName: '河北省区',
    total: 100,
    rate: 10,
    zone_name: '西北省区'
  }
]
