<!--
 * @Author: ZHANG
 * @Date: 2021-06-25 13:53:55
 * @LastEditTime: 2023-12-12 10:01:04
 * @Description:
-->
<template>
  <div class="profit_page page_bgc">
    <div class="date_container">
      <KyDatePicker style="width: 3rem" :type="dateType" :dateValue="dateValue" @onChange="dateChange"
        :holidayData="holidayData"></KyDatePicker>
      <div class="grey666" @click="showCalculationTip">规则说明 <i class="iconfont icon-dayuhao fs20"></i></div>
    </div>
    <PageContent>
      <component
        v-for="item in ['QualityOverview', 'AbilityOverview', 'QualityRank']"
        :is="item"
        :key="item"
        @btnOpenDia="btnOpenDia"
      ></component>
    </PageContent>
    <BottomModal :visible="tipVisible" :tipConfig="tipConfig" :handleClose="closeTopModal"></BottomModal>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import QualityOverview from './qualityOverview.vue'
import AbilityOverview from './abilityOverview.vue'
import QualityRank from './qualityRank.vue'
import BottomModal from '../components/bottomModal.vue'
import mixins from './commonMixins/mixins.js'
import request from './commonMixins/request.js'
export default {
  mixins: [mixins, request],
  components: { QualityOverview, AbilityOverview, QualityRank, BottomModal },
  data() {
    return {
      mixinsTableMaxHeight: '8rem',
      dateType: 'day',
      // dateValue: '',
      tipVisible: false,
      tipConfig: {
        title: '提示',
        content: '123'
      }
    }
  },
  computed: {
    ...mapState({
      holidayData: 'holidayData'
    }),
    ...mapState('qualityBord', {
      qualityDate: state => state.all.dateDay
    })
  },
  watch: {
    qualityDate(val) {
      console.log('qualityDate', val)
      this.initOption()
    },
    zoneCode(val) {
      this.initOption()
      // this.getHomePageData()
      // this.getQualityStarRankData()
    }
  },
  methods: {
    ...mapMutations('qualityBord', ['setDate', 'setDateIndex', 'setPageData']),
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    dateChange(date) {
      this.setDate({
        type: 'all',
        key: 'dateDay',
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
    },
    showCalculationTip(data) {
      this.tipConfig = {
        title: '规则说明',
        content: ''
      }
      this.tipVisible = true
    },
    closeTopModal() {
      this.tipVisible = false
    },
    // 展开品质之星-区域排名
    async btnOpenDia(data) {
      // this.visible = true
    },
    // 底部抽屉弹窗
    diaClose() {
      this.visible = false
    },
    // 获取首页概览数据
    async getHomePageData() {
      const time = this.$moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        date: time,
        dataLevel: this.zoneLevel,
        dateType: 3
      }
      if (this.level_code) {
        params[this.level_code] = this.zoneCode
      }
      const res = await this._getQualityOverviewsData(params)
      this.setPageData({
        type: 'homePage',
        dataType: 'overviewData',
        data: res?.obj || {}
      })
    },
    // 获取品质之星-省区排名数据
    async getQualityStarRankData() {
      const time = this.$moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        date: time,
        dataLevel: 32,
        dateType: 3,
        sortType: 1
      }
      // if (this.level_code) {
      //   params[this.level_code] = this.zoneCode
      // }
      const res = await this._getQualityStarRankData(params)
      this.setPageData({
        type: 'homePage',
        dataType: 'qualityStarRankData',
        data: res?.obj || []
      })
    },
    initOption() {
      this.getHomePageData()
      this.getQualityStarRankData()
    },
    init() {
      this.initOption()
    }
  },
  mounted() {
    this.setDateIndex({
      type: 'all',
      index: 0
    })
    // this.initOptions()
    this.init()
  }
}
</script>
<style lang="less" scoped>
.profit_page {
  color: #333;
}
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.top_rule_desc {
  color: #666;
}
</style>
