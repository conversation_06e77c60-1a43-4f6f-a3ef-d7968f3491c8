<!--
 * @Author: ZHANG
 * @Date: 2021-06-25 13:53:55
 * @LastEditTime: 2023-12-12 10:01:04
 * @Description:
-->
<template>
  <div>
    <CardList title="品质之星-省区排名">
      <div slot="nav" class="rounded_button_box">
        <div class="rounded_button" @click="btnOpenDia">查看区域排名<i class="iconfont icon-dayuhao fs20"></i></div>
      </div>
      <div class="cus_table_wrap">
        <ExpandTable :datas="qualityStarRankList" :parentColumns="provinceColumns" :childColumns="diaTableColumns" levelName="省区"/>
      </div>
    </CardList>
    <KyDataDrawer
      :visible="visible"
      :title="diaTitle"
      height="80%"
      @close="diaClose"
      @drawerHeight="mixinsDrawerHeight"
    >
      <div class="detail_table_warp">
        <ExpandTable :datas="diaTableDataSource" :parentColumns="areaColumns" :childColumns="diaTableColumns" levelName="区域"/>
      </div>
    </KyDataDrawer>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { diaTableColumns, provinceColumns, areaColumns } from '../constant'
import mixins from './commonMixins/mixins.js'
import request from './commonMixins/request.js'
import medal_one from '../img/medal_one.png'
import medal_two from '../img/medal_two.png'
import medal_three from '../img/medal_three.png'
import medal_normal from '../img/medal_normal.png'
import ExpandTable from '../components/expandTable.vue'
export default {
  name: 'qualityRank',
  mixins: [mixins, request],
  components: { ExpandTable },
  data() {
    return {
      medalList: [medal_one, medal_two, medal_three, medal_normal],
      mixinsTableMaxHeight: '8rem',
      visible: false,
      diaTitle: '品质之星-区域排名',
      diaTableColumns,
      diaTableDataSource: [],
      provinceColumns,
      areaColumns
    }
  },
  computed: {
    ...mapState('qualityBord', {
      qualityDate: state => state.all.dateDay
    }),
    qualityStarRankList() {
      return this.formatTableData(this.qualityStarRankData || [])
    }
  },
  watch: {},
  methods: {
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    onSelect(tr, index) {
      console
      tr.expand = !tr.expand
    },
    async btnOpenDia() {
      // this.$emit('btnOpenDia', 'shishi')
      this.visible = true
      const time = this.$moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        date: time,
        dataLevel: 33,
        dateType: 3,
        sortType: 1
      }
      const res = await this._getQualityStarRankData(params)
      this.diaTableDataSource = this.formatTableData(res?.obj || [])
    },
    diaClose() {
      this.visible = false
    },
    // 整理表格排名数据
    formatTableData(data) {
      return data.map(item => {
        return {
          allRank: item.qualityStarRank,
          orgName: item.zoneName,
          isExpand: false,
          children: [
            {
              targetName: '百万件遗失率',
              targetRank: item.lostIndRank,
              targetValue: item.lostRate,
              targetCom: item.lostRateCom
            },
            {
              targetName: '万件损坏率',
              targetRank: item.damageIndRank,
              targetValue: item.damageQtyRate,
              targetCom: item.damageQtyRateCom
            },
            // {
            //   targetName: '万票损坏率',
            //   targetRank: item.damageIndRank,
            //   targetValue: item.damageWanRate,
            //   targetCom: item.damageWanRateCom
            // },
            // {
            //   targetName: '票均异常率',
            //   targetRank: item.abnormalIndRank,
            //   targetValue: item.abnormalRate,
            //   targetCom: item.abnormalComRate
            // },
            {
              targetName: '工单一次性解决率',
              targetRank: item.oneTimeSolutionIndRank,
              targetValue: item.oneTimeSolutionRate,
              targetCom: item.oneTimeSolutionComRate
            },
            {
              targetName: '百万票客诉率',
              targetRank: item.customerIndRank,
              targetValue: item.totalCustomerCompRate,
              targetCom: item.totalCustomerComplete
            },
            {
              targetName: '尾部门店占比',
              targetRank: item.tailCompIndRank,
              targetValue: item.tailRatio,
              targetCom: item.tailCompletionRate
            }
          ]
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.rounded_button_box {
  .rounded_button {
    height: 0.48rem;
    line-height: 0.48rem;
    border-radius: 0.24rem;
    background: #F8F9FC;
    padding: 0 0.16rem;
  }
}
.cus_table_wrap {
  margin-top: 0.24rem;
  padding: 0 10px;
}
.detail_table_warp {
  margin-top: 18px;
  margin-bottom: 20px;
  overflow-y: scroll;
  max-height: 82%;
}
</style>
