/*
 * @Author: shigl
 * @Date: 2022-07-19 17:25:34
 * @LastEditTime: 2022-07-19 17:35:52
 * @Description:
 */
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
import baseMixins from '../../baseMixins'

export default {
  mixins: [requestMixins, baseMixins],

  data() {
    return {}
  },
  computed: {
    incDate() {
      if (!this.dateIndex) {
        return this.$moment(this.dateValue)
          .subtract(15, 'days')
          .format('YYYYMMDD')
      }
      return this.$moment(this.dateValue)
        .subtract(5, 'month')
        .format('YYYYMM')
    },
    key_unit() {
      return ['d', 'w', 'm'][this.dateIndex]
    },
    key_dateunit() {
      return 'time'
      // return ['inc_day', 'inc_month'][this.dateIndex]
    },
    key_value() {
      return ['this_day_value', 'this_month_value'][this.dateIndex]
    },
    key_rateCar() {
      return ['kd_weight_rate', 'kd_weight_m_rate'][this.dateIndex]
    },
    key_rateOd() {
      return ['od_weight_rate', 'od_weight_m_rate'][this.dateIndex]
    },
    key_star() {
      return ['star_day', 'star_mon'][this.dateIndex]
    },
    key_end() {
      return ['end_day', 'end_mon'][this.dateIndex]
    },
    ...mapGetters('qualityBord', {
      dateValue: 'dateValue'
    }),
    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('qualityBord', {
      dateIndex: state => state.all.dateIndex,

      cargoOverviewMon: state => state.quality.cargoOverviewMon,
      cargoOverviewDay: state => state.quality.cargoOverviewDay,
      trendChangeMon: state => state.quality.trendChangeMon,
      trendChangeDay: state => state.quality.trendChangeDay,
      trendChangeComMon: state => state.quality.trendChangeComMon,
      trendChangeComDay: state => state.quality.trendChangeComDay
    })
  },
  methods: {}
}
