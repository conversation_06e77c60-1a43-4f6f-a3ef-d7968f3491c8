/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-07-05 18:43:55
 * @Description:
 */
import mixins from './mixins'
const defaultSxjdSiteCode = 'SFC015'
export default {
  mixins: [mixins],
  data() {
    return {}
  },
  computed: {
    defaultData() {
      let levelMap = {}
      if (this.dateIndex) {
        levelMap = {
          level_code: this.zoneLevel
        }
      }
      return {
        [this.key_star]: this.incDate,
        [this.key_end]: this.dateValue,
        // zone_level: String(this.zoneLevel),
        zone_code: this.zoneCode === '001' ? 'SFC015' : this.zoneCode, // 收入页面各层级都是zone_code
        // zone_code: 'SFC015'
        ...levelMap
      }
    }
  },
  methods: {
    _changeDefaultSiteCode(code) {
      if (code === '001') return defaultSxjdSiteCode
      return code
    },
    // 【收入】【货量总览】月维度
    _getWphslMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_income_weight_mi_01', this.forMapData(tmp))
    },
    // 【收入】【货量总览】日维度
    _getData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryDamage', params)
    },
    // 损坏率省区
    // http://yapi.sit.sf-express.com/project/4230/interface/api/473690
    _getWpshlProvinceData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryDamageDetails', params)
    },
    // 损坏率省区
    // http://yapi.sit.sf-express.com/project/4230/interface/api/473690
    _getBwjyslProvinceData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryLossDetails', params)
    },
    // 票均异常省区
    // http://yapi.sit.sf-express.com/project/4230/interface/api/473682
    _getPjycProvinceData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryAbnormalTicketDetails', params)
    },

    // http://yapi.sit.sf-express.com/project/4230/interface/api/473694
    _getBwjyslData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryLoss', params)
    },
    // http://yapi.sit.sf-express.com/project/4230/interface/api/473678
    _getPjyclData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryAbnormalTicket', params)
    }
  }
}
