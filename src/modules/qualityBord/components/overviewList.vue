<!--
 * @Author: ZHANG
 * @Date: 2021-06-25 13:53:55
 * @LastEditTime: 2023-12-12 10:01:04
 * @Description:
-->
<template>
  <div>
    <div class="quality_card">
      <div class="card_list_item" v-for="(item, index) in options" :key="index" @click="navToTarget(item)">
        <div class="list_item_top">{{ item.label }}</div>
        <div class="list_item_middle">
          <!-- <div class="list_item_middle_value">{{ data[item?.dataIndex] }}</div> -->
          <div class="list_item_middle_value">{{ numFormat(data[item?.dataIndex], item) }}</div>
          <div class="list_item_middle_unit">{{ item?.unit }}</div>
        </div>
        <div class="list_item_bottom">
          <div class="list_item_bottom_item">
            <span class="bottom_item_label">{{ item.bottom[0].label }}</span>
            <!-- <span class="bottom_item_value">{{ data[item.bottom[0].dataIndex] }}{{ item.bottom[0].unit }}</span> -->
            <span>
              <span v-if="item?.isReverse" class="bottom_item_value" :class="data[item.bottom[0].dataIndex] >= 0 ? 'down_text' : 'up_text'">{{ numFormat(data[item.bottom[0].dataIndex], item.bottom[0]) }}{{ item.bottom[0].unit }}</span>
              <span v-else class="bottom_item_value" :class="data[item.bottom[0].dataIndex] >= 0 ? 'up_text' : 'down_text'">{{ numFormat(data[item.bottom[0].dataIndex], item.bottom[0]) }}{{ item.bottom[0].unit }}</span>
            </span>
          </div>
          <div class="list_item_bottom_item">
            <span class="bottom_item_label">{{ item.bottom[1].label }}</span>
            <!-- <span class="bottom_item_value">{{ data[item.bottom[1].dataIndex] }}{{ item.bottom[0].unit }}</span> -->
            <span class="bottom_item_value">{{ numFormat(data[item.bottom[1].dataIndex], item.bottom[1]) }}{{ item.bottom[1].unit }}</span>
          </div>
        </div>
        <!-- v-if="zoneLevel > 30"  -->
        <div class="medal_box" :style="`backgroundImage: url(${getRankStatus(data[item.sortIndex], item?.isSort)}`" v-if="zoneLevel > 30">
          <div v-if="item?.isSort && data[item.sortIndex]> 3" class="medal_box_title">第<span>{{ data[item.sortIndex] }}</span>名</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import baseMixins from '../baseMixins'
import medalOneImg from '../img/medal_one.png'
import medalTwoImg from '../img/medal_two.png'
import medalThreeImg from '../img/medal_three.png'
import medalNormalImg from '../img/medal_normal.png'
import { numToPercent, numToInteger } from 'common/js/numFormat'
import { tabDataList } from "../constant"
export default {
  name: 'OverviewList',
  mixins: [baseMixins],
  components: {},
  props: {
    options: {
      type: Array,
      default() {
        return []
      }
    },
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      medalImgList: [medalOneImg, medalTwoImg, medalThreeImg, medalNormalImg]
    }
  },
  computed: {
    ...mapState({
      holidayData: 'holidayData'
    }),
    ...mapState('qualityBord', {
      pageTab: state => state.menu.pageTab
    })
  },
  watch: {},
  methods: {
    ...mapMutations('qualityBord', ['setPageIndex']),
    dateChange() {},
    closeTopModal() {
      this.tipVisible = false
    },
    // 获取排名图案
    getRankStatus(rank, isSort) {
      if (!isSort) {
        return ''
      }
      if (rank && rank < 4) {
        return this.medalImgList[rank - 1]
      } else {
        return this.medalImgList[3]
      }
    },
    // 格式化数字
    numFormat(value, colItem) {
      if (colItem?.per) {
        const str = numToPercent(value, ...(colItem?.per || [])) + ''
        return str.replace(/%/, '')
      } else if (colItem?.int) {
        return numToInteger(value, ...(colItem?.int || []))
      } else {
        return value
      }
    },
    navToTarget(item) {
      console.log("jbsds==aa菜单", item, tabDataList)
      const target = tabDataList.find(v => v.name === item?.label)
      this.setPageIndex({
        type: 'menu',
        dataType: 'pageTab',
        data: {
          pageTabIndex: target.topMenuCode,
          pageTabLabel: target.topMenuName
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.quality_card {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.2rem;
  padding: 0 0.2rem;
  margin-top: 0.2rem;
}
.card_list_item {
  background: #F6F6F6;
  color: #333;
  border-radius: 0.08rem;
  padding: 10px 8px;
  box-sizing: border-box;
  position: relative;
  .list_item_top {
    font-weight: 500;
    font-size: 0.24rem;
    margin-bottom: 0.16rem;
  }
  .list_item_middle {
    font-size: 0.42rem;
    margin-bottom: 0.20rem;
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
    .list_item_middle_value {
      height: 27px;
      line-height: 27px;
      font-weight: bold;
    }
    .list_item_middle_unit {
      font-size: 0.28rem;
      margin-left: 0.04rem;
    }
  }
  .list_item_bottom {
    font-size: 0.22rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.00rem;
  }
  .list_item_bottom_item {
    .bottom_item_label {
      color: #999999;
    }
    .bottom_item_value{
      color: #333333;
      margin-left: 0.08rem;
      font-weight: bold;
    }
    .up_text {
    color: #08BCA0;
    }
    .down_text {
      color: #FF4902;
    }
  }
  .medal_box {
    background-repeat: no-repeat;
    background-size: 100%;
    height: 1.2rem;
    width: 1.2rem;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .medal_box_title {
      background: linear-gradient(293deg, #525252 7%, #9B9B9B 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 0.22rem;
      font-weight: 500;
      span {
        font-family: DIN-BlackItalic;
        font-size: 0.24rem;
      }
    }
  }
}

</style>
