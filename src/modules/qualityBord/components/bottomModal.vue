 <!-- @Author: author
 @Date: 2024-10-17 09:25:34
 @LastEditTime: 2024-10-17 09:25:34
 @Description: 底部弹窗 --- 规则说明 -->
 <template>
  <KyDataDrawer :visible="innerVisible" :title="tipConfig?.title" minHeight="10%" height="auto" @close="diaClose" @drawerHeight="mixinsDrawerHeight">
    <div class="modal_tip_box">
      <!-- <div class="modal_tip_content">{{ tipConfig?.content }}</div> -->
      <div class="modal_tip_content">
        <div class="content_list_item">
          <div class="fs28 fw700 grey333 line_h45">1、品质之星名次依据综合得分进行排名，计算如下：</div>
          <div class="content_title line_h45">综合得分=客诉率排名*25%+损坏率排名*25%+尾部占比排名*25%+工单一次性解决率排名*15%+百万票遗失率排名*10%</div>
        </div>
        <div  class="content_list_item mt24">
          <div class="fs28 fw700 grey333 line_h45">2、各指标单项名次依据单项得分进行排名，计算如下：</div>
          <div class="fs28 fw400 grey666 line_h45">单项得分=单项指标完成比排名*70%+单项指标环比排名*30% </div>
          <div class="line_h45 pd_r40">
            <span class="fs28 fw700 grey333">注：</span>
            负向指标完成比=2-完成值/目标值*100%；正向指标完成比=完成值/目标值
          </div>
        </div>
      </div>
    </div>
  </KyDataDrawer>
</template>
<script>
// import mixins from "../commonMixins/mixins"
export default {
  name: 'BottomModal',
  mixins: [],
  components: { },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    handleClose: {
      type: Function,
      default() {
        return () => { }
      }
    },
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    tipConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      mixinsTableMaxHeight: '8rem',
      innerVisible: false,
      moreTableWidth: '100%'
    }
  },
  computed: {},
  watch: {
    visible(val) {
      console.log("wqttt", val)
      this.innerVisible = val
    }
  },
  mounted() {
    this.initOption()
  },
  methods: {
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    diaClose() {
      console.log("关闭---2")
      this.handleClose()
    },
    initOption() {}
  }
}
</script>
<style lang="less" scoped>
.modal_tip_box {
  margin-top: 20px;
  padding: 0 20px;
  padding-right: 8px;
  .pd_r40 {
    padding-right: 4px;
  }
}
.modal_tip_content {
  font-family: PingFang SC;
  font-size: 0.28rem;
  font-weight: normal;
  line-height: 0.40rem;
  color: #666666;
  padding-bottom: 0.56rem;
}
</style>
