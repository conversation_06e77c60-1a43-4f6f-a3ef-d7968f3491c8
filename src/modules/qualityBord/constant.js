import { numToPercent, numToInFloat } from 'common/js/numFormat'

export const DateTabTypes = [
  { label: '日', value: 'day' },
  { label: '周', value: 'week' },
  { label: '月', value: 'month' }
]

export const DateTabLabels = DateTabTypes.map(item => item.label)
// 1、万票货损率（页面展示已经替换成万件损坏率 20250326）
export const WphslProvinceTab = [
  {
    value: '1',
    label: '整体损坏率',
    keys: [
      { dataIndex: 'damageIndRank', label: '排名', align: 'center' },
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'damageWanRate',
        label: '损坏率',
        align: 'left',
        render(_, i) {
          // return numToInFloat(i, 2)
          return numToInFloat(i, 2)
        }
      },
      {
        dataIndex: 'damageWanRateCom',
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      }
    ]
  },
  {
    value: '2',
    label: '业务发展端',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'wlDamageWanRate',
        label: '损坏率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      },
      {
        dataIndex: 'wlDamageWanRateCom',
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      }
    ]
  },
  {
    value: '3',
    label: '操作管理端',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'yyDamageWanRate',
        label: '损坏率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      },
      {
        dataIndex: 'yyDamageWanRateCom',
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
        // render(_, i) {
        //   return numToInFloat(i, 2)
        // }
      }
    ]
  }
]
// 1、万件损坏率排行表头配置
export const WjhslProvinceTab = [
  {
    value: '1',
    label: '整体损坏率',
    keys: [
      { dataIndex: 'damageIndRank', label: '排名', align: 'center' },
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'damageQtyRate',
        label: '损坏率',
        align: 'left',
        render(_, i) {
          // return numToInFloat(i, 2)
          return numToInFloat(i, 2)
        }
      },
      {
        dataIndex: 'damageQtyRateCom',
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      }
    ]
  },
  {
    value: '2',
    label: '网络万件损坏率',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'wlDamageQtyRate',
        label: '损坏率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '3',
    label: '运营万件损坏率',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'yyDamageQtyRate',
        label: '损坏率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '4',
    label: 'SX场站万件损坏率',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'sxDamageQtyRate',
        label: '损坏率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '3',
    label: 'SF场站万件损坏率',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'sfDamageQtyRate',
        label: '损坏率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  }
]
// export const WphslProvinceColumn = [
//   { dataIndex: '1', label: '组织' },
//   { dataIndex: '2', label: '损坏率' },
//   { dataIndex: '3', label: '完成比' },
//   { dataIndex: '4', label: '月环比' }
// ]
// 2、百万件遗失率
export const BwjyslTab = [
  {
    value: '1',
    label: '整体遗失率',
    keys: [
      { dataIndex: 'lostIndRank', label: '排名', align: 'center' },
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'lostRate',
        label: '遗失率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      },
      {
        dataIndex: 'lostRateCom',
        label: '完成比',
        render(_, i) {
          return numToPercent(i, 1)
        }
      }
    ]
  },
  {
    value: '2',
    label: '业务发展端',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'lostWlRate',
        label: '遗失率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '3',
    label: '操作管理端',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'lostYyRate',
        label: '遗失率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '4',
    label: '门店端',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'lostMdRate',
        label: '遗失率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '5',
    label: '接驳点',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'lostJbRate',
        label: '遗失率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  }
]
export const BwjyslColumn = [
  { dataIndex: '1', label: '组织' },
  { dataIndex: '2', label: '遗失率' },
  { dataIndex: '3', label: '完成比' },
  { dataIndex: '4', label: '月环比' }
]
// 3、票均异常率
export const PjyclTab = [
  {
    value: '1',
    label: '整体',
    keys: [
      { dataIndex: 'abnormalIndRank', label: '排名', align: 'center' },
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'abnormalRate',
        label: '异常率',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 2)
        }
      },
      {
        dataIndex: 'abnormalComRate',
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 2)
        }
      }
    ]
  },
  {
    value: '2',
    label: '业务发展端',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'networkAbnormalRate',
        label: '异常率',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 2)
        }
      }
    ]
  },
  {
    value: '3',
    label: '操作管理端',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'operateAbnormalRate',
        label: '异常率',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 2)
        }
      }
    ]
  },
  {
    value: '4',
    label: '时效类',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'ageingAbnormalPreent',
        label: '异常率',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 2)
        }
      }
    ]
  },
  {
    value: '5',
    label: '问题件类',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'pbAbnormalPreent',
        label: '异常率',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 2)
        }
      }
    ]
  }
]
export const PjyclColumn = [
  { dataIndex: '1', label: '组织' },
  { dataIndex: '2', label: '异常率' },
  { dataIndex: '3', label: '完成比' },
  { dataIndex: '4', label: '月环比' }
]
// 4、百万票客诉率
export const BwpkslTab = [
  {
    value: '1',
    label: '整体数据',
    keys: [
      { dataIndex: 'customerIndRank', label: '排名', align: 'center' },
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'totalCustomerCompRate',
        label: '百万票客诉率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      },
      {
        dataIndex: 'totalCustomerComplete',
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      }
    ]
  },
  {
    value: '2',
    label: '内部数据',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'internaCustomerCompRate',
        label: '百万票客诉率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '3',
    label: '外部数据',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'externaCustomerCompRate',
        label: '百万票客诉率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '4',
    label: '收派端数据',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'spCustomerCompRate',
        label: '百万票客诉率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  },
  {
    value: '5',
    label: '营运端数据',
    keys: [
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'opCustomerCompRate',
        label: '百万票客诉率',
        align: 'left',
        render(_, i) {
          return numToInFloat(i, 2)
        }
      }
    ]
  }
]
// 5、工单一次性解决率
export const GdycxjjlTab = [
  {
    value: '1',
    label: '工单一次性解决率',
    keys: [
      { dataIndex: 'oneTimeSolutionIndRank', label: '排名' },
      { dataIndex: 'zoneName', label: '组织', align: 'left' },
      {
        dataIndex: 'oneTimeSolutionRate',
        label: '工单一次性解决率',
        align: 'left',
        render(_, i) {
          return numToPercent(i)
        }
      },
      {
        dataIndex: 'oneTimeSolutionComRate',
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      }
    ]
  }
]
// 首页-品质类能力总览
export const qualityOverviewList = [
  {
    label: '百万件遗失率',
    dataIndex: 'lostRate',
    unit: '',
    int: [2],
    isReverse: true, // 是否为负向指标
    bottom: [
      {
        label: '月环比',
        dataIndex: 'lostMom',
        unit: '%',
        per: [1]
      },
      {
        label: '完成比',
        dataIndex: 'lostRateCom',
        unit: '%',
        per: [1]
      }
    ],
    isSort: true,
    sortIndex: 'lostIndRank'
  },
  {
    label: '万件损坏率',
    dataIndex: 'damageQtyRate',
    unit: '',
    int: [2],
    isReverse: true, // 是否为负向指标
    bottom: [
      {
        label: '月环比',
        dataIndex: 'momQty',
        unit: '%',
        per: [1]
      },
      {
        label: '完成比',
        dataIndex: 'damageQtyRateCom',
        unit: '%',
        per: [1]
      }
    ],
    isSort: true,
    sortIndex: 'damageIndRank'
  },
//   {
//     label: '万票损坏率',
//     dataIndex: 'damageWanRate',
//     unit: '',
//     int: [2],
//     isReverse: true, // 是否为负向指标
//     bottom: [
//       {
//         label: '月环比',
//         dataIndex: 'damageMom',
//         unit: '%',
//         per: [1]
//       },
//       {
//         label: '完成比',
//         dataIndex: 'damageWanRateCom',
//         unit: '%',
//         per: [1]
//       }
//     ],
//     isSort: true,
//     sortIndex: 'damageIndRank'
//  },
  {
    label: '票均异常率',
    dataIndex: 'abnormalRate',
    unit: '%',
    per: [1],
    isReverse: true, // 是否为负向指标
    bottom: [
      {
        label: '月环比',
        dataIndex: 'abnormalRateMom',
        unit: '%',
        per: [1]
      },
      {
        label: '完成比',
        dataIndex: 'abnormalComRate',
        unit: '%',
        per: [1]
      }
    ],
    isSort: true,
    sortIndex: 'abnormalIndRank'
  },
  {
    label: '工单一次性解决率',
    dataIndex: 'oneTimeSolutionRate',
    unit: '%',
    per: [1],
    bottom: [
      {
        label: '月环比',
        dataIndex: 'oneTimeSolutionRateMom',
        unit: '%',
        per: [1]
      },
      {
        label: '完成比',
        dataIndex: 'oneTimeSolutionComRate',
        unit: '%',
        per: [1]
      }
    ],
    isSort: true,
    sortIndex: 'oneTimeSolutionIndRank'
  },
  {
    label: '百万票客诉率',
    dataIndex: 'totalCustomerCompRate',
    unit: '',
    int: [1],
    isReverse: true, // 是否为负向指标
    bottom: [
      {
        label: '月环比',
        dataIndex: 'customerMom',
        unit: '%',
        per: [1]
      },
      {
        label: '完成比',
        dataIndex: 'totalCustomerComplete',
        unit: '%',
        per: [1]
      }
    ],
    isSort: true,
    sortIndex: 'customerIndRank'
  }
]
// 首页-能力类总览
export const abilityOverviewList = [
  {
    label: '新开门店健康度',
    dataIndex: 'newHealthyRatio',
    unit: '%',
    per: [1],
    bottom: [
      {
        label: '月环比',
        dataIndex: 'newHealthyMonthlyChange',
        unit: '%',
        per: [1]
      },
      {
        label: '完成比',
        dataIndex: 'newHealthyCompletionRate',
        unit: '%',
        per: [1]
      }
    ],
    isSort: true,
    sortIndex: 'newHealthyCompIndRank'
 },
  {
    label: '尾部门店占比',
    dataIndex: 'tailRatio',
    unit: '%',
    per: [1],
    isReverse: true, // 是否为负向指标
    bottom: [
      {
        label: '月环比',
        dataIndex: 'tailMonthlyChange',
        unit: '%',
        per: [1]
      },
      {
        label: '完成比',
        dataIndex: 'tailCompletionRate',
        unit: '%',
        per: [1]
      }
    ],
    isSort: true,
    sortIndex: 'tailCompIndRank'
  }
]
// 能力-新开门店健康度
export const newStoreHealthyColumns = [
  {
    value: '1',
    label: '新开门店健康度',
    keys: [
      { dataIndex: 'newHealthyCompIndRank', label: '排名', align: 'center' },
      { dataIndex: 'zoneName', label: '组织' },
      {
        dataIndex: 'newHealthyRatio',
        label: '新开门店健康度',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      },
      {
        dataIndex: 'newHealthyCompletionRate',
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      }
    ]
  }
]
// 能力-尾部门店占比
export const tailStoreRateColumns = [
  {
    value: '1',
    label: '尾部门店占比',
    keys: [
      { dataIndex: 'tailCompIndRank', label: '排名', align: 'center' },
      { dataIndex: 'zoneName', label: '组织' },
      {
        dataIndex: 'tailRatio',
        label: '尾部门店占比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      },
      {
        dataIndex: 'tailCompletionRate',
        per: [2],
        label: '完成比',
        align: 'left',
        render(_, i) {
          return numToPercent(i, 1)
        }
      }
    ]
  }
]
const fieldList = ['票均异常率', '工单一次性解决率', '尾部门店占比']
// 品质之星--省区排名
export const diaTableColumns = [
  {
    label: '指标类型',
    dataIndex: 'targetName'
  },
  {
    label: '排名',
    dataIndex: 'targetRank',
    align: 'left'
  },
  {
    label: '达成值',
    dataIndex: 'targetValue',
    align: 'left',
    render(_, i, rowData) {
      console.log('rowData---2', rowData)
      if (fieldList.includes(rowData.targetName)) {
        return numToPercent(i, 1)
      } else {
        return numToInFloat(i, 2)
      }
    }
  },
  {
    label: '完成比',
    dataIndex: 'targetCom',
    align: 'left',
    render(_, i) {
      return numToPercent(i, 1)
    }
  }
]
// 省区排名-columns配置
export const provinceColumns = [
  {
    label: '排名',
    dataIndex: 'targetName'
  },
  {
    label: '省区',
    dataIndex: 'targetName'
  },
  {
    label: '指标操作',
    dataIndex: 'targetName'
  }
]
// 区域排名-columns配置
export const areaColumns = [
  {
    label: '排名',
    dataIndex: 'targetName'
  },
  {
    label: '区域',
    dataIndex: 'targetName'
  },
  {
    label: '指标操作',
    dataIndex: 'targetName'
  }
]
// 品质之星--区域排名
// export const areaDiaTableColumns = [
//   {
//     label: '指标类型',
//     dataIndex: 'targetName'
//   },
//   {
//     label: '排名',
//     dataIndex: 'targetRank',
//     align: 'center'
//   },
//   {
//     label: '达成值',
//     dataIndex: 'targetValue',
//     align: 'left',
//     render(_, i) {
//       return numToInFloat(i, 2)
//     }
//   },
//   {
//     label: '完成比',
//     dataIndex: 'targetCom',
//     align: 'left',
//     render(_, i) {
//       return numToInFloat(i, 2)
//     }
//   }
// ]
// 首页概览跳转对应卡片数据配置
export const tabDataList = [
  {
    name: '百万件遗失率',
    topMenuCode: 'quality',
    topMenuName: '品质'
  },
  {
    name: '万件损坏率',
    topMenuCode: 'quality',
    topMenuName: '品质'
  },
  {
    name: '票均异常率',
    topMenuCode: 'quality',
    topMenuName: '品质'
  },
  {
    name: '工单一次性解决率',
    topMenuCode: 'quality',
    topMenuName: '品质'
  },
  {
    name: '百万票客诉率',
    topMenuCode: 'quality',
    topMenuName: '品质'
  },
  {
    name: '新开门店健康度',
    topMenuCode: 'ability',
    topMenuName: '能力'
  },
  {
    name: '尾部门店占比',
    topMenuCode: 'ability',
    topMenuName: '能力'
  }
]

