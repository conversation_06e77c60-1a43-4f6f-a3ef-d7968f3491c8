<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-01-02 15:39:35
 * @LastEditTime: 2023-07-04 15:53:10
 * @Description:
-->
<template>
  <div>
    <MAlertWarning type="error" v-if="zoneData.zoneLevel > 33"> 质控暂未开发{{ zoneData.levelName }}层级</MAlertWarning>
    <ScrollTabBar color="black" :tabData="tabData" :tabValue="pageTab.pageTabIndex" @change="change"></ScrollTabBar>
    <component :is="pageTab.pageTabIndex"></component>
  </div>
</template>
<script>
import quality from './qualityNew'
import homePage from './homePage'
import ability from './ability'
import { mapActions, mapMutations, mapState } from 'vuex'
import baseMixins from './baseMixins'
export default {
  mixins: [baseMixins],
  components: {
    quality,
    homePage,
    ability
  },
  data() {
    return {
      tabData: [
        {
          label: '首页',
          value: 'homePage'
        },
        {
          label: '品质',
          value: 'quality'
        },
        {
          label: '能力',
          value: 'ability'
        }
      ],
      tabLabel: '品质',
      tabValue: 'quality'
    }
  },
  computed: {
    ...mapState('qualityBord', {
      pageTab: state => state.menu.pageTab
    })
  },
  methods: {
    ...mapActions('qualityBord', ['initDate']),
    ...mapMutations('qualityBord', ['setPageIndex']),
    change({ label, value }) {
      this.$sensors.pageStay('质控-' + this.tabLabel)
      this.tabLabel = label
      this.tabValue = value
      this.$sensors.pageview('质控-' + this.tabLabel)
      this.setPageIndex({
        type: 'menu',
        dataType: 'pageTab',
        data: {
          pageTabIndex: value,
          pageTabLabel: label
        }
      })
    }
  },
  mounted() {
    this.$sensors.pageview('质控-' + this.tabLabel)
  },
  created() {
    // 初始化日期
    this.initDate()
  }
}
</script>
