/*
 * @Author: shigl
 * @Date: 2022-07-19 17:25:34
 * @LastEditTime: 2022-07-19 17:35:52
 * @Description:
 */
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
import baseMixins from '../../baseMixins'

export default {
  mixins: [requestMixins, baseMixins],

  data() {
    return {}
  },
  computed: {
    incDate() {
      if (!this.dateIndex) {
        return this.$moment(this.dateValue)
          .subtract(15, 'days')
          .format('YYYYMMDD')
      }
      return this.$moment(this.dateValue)
        .subtract(5, 'month')
        .format('YYYYMM')
    },
    key_star() {
      return ['star_day', 'star_mon'][this.dateIndex]
    },
    key_end() {
      return ['end_day', 'end_mon'][this.dateIndex]
    },
    level_code() {
      const obj = {
        30: null,
        32: 'provinceAreaCode',
        33: 'areaCode',
        34: 'deptCode'
      }
      return obj[this.zoneLevel]
    },
    next_level_code() {
      const obj = {
        30: 32,
        32: 33,
        33: 34,
        34: null
      }
      return obj[this.zoneLevel]
    },
    ...mapGetters('qualityBord', {
      dateValue: 'dateValue'
    }),
    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('qualityBord', {
      dateIndex: state => state.all.dateIndex,
      xkmdjkdData: state => state.ability.xkmdjkdData, // 新开门店健康度
      wbmdzbData: state => state.ability.wbmdzbData // 尾部门店占比
    })
  },
  methods: {}
}
