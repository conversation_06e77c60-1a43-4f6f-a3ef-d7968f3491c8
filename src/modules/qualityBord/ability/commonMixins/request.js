/*
 * @Author: <PERSON><PERSON><PERSON> <PERSON><PERSON> yang
 * @Date: 2024-10-28 09:30:15
 * @LastEditTime: 2024-10-28 09:30:15
 * @Description:
 */
import mixins from './mixins'
const defaultSxjdSiteCode = 'SFC015'
export default {
  mixins: [mixins],
  data() {
    return {}
  },
  computed: {
    defaultData() {
      let levelMap = {}
      if (this.dateIndex) {
        levelMap = {
          level_code: this.zoneLevel
        }
      }
      return {
        [this.key_star]: this.incDate,
        [this.key_end]: this.dateValue,
        // zone_level: String(this.zoneLevel),
        zone_code: this.zoneCode === '001' ? 'SFC015' : this.zoneCode, // 收入页面各层级都是zone_code
        // zone_code: 'SFC015'
        ...levelMap
      }
    }
  },
  methods: {
    _changeDefaultSiteCode(code) {
      if (code === '001') return defaultSxjdSiteCode
      return code
    },
    // 1、质控战况--首页概览数据（品质和能力）
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488414
    _getQualityOverviewsData(params) {
      return this.postRequest('/cockpit/sxzk_qc/detail', params)
    },
    // 2、质控战况--首页品质之星排名（品质和能力）
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488422
    _getQualityStarRankData(params) {
      return this.postRequest('/cockpit/sxzk_qc/list', params)
    },
    // 3、质控战况--历史明细（品质和能力及能力模块）
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488438
    _getHistoricalListData(params) {
      return this.postRequest('/cockpit/sxzk_qc/historicalList', params)
    }
  }
}
