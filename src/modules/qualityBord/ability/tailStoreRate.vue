<template>
  <div>
    <CardList title="尾部门店占比">
      <Tabs slot="nav" :options="tabData" :tabIndex="dateTypeIndex" @tabSelect="dateTypeSelect"></Tabs>
      <div class="pd_lr20">
        <div class="chart-container">
          <div class="right-btn-wrap" @click="handleShowProvince">
            展开{{ nextLevelName }} <i class="iconfont icon-dayuhao fs20"></i>
          </div>
          <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>

      </div>
    </CardList>
    <ProvinceDrawer :show="provinceShow" :handleClose="handleProvinceClose" :title="provinceTitle" :tabs="tailStoreRateColumns"
      :request="getProvinceData"></ProvinceDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawScrollChartNew } from 'common/charts/chartOption'
import request from './commonMixins/request'
import { tailStoreRateColumns } from '../constant'
import moment from 'moment'
import ProvinceDrawer from './provinceDrawer.vue'
import { numToPercent } from 'common/js/numFormat'
import { mapMutations, mapState } from 'vuex'
// import { DateTabTypes } from '../constant'
export default {
  mixins: [mixins, request],
  components: { ProvinceDrawer },
  data() {
    return {
      tailStoreRateColumns,
      provinceShow: false,
      tabData: ['周', '月'],
      dateTypeList: [2, 3],
      dateTypeIndex: 0,
      dataSource: {},
      tableData: {},
      legendDataSource: [],
      provinceTitle: '尾部门店占比'
    }
  },
  computed: {
    ...mapState('qualityBord', {
      qualityDate: state => state.all.dateDay,
      wbmdzbData: state => state.ability.wbmdzbData
    }),
    nextLevelName() {
      if (+this.zoneLevel === 34) {
        return '网点'
      }
      return this.zoneData.dLevelName === '网管' ? '网点' : this.zoneData.dLevelName
    },
    legendOption() {
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: [
          { label: '实际达成值', per: [1], seriesIndex: 0 },
          { label: '目标值', per: [1], seriesIndex: 1 }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    qualityDate(val) {
      this.init()
    },
    zoneCode(val) {
      this.init()
    },
    dateTypeIndex(val) {
      this.init()
    },
    wbmdzbData(val) {
      this.initData()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('qualityBord', ['setPageData']),
    // 展开详情
    async getProvinceData() {
      // const siteCode = this._changeDefaultSiteCode(this.zoneCode)
      const time = moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        date: time,
        dataLevel: this.next_level_code,
        dateType: this.dateTypeList[this.dateTypeIndex],
        sortType: 3
      }
      if (this.level_code) {
        params[this.level_code] = this.zoneCode
      }
      const data = await this._getQualityStarRankData(params)
      return data?.obj || []
    },
    handleShowProvince() {
      this.provinceShow = true
    },
    handleProvinceClose() {
      this.provinceShow = false
    },
    // 初始化趋势图
    init() {
      this.getData().then(() => {
        // this.initData()
      })
    },
    async getData() {
      // const siteCode = this._changeDefaultSiteCode(this.zoneCode)
      const time = moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        date: time,
        dataLevel: this.zoneLevel,
        dateType: this.dateTypeList[this.dateTypeIndex],
        sortType: 3
      }
      if (this.level_code) {
        params[this.level_code] = this.zoneCode
      }
      const data = await this._getHistoricalListData(params)
      const obj = data.obj
      this.setPageData({
        type: 'ability',
        dataType: 'wbmdzbData',
        data: obj?.itemList || []
      })
    },
    dateTypeSelect(index) {
      this.dateTypeIndex = index
    },
    initData() {
      const currentData = this.wbmdzbData || []
      this.initModelChart(currentData, 'tailRatio', 'tailTarget')
    },
    initModelChart(result, key1, key2) {
      const fliterList = JSON.parse(JSON.stringify(result))
      const xData = []
      const sData = [[], []]
      if (fliterList && fliterList.length > 0) {
        fliterList.forEach((item, index) => {
          xData.push(item.date)
          sData[0].push({
            ...item,
            value: item?.data[key1] || 0
          })
          sData[1].push({
            ...item,
            value: item?.data[key2] || 0
          })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 12
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              interval: 0, // 每个标签都显示
              rich: {
                red: {
                  color: 'red', // 第一部分的颜色为红色
                  background: 'red'
                },
                blue: {
                  color: 'blue' // 第二部分的颜色为蓝色
                }
              },
              formatter: value => {
                if (this.dateTypeIndex === 0) {
                  return this.$dateFormat(value, 1, true)
                } else {
                  const date = moment(value).format('YYYYMM')
                  return this.$dateFormat(date, 2, true)
                }
                // if (this.dateTypeIndex === 0) {
                //   return this.$dateFormat(value, this.dateIndex)
                // } else if (this.dateTypeIndex === 1) {
                //   return `第${value}周`
                // } else {
                //   return `${moment(value).format('M')}月`
                // }
              }
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: (value, ...reset) => {
                // return value
                return `${numToPercent(value)}`
              }
            }
          }
        ],
        series: [
          {
            type: 'line',
            data: sData[0],
            smooth: true
          },
          {
            type: 'line',
            data: sData[1],
            smooth: true
          }
        ]
      }
      drawScrollChartNew(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.multi_data_list_wrapper {
  background-color: #fff !important;
}

/deep/ .chart_legend_box {
  width: 85% !important;
}

.gauge-chart-box {
  height: 1.76rem;
  width: 96%;
}

.data_list_custom {
  background-color: #fff !important;
}

.text_conent {
  height: 0.81rem;
  width: 3.34rem;
  background: #f8f9fc;
  border-radius: 8px;
}

.chart-container {
  position: relative;
  padding-top: 0.12rem;
}

.right-btn-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  position: absolute;
  right: 0;
  top: 0.30rem;
}
</style>

