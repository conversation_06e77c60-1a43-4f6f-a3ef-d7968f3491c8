<!--
 * @Author: gilshi
 * @Date: 2021-06-25 13:53:55
 * @LastEditTime: 2023-12-12 10:01:04
 * @Description:
-->
<template>
  <div class="profit_page page_bgc">
    <div class="date_container">
      <KyDatePicker style="width: 3rem" :type="dateType" :dateValue="dateValue" @onChange="dateChange"
        :holidayData="holidayData"></KyDatePicker>
    </div>
    <PageContent>
      <Wjshl></Wjshl>
      <!-- <Wphsl></Wphsl> -->
      <Bwjysl></Bwjysl>
      <Pjycl></Pjycl>
      <Bwpksl></Bwpksl>
      <Gdycxjjl></Gdycxjjl>
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>
<script>
import flowLayout from 'common/mixins/flowLayout.js'
// import Wphsl from './wphsl.vue'
import Bwjysl from './bwjysl.vue'
import Pjycl from './pjycl.vue'
import Bwpksl from './bwpksl.vue'
import Gdycxjjl from './gdycxjjl.vue'
import Wjshl from './wjshl.vue'

import { mapMutations, mapState } from 'vuex'
import request from './commonMixins/request'
export default {
  mixins: [flowLayout, request],
  components: {
    // Wphsl,
    Bwjysl,
    Pjycl,
    Bwpksl,
    Gdycxjjl,
    Wjshl
  },
  data() {
    return {}
  },
  computed: {
    dateType() {
      return 'day'
    },
    ...mapState({
      holidayData: 'holidayData'
    })
  },
  watch: {
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    ...mapMutations('qualityBord', ['setDate', 'setDateIndex', 'setPageData']),
    dateChange(date) {
      this.setDate({
        type: 'all',
        key: 'dateDay',
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
      this.initOptions()
    },
    //  【收入】【货量总览】

    initOptions() {
      // if (!this.dateIndex) {
      //   this.getWphslDay()
      // } else {
      //   this.getWphslMon()
      // }
    }
  },
  async mounted() {
    // 占时只有月,每次回来将dateIndex=1
    this.setDateIndex({
      type: 'all',
      index: 0
    })
    this.initOptions()
  }
}
</script>
<style lang="less" scoped>
.profit_page {
  color: #333;
}

.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
