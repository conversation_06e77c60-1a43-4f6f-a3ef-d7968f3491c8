/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-07-05 18:43:55
 * @Description:
 */
import mixins from './mixins'
const defaultSxjdSiteCode = 'SFC015'
export default {
  mixins: [mixins],
  data() {
    return {}
  },
  computed: {
    defaultData() {
      let levelMap = {}
      if (this.dateIndex) {
        levelMap = {
          level_code: this.zoneLevel
        }
      }
      return {
        [this.key_star]: this.incDate,
        [this.key_end]: this.dateValue,
        // zone_level: String(this.zoneLevel),
        zone_code: this.zoneCode === '001' ? 'SFC015' : this.zoneCode, // 收入页面各层级都是zone_code
        // zone_code: 'SFC015'
        ...levelMap
      }
    }
  },
  methods: {
    _changeDefaultSiteCode(code) {
      if (code === '001') return defaultSxjdSiteCode
      return code
    },
    // 【收入】【货量总览】月维度
    _getWphslMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_income_weight_mi_01', this.forMapData(tmp))
    },
    // 【收入】【货量总览】日维度
    _getData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryDamage', params)
    },
    // 损坏率省区
    // http://yapi.sit.sf-express.com/project/4230/interface/api/473690
    _getWpshlProvinceData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryDamageDetails', params)
    },
    // 损坏率省区
    // http://yapi.sit.sf-express.com/project/4230/interface/api/473690
    _getBwjyslProvinceData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryLossDetails', params)
    },
    // 票均异常省区
    // http://yapi.sit.sf-express.com/project/4230/interface/api/473682
    _getPjycProvinceData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryAbnormalTicketDetails', params)
    },

    // http://yapi.sit.sf-express.com/project/4230/interface/api/473694
    _getBwjyslData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryLoss', params)
    },
    // http://yapi.sit.sf-express.com/project/4230/interface/api/473678
    _getPjyclData(params) {
      return this.postRequest('/cockpit/sxQc/situation/queryAbnormalTicket', params)
    },
    // 《《《《《《《《《======以下为新接口======》》》》》》》》》》
    // 万票损坏率---查询损坏率---趋势 （202503 指标展示替换成万件损坏率）
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488230
    _getWpshlDataNew(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryDamage', params)
    },
    // 万票损坏率---查询损坏率---详情 （202503 指标展示替换成万件损坏率）
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488230
    _getWpshlDataNewDetail(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryDamageDetails', params)
    },
    // 万件损坏率---查询损坏率---趋势
    // http://yapi.sit.sf-express.com/project/4230/interface/api/538464
    _getWjshlData(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryTenThousandPiecesDamage', params)
    },
    // 万件损坏率---查询损坏率---详情
    // http://yapi.sit.sf-express.com/project/4230/interface/api/538465
    _getWjshlDataDetail(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryTenThousandPiecesDamageDetails', params)
    },
    // 百万件遗失率---查询遗失率---趋势
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488238
    _getBwjyslDataNew(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryLoss', params)
    },
    // 百万件遗失率---查询遗失率详情---详情
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488242
    _getBwjyslDataNewDetail(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryLossDetails', params)
    },
    // 票均异常率---查询票均异常率---趋势
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488214
    _getPjyclDataNew(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryAbnormalTicket', params)
    },
    // 票均异常率---查询票均异常率详情---详情
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488218
    _getPjyclDataNewDetail(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryAbnormalTicketDetails', params)
    },
    // 工单一次性解决率---查询工单一次性解决率---趋势
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488246
    _getGdycxjjlDataNew(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryWorkResolu', params)
    },
    // 工单一次性解决率---查询工单一次性解决率详情--详情
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488250
    _getGdycxjjlDataNewDetail(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryWorkResoluDetails', params)
    },
    // 百万票客诉率---查询百万票客诉率---趋势
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488222
    _getBwpkslDataNew(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryCusCom', params)
    },
    // 百万票客诉率---查询百万票客诉率详情--详情
    // http://yapi.sit.sf-express.com/project/4230/interface/api/488226
    _getBwpkslDataNewDetail(params) {
      return this.postRequest('/cockpit/sxQcQuaSumDi/queryCusComDetails', params)
    }
  }
}
