<template>
  <div>
    <CardList title="工单一次性解决率">
      <Tabs slot="nav" :options="tabData" :tabIndex="dateTypeIndex" @tabSelect="dateTypeSelect"></Tabs>
      <div class="pd_lr20">
        <!-- <BtnTabs class="mt32" :options="dataTypeList" :activeIndex="dataTypeIndex" @btnConfirm="btnConfirm" column="3">
        </BtnTabs> -->
        <div class="chart-container">
          <div class="right-btn-wrap" @click="handleShowProvince">
            展开{{ nextLevelName }} <i class="iconfont icon-dayuhao fs20"></i>
          </div>
          <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>

      </div>
    </CardList>
    <ProvinceDrawer :show="provinceShow" :handleClose="handleProvinceClose" :title="provinceTitle" :tabs="GdycxjjlTab"
      :request="getProvinceData"></ProvinceDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawScrollChartNew } from 'common/charts/chartOption'
import request from './commonMixins/request'
import { DateTabLabels, GdycxjjlTab } from '../constant'
import moment from 'moment'
import ProvinceDrawer from './provinceDrawer.vue'
import { mapMutations, mapState } from 'vuex'
import { numToPercent } from 'common/js/numFormat'
import { DateTabTypes } from '../constant'
const typeData = [
  {
    label: '工单一次性解决率',
    key1: 'oneTimeSolutionRate',
    key2: 'oneTimeSolutionTarget'
  }
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [mixins, request],
  components: { ProvinceDrawer },
  data() {
    return {
      GdycxjjlTab,
      provinceShow: false,
      tabData: [...DateTabLabels],
      dateTypeIndex: 0,
      dataTypeList: [...dataTypeList],
      dataTypeIndex: 0,
      dataSource: {},
      tableData: {},
      legendDataSource: [],
      provinceTitle: '工单一次性解决率'
    }
  },
  computed: {
    ...mapState('qualityBord', {
      qualityDate: state => state.all.dateDay,
      gdycxjjlDay: state => state.quality.gdycxjjlDay,
      gdycxjjlWeek: state => state.quality.gdycxjjlWeek,
      gdycxjjlMon: state => state.quality.gdycxjjlMon
    }),
    nextLevelName() {
      if (+this.zoneLevel === 34) {
        return '网点'
      }
      return this.zoneData.dLevelName === '网管' ? '网点' : this.zoneData.dLevelName
    },
    legendOption() {
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: [
          { label: '实际达成值', per: [1], seriesIndex: 0 },
          { label: '目标值', per: [1], seriesIndex: 1 }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    qualityDate(val) {
      this.init()
    },
    zoneCode(val) {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('qualityBord', ['setPageData']),
    async getProvinceData() {
      // const siteCode = this._changeDefaultSiteCode(this.zoneCode)
      // const time = moment(this.qualityDate).format('YYYY-MM-DD')
      // const data = await this._getBwjyslProvinceData({
      //   siteCode,
      //   dateType: DateTabTypes[this.dateTypeIndex]?.value,
      //   time
      // })
      const time = moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        dateType: DateTabTypes[this.dateTypeIndex]?.value,
        incDate: time,
        zoneLevel: this.zoneLevel
      }
      if (this.level_code) {
        params[this.level_code] = this.zoneCode
      }
      const data = await this._getGdycxjjlDataNewDetail(params)
      return data?.obj || []
    },
    handleShowProvince() {
      this.provinceShow = true
    },
    handleProvinceClose() {
      this.provinceShow = false
    },
    init() {
      this.getData().then(() => {
        this.initData()
      })
    },
    async getData() {
      // const siteCode = this._changeDefaultSiteCode(this.zoneCode)
      // const time = moment(this.qualityDate).format('YYYY-MM-DD')
      // const data = await this._getBwjyslData({
      //   siteCode,
      //   dateType: 'day',
      //   time
      // })
      const time = moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        dateType: DateTabTypes[this.dateTypeIndex]?.value,
        incDate: time,
        zoneLevel: this.zoneLevel
      }
      if (this.level_code) {
        params[this.level_code] = this.zoneCode
      }
      const data = await this._getGdycxjjlDataNew(params)
      const obj = data.obj
      this.setPageData({
        type: 'quality',
        dataType: 'gdycxjjlDay',
        data: obj?.day
      })
      this.setPageData({
        type: 'quality',
        dataType: 'gdycxjjlWeek',
        data: obj?.week
      })
      this.setPageData({
        type: 'quality',
        dataType: 'gdycxjjlMon',
        data: obj?.month
      })
    },
    dateTypeSelect(index) {
      this.dateTypeIndex = index
      this.initData()
    },
    // btnConfirm({ index }) {
    //   this.dataTypeIndex = index
    //   this.initData()
    // },
    initData() {
      const dataList = [this.gdycxjjlDay, this.gdycxjjlWeek, this.gdycxjjlMon]
      const currentData = dataList[this.dateTypeIndex]
      // const dataTypeIndex = this.dataTypeIndex
      // const { key1, key2 } = typeData.find((item, index) => index === dataTypeIndex)
      // this.initModelChart(currentData, key1, key2)
      this.initModelChart(currentData, 'oneTimeSolutionRate', 'oneTimeSolutionTarget')
    },
    initModelChart(result, key1, key2) {
      const fliterList = JSON.parse(JSON.stringify(result))
      const xData = []
      const sData = [[], []]
      if (fliterList && fliterList.length > 0) {
        fliterList.forEach((item, index) => {
          xData.push(item.time)
          sData[0].push({
            value: item[key1] || 0,
            ...item
          })
          sData[1].push({
            ...item,
            value: item[key2] || 0
          })
        })
      }
      console.log("sasa===4", sData)
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 12
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              interval: 0, // 每个标签都显示
              rich: {
                red: {
                  color: 'red', // 第一部分的颜色为红色
                  // fontSize: 14 // 设置字体大小
                  background: 'red'
                },
                blue: {
                  color: 'blue' // 第二部分的颜色为蓝色
                  // fontSize: 12
                }
              },
              formatter: value => {
                if (this.dateTypeIndex === 0) {
                  return this.$dateFormat(value, this.dateIndex)
                } else if (this.dateTypeIndex === 1) {
                  return `第${value}周`
                  // if (value === '1') {
                  //   // return `第${value}周\n`
                  //   return "第" + value + '}\n{red|' + "" + '}';
                  // } else {
                  // }
                } else {
                  return `${moment(value).format('M')}月`
                }
              }
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: (value, ...reset) => {
                // return value
                return `${numToPercent(value, 0)}`
              }
            }
          }
        ],
        series: [
          {
            type: 'line',
            data: sData[0],
            smooth: true
          },
          {
            type: 'line',
            data: sData[1],
            smooth: true
          }
        ]
      }
      drawScrollChartNew(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.multi_data_list_wrapper {
  background-color: #fff !important;
}

/deep/ .chart_legend_box {
  width: 85% !important;
}

.gauge-chart-box {
  height: 1.76rem;
  width: 96%;
}

.data_list_custom {
  background-color: #fff !important;
}

.text_conent {
  height: 0.81rem;
  width: 3.34rem;
  background: #f8f9fc;
  border-radius: 8px;
}

.chart-container {
  position: relative;
  padding-top: 0.12rem;
}

.right-btn-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  position: absolute;
  right: 0;
  top: 0.30rem;
}
</style>
