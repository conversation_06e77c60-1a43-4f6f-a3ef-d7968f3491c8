<template>
  <KyDataDrawer :visible="show" :title="title" height="80%" @close="diaClose" @drawerHeight="mixinsDrawerHeight">
    <div class="flex_between pd_lr20 mt32">
      <div class="tab-list" v-if="tabs?.length &&tabs.length>1">
        <div class="tab-list__item" v-for="(i, index) in tabs" :key="index"
          :class="{ 'active-tab': selectedTabIndex === index }" @click="selectTab(index)">
          {{ i?.label }}
        </div>
      </div>
    </div>
    <div class="cus_table_wrap">
      <NormalTable :columns="tableColumns" :dataSource="tableData" :maxHeight="mixinsTableMaxHeight" />
    </div>
  </KyDataDrawer>
</template>
<script>
import request from './commonMixins/request'

export default {
  name: 'ProvinceDrawer',
  mixins: [request],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    handleClose: {
      type: Function,
      default() {
        return () => { }
      }
    },
    title: {
      type: String,
      default: ''
    },
    tabs: {
      type: Array,
      default() {
        return []
      }
    },
    columns: {
      type: Array,
      default() {
        return []
      }
    },
    request: {
      type: Function,
      default() {
        return () => { }
      }
    }
  },
  computed: {
    tableColumns() {
      // return [{ dataIndex: 'zoneName', label: '组织' },]
      return this.tabs[this.selectedTabIndex]?.keys.map(i => {
        return i
      })
      // return [
      //   { label: this.nextLevelName, dataIndex: 'group_name' },
      //   {
      //     label: '应交票数',
      //     dataIndex: 'total_ewbno'
      //   },
      //   {
      //     label: '延误票数',
      //     dataIndex: 'delay_ewbno'
      //   },
      //   // {
      //   //   label: '交货率',
      //   //   dataIndex: 'intime_rate',
      //   //   render: (h, val) => this.$numToPercent(val, 1)
      //   // }
      // ]
    },
    tableData() {
      if (this.tableColumns[0].label === '排名') {
        const list = [...this.data]
        const sortKey = this.tableColumns[0].dataIndex
        list.sort((a, b) => {
          return a[sortKey] - b[sortKey]
        })
        return list
      }
      return this.data
    }
  },
  data() {
    return {
      mixinsTableMaxHeight: '8rem',
      visible: false,
      selectedTabIndex: 0, // 选中的tab索引
      data: []
    }
  },
  watch: {
    show(val) {
      if (val) this.init()
    }
  },
  methods: {
    init() {
      this.request().then(res => {
        console.log('request:', res)
        this.data = res
      })
    },
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    diaClose() {
      this.visible = false
      this.handleClose()
    },
    selectTab(index) {
      this.selectedTabIndex = index // 更新选中的tab索引
      // getData()
    }
  }
}
</script>
<style lang="less" scoped>
.tab-list {
  display: flex;
  // margin-top: 20px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 16px;
  padding-top: 6px;
  overflow-x: auto;
}

.tab-list__item {
  // flex: 1;
  padding: 0 4px;
  font-size: 13px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #999999;
  white-space: nowrap;
}

.active-tab {
  color: #dc1e32;
  font-weight: bold;
  position: relative;

  &::before {
    position: absolute;
    content: '';
    bottom: -10px;
    left: 0;
    right: 0;
    margin: auto;
    height: 2px;
    width: 30px;
    border-radius: 4px;
    background-color: #dc1e32;
  }
}
/deep/ .cus_table_wrap .sheader .flex-left {
  display: flex;
  justify-content: space-around !important;
  align-items: center;
}
/deep/ .cus_table_wrap .sbody .flex_start {
  display: flex;
  justify-content: space-around !important;
  align-items: center;
}
</style>
