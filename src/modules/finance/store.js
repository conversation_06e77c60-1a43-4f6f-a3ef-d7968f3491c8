/*
 * @Author: shigl
 * @Date: 2022-07-19 17:25:34
 * @LastEditTime: 2022-07-20 18:46:28
 * @Description:
 */
import moment from 'moment'
export default {
  name: 'finance',
  namespaced: true,
  state: {
    all: {
      dateIndex: 0,
      dateDay: '',
      dateMon: ''
    },
    profit: {
      overProfitMon: [],
      overProfitDay: [],
      grossChangeDay: [],
      grossChangeMon: [],
      grossPriceDay: [],
      grossPriceMon: [],
      profitChangeMon: [],
      profitChangeDay: [],
      areaProfitMon: [],
      areaProfitDay: []
    },
    revenue: {
      cargoOverviewMon: [],
      cargoOverviewDay: [],
      trendChangeMon: [],
      trendChangeDay: [],
      trendChangeComMon: [],
      trendChangeComDay: []
    },
    cost: {
      costDay: {
        objCard: [],
        objLine: []
      },
      costMon: []
    }
  },
  getters: {
    dateValue(state, getters) {
      const dateIndex = state.all.dateIndex
      const dateDay = state.all.dateDay
      const dateMon = state.all.dateMon
      if (!dateIndex) {
        return dateDay
      }
      return dateMon
    }
  },
  mutations: {
    setDate(state, { type, key, date }) {
      state[type][key] = date
    },
    setDateIndex(state, { type, index }) {
      state[type].dateIndex = index
    },
    // 数据赋值,type:页面,dataType:变量
    setPageData(state, { type, dataType, data }) {
      state[type][dataType] = data
    }
  },
  actions: {
    initDate({ state }) {
      state.all.dateDay = moment().add(-1, 'days').format('YYYYMMDD')
      const tody = moment().format('DD')
      state.all.dateMon =
        tody < 15 ? moment().add(-2, 'month').format('YYYYMM') : moment().add(-1, 'month').format('YYYYMM')
    }
  }
}
