import { numToInteger, numToPercent } from 'common/js/numFormat'

export const overAllProfit = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'day_value',
          int: [0, 10000]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'day_value_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'day_value_cum',
          int: [0, 10000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'day_value_cum_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'inc_value',
          int: [0, 10000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'hb_rate',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'acc_value',
    //       int: [0, 10000]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成比',
    //       dataIndex: 'y_target_com',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]
export const carProfit = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'day_value',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'day_value_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'day_value_cum',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'day_value_cum_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'inc_value',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'hb_rate',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'acc_value',
    //       int: [0, 1000]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成比',
    //       dataIndex: 'y_target_com',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]
export const tableColumns = [
  [
    {
      label: '排名',
      dataIndex: '',
      align: 'center',
      fixed: 'left',
      width: '0.8rem',
      render: (h, value) => {
        return (
          <div class={'flex_center'}>
            <div class="normal-rank">{value <= 2 ? '' : value + 1}</div>
          </div>
        )
      }
    },
    { label: '省区', dataIndex: 'zone_name', width: '1.4rem', fixed: 'left' },

    {
      label: '当日值',
      dataIndex: 'day_value',
      render: (h, value) => numToInteger(value, 1, 10000)
    },
    {
      label: '日环比',
      dataIndex: 'day_value_hb',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '同比上周',
      dataIndex: 'week_value_tb',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '月累计(万)',
      dataIndex: 'day_value_cum',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '预算值',
      dataIndex: 'target_value',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '完成比',
      dataIndex: 'complete_rate',
      render: (h, value) => numToPercent(value)
    },
    {
      label: '差额',
      dataIndex: 'balance',
      render: (h, value) => numToInteger(value, 0, 10000)
    }
    // {
    //   label: '负责人',
    //   dataIndex: 'person_liable'
    // }
  ],
  [
    {
      label: '排名',
      dataIndex: '',
      align: 'center',
      fixed: 'left',
      width: '0.8rem',
      render: (h, value) => {
        return (
          <div class={'flex_center'}>
            <div class="normal-rank">{value <= 2 ? '' : value + 1}</div>
          </div>
        )
      }
    },
    { label: '省区', dataIndex: 'zone_name', width: '1.4rem', fixed: 'left' },
    {
      label: '当月值',
      dataIndex: 'inc_value',
      // width: '1.4rem',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '月环比',
      dataIndex: 'hb_rate',
      // width: '1.4rem',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '月完成比',
      dataIndex: 'm_target_com',
      // width: '1.4rem',
      render: (h, value) => numToPercent(value)
    }
    // {
    //   label: '年累计',
    //   dataIndex: 'acc_value',
    //   render: (h, value) => numToInteger(value, 0, 10000)
    // },
    // {
    //   label: '年完成比',
    //   dataIndex: 'y_target_com',
    //   render: (h, value) => numToPercent(value)
    // },
    // {
    //   label: '负责人',
    //   dataIndex: 'person_liable'
    // }
  ]
]
