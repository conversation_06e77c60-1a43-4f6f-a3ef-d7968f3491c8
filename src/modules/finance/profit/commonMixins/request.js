/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-07-05 18:47:39
 * @Description:
 */
import mixins from './mixins'
export default {
  mixins: [mixins],
  data() {
    return {}
  },
  computed: {
    defaultData() {
      let levelMap = {}
      if (this.dateIndex) {
        levelMap = {
          level_code: this.zoneLevel
        }
      }
      return {
        [['inc_day', 'inc_month'][this.dateIndex]]: this.incDate,
        // zone_level: String(this.zoneLevel),
        zone_code: this.zoneCode === '001' ? 'SFC015' : this.zoneCode, // 收入页面各层级都是zone_code
        // zone_code: 'SFC015' // 收入页面各层级都是zone_code
        ...levelMap
      }
    }
  },
  methods: {
    // 【利润】【利润总览】-日维度
    _getOverProfitDay(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_fin_sx_zk_profit_di_01', this.forMapData(tmp))
    },
    // 【利润】【利润总览】-月维度
    _getOverProfitMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_profit_sum_mi01', this.forMapData(tmp))
    },
    // 【利润】【毛利变化趋势】-日维度
    _getGrossChangeDay(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_fin_sx_zk_profit_di_02', this.forMapData(tmp))
    },
    // 【利润】【毛利变化趋势】-月维度
    _getGrossChangetMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_profit_sum_mi02', this.forMapData(tmp))
    },
    // 【利润】【毛利单价趋势】-日维度
    _getGrossPriceDay(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_fin_sx_zk_profit_di_03', this.forMapData(tmp))
    },
    // 【利润】【毛利单价趋势】-月维度
    _getGrossPriceMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_profit_sum_mi03', this.forMapData(tmp))
    },
    // 【利润】【利润变化趋势】-日维度
    _getprofitChangeDay(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_fin_sx_zk_profit_di_04', this.forMapData(tmp))
    },
    // 【利润】【利润变化趋势】-月维度
    _getprofitChangeMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_profit_sum_mi04', this.forMapData(tmp))
    },
    // todo 【利润】【省区排名】-日维度
    _getAreaProfitDay(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_fin_sx_zk_profit_province_rank_di_01_new', this.forMapData(tmp))
    },
    // todo【利润】【省区排名】-月维度 不需要zoneLevel但是也传了
    _getAreaProfiteMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_profit_sum_mi_rank_new', this.forMapData(tmp))
    }
  }
}
