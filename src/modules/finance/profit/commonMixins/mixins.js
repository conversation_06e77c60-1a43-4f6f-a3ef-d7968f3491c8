/*
 * @Author: shigl
 * @Date: 2022-07-19 17:25:34
 * @LastEditTime: 2022-07-19 17:35:25
 * @Description:
 */
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
import baseMixins from '../../baseMixins'
export default {
  mixins: [requestMixins, baseMixins],
  data() {
    return {}
  },
  computed: {
    incDate() {
      console.log(this.dateValue, 'this.dateValue')
      if (!this.dateIndex) {
        return this.isDev ? '20210519' : this.$moment(this.dateValue).format('YYYYMMDD')
      }
      return this.isDev ? '202105' : this.$moment(this.dateValue).format('YYYYMM')
    },
    key_date() {
      return ['inc_day', 'inc_month'][this.dateIndex]
    },
    key_name() {
      return ['kpi_name', 'index_name'][this.dateIndex]
    },
    key_rate() {
      return ['complete_rate', 'm_target_com'][this.dateIndex]
    },
    key_value() {
      return ['day_value', 'inc_value'][this.dateIndex]
    },
    ...mapGetters('finance', {
      dateValue: 'dateValue'
    }),
    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('finance', {
      dateIndex: state => state.all.dateIndex,

      overProfitDay: state => state.profit.overProfitDay,
      overProfitMon: state => state.profit.overProfitMon,
      grossChangeMon: state => state.profit.grossChangeMon,
      grossChangeDay: state => state.profit.grossChangeDay,
      grossPriceMon: state => state.profit.grossPriceMon,
      grossPriceDay: state => state.profit.grossPriceDay,
      profitChangeMon: state => state.profit.profitChangeMon,
      profitChangeDay: state => state.profit.profitChangeDay,
      areaProfitMon: state => state.profit.areaProfitMon,
      areaProfitDay: state => state.profit.areaProfitDay
    })
  },
  methods: {}
}
