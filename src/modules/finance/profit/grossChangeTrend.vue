<template>
  <div>
    <CardList title="毛利变化趋势">
      <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
        <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
      </KydChartModel>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawScrollChart } from 'common/charts/chartOption'

export default {
  mixins: [mixins],
  data() {
    return {
      tableWidth: '100%',
      tableDataSource: [],
      tableData: {},
      tableColumns: [],
      childColumns: [],
      legendDataSource: []
    }
  },
  watch: {
    grossChangeMon(val) {
      this.initData(val)
    },
    grossChangeDay(val) {
      this.initData(val)
    }
  },
  computed: {
    legendOption() {
      const optionList = {
        30: [
          { label: '毛利额', dataIndex: 'value', int: [0, 10000] },
          { label: '毛利率', dataIndex: 'value', per: [1] },
          { label: 'OD毛利', color: '#ffffff', seriesIndex: 0, dataIndex: 'value3', int: [0, 10000] },
          { label: '其他毛利', color: '#ffffff', seriesIndex: 0, dataIndex: 'value4', int: [0, 10000] }
        ],
        32: [
          { label: '毛利额', dataIndex: 'value', int: [0, 10000] },
          { label: '毛利率', dataIndex: 'value', per: [1] },
          { label: 'OD毛利', color: '#ffffff', seriesIndex: 0, dataIndex: 'value3', int: [0, 10000] },
          { label: '内部结算毛利', color: '#ffffff', seriesIndex: 0, dataIndex: 'value5', int: [0, 10000] },
          { label: '其他毛利', color: '#ffffff', seriesIndex: 0, dataIndex: 'value4', int: [0, 10000] }
        ],
        33: [
          { label: '毛利额', dataIndex: 'value', int: [0, 10000] },
          { label: '毛利率', dataIndex: 'value', per: [1] },
          { label: 'OD毛利', color: '#ffffff', seriesIndex: 0, dataIndex: 'value3', int: [0, 10000] },
          { label: '内部结算毛利', color: '#ffffff', seriesIndex: 0, dataIndex: 'value5', int: [0, 10000] },
          { label: '其他毛利', color: '#ffffff', seriesIndex: 0, dataIndex: 'value4', int: [0, 10000] }
        ]
      }[+this.zoneLevel]
      return {
        type: 'column',
        options: optionList,
        dataSource: this.legendDataSource
      }
    }
  },
  mounted() {},
  methods: {
    initData(result) {
      let xData = []
      const sData = [[], []]
      if (result.length) {
        const tmp = this.legendOption.options.map(item => {
          return result.filter(data => data[this.key_name] === item['label'])
        })
        console.log(JSON.parse(JSON.stringify(tmp)), 'tmp')
        xData = this.$mergexAxisData(tmp, this.key_date)
        const value1 = this.$changeSeriesData(tmp[0], xData, this.key_date, this.key_value, 'value1')
        const value2 = this.$changeSeriesData(tmp[1], xData, this.key_date, this.key_value, 'value2')
        const value3 = this.$changeSeriesData(tmp[2], xData, this.key_date, this.key_value, 'value3')
        const value4 = this.$changeSeriesData(tmp[3], xData, this.key_date, this.key_value, 'value4')

        let dataList = []
        if (+this.zoneLevel === 32 || +this.zoneLevel === 33) {
          const value5 = this.$changeSeriesData(tmp[4], xData, this.key_date, this.key_value, 'value5')
          dataList = _.defaultsDeep(value1, value2, value3, value4, value5)
          dataList.forEach((item, index) => {
            sData[0].push({
              value: item.value1,
              value3: item.value3,
              value4: item.value5,
              value5: item.value4
            })
            sData[1].push({
              value: item.value2
            })
          })
        } else {
          dataList = _.defaultsDeep(value1, value2, value3, value4)
          dataList.forEach((item, index) => {
            sData[0].push({
              value: item.value1,
              value3: item.value3,
              value4: item.value4
            })
            sData[1].push({
              value: item.value2
            })
          })
        }
      }

      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 10000)
            }
          },
          {
            axisLabel: {
              formatter: value => this.$perFormat(value)
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: sData[0]
          },
          {
            type: 'line',

            yAxisIndex: 1,
            data: sData[1]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped></style>
