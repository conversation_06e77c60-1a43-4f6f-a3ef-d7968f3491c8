<template>
  <div>
    <CardList title="毛利单价趋势">
      <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
        <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
      </KydChartModel>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawScrollChart } from 'common/charts/chartOption'

export default {
  mixins: [mixins],
  data() {
    return {
      tableData: {},
      legendDataSource: []
    }
  },
  watch: {
    grossPriceMon(val) {
      this.initData(val)
    },
    grossPriceDay(val) {
      this.initData(val)
    }
  },
  computed: {
    legendOption() {
      return {
        type: 'column',
        options: [
          { label: '毛利单价', int: [3, 1, 3] },
          { label: 'OD毛利单价', int: [3, 1, 3] },
          { label: '其他毛利单价', color: '#52CCA3', int: [3, 1, 3] }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  mounted() {},
  methods: {
    initData(result) {
      this.legendDataSource = []
      if (result.length) {
        let xData = []
        const sData = [[], [], []]
        const tem = this.legendOption.options.map(item => {
          return result.filter(data => data[this.key_name] === item['label'])
        })

        xData = this.$mergexAxisData(tem, this.key_date)
        const value1 = this.$changeSeriesData(tem[0], xData, this.key_date, this.key_value, 'value1')
        const value2 = this.$changeSeriesData(tem[1], xData, this.key_date, this.key_value, 'value2')
        const value3 = this.$changeSeriesData(tem[2], xData, this.key_date, this.key_value, 'value3')
        const dataList = _.defaultsDeep(value1, value2, value3)
        dataList.forEach(item => {
          sData[0].push(item.value1)
          sData[1].push(item.value2)
          sData[2].push(item.value3)
        })
        const option = {
          tooltip: {
            formatter: params => {
              this.legendDataSource = params
            }
          },
          xAxis: [
            {
              data: xData,
              axisLabel: {
                formatter: value => this.$dateFormat(value, this.dateIndex)
              }
            }
          ],
          yAxis: [
            {
              axisLabel: {
                formatter: value => this.$intFormat(value)
              }
            },
            {
              axisLabel: {
                formatter: value => this.$perFormat(value)
              }
            }
          ],
          series: [
            { type: 'line', data: sData[0] },
            { type: 'line', data: sData[1] },
            { type: 'line', data: sData[2] }
          ]
        }
        drawScrollChart(option, this.$refs['chart-model-trend'])
        this.tableData = {
          options: option
        }
      }
    }
  }
}
</script>
<style lang="less" scoped></style>
