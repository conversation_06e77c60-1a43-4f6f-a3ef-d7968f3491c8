<!--
 * @Author: shigl
 * @Date: 2022-09-26 16:42:43
 * @LastEditTime: 2023-07-05 11:11:58
 * @Description:
-->
<template>
  <div>
    <div class="flex_between pd_lr20">
      <div class="fs28 fw700">{{ zoneData.dLevelName }}利润</div>
      <div class="fw700 grey666 fs20">单位:万元</div>
    </div>
    <NormalTable
      class="mt24"
      size="small"
      isIndicator
      :width="tableWidth"
      :dataSource="tableDataSource"
      :columns="columns"
      minHeight="2rem"
    >
    </NormalTable>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { tableColumns } from './commonMixins/config'

export default {
  mixins: [mixins],
  props: ['subName', 'subIndex'],
  data() {
    return {
      tableDataSource: []
    }
  },
  watch: {
    areaProfitMon(val) {
      this.setTable(val)
    },
    areaProfitDay(val) {
      this.setTable(val)
    }
  },

  computed: {
    columns() {
      const tmp = _.defaultsDeep([], tableColumns[this.dateIndex])
      tmp[1]['label'] = this.zoneData.dLevelName
      return tmp
    },
    tableWidth() {
      return ['160%', '100%'][this.dateIndex]
    }
  },
  methods: {
    setTable(result) {
      this.tableDataSource = []
      if (result && result.length) {
        const fliterList = JSON.parse(JSON.stringify(result))
        this.$objectSortUp(fliterList, 'rank_no')
        this.tableDataSource = fliterList
      }
    }
  },
  mounted() {}
}
</script>
<style lang="less" scoped></style>
