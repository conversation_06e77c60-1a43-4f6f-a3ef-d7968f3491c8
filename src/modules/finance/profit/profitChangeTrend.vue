<template>
  <div>
    <CardList title="利润变化趋势">
      <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
        <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
      </KydChartModel>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawScrollChart } from 'common/charts/chartOption'

export default {
  mixins: [mixins],
  data() {
    return {
      tableDataSource: [],
      tableData: {},
      legendDataSource: []
    }
  },
  watch: {
    profitChangeMon(val) {
      this.initData(val)
    },
    profitChangeDay(val) {
      this.initData(val)
    }
  },
  computed: {
    legendOption() {
      return {
        type: 'line',
        options: [
          { label: '利润额', int: [0, 10000] },
          { label: '利润率', per: [1] }
        ],
        dataSource: this.legendDataSource
      }
    }
  },

  mounted() {},
  methods: {
    initData(result) {
      if (result.length) {
        let xData = []
        const sData = [[], []]
        const tem = this.legendOption.options.map(item => {
          return result.filter(data => data[this.key_name] === item['label'])
        })
        // console.log('====================================')
        // console.log(JSON.parse(JSON.stringify(tem)))
        // console.log('====================================')
        xData = this.$mergexAxisData(tem, this.key_date)
        const value1 = this.$changeSeriesData(tem[0], xData, this.key_date, this.key_value, 'value1')
        const value2 = this.$changeSeriesData(tem[1], xData, this.key_date, this.key_value, 'value2')
        const dataList = _.defaultsDeep(value1, value2)
        dataList.forEach(item => {
          sData[0].push(item.value1)
          sData[1].push(item.value2)
        })
        const option = {
          tooltip: {
            formatter: params => {
              this.legendDataSource = params
            }
          },
          xAxis: [
            {
              data: xData,
              axisLabel: {
                formatter: value => this.$dateFormat(value, this.dateIndex)
              }
            }
          ],
          yAxis: [
            {
              axisLabel: {
                formatter: value => this.$intFormat(value, 10000)
              }
            },
            {
              axisLabel: {
                formatter: value => this.$perFormat(value)
              }
            }
          ],
          series: [
            { type: 'bar', data: sData[0] },
            { type: 'line', yAxisIndex: 1, data: sData[1] }
          ]
        }
        drawScrollChart(option, this.$refs['chart-model-trend'])
        this.tableData = {
          options: option
        }
      }
    }
  }
}
</script>
<style lang="less" scoped></style>
