<template>
  <div>
    <CardList title="利润总览">
      <div slot="nav">
        <div>货量:吨 额度:万元</div>
      </div>
      <div class="mt24 pd_lr20">
        <div class="mt40">
          <div class="fw700 fs28">货量</div>
          <div class="flex_center">
            <div style="width: 2.5rem; height: 1.4rem" ref="gaugeChartBoxCargo"></div>
            <MultiDataList class="flex1" :dataSource="dataSource0" :columns="carColumns"></MultiDataList>
          </div>
        </div>
        <div class="mt40">
          <div class="fw700 fs28">收入</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxIncome"></div>
            <MultiDataList class="flex1" :dataSource="dataSource1" :columns="columns"></MultiDataList>
          </div>
        </div>
        <div class="mt40">
          <div class="fw700 fs28">成本</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxCost"></div>
            <MultiDataList class="flex1" :dataSource="dataSource2" :columns="columns"></MultiDataList>
          </div>
        </div>
        <div class="mt40">
          <div class="fw700 fs28">费用</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxFee"></div>
            <MultiDataList class="flex1" :dataSource="dataSource3" :columns="columns"></MultiDataList>
          </div>
        </div>
        <div class="mt40">
          <div class="fw700 fs28">毛利</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxGross"></div>
            <MultiDataList class="flex1" :dataSource="dataSource4" :columns="columns"></MultiDataList>
          </div>
        </div>
        <div class="mt40">
          <div class="fw700 fs28">利润</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxProfit"></div>
            <MultiDataList class="flex1" :dataSource="dataSource5" :columns="columns"></MultiDataList>
          </div>
        </div>
      </div>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'

import { drawNewGaugeChart } from 'common/charts/chartOption'
import { overAllProfit, carProfit } from './commonMixins/config'

export default {
  mixins: [mixins],
  components: {},
  computed: {
    columns() {
      return _.defaultsDeep([], overAllProfit[this.dateIndex])
    },
    carColumns() {
      return _.defaultsDeep([], carProfit[this.dateIndex])
    }
  },
  data() {
    return {
      dataSource0: {},
      dataSource1: {},
      dataSource2: {},
      dataSource3: {},
      dataSource4: {},
      dataSource5: {},
      titleList: ['Cargo', 'Income', 'Cost', 'Fee', 'Gross', 'Profit']
    }
  },
  watch: {
    overProfitMon(val) {
      this.initData(val)
    },
    overProfitDay(val) {
      this.initData(val)
    }
  },
  mounted() {},
  methods: {
    initData(result) {
      this.dataSource0 = result.find(item => item[this.key_name] === '货量') || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxCargo'], {
        value: this.dataSource0[this.key_rate] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      this.dataSource1 = result.find(item => item[this.key_name] === '收入') || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxIncome'], {
        value: this.dataSource1[this.key_rate] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      this.dataSource2 = result.find(item => item[this.key_name] === '成本') || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxCost'], {
        value: this.dataSource2[this.key_rate] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      this.dataSource3 = result.find(item => item[this.key_name] === '费用') || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxFee'], {
        value: this.dataSource3[this.key_rate] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      this.dataSource4 = result.find(item => item[this.key_name] === '毛利') || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxGross'], {
        value: this.dataSource4[this.key_rate] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      this.dataSource5 = result.find(item => item[this.key_name] === '利润') || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxProfit'], {
        value: this.dataSource5[this.key_rate] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .multi_data_list_wrapper {
  background-color: #fff;
}
</style>
