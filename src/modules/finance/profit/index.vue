<template>
  <div class="profit_page page_bgc">
    <div class="date_container flex_between pd_lr20">
      <KyDatePicker
        style="width: 3rem"
        :type="dateType"
        :dateValue="dateValue"
        @onChange="dateChange"
        :holidayData="holidayData"
      ></KyDatePicker>
      <Tabs :options="['日预测', '月实际']" :tabIndex="dateIndex" @tabSelect="tabSelect"></Tabs>
    </div>
    <PageContent>
      <OverProfit></OverProfit>
      <GrossChangeTrend class="mt24"></GrossChangeTrend>
      <GrossPriceTrend class="mt24"></GrossPriceTrend>
      <ProfitChangeTrend class="mt24"></ProfitChangeTrend>
      <AreaProfit v-if="+zoneLevel === 30 || +zoneLevel === 32" class="mt24"></AreaProfit>
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>
<script>
import OverProfit from './overProfit.vue'
import GrossChangeTrend from './grossChangeTrend.vue'
import GrossPriceTrend from './grossPriceTrend.vue'
import ProfitChangeTrend from './profitChangeTrend.vue'
import AreaProfit from './areaProfit.vue'

import { mapMutations, mapState } from 'vuex'
import request from './commonMixins/request'
export default {
  mixins: [request],
  components: {
    OverProfit,
    GrossChangeTrend,
    GrossPriceTrend,
    ProfitChangeTrend,
    AreaProfit
  },
  data() {
    return {}
  },
  computed: {
    dateType() {
      return ['day', 'month'][this.dateIndex]
    },
    ...mapState({
      holidayData: 'holidayData'
    })
  },
  watch: {
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    ...mapMutations('finance', ['setDate', 'setDateIndex', 'setPageData']),
    tabSelect(index) {
      this.setDateIndex({
        type: 'all',
        index: index
      })
      this.initOptions()
    },
    dateChange(date) {
      this.setDate({
        type: 'all',
        key: ['dateDay', 'dateMon'][this.dateIndex],
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
      this.initOptions()
    },
    // 利润总览
    async getOverProfitDay() {
      const { obj } = await this._getOverProfitDay()
      this.setPageData({
        type: 'profit',
        dataType: 'overProfitDay',
        data: obj
      })
    },
    async getOverProfitMon() {
      const { obj } = await this._getOverProfitMon()
      this.setPageData({
        type: 'profit',
        dataType: 'overProfitMon',
        data: obj
      })
    },
    // 毛利变化趋势
    async getGrossChangetMon() {
      const { obj } = await this._getGrossChangetMon()
      this.setPageData({
        type: 'profit',
        dataType: 'grossChangeMon',
        data: obj
      })
    },
    async getGrossChangeDay() {
      const { obj } = await this._getGrossChangeDay()
      this.setPageData({
        type: 'profit',
        dataType: 'grossChangeDay',
        data: obj
      })
    },
    // 毛利单价趋势
    async getGrossPriceMon() {
      const { obj } = await this._getGrossPriceMon()
      this.setPageData({
        type: 'profit',
        dataType: 'grossPriceMon',
        data: obj
      })
    },
    async getGrossPriceDay() {
      const { obj } = await this._getGrossPriceDay()
      this.setPageData({
        type: 'profit',
        dataType: 'grossPriceDay',
        data: obj
      })
    },
    // 利润变化趋势
    async getprofitChangeMon() {
      const { obj } = await this._getprofitChangeMon()
      this.setPageData({
        type: 'profit',
        dataType: 'profitChangeMon',
        data: obj
      })
    },
    async getprofitChangeDay() {
      const { obj } = await this._getprofitChangeDay()
      this.setPageData({
        type: 'profit',
        dataType: 'profitChangeDay',
        data: obj
      })
    },
    // 省区排名
    async getAreaProfiteMon() {
      const { obj } = await this._getAreaProfiteMon()
      const noAuth = [30, 32].some(item => item === +this.zoneLevel)
      this.setPageData({
        type: 'profit',
        dataType: 'areaProfitMon',
        data: noAuth ? obj : []
      })
    },
    async getAreaProfitDay() {
      const { obj } = await this._getAreaProfitDay()
      const noAuth = [30, 32].some(item => item === +this.zoneLevel)
      this.setPageData({
        type: 'profit',
        dataType: 'areaProfitDay',
        data: noAuth ? obj : []
      })
    },
    initOptions() {
      if (!this.dateIndex) {
        this.getOverProfitDay()
        this.getGrossChangeDay()
        this.getGrossPriceDay()
        this.getprofitChangeDay()
        this.getAreaProfitDay()
      } else {
        this.getOverProfitMon()
        this.getGrossChangetMon()
        this.getGrossPriceMon()
        this.getprofitChangeMon()
        this.getAreaProfiteMon()
      }
    }
  },
  activated() {},
  mounted() {
    // 占时只有月,每次回来将dateIndex=1
    this.setDateIndex({
      type: 'all',
      index: 0
    })
    this.initOptions()
  }
}
</script>
<style lang="less" scoped>
.profit_page {
  color: #333;
}
.date_container {
  width: 100vw;
  height: 0.8rem;
  color: #999;
}
</style>
