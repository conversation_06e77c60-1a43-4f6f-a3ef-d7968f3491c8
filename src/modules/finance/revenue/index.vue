<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-06-25 13:53:55
 * @LastEditTime: 2023-12-12 10:01:04
 * @Description:
-->
<template>
  <div class="profit_page page_bgc">
    <div class="date_container">
      <KyDatePicker
        style="width: 3rem"
        :type="dateType"
        :dateValue="dateValue"
        @onChange="dateChange"
        :holidayData="holidayData"
      ></KyDatePicker>
      <Tabs :options="['日预测', '月实际']" :tabIndex="dateIndex" @tabSelect="tabSelect"></Tabs>
    </div>
    <PageContent>
      <CargoOverview></CargoOverview>
      <TrendChange class="mt24"></TrendChange>
      <IncomeTrendChange class="mt24"></IncomeTrendChange>
      <PriceTrendChange class="mt24"></PriceTrendChange>
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>
<script>
import flowLayout from 'common/mixins/flowLayout.js'
import CargoOverview from './cargoOverview.vue'
import TrendChange from './trendChange'
import IncomeTrendChange from './incomeTrendChange.vue'
import PriceTrendChange from './priceTrendChange.vue'

import { mapMutations, mapState } from 'vuex'
import request from './commonMixins/request'
export default {
  mixins: [flowLayout, request],
  components: {
    CargoOverview,
    TrendChange,
    IncomeTrendChange,
    PriceTrendChange
    // GrossProfitTrend,
    // ProfitChangeTrend,
    // AreaProfit
  },
  data() {
    return {}
  },
  computed: {
    dateType() {
      return ['day', 'month'][this.dateIndex]
    },
    ...mapState({
      holidayData: 'holidayData'
    })
  },
  watch: {
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    ...mapMutations('finance', ['setDate', 'setDateIndex', 'setPageData']),
    tabSelect(index) {
      this.setDateIndex({
        type: 'all',
        index: index
      })
      this.initOptions()
    },
    dateChange(date) {
      this.setDate({
        type: 'all',
        key: ['dateDay', 'dateMon'][this.dateIndex],
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
      this.initOptions()
    },
    //  【收入】【货量总览】
    async getCargoOverviewDay() {
      const { obj } = await this._getCargoOverviewDay()
      this.setPageData({
        type: 'revenue',
        dataType: 'cargoOverviewDay',
        data: obj
      })
    },
    async getCargoOverviewMon() {
      const { obj } = await this._getCargoOverviewMon()
      this.setPageData({
        type: 'revenue',
        dataType: 'cargoOverviewMon',
        data: obj
      })
    },
    //  【收入】【票件重趋势变化】
    async getTrendChangeDay() {
      const { obj } = await this._getTrendChangeDay()
      this.setPageData({
        type: 'revenue',
        dataType: 'trendChangeDay',
        data: obj
      })
    },
    async getTrendChangeMon() {
      const { obj } = await this._getTrendChangeMon()
      this.setPageData({
        type: 'revenue',
        dataType: 'trendChangeMon',
        data: obj
      })
    },
    //  【收入】【收入&&单价趋势变化】
    async getTrendChangeComDay() {
      const { obj } = await this._getTrendChangeComDay()
      this.setPageData({
        type: 'revenue',
        dataType: 'trendChangeComDay',
        data: obj
      })
    },
    async getTrendChangeComMon() {
      const { obj } = await this._getTrendChangeComMon()
      this.setPageData({
        type: 'revenue',
        dataType: 'trendChangeComMon',
        data: obj
      })
    },
    initOptions() {
      if (!this.dateIndex) {
        this.getCargoOverviewDay()
        this.getTrendChangeDay()
        this.getTrendChangeComDay()
      } else {
        this.getCargoOverviewMon()
        this.getTrendChangeMon()
        this.getTrendChangeComMon()
      }
    }
  },
  async mounted() {
    // 占时只有月,每次回来将dateIndex=1
    this.setDateIndex({
      type: 'all',
      index: 0
    })
    this.initOptions()
  }
}
</script>
<style lang="less" scoped>
.profit_page {
  color: #333;
}
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
