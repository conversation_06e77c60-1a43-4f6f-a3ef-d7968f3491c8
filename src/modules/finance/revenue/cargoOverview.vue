<template>
  <div>
    <CardList title="货量总览">
      <div slot="nav">
        <div>货量:吨 收入:万元 单价:元/kg</div>
      </div>
      <div class="mt24 pd_lr20">
        <div class="mt40">
          <div class="fw700 fs28">开单货量</div>
          <div class="flex_center">
            <div style="width: 2.5rem; height: 1.4rem" ref="gaugeChartBoxKd"></div>
            <MultiDataList class="flex1" :dataSource="dataSource" :columns="columnsKd"></MultiDataList>
          </div>
        </div>
        <div class="mt40">
          <div class="fw700 fs28">签收货量</div>
          <div class="flex_center">
            <div style="width: 2.5rem; height: 1.4rem" ref="gaugeChartBoxQs"></div>
            <MultiDataList class="flex1" :dataSource="dataSource" :columns="columnsQs"></MultiDataList>
          </div>
        </div>
      </div>
      <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
        <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
      </KydChartModel>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawNewGaugeChart, drawScrollChart } from 'common/charts/chartOption'
import { overAllProfitKd, overAllProfitQs } from './commonMixins/config'

export default {
  mixins: [mixins],
  components: {},
  data() {
    return {
      titleList: ['开单货量', '签收货量'],
      dataSource: {},
      tableData: {},
      legendDataSource: []
    }
  },
  computed: {
    columnsKd() {
      return _.defaultsDeep([], overAllProfitKd[this.dateIndex])
    },
    columnsQs() {
      return _.defaultsDeep([], overAllProfitQs[this.dateIndex])
    },
    legendOption() {
      return {
        type: 'column',
        colNum: 3,
        isGrid: true,
        options: [
          { label: '开单货量', int: [0, 1000] },
          { label: '签收货量', int: [0, 1000] },
          {
            label: '加盟货量',
            color: '#ffffff',
            seriesIndex: 0,
            dataIndex: `od_jm_weight_${this.key_unit}`,
            int: [0, 1000]
          },
          {
            label: '融通货量',
            color: '#ffffff',
            seriesIndex: 0,
            dataIndex: `od_rt_weight_${this.key_unit}`,
            int: [0, 1000]
          },
          {
            label: '直营大客户货量',
            color: '#ffffff',
            seriesIndex: 0,
            dataIndex: `od_zy_weight_${this.key_unit}`,
            int: [0, 1000]
          }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    cargoOverviewMon(val) {
      this.initData(val)
    },
    cargoOverviewDay(val) {
      this.initData(val)
    }
  },
  mounted() {},
  methods: {
    initData(result) {
      this.dataSource = result.find(item => item[this.key_dateunit] === this.dateValue) || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxKd'], {
        value: this.dataSource[this.key_rateCar] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })

      drawNewGaugeChart({}, this.$refs['gaugeChartBoxQs'], {
        value: this.dataSource[this.key_rateOd] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      this.initModelChart(result)
    },
    initModelChart(result) {
      const fliterList = JSON.parse(JSON.stringify(result))
      console.log(JSON.parse(JSON.stringify(fliterList)), 'fliterList-----')
      this.$objectSortUp(fliterList, this.key_dateunit)
      const xData = []
      const sData = [[], []]
      if (fliterList && fliterList.length > 0) {
        fliterList.forEach((item, index) => {
          xData.push(item[this.key_dateunit])
          sData[0].push({
            value: item[`kd_weight_${this.key_unit}`],
            ...item
          })
          sData[1].push(item[`od_weight_${this.key_unit}`])
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 1000)
            }
          }
        ],
        series: [
          {
            type: 'line',
            data: sData[0]
          },
          {
            type: 'line',
            data: sData[1]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.multi_data_list_wrapper {
  background-color: #fff !important;
}
.gauge-chart-box {
  height: 1.76rem;
  width: 96%;
}
.data_list_custom {
  background-color: #fff !important;
}
.text_conent {
  height: 0.81rem;
  width: 3.34rem;
  background: #f8f9fc;
  border-radius: 8px;
}
</style>
