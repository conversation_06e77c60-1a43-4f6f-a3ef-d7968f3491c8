<template>
  <div>
    <CardList title="单价趋势变化">
      <div slot="nav">
        <div>金额:万元</div>
      </div>
      <div class="pd_lr20">
        <KydChartModel
          isScroll
          position="right"
          class="mt32"
          :legendOption="legendOption"
          :tableOption="tableData"
          tableLength="5"
        >
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
      <NormalTable
        class="mt24"
        size="small"
        minHeight="2rem"
        :width="moreTableWidth"
        :dataSource="warnDataSource"
        :columns="priceTableColumns"
        :onSelect="onSelect"
        :childColumns="childColumns"
      >
      </NormalTable>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawLineChart } from 'common/charts/chartOption'

export default {
  mixins: [mixins],
  data() {
    return {
      dataSource: [],
      warnDataSource: [],
      tableData: {},
      legendDataSource: [],
      priceTableColumns: [],
      childColumns: []
    }
  },
  watch: {
    trendChangeComMon(val) {
      this.initData(val)
    },
    trendChangeComDay(val) {
      this.initData(val)
    }
  },
  computed: {
    legendOption() {
      const optionList = {
        33: [
          { label: '总收入单价', int: [3, 1, 3] },
          { label: 'OD收入单价', int: [3, 1, 3] },
          { label: '内部结算收入单价', int: [3, 1, 3] },
          { label: '其他收入单价', int: [3, 1, 3] }
        ],
        32: [
          { label: '总收入单价', int: [3, 1, 3] },
          { label: 'OD收入单价', int: [3, 1, 3] },
          { label: '内部结算收入单价', int: [3, 1, 3] },
          { label: '其他收入单价', int: [3, 1, 3] }
        ],
        30: [
          { label: '总收入单价', int: [3, 1, 3] },
          { label: 'OD收入单价', int: [3, 1, 3] },
          { label: '其他收入单价', int: [3, 1, 3] }
        ]
      }[+this.zoneLevel]
      return {
        type: 'column',
        colNum: 3,
        isGrid: true,
        options: optionList,
        dataSource: this.legendDataSource
      }
    },
    moreTableWidth() {
      return ['140%', '100%'][this.dateIndex]
    }
  },
  mounted() {},
  methods: {
    initData(result) {
      let xData = []
      if (result && result.length > 0) {
        const priceList = result.filter(item => item.index_type === '单价趋势')
        const tem = this.legendOption.options.map(item => {
          return priceList.filter(data => data.index_name === item['label'])
        })
        // console.log('====================================')
        // console.log(JSON.parse(JSON.stringify(tem)), 'tem')
        // console.log('====================================')
        xData = this.$mergexAxisData(tem, this.key_dateunit)
        const value1 = this.$changeSeriesData(tem[0], xData, this.key_dateunit, this.key_value, 'value1')
        const value2 = this.$changeSeriesData(tem[1], xData, this.key_dateunit, this.key_value, 'value2')
        const value3 = this.$changeSeriesData(tem[2], xData, this.key_dateunit, this.key_value, 'value3')
        let dataList = []
        if (+this.zoneLevel === 32 || +this.zoneLevel === 33) {
          const value4 = this.$changeSeriesData(tem[3], xData, this.key_dateunit, this.key_value, 'value4')
          dataList = _.defaultsDeep(value1, value2, value3, value3, value4)
          const sData = [[], [], [], []]
          dataList.forEach(item => {
            sData[0].push({
              value: item.value1
            })
            sData[1].push({
              value: item.value2
            })
            sData[2].push({
              value: item.value3
            })
            sData[3].push({
              value: item.value4
            })
          })
          this.legendDataSource = []
          const option = {
            tooltip: {
              formatter: params => {
                this.legendDataSource = params
              }
            },
            xAxis: [
              {
                data: xData,
                axisLabel: {
                  formatter: value => this.$dateFormat(value, this.dateIndex)
                }
              }
            ],
            yAxis: [
              {
                axisLabel: {
                  formatter: value => this.$intFormat(value, 1)
                }
              }
            ],
            series: [
              {
                type: 'line',
                data: sData[0]
              },
              {
                type: 'line',
                data: sData[1]
              },
              {
                type: 'line',
                data: sData[2]
              },
              {
                type: 'line',
                data: sData[3]
              }
            ]
          }
          drawLineChart(option, this.$refs['chart-model-trend'])
          this.tableData = {
            options: option
          }
        } else {
          dataList = _.defaultsDeep(value1, value2, value3)
          const sData = [[], [], []]
          dataList.forEach(item => {
            sData[0].push({
              value: item.value1
            })
            sData[1].push({
              value: item.value2
            })
            sData[2].push({
              value: item.value3
            })
          })
          this.legendDataSource = []
          const option = {
            tooltip: {
              formatter: params => {
                this.legendDataSource = params
              }
            },
            xAxis: [
              {
                data: xData,
                axisLabel: {
                  formatter: value => this.$dateFormat(value, this.dateIndex)
                }
              }
            ],
            yAxis: [
              {
                axisLabel: {
                  formatter: value => this.$intFormat(value, 1)
                }
              }
            ],
            series: [
              {
                type: 'line',
                data: sData[0]
              },
              {
                type: 'line',
                data: sData[1]
              },
              {
                type: 'line',
                data: sData[2]
              }
            ]
          }
          drawLineChart(option, this.$refs['chart-model-trend'])
          this.tableData = {
            options: option
          }
        }
        this.setTable(priceList)
      } else {
        this.setTable([])
        this.tableData = {}
      }
    },
    onSelect(tr, index) {
      tr.expand = !tr.expand
    },
    setTable(incomeList) {
      let dataList = []
      if (incomeList.length > 0) {
        const nameListOther = ['外网收入单价', '加盟费类收入单价', '网络罚款收入单价', '仲裁补偿收入单价']
        const nameListOd = [
          '加盟运输收入单价',
          '加盟平台收入单价',
          '末端派送收入单价',
          '直营大客收入单价',
          '融通业务收入单价'
        ]
        const incomOD = nameListOd.map(item => {
          const obj =
            incomeList.find(data => data.index_name === item && data[this.key_dateunit] === this.dateValue) || {}
          const objDefeat = JSON.parse(JSON.stringify(obj))
          if (objDefeat.index_name === '直营大客收入单价') {
            objDefeat.index_name = '直营大客户收入单价'
          }
          return objDefeat
        })
        const incomOther = nameListOther.map(item => {
          return incomeList.find(data => data.index_name === item && data[this.key_dateunit] === this.dateValue) || {}
        })
        dataList = this.legendOption.options.map(item => {
          const obj =
            incomeList.find(data => data.index_name === item['label'] && data[this.key_dateunit] === this.dateValue) ||
            {}
          const objDefeat = JSON.parse(JSON.stringify(obj))
          if (objDefeat.index_name === 'OD收入单价') {
            objDefeat['expand'] = false
            objDefeat['child'] = incomOD
          }
          if (objDefeat.index_name === '其他收入单价') {
            objDefeat['expand'] = false
            objDefeat['child'] = incomOther
          }
          return objDefeat
        })
      }
      const commontColumns = [
        [
          { label: '当日值', dataIndex: 'this_day_value', render: (h, val) => this.$numToInteger(val, 3, 1, 3) },
          {
            label: '日环比',
            dataIndex: 'this_day_value_hb',
            render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToInteger(val, 3, 1, 3)}</div>
          },
          {
            label: '同比上周',
            dataIndex: 'last_week_value_hb',
            render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToInteger(val, 0, 1)}</div>
          },
          { label: '月累计', dataIndex: 'this_sum_month_value', render: (h, val) => this.$numToInteger(val, 3, 1, 3) },
          {
            label: '预算值',
            dataIndex: 'this_month_target_value',
            render: (h, val) => this.$numToInteger(val, 3, 1, 3)
          },
          { label: '完成比', dataIndex: 'month_target_rate', render: (h, val) => this.$numToPercent(val) },
          { label: '差额', dataIndex: 'month_target_diff', render: (h, val) => this.$numToInteger(val, 3, 1, 3) }
        ],
        [
          { label: '当月值', dataIndex: 'this_month_value', render: (h, val) => this.$numToInteger(val, 3, 1, 3) },
          {
            label: '月环比',
            dataIndex: 'this_month_value_hb',
            render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToInteger(val, 3, 1, 3)}</div>
          },
          { label: '月完成比', dataIndex: 'this_month_value_rate', render: (h, val) => this.$numToPercent(val) }
          // { label: '年累计', dataIndex: 'this_sum_yaer_value', render: (h, val) => this.$numToInteger(val, 0, 1) },
          // { label: '年完成比', dataIndex: 'this_sum_yaer_value_rate', render: (h, val) => this.$numToPercent(val) }
        ]
      ]
      this.priceTableColumns = [
        { label: '项目', width: '2.2rem', dataIndex: 'index_name', fixed: 'left', isExpandIcon: true },
        ...commontColumns[this.dateIndex]
      ]
      this.childColumns = [
        {
          label: '项目',
          width: '2.2rem',
          dataIndex: 'index_name',
          fixed: 'left',
          render: (h, value) => {
            return <div class='ml32'>{value}</div>
          }
        },
        ...commontColumns[this.dateIndex]
      ]
      this.warnDataSource = dataList
    }
  }
}
</script>
<style lang="less" scoped></style>
