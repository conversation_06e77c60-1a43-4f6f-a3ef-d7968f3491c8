<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2023-03-20 15:51:21
 * @Description:OD环节毛利
-->
<template>
  <div>
    <CardList title="毛利单价趋势">
      <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
        <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
      </KydChartModel>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawMoreBarChart } from 'common/charts/chartOption'

export default {
  mixins: [mixins],
  data() {
    return {
      tableWidth: '100%',
      tableDataSource: [],
      tableData: {},
      tableColumns: [],
      childColumns: []

    }
  },
  watch: {
    odProfitMon(val) {
      this.setTable(val)
    }
  },
  computed: {
    legendOption() {
      return {
        type: 'column',
        options: [
          { label: '毛利单价', color: '#4C83F9', seriesIndex: 0, dataIndex: 'value', int: [0] },
          { label: 'OD毛利单价', color: '#4C83F9', seriesIndex: 0, dataIndex: 'value', int: [0] },
          // { label: '毛利率', color: '#22F0AF', seriesIndex: 2, dataIndex: 'value', per: [1] },
          { label: '其他毛利单价', color: '#DC1E32', dataIndex: 'value', seriesIndex: 1, int: [0] }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  mounted() {
    this.initModelChart()
  },
  methods: {
    onSelect(res) {
      res.expand = !res.expand
    },
    setTable(result) {
      this.tableDataSource = []
      if (result.length) {
        // 避免改变原数据结构
        const obj = JSON.parse(JSON.stringify(result))
        // 数据处理
        // 加粗的数据
        const weightCodeList = [
          '2_01',
          '2_04',
          '2_05',
          '2_06',
          '2_08',
          '2_12',
          '2_15',
          '2_16',
          '2_17'
        ]
        // 默认隐藏的数据
        const deleteCodeList = ['2_07', '2_09', '2_10', '2_11', '2_13', '2_14']
        const parentCodeList = ['2_06', '2_08', '2_12']
        const childCodeList = [['2_07'], ['2_09', '2_10', '2_11'], ['2_13', '2_14']]
        // 可拓展数据
        const childDataList = childCodeList.map((item, index) => {
          return item.map((data, index2) => {
            return obj.find(value => value['iloc'] === data)
          })
        })
        // 删除原数据
        deleteCodeList.forEach((item, index) => {
          const tmpIndex = obj.findIndex(data => data['iloc'] === item)
          obj.splice(tmpIndex, 1)
        })
        // 处理数据结构
        obj.forEach((item, index) => {
          if (weightCodeList.indexOf(item.iloc) !== -1) {
            item.row_weight = true
          } else {
            item.row_weight = false
          }
          parentCodeList.forEach((data, index2) => {
            if (data === item['iloc']) {
              item.child = childDataList[index2]
              // 可展开条件,expand=true
              item.expand = false
            }
          })
        })
        this.tableDataSource = []
      }
      this.tableColumns = [
        {
          label: '项目',
          dataIndex: 'od_item',
          width: '2.2rem',
          fixed: 'left',
          isExpandIcon: true,
          render: (h, value, res, index) => {
            if (index >= 4 && index <= 9) {
              if (index >= 5 && index <= 8) {
                return <div class={res.row_weight ? 'fw700 grey333 ml20' : 'ml20'}>{value}</div>
              }
              return <div class={res.row_weight ? 'fw700 grey333 ml12' : 'ml12'}>{value}</div>
            }
            return <div class={res.row_weight ? 'fw700 grey333' : ''}>{value}</div>
          }
        },
        {
          label: '单公斤',
          dataIndex: 'value_unit',
          render: (h, value, res) => {
            return (
              <div class={res.row_weight ? 'fw700 grey333' : ''}>
                {this.$numToInteger(value, 3, 1, 3)}
              </div>
            )
          }
        },
        {
          label: '单公斤环比',
          dataIndex: 'value_unit_hb',
          render: (h, value, res) => {
            if (res.row_weight) {
              return (
                <div class={value <= 0 ? 'orange fw700' : 'green fw700'}>
                  {this.$numToInteger(value, 3, 1, 3)}
                </div>
              )
            }
            return (
              <div class={value <= 0 ? 'orange' : 'green'}>
                {this.$numToInteger(value, 3, 1, 3)}
              </div>
            )
          }
        },
        {
          label: '预算达成',
          dataIndex: 'value_comp_rate',
          render: (h, value, res) => {
            if (res.row_weight) {
              return (
                <div class={value < 1 ? 'orange fw700' : 'green fw700'}>
                  {this.$numToPercent(value)}
                </div>
              )
            }
            return <div class={value < 1 ? 'orange' : 'green'}>{this.$numToPercent(value)}</div>
          }
        },
        {
          label: '成本占收入比',
          dataIndex: 'value_ratio',
          render: (h, value, res) => {
            return (
              <div class={res.row_weight ? 'fw700 grey333' : ''}>{this.$numToPercent(value)}</div>
            )
          }
        }
      ]
      this.childColumns = _.defaultsDeep([], this.tableColumns)
      this.childColumns.splice(0, 1, {
        render: (h, value, res, index) => {
          return ''
        }
      })
      this.childColumns.splice(0, 1, {
        dataIndex: 'od_item',
        width: '2.2rem',
        fixed: 'left',
        render: (h, value, res, index) => {
          return <div class={'ml20'}>{value}</div>
        }
      })
    },
    initModelChart(mainInfo) {
      const { trend } = mainInfo || {}
      const xData = []
      const sData = [[], [], []]
      if (trend && trend.length > 0) {
        trend.forEach(item => {
          xData.push(item.signTimeDate)
          sData[0].push({
            value: item.totalSign,
            index_value: ''
          })
          sData[1].push(item.delaySign)
          sData[2].push(item.realTimeSignRate / 100)
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [

          {
            data: xData,
            axisLabel: {
              formatter: value => this.$moment().format('M.DD')
            },
            axisLine: {
              show: false
            }
          },
          {
            axisLine: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: value => this.$intFormat(value)
            }
          },
          {
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: value => this.$perFormat(value)
            }
          }
        ],
        series: [
          // {
          //   stack: 'Total',
          //   itemStyle: {
          //     borderColor: 'transparent',
          //     color: 'transparent'
          //   },
          //   emphasis: {
          //     itemStyle: {
          //       borderColor: 'transparent',
          //       color: 'transparent'
          //     }
          //   },
          //   data: [0, 900, 1245, 1530, 1376, 1376, 1511, 1689, 1856, 1495, 1292]
          // },
          {
            type: 'line',
            stack: 'Total',
            lineStyle: {
              color: '#4C83F9'
            },
            smooth: true,
            label: {
              show: true,
              position: 'top'
            },
            data: [900, 345, 393, 344, 666, 135, 178, 286]
          },
          {
            type: 'line',
            stack: 'Total',
            lineStyle: {
              color: '#FF8066'
            },
            smooth: true,
            label: {
              show: true,
              position: 'top'
            },
            data: [900, 345, 393, 344, 666, 135, 178, 286]
          },
          {
            type: 'line',
            lineStyle: {
              color: '#52CCA3'
            },
            smooth: true,
            yAxisIndex: 1,
            data: [123, 335, 456, 556, 334, 556]
          }
        ]
      }
      drawMoreBarChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }

}
</script>
<style lang="less" scoped></style>
