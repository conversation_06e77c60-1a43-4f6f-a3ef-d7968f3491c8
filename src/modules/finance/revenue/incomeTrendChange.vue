<template>
  <div>
    <CardList title="收入趋势变化">
      <div slot="nav">
        <div>金额:万元</div>
      </div>
      <div class="pd_lr20">
        <KydChartModel
          isScroll
          position="right"
          class="mt32"
          :legendOption="legendOption"
          :tableOption="tableData"
          tableLength="5"
        >
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
      <NormalTable
        class="mt24"
        size="small"
        minHeight="2rem"
        :width="moreTableWidth"
        :dataSource="warnDataSource"
        :columns="incomeTableColumns"
        :onSelect="onSelect"
        :childColumns="childColumns"
      >
      </NormalTable>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawOneLineChart } from 'common/charts/chartOption'

export default {
  mixins: [mixins],
  data() {
    return {
      // moreTableWidth: ['140%', '100%'][this.dateIndex],
      dataSource: [],
      warnDataSource: [],
      tableData: {},
      legendDataSource: [],
      incomeTableColumns: [],
      childColumns: []
    }
  },
  watch: {
    trendChangeComMon(val) {
      this.initData(val)
    },
    trendChangeComDay(val) {
      this.initData(val)
    }
  },
  computed: {
    legendOption() {
      const optionList = {
        33: [
          { label: '总收入', int: [0, 10000] },
          { label: '总收入(不含内部)', seriesIndex: 0, dataIndex: 'value2', color: '#ffffff', int: [0, 10000] },
          { label: 'OD收入', color: '#ffffff', seriesIndex: 0, dataIndex: 'value3', int: [0, 10000] },
          { label: '内部结算收入', color: '#ffffff', seriesIndex: 0, dataIndex: 'value4', int: [0, 10000] },
          { label: '其他收入', color: '#ffffff', seriesIndex: 0, dataIndex: 'value5', int: [0, 10000] }
        ],
        32: [
          { label: '总收入', int: [0, 10000] },
          { label: '总收入(不含内部)', seriesIndex: 0, dataIndex: 'value2', color: '#ffffff', int: [0, 10000] },
          { label: 'OD收入', color: '#ffffff', seriesIndex: 0, dataIndex: 'value3', int: [0, 10000] },
          { label: '内部结算收入', color: '#ffffff', seriesIndex: 0, dataIndex: 'value4', int: [0, 10000] },
          { label: '其他收入', color: '#ffffff', seriesIndex: 0, dataIndex: 'value5', int: [0, 10000] }
        ],
        30: [
          { label: '总收入', int: [0, 10000] },
          { label: '总收入(不含内部)', seriesIndex: 0, dataIndex: 'value2', color: '#ffffff', int: [0, 10000] },
          { label: 'OD收入', color: '#ffffff', seriesIndex: 0, dataIndex: 'value3', int: [0, 10000] },
          { label: '其他收入', color: '#ffffff', seriesIndex: 0, dataIndex: 'value4', int: [0, 10000] }
        ]
      }[+this.zoneLevel]
      return {
        type: 'column',
        // colNum: +this.zoneLevel === 30 ? 4 : 3,
        // isGrid: true,
        options: optionList,
        dataSource: this.legendDataSource
      }
    },
    moreTableWidth() {
      return ['140%', '100%'][this.dateIndex]
    }
  },
  mounted() {},
  methods: {
    initData(result) {
      let xData = []
      const sData = [[], [], []]
      if (result && result.length > 0) {
        const incomeList = result.filter(item => item.index_type === '收入趋势')

        const legendOptionList = {
          33: ['总收入', '总收入（不含内部）', 'OD收入', '内部结算收入', '其他收入'],
          32: ['总收入', '总收入（不含内部）', 'OD收入', '内部结算收入', '其他收入'],
          30: ['总收入', '总收入（不含内部）', 'OD收入', '其他收入']
        }[+this.zoneLevel]
        const tem = legendOptionList.map(item => {
          return incomeList.filter(data => data.index_name === item)
        })
        xData = this.$mergexAxisData(tem, this.key_dateunit)
        const value1 = this.$changeSeriesData(tem[0], xData, this.key_dateunit, this.key_value, 'value1')
        const value2 = this.$changeSeriesData(tem[1], xData, this.key_dateunit, this.key_value, 'value2')
        const value3 = this.$changeSeriesData(tem[2], xData, this.key_dateunit, this.key_value, 'value3')
        const value4 = this.$changeSeriesData(tem[3], xData, this.key_dateunit, this.key_value, 'value4')
        let dataList = []
        if (+this.zoneLevel === 32 || +this.zoneLevel === 33) {
          const value5 = this.$changeSeriesData(tem[4], xData, this.key_dateunit, this.key_value, 'value5')
          dataList = _.defaultsDeep(value1, value2, value3, value4, value5)
        } else {
          dataList = _.defaultsDeep(value1, value2, value3, value4)
        }

        dataList.forEach(item => {
          sData[0].push({
            value: item.value1,
            value2: item.value2,
            value3: item.value3,
            value4: item.value4,
            value5: item.value5
          })
        })
        this.setTable(incomeList)
      } else {
        this.setTable([])
      }

      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 10000)
            }
          }
        ],
        series: [
          {
            type: 'line',
            data: sData[0]
          }
        ]
      }
      drawOneLineChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    },
    onSelect(tr, index) {
      tr.expand = !tr.expand
    },
    setTable(incomeList) {
      let dataList = []
      if (incomeList.length > 0) {
        const nameListOther = ['外网收入', '网络建设费', '加盟管理收入', '加盟物料收入', '网络罚款收入', '仲裁罚款收入']
        const nameListOd = [
          '中转费收入',
          '派送费收入',
          '操作费收入',
          '增值收入',
          '五包收入',
          '其他加盟运输收入',
          '直营大客户收入',
          '融通业务收入'
        ]
        const incomOD = nameListOd.map(item => {
          return incomeList.find(data => data.index_name === item && data[this.key_dateunit] === this.dateValue) || {}
        })
        const incomOther = nameListOther.map(item => {
          return incomeList.find(data => data.index_name === item && data[this.key_dateunit] === this.dateValue) || {}
        })
        const legendOptionList = {
          33: ['总收入', '总收入（不含内部）', 'OD收入', '内部结算收入', '其他收入'],
          32: ['总收入', '总收入（不含内部）', 'OD收入', '内部结算收入', '其他收入'],
          30: ['总收入', '总收入（不含内部）', 'OD收入', '其他收入']
        }[+this.zoneLevel]
        dataList = legendOptionList.map(item => {
          const obj =
            incomeList.find(data => data.index_name === item && data[this.key_dateunit] === this.dateValue) || {}
          const objDefeat = JSON.parse(JSON.stringify(obj))
          if (objDefeat.index_name === '总收入（不含内部）') {
            objDefeat.index_name = '总收入(不含内部)'
          }
          if (objDefeat.index_name === 'OD收入') {
            objDefeat['expand'] = false
            objDefeat['child'] = incomOD
          }
          if (objDefeat.index_name === '其他收入') {
            objDefeat['expand'] = false
            objDefeat['child'] = incomOther
          }
          return objDefeat
        })
      }
      const commontColumns = [
        [
          { label: '当日值', dataIndex: 'this_day_value', render: (h, val) => this.$numToInteger(val, 0, 10000) },
          {
            label: '日环比',
            dataIndex: 'this_day_value_hb',
            render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val)}</div>
          },
          {
            label: '同比上周',
            dataIndex: 'last_week_value_hb',
            render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToInteger(val, 0, 1)}</div>
          },
          { label: '月累计', dataIndex: 'this_sum_month_value', render: (h, val) => this.$numToInteger(val, 0, 10000) },
          {
            label: '预算值',
            dataIndex: 'this_month_target_value',
            render: (h, val) => this.$numToInteger(val, 0, 1)
          },
          { label: '完成比', dataIndex: 'month_target_rate', render: (h, val) => this.$numToPercent(val) },
          { label: '差额', dataIndex: 'month_target_diff', render: (h, val) => this.$numToInteger(val, 0, 1, 0) }
        ],
        [
          { label: '当月值', dataIndex: 'this_month_value', render: (h, val) => this.$numToInteger(val, 0, 10000) },
          {
            label: '月环比',
            dataIndex: 'this_month_value_hb',
            render: (h, val) => <div class={val > 0 ? 'green' : 'orange'}>{this.$numToPercent(val)}</div>
          },
          { label: '月完成比', dataIndex: 'this_month_value_rate', render: (h, val) => this.$numToPercent(val) }
          // { label: '年累计', dataIndex: 'this_sum_yaer_value', render: (h, val) => this.$numToInteger(val, 0, 10000) },
          // { label: '年完成比', dataIndex: 'this_sum_yaer_value_rate', render: (h, val) => this.$numToPercent(val) }
        ]
      ]
      this.incomeTableColumns = [
        { label: '项目', width: '2.2rem', dataIndex: 'index_name', fixed: 'left', isExpandIcon: true },
        ...commontColumns[this.dateIndex]
      ]
      this.childColumns = [
        {
          label: '项目',
          width: '2.2rem',
          dataIndex: 'index_name',
          fixed: 'left',
          render: (h, value) => {
            return <div style={{ marginLeft: '0.34rem' }}>{value}</div>
          }
        },
        ...commontColumns[this.dateIndex]
      ]
      this.warnDataSource = dataList
    }
  }
}
</script>
<style lang="less" scoped></style>
