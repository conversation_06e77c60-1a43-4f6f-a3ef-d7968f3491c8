<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description:OD环节毛利
-->
<template>
  <div>
    <CardList title="票件重趋势变化">
      <div class="pd_lr20">
        <BtnTabs class="mt32" :options="btnData" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3"></BtnTabs>
        <MultiDataList class="mt32" :dataSource="dataSource" :columns="columns"></MultiDataList>
        <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawScrollChart } from 'common/charts/chartOption'
import { overAllProfit, optionList } from './commonMixins/config'

export default {
  mixins: [mixins],
  data() {
    return {
      btnData: ['件均重量', '票均重量', '单票件数'],
      tableWidth: '100%',
      dataSource: {},
      tableData: {},
      btnIndex: 0,
      legendDataSource: [],
      tabName: ['weight', 'ticket', 'quantity']
    }
  },
  watch: {
    trendChangeMon(val) {
      this.initData(val)
    },
    trendChangeDay(val) {
      this.initData(val)
    },
    btnIndex() {
      this.initData([this.trendChangeDay, this.trendChangeMon][this.dateIndex])
    }
  },
  computed: {
    columns() {
      return _.defaultsDeep([], overAllProfit[this.btnIndex][this.dateIndex])
    },
    legendOption() {
      return {
        type: 'line',
        options: optionList[this.btnIndex],
        dataSource: this.legendDataSource
      }
    }
  },
  mounted() {},
  methods: {
    btnConfirm({ index }) {
      this.btnIndex = index
    },
    initData(result) {
      const xData = []
      const sData = [[], []]
      if (result && result.length > 0) {
        const fliterList = JSON.parse(JSON.stringify(result))
        this.$objectSortUp(fliterList, this.key_dateunit)
        this.dataSource = fliterList.find(item => item[this.key_dateunit] === this.dateValue) || {}
        fliterList.forEach(item => {
          xData.push(item[this.key_dateunit])
          sData[0].push(item[`od_${this.tabName[this.btnIndex]}_${this.key_unit}`])
          sData[1].push(
            item[`od_${['quantity_weight', 'ticket_weight', 'ticket_quantity'][this.btnIndex]}_${this.key_unit}`]
          )
        })
      } else {
        this.dataSource = []
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 1000)
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: sData[0]
          },
          {
            type: 'line',
            yAxisIndex: 1,
            data: sData[1]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped></style>
