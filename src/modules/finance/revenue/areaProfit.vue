<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-15 16:04:27
 * @Description:Top10运营利润下降场站
-->
<template>
  <div v-if="zoneLevel <= 33">
    <div class="flex_between pd_lr20">
      <div class="fs28 fw700">省区利润</div>
      <div class="fw700 grey666 fs20">利润:万元</div>
    </div>
    <NormalTable
      class="mt24"
      size="small"
      isIndicator
      :width="tableWidth"
      :dataSource="tableDataSource"
      :columns="tableColumns"
      minHeight="2rem"
    >
    </NormalTable>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
export default {
  mixins: [mixins],
  props: ['subName', 'subIndex'],
  data() {
    return {
      tableWidth: '100%',
      tableDataSource: [],
      tableColumns: []
    }
  },
  computed: {
    key_num() {
      return ['2', '3', '4', '0'][this.subIndex]
    }
  },
  watch: {
    deptProfitMon(val) {
      this.setTable(val)
    },
    subIndex() {
      this.setTable(this.deptProfitMon)
    }
  },
  methods: {
    setTable(result) {
      this.tableDataSource = []
      if (result && result.length) {
        const obj = result.filter(item => item['link_level'] === this.key_num)
        this.$objectSortUp(obj, 'profit_hb_rank')
        this.tableDataSource = obj
      }
      this.tableColumns = [
        {
          label: '序号',
          dataIndex: '',
          align: 'center',
          fixed: 'left',
          render: (h, value) => {
            return (
              <div class={'flex_center'}>
                <div class='normal-rank'>{value <= 2 ? '' : value + 1}</div>
              </div>
            )
          }
        },
        {
          label: '省区',
          dataIndex: 'dept_name1'
        },
        {
          label: '当日值',
          dataIndex: 'dept_name2'
        },
        {
          label: '日环比',
          dataIndex: 'dept_name3',
          render: (h, value) => {
            return <div class={value <= 0 ? 'orange' : 'green'}>{this.$numToPercent(value)}</div>
          }
        },
        {
          label: '同比上周',
          dataIndex: 'dept_name4',
          render: (h, value) => {
            return <div class={value <= 0 ? 'orange' : 'green'}>{this.$numToPercent(value)}</div>
          }
        },
        {
          label: '预算值',
          dataIndex: 'dept_name5'
        }
        // {
        //   label: this.titleName,
        //   dataIndex: 'parent_zone_name',
        //   render: (h, value) => {
        //     if (/战区/.test(value)) {
        //       return value.replace('战区', '')
        //     }
        //     return value
        //   }
        // },
        // {
        //   label: '利润额',
        //   dataIndex: 'profit',
        //   render: (h, value) => this.$numToInteger(value, 0, 10000)
        // },
        // {
        //   label: '利润额环比',
        //   dataIndex: 'profit_hb',
        //   render: (h, value) => {
        //     return <div class={value <= 0 ? 'orange' : 'green'}>{this.$numToPercent(value)}</div>
        //   }
        // }
      ]
    }
  },
  mounted() {
    this.setTable()
  }
}
</script>
<style lang="less" scoped></style>
