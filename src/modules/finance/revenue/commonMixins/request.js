/*
 * @Author: shigl
 * @Date: 2022-07-28 09:30:15
 * @LastEditTime: 2023-07-05 18:43:55
 * @Description:
 */
import mixins from './mixins'
export default {
  mixins: [mixins],
  data() {
    return {}
  },
  computed: {
    defaultData() {
      let levelMap = {}
      if (this.dateIndex) {
        levelMap = {
          level_code: this.zoneLevel
        }
      }
      return {
        [this.key_star]: this.incDate,
        [this.key_end]: this.dateValue,
        // zone_level: String(this.zoneLevel),
        zone_code: this.zoneCode === '001' ? 'SFC015' : this.zoneCode, // 收入页面各层级都是zone_code
        // zone_code: 'SFC015'
        ...levelMap
      }
    }
  },
  methods: {
    // 【收入】【货量总览】月维度
    _getCargoOverviewMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_income_weight_mi_01', this.forMapData(tmp))
    },
    // 【收入】【货量总览】日维度
    _getCargoOverviewDay(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_income_weight_di_01', this.forMapData(tmp))
    },
    // 【收入】【票件重趋势变化】月维度
    _getTrendChangeMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_income_pjz_mi_01', this.forMapData(tmp))
    },
    // 【收入】【票件重趋势变化】日维度
    _getTrendChangeDay(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_income_pjz_di_01', this.forMapData(tmp))
    },
    // 【收入】【趋势变化】月维度
    _getTrendChangeComMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_income_price_mi_01', this.forMapData(tmp))
    },
    // 【收入】【趋势变化】日维度
    _getTrendChangeComDay(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('ads_sx_fin_sxzk_income_price_di_01', this.forMapData(tmp))
    }
  }
}
