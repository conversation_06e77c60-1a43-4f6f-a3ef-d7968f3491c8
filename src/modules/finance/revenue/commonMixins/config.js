import { numToInteger, numToPercent } from 'common/js/numFormat'

export const overAllProfitKd = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'kd_weight_d',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'kd_weight_d_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'kd_weight_m',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'kd_weight_m_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'kd_weight_m',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'kd_weight_m_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'kd_weight_y',
    //       int: [0, 1000]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成率',
    //       dataIndex: 'kd_weight_y_rate',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]
export const overAllProfitQs = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'od_weight_d',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'od_weight_d_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'od_weight_m',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'od_weight_m_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'od_weight_m',
          int: [0, 1000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'od_weight_m_hb',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'od_weight_y',
    //       int: [0, 1000]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成率',
    //       dataIndex: 'od_weight_y_rate',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]
export const tableColumns = [
  [
    {
      label: '排名',
      dataIndex: '',
      align: 'center',
      fixed: 'left',
      width: '0.8rem',
      render: (h, value) => {
        return (
          <div class={'flex_center'}>
            <div class="normal-rank">{value <= 2 ? '' : value + 1}</div>
          </div>
        )
      }
    },
    { label: '省区', dataIndex: 'zone_name', width: '1.4rem', fixed: 'left' },

    {
      label: '当日值',
      dataIndex: 'day_value',
      render: (h, value) => numToInteger(value, 1, 10000)
    },
    {
      label: '日环比',
      dataIndex: 'day_value_hb',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '同比上周',
      dataIndex: 'week_value_tb',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '月累计(万)',
      dataIndex: 'day_value_cum',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '预算值',
      dataIndex: 'target_value',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '完成比',
      dataIndex: 'complete_rate',
      render: (h, value) => numToPercent(value)
    },
    {
      label: '差额',
      dataIndex: 'balance',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '负责人',
      dataIndex: 'person_liable'
    }
  ],
  [
    {
      label: '排名',
      dataIndex: '',
      align: 'center',
      fixed: 'left',
      width: '0.8rem',
      render: (h, value) => {
        return (
          <div class={'flex_center'}>
            <div class="normal-rank">{value <= 2 ? '' : value + 1}</div>
          </div>
        )
      }
    },
    { label: '省区', dataIndex: 'zone_name', width: '1.4rem', fixed: 'left' },
    {
      label: '当月值',
      dataIndex: 'inc_value',
      render: (h, value) => numToInteger(value, 0, 10000)
    },
    {
      label: '月环比',
      dataIndex: 'hb_rate',
      render: (h, value) => {
        return <div class={value <= 0 ? 'orange' : 'green'}>{numToPercent(value)}</div>
      }
    },
    {
      label: '月完成比',
      dataIndex: 'm_target_com',
      render: (h, value) => numToPercent(value)
    },
    // {
    //   label: '年累计',
    //   dataIndex: 'acc_value',
    //   render: (h, value) => numToInteger(value, 0, 10000)
    // },
    // {
    //   label: '年完成比',
    //   dataIndex: 'y_target_com',
    //   render: (h, value) => numToPercent(value)
    // },
    {
      label: '负责人',
      dataIndex: 'person_liable'
    }
  ]
]
export const overAllProfit = [
  // 件均
  [
    [
      {
        parent: [
          {
            label: '当日值',
            dataIndex: 'od_quantity_weight_d',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '日环比',
            dataIndex: 'od_quantity_weight_d_hb',
            int: [3, 1, 3],
            indexType: 'up'
          }
        ]
      },
      {
        parent: [
          {
            label: '月累计',
            dataIndex: 'od_quantity_weight_m',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_quantity_weight_m_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      }
    ],
    [
      {
        parent: [
          {
            label: '当月值',
            dataIndex: 'od_quantity_weight_m',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_quantity_weight_m_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      }
      // {
      //   parent: [
      //     {
      //       label: '年累计',
      //       dataIndex: 'od_quantity_weight_y',
      //       int: [0, 1]
      //     }
      //   ],
      //   child: [
      //     {
      //       label: '年环比',
      //       dataIndex: 'od_quantity_weight_y_hb',
      //       int: [3, 1, 3],
      //       indexType: 'up'
      //     }
      //   ]
      // }
    ]
  ],
  [
    [
      {
        parent: [
          {
            label: '当日值',
            dataIndex: 'od_ticket_weight_d',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '日环比',
            dataIndex: 'od_ticket_weight_d_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      },
      {
        parent: [
          {
            label: '月累计',
            dataIndex: 'od_ticket_weight_m',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_ticket_weight_m_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      }
    ],
    [
      {
        parent: [
          {
            label: '当月值',
            dataIndex: 'od_ticket_weight_m',
            int: [0, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_ticket_weight_m_hb',
            int: [3, 1, 3],

            indexType: 'up'
          }
        ]
      }
      // {
      //   parent: [
      //     {
      //       label: '年累计',
      //       dataIndex: 'od_ticket_weight_y',
      //       int: [0, 1]
      //     }
      //   ],
      //   child: [
      //     {
      //       label: '年环比',
      //       dataIndex: 'od_ticket_weight_y_hb',
      //       int: [3, 1, 3],

      //       indexType: 'up'
      //     }
      //   ]
      // }
    ]
  ],
  [
    [
      {
        parent: [
          {
            label: '当日值',
            dataIndex: 'od_ticket_quantity_d',
            int: [1, 1, 1]
          }
        ],
        child: [
          {
            label: '日环比',
            dataIndex: 'od_ticket_quantity_d_hb',
            int: [3, 1, 3],
            indexType: 'up'
          }
        ]
      },
      {
        parent: [
          {
            label: '月累计',
            dataIndex: 'od_ticket_quantity_m',
            int: [1, 1, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_ticket_quantity_m_hb',
            int: [3, 1, 3],
            indexType: 'up'
          }
        ]
      }
    ],
    [
      {
        parent: [
          {
            label: '当月值',
            dataIndex: 'od_ticket_quantity_m',
            int: [1, 1, 1]
          }
        ],
        child: [
          {
            label: '月环比',
            dataIndex: 'od_ticket_quantity_m_hb',
            int: [3, 1, 3],
            indexType: 'up'
          }
        ]
      }
      // {
      //   parent: [
      //     {
      //       label: '年累计',
      //       dataIndex: 'od_ticket_quantity_y',
      //       int: [1, 1, 1]
      //     }
      //   ],
      //   child: [
      //     {
      //       label: '年环比',
      //       dataIndex: 'od_ticket_quantity_y_hb',
      //       int: [3, 1, 3],

      //       indexType: 'up'
      //     }
      //   ]
      // }
    ]
  ]
]
export const optionList = [
  [
    { label: '重量(吨)', int: [0, 1000] },
    { label: '件均重量(kg/件)', int: [0] }
  ],
  [
    { label: '票数', int: [0, 1] },
    { label: '件均重量(kg/件)', int: [0] }
  ],
  [
    { label: '件数', int: [0, 1] },
    { label: '单联件数(件/票)', int: [1, 1, 1] }
  ]
]
