<!--
 * @Author: gilshi
 * @Date: 2021-06-25 13:53:55
 * @LastEditTime: 2023-12-12 10:02:10
 * @Description:
-->
<template>
  <div class="profit_page page_bgc">
    <div class="date_container flex_between pd_lr20">
      <KyDatePicker
        style="width: 3rem"
        :type="dateType"
        :dateValue="dateValue"
        @onChange="dateChange"
        :holidayData="holidayData"
      ></KyDatePicker>
      <Tabs :options="['日预测', '月实际']" :tabIndex="dateIndex" @tabSelect="tabSelect"></Tabs>
    </div>
    <PageContent>
      <CostOverview></CostOverview>
      <PickCost class="mt24"></PickCost>
      <AppreciationCost class="mt24"></AppreciationCost>
      <TransitCost class="mt24"></TransitCost>
      <TrunkCost class="mt24"></TrunkCost>
      <SelfPayCost v-if="+zoneLevel === 32" class="mt24"></SelfPayCost>
      <OtherCost class="mt24"></OtherCost>
      <PipeCost class="mt24"></PipeCost>
      <div style="height: 0.5rem"></div>
    </PageContent>
  </div>
</template>
<script>
import CostOverview from './costOverview.vue'
import PickCost from './pickCost.vue'
import AppreciationCost from './appreciationCost.vue'
import TrunkCost from './trunkCost.vue'
import TransitCost from './transitCost.vue'
import OtherCost from './otherCost.vue'
import SelfPayCost from './selfPayCost.vue'
import PipeCost from './pipeCost.vue'

import { mapMutations, mapState } from 'vuex'
import request from './commonMixins/request'
export default {
  mixins: [request],
  components: {
    CostOverview,
    PickCost,
    AppreciationCost,
    TrunkCost,
    OtherCost,
    TransitCost,
    SelfPayCost,
    PipeCost
  },
  data() {
    return {}
  },
  computed: {
    dateType() {
      return ['day', 'month'][this.dateIndex]
    },
    ...mapState({
      holidayData: 'holidayData'
    })
  },
  watch: {
    zoneCode(val) {
      this.initOptions()
    }
  },
  methods: {
    ...mapMutations('finance', ['setDate', 'setDateIndex', 'setPageData']),
    tabSelect(index) {
      this.setDateIndex({
        type: 'all',
        index: index
      })
      this.initOptions()
    },
    dateChange(date) {
      this.setDate({
        type: 'all',
        key: ['dateDay', 'dateMon'][this.dateIndex],
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
      this.initOptions()
    },
    async getCostMon() {
      const { obj } = await this._getCostMon()
      this.setPageData({
        type: 'cost',
        dataType: 'costMon',
        data: obj
      })
    },
    async getCostDay() {
      const levelMap = {
        30: '总部',
        32: '省区',
        33: '区域'
      }
      const { obj: objCard } = await this._getCostDayCard({
        level_name: levelMap[+this.zoneLevel]
      })
      const { obj: objLine } = await this._getCostDayLine({
        level_name: levelMap[+this.zoneLevel]
      })

      this.setPageData({
        type: 'cost',
        dataType: 'costDay',
        data: { objCard, objLine }
      })
    },
    initOptions() {
      if (!this.dateIndex) {
        this.getCostDay()
      } else {
        this.getCostMon()
      }
    }
  },
  activated() {},
  mounted() {
    this.setDateIndex({
      type: 'all',
      index: 0
    })
    this.initOptions()
  }
}
</script>
<style lang="less" scoped>
.profit_page {
  color: #333;
}
.date_container {
  width: 100vw;
  height: 0.8rem;
  color: #999;
}
.content-wrapper {
  height: calc(100vh - 3.3rem);
  overflow-y: scroll;
}
</style>
