<template>
  <div>
    <CardList title="增值成本">
      <div class="mt24 pd_lr20">
        <div class="mt40">
          <div class="fw700 fs28">增值成本</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxAll"></div>
            <MultiDataList class="flex1" :dataSource="dataSourceAll" :columns="columnsCost"></MultiDataList>
          </div>
        </div>

        <div class="mt40">
          <div class="fw700 fs28">增值单价</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxPrice"></div>
            <MultiDataList class="flex1" :dataSource="dataSourcePrice" :columns="columnsPrice"></MultiDataList>
          </div>
        </div>
      </div>
      <div class="pd_lr20">
        <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'

import { drawNewGaugeChart, drawScrollChart } from 'common/charts/chartOption'
import { overAllProfitCost, overAllProfitPrice } from './commonMixins/config'

export default {
  mixins: [mixins],
  components: {},
  data() {
    return {
      dataSourcePrice: {},
      dataSourceAll: {},
      tableData: {},
      legendDataSource: []
    }
  },
  watch: {
    costMon(val) {
      this.initData(val)
    },
    costDay(val) {
      this.initData(val)
    },
    btnIndex() {
      if (!this.dateIndex) {
        const newList = JSON.parse(JSON.stringify(this.costDay))
        this.initModelChartDay(newList.objLine)
      } else {
        this.initModelChart(this.costMon)
      }
    }
  },
  computed: {
    columnsCost() {
      return _.defaultsDeep([], overAllProfitCost[this.dateIndex])
    },
    columnsPrice() {
      return _.defaultsDeep([], overAllProfitPrice[this.dateIndex])
    },
    legendOption() {
      return {
        type: 'line',
        options: [
          { label: '增值成本', int: [0, 10000] },
          { label: '增值单价', int: [3, 1, 3] }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  mounted() {},
  methods: {
    initCostData(result) {
      this.dataSourceAll =
        result.find(
          item => item[this.key_date] === this.dateValue && item.itype === '增值成本' && item.item === '增值成本'
        ) || {}
      this.dataSourcePrice =
        result.find(
          item => item[this.key_date] === this.dateValue && item.itype === '增值成本' && item.item === '增值成本单价'
        ) || {}
      // console.log('====================================')
      // console.log(JSON.parse(JSON.stringify(this.dataSourceAll)), '总成本')
      // console.log('====================================')
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxAll'], {
        value: this.dataSourceAll[`${this.key_com_rate}`] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxPrice'], {
        value: this.dataSourcePrice[`${this.key_com_rate}`] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
    },
    initCostDataDay(result) {
      this.dataSourceAll = result.find(item => item.index_name === '增值成本') || {}
      this.dataSourcePrice = result.find(item => item.index_name === '增值单价') || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxAll'], {
        value: this.dataSourceAll.complete_rate * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxPrice'], {
        value: this.dataSourcePrice.complete_rate * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
    },
    initData(result) {
      if (!this.dateIndex) {
        // console.log(result, 'result')
        const newList = JSON.parse(JSON.stringify(result))
        this.initCostDataDay(newList.objCard)
        this.initModelChartDay(newList.objLine)
      } else {
        this.initCostData(result)
        this.initModelChart(result)
      }
    },

    initModelChart(result) {
      const fliterList = JSON.parse(JSON.stringify(result))
      const tem = this.typeNameList.map(item => ({ name: item, data: fliterList.filter(data => data.itype === item) }))
      const filterObj = tem.find(item => item.name === '增值成本') || {}
      this.$objectSortUp(filterObj.data, this.key_date)
      const allCostData = filterObj.data.filter(v => v.item === '增值成本')
      const priceData = filterObj.data.filter(v => v.item === '增值成本单价')
      const xData = []
      const sData = [[], []]
      if (result && result.length > 0) {
        allCostData.forEach((item, index) => {
          xData.push(item[this.key_date])
          sData[0].push(item.imoney)
        })
        priceData.forEach((item, index) => {
          sData[1].push(item.imoney)
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 10000)
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: sData[0]
          },
          {
            type: 'line',
            yAxisIndex: 1,

            data: sData[1]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    },
    initModelChartDay(result) {
      const fliterList = JSON.parse(JSON.stringify(result))
      const xData = []
      const sData = [[], []]
      if (fliterList && fliterList.length > 0) {
        fliterList.forEach((item, index) => {
          xData.push(item.date_id)
          sData[0].push(item.zengzhi_cost)
          sData[1].push(item.zengzhi_cost_price)
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 10000)
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: sData[0]
          },
          {
            type: 'line',
            yAxisIndex: 1,

            data: sData[1]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.multi_data_list_wrapper {
  background-color: #fff !important;
}
.gauge-chart-box {
  height: 1.76rem;
  width: 96%;
}
.data_list_custom {
  background-color: #fff !important;
}
.text_conent {
  height: 0.81rem;
  width: 3.34rem;
  background: #f8f9fc;
  border-radius: 8px;
}
</style>
