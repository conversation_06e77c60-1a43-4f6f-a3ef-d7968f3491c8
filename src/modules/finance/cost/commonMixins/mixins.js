/*
 * @Author: shigl
 * @Date: 2022-07-19 17:25:34
 * @LastEditTime: 2022-07-20 17:36:06
 * @Description:
 */
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
import baseMixins from '../../baseMixins'
export default {
  mixins: [requestMixins, baseMixins],

  data() {
    return {
      typeNameList: [
        '总成本',
        'OD成本',
        '收派成本',
        '增值成本',
        '中转成本',
        '干线成本',
        '内部结算成本',
        '其他成本',
        '管销费用'
      ]
    }
  },
  computed: {
    incDate() {
      console.log(this.dateValue, 'this.dateValue')
      if (!this.dateIndex) {
        return this.isDev ? '20210519' : this.$moment(this.dateValue).format('YYYYMMDD')
      }
      return this.isDev ? '202105' : this.$moment(this.dateValue).format('YYYYMM')
    },
    // 子标题前缀
    prefixName() {
      return ['当月累计', ''][this.dateIndex]
    },
    key_date() {
      return ['date_id', 'inc_month'][this.dateIndex]
    },
    key_com_rate() {
      return ['complete_rate', 'imoney_comp_rate'][this.dateIndex]
    },
    ...mapGetters('finance', {
      dateValue: 'dateValue'
    }),
    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('finance', {
      dateIndex: state => state.all.dateIndex,

      costMon: state => state.cost.costMon,
      costDay: state => state.cost.costDay
    })
  },
  methods: {}
}
