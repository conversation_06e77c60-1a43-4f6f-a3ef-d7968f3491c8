/*
 * @Author: shigl
 * @Date: 2022-07-19 17:25:34
 * @LastEditTime: 2023-07-05 18:44:26
 * @Description:
 */
import mixins from './mixins'
export default {
  mixins: [mixins],
  data() {
    return {}
  },
  computed: {
    defaultData() {
      let levelMap = {}
      if (this.dateIndex) {
        levelMap = {
          level_code: this.zoneLevel
        }
      }
      return {
        [['date_id', 'inc_month'][this.dateIndex]]: this.incDate,
        zone_code: this.zoneCode === '001' ? 'SFC015' : this.zoneCode, // 收入页面各层级都是zone_code
        ...levelMap
      }
    }
  },
  methods: {
    // 【成本】-日维度  指标卡数据
    _getCostDayCard(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('dws_fin_sxzk_cost_index_card_di', this.forMapData(tmp))
    },
    // 【成本】-日维度  折线图数据
    _getCostDayLine(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('dws_fin_sxzk_cost_line_di', this.forMapData(tmp))
    },
    // 【成本】-月维度
    _getCostMon(data) {
      const tmp = _.defaultsDeep(data, this.defaultData)
      return this.sendTwoDimenRequest('sxzk_fin_cost_sum_mi', this.forMapData(tmp))
    }
  }
}
