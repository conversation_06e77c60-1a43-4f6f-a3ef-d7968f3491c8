// import { numToInteger } from 'common/js/numFormat'
export const overAllProfitCost = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'now_value',
          int: [0, 10000]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'day_hb_rate',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'month_value',
          int: [0, 10000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'month_hb_rate',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'imoney',
          int: [0, 10000]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'imoney_mom',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'imoney_ytm',
    //       int: [0, 10000]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成比',
    //       dataIndex: 'imoney_ytm_comp_rate',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]

export const overCostPrice = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'now_value',
          per: [1]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'day_hb_rate',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'month_value',
          per: [1]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'month_hb_rate',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'imoney',
          per: [1]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'imoney_mom',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'imoney_ytm',
    //       per: [1]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成比',
    //       dataIndex: 'imoney_ytm_comp_rate',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]

export const overAllProfitPrice = [
  [
    {
      parent: [
        {
          label: '当日值',
          dataIndex: 'now_value',
          int: [3, 1, 3]
        }
      ],
      child: [
        {
          label: '日环比',
          dataIndex: 'day_hb_rate',
          per: [1],
          indexType: 'up'
        }
      ]
    },
    {
      parent: [
        {
          label: '月累计',
          dataIndex: 'month_value',
          int: [3, 1, 3]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'month_hb_rate',
          per: [1],
          indexType: 'up'
        }
      ]
    }
  ],
  [
    {
      parent: [
        {
          label: '当月值',
          dataIndex: 'imoney',
          int: [3, 1, 3]
        }
      ],
      child: [
        {
          label: '月环比',
          dataIndex: 'imoney_mom',
          per: [1],
          indexType: 'up'
        }
      ]
    }
    // {
    //   parent: [
    //     {
    //       label: '年累计',
    //       dataIndex: 'imoney_ytm',
    //       int: [3, 1, 3]
    //     }
    //   ],
    //   child: [
    //     {
    //       label: '年完成比',
    //       dataIndex: 'imoney_ytm_comp_rate',
    //       per: [1],
    //       indexType: 'up'
    //     }
    //   ]
    // }
  ]
]
