<template>
  <div>
    <CardList title="管销费用">
      <div class="mt24 pd_lr20">
        <div class="mt40">
          <div class="fw700 fs28">管销费用</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxAll"></div>
            <MultiDataList class="flex1" :dataSource="dataSourceAll" :columns="columnsCost"></MultiDataList>
          </div>
        </div>

        <div class="mt40">
          <div class="fw700 fs28">管销占收入比</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxPrice"></div>
            <MultiDataList class="flex1" :dataSource="dataSourcePrice" :columns="columnsPrice"></MultiDataList>
          </div>
        </div>
      </div>
      <div class="pd_lr20">
        <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'

import { drawNewGaugeChart, drawScrollChart } from 'common/charts/chartOption'
import { overAllProfitCost, overCostPrice } from './commonMixins/config'

export default {
  mixins: [mixins],
  components: {},
  data() {
    return {
      dataSourcePrice: {},
      dataSourceAll: {},
      tableData: {},
      legendDataSource: []
    }
  },
  watch: {
    costMon(val) {
      this.initData(val)
    },
    costDay(val) {
      this.initData(val)
    }
  },
  computed: {
    columnsCost() {
      return _.defaultsDeep([], overAllProfitCost[this.dateIndex])
    },
    columnsPrice() {
      return _.defaultsDeep([], overCostPrice[this.dateIndex])
    },
    legendOption() {
      return {
        type: 'column',
        colNum: 3,
        isGrid: true,
        options: [
          { label: '管销费用', int: [0, 10000] },
          { label: '人力成本', color: 'ffffff', seriesIndex: 0, dataIndex: 'value1', int: [0, 10000] },
          { label: '行政费用', color: 'ffffff', seriesIndex: 0, dataIndex: 'value2', int: [0, 10000] },
          { label: '管销占收入比', color: '#FF8066', seriesIndex: 1, dataIndex: 'value', per: '1' },
          { label: '人力成本率', color: 'ffffff', seriesIndex: 0, dataIndex: 'value3', per: '1' },
          { label: '行政成本率', color: 'ffffff', seriesIndex: 0, dataIndex: 'value4', per: '1' }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  mounted() {},
  methods: {
    initData(result) {
      if (!this.dateIndex) {
        const newList = JSON.parse(JSON.stringify(result))
        this.initCostDataDay(newList.objCard)
        this.initModelChartDay(newList.objLine)
      } else {
        this.initCostData(result)
        this.initModelChart(result)
      }
    },
    initCostData(result) {
      this.dataSourceAll =
        result.find(
          item => item[this.key_date] === this.dateValue && item.itype === '管销费用' && item.item === '管销费用'
        ) || {}
      this.dataSourcePrice =
        result.find(
          item => item[this.key_date] === this.dateValue && item.itype === '管销费用' && item.item === '管销占收入比'
        ) || {}
      // console.log('====================================')
      // console.log(JSON.parse(JSON.stringify(this.dataSourceAll)), '总成本')
      // console.log('====================================')
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxAll'], {
        value: this.dataSourceAll[`${this.key_com_rate}`] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxPrice'], {
        value: this.dataSourcePrice[`${this.key_com_rate}`] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
    },
    initCostDataDay(result) {
      this.dataSourceAll = result.find(item => item.index_name === '管销费用') || {}
      this.dataSourcePrice = result.find(item => item.index_name === '管销占收入比') || {}
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxAll'], {
        value: this.dataSourceAll.complete_rate * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxPrice'], {
        value: this.dataSourcePrice.complete_rate * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
    },

    initModelChart(result) {
      // debugger
      let xData = []
      const fliterList = JSON.parse(JSON.stringify(result))
      const tem = this.typeNameList.map(item => ({ name: item, data: fliterList.filter(data => data.itype === item) }))
      const filterObj = tem.find(item => item.name === '管销费用') || {}
      // console.log('====================================')
      // console.log(JSON.parse(JSON.stringify(result)), '管销费用')
      // console.log('====================================')
      const labelList = ['管销费用', '人力成本', '行政成本', '管销占收入比', '人力成本率', '行政成本率']
      const temm = labelList.map((item, index) => {
        return filterObj.data.filter(data => data.item === item)
      })
      xData = this.$mergexAxisData(temm, this.key_date)
      const value1 = this.$changeSeriesData(temm[0], xData, this.key_date, 'imoney', 'value1')
      const value2 = this.$changeSeriesData(temm[1], xData, this.key_date, 'imoney', 'value2')
      const value3 = this.$changeSeriesData(temm[2], xData, this.key_date, 'imoney', 'value3')
      const value4 = this.$changeSeriesData(temm[3], xData, this.key_date, 'imoney', 'value4')
      const value5 = this.$changeSeriesData(temm[4], xData, this.key_date, 'imoney', 'value5')
      const value6 = this.$changeSeriesData(temm[5], xData, this.key_date, 'imoney', 'value6')
      const dataList = _.defaultsDeep(value1, value2, value3, value4, value5, value6)
      const sData = [[], [], [], [], [], []]
      if (dataList && dataList.length > 0) {
        dataList.forEach(item => {
          sData[0].push({
            value: item.value1,
            value1: item.value2,
            value2: item.value3,
            value3: item.value5,
            value4: item.value6
          })
          sData[1].push({
            value: item.value4
          })
        })
      }
      // console.log('====================================')
      // console.log(sData, 'sData')
      // console.log('====================================')
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 10000)
            }
          }
        ],
        series: [
          { type: 'bar', data: sData[0] },
          {
            type: 'line',
            yAxisIndex: 1,

            data: sData[1]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    },
    initModelChartDay(result) {
      const xData = []
      const sData = [[], [], [], [], [], []]
      const fliterList = JSON.parse(JSON.stringify(result))
      this.$objectSortUp(fliterList, this.key_date)
      if (fliterList && fliterList.length > 0) {
        fliterList.forEach(item => {
          xData.push(item[`${this.key_date}`])
          sData[0].push({
            value: item['guanxiao_cost'],
            value1: item['worker_cost'],
            value2: item['xzgl_cost'],
            value3: item['worker_cost_rate'],
            value4: item['xzgl_cost_rate']
          })
          sData[1].push({
            value: item['guanxiao_cost_rate']
          })
        })
      }
      // console.log('====================================')
      // console.log(sData, 'sData')
      // console.log('====================================')
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 10000)
            }
          }
        ],
        series: [
          { type: 'bar', data: sData[0] },
          { type: 'line', yAxisIndex: 1, data: sData[1] }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.multi_data_list_wrapper {
  background-color: #fff !important;
}
.gauge-chart-box {
  height: 1.76rem;
  width: 96%;
}
.data_list_custom {
  background-color: #fff !important;
}
.text_conent {
  height: 0.81rem;
  width: 3.34rem;
  background: #f8f9fc;
  border-radius: 8px;
}
</style>
