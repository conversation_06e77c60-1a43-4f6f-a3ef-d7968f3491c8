<template>
  <div>
    <CardList title="其他成本">
      <div class="mt24 pd_lr20">
        <div class="mt40">
          <div class="fw700 fs28">其他成本</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxAll"></div>
            <MultiDataList class="flex1" :dataSource="dataSourceAll" :columns="columnsCost"></MultiDataList>
          </div>
        </div>

        <div class="mt40">
          <div class="fw700 fs28">其他单价</div>
          <div class="flex_center">
            <div style="width: 2.6rem; height: 1.4rem" ref="gaugeChartBoxPrice"></div>
            <MultiDataList class="flex1" :dataSource="dataSourcePrice" :columns="columnsPrice"></MultiDataList>
          </div>
        </div>
      </div>
      <div class="pd_lr20">
        <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
          <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
        </KydChartModel>
      </div>
    </CardList>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'

import { drawNewGaugeChart, drawScrollChart } from 'common/charts/chartOption'
import { overAllProfitCost, overAllProfitPrice } from './commonMixins/config'

export default {
  mixins: [mixins],
  components: {},
  data() {
    return {
      dataSourcePrice: {},
      dataSourceAll: {},
      tableData: {},
      legendDataSource: []
    }
  },
  watch: {
    costMon(val) {
      this.initData(val)
    },
    costDay(val) {
      this.initData(val)
    }
  },
  computed: {
    columnsCost() {
      return _.defaultsDeep([], overAllProfitCost[this.dateIndex])
    },
    columnsPrice() {
      return _.defaultsDeep([], overAllProfitPrice[this.dateIndex])
    },
    legendOption() {
      return {
        type: 'column',
        colNum: 3,
        isGrid: true,
        options: [
          { label: '其他成本', int: [0, 10000] },
          { label: '其他单价', int: [3, 1, 3] },
          { label: '外网成本单价', color: 'ffffff', seriesIndex: 0, dataIndex: 'value3', int: [3, 1, 3] },
          { label: '保险成本单价', color: 'ffffff', seriesIndex: 0, dataIndex: 'value4', int: [3, 1, 3] },
          { label: '加盟费类单价', color: 'ffffff', seriesIndex: 0, dataIndex: 'value5', int: [3, 1, 3] },
          { label: '其他成本单价', color: 'ffffff', seriesIndex: 0, dataIndex: 'value6', int: [3, 1, 3] }
        ],
        dataSource: this.legendDataSource
      }
    }
  },
  mounted() {},
  methods: {
    initCostData(result) {
      this.dataSourceAll =
        result.find(
          item => item[this.key_date] === this.dateValue && item.itype === '其他成本' && item.item === '其他成本'
        ) || {}
      this.dataSourcePrice =
        result.find(
          item => item[this.key_date] === this.dateValue && item.itype === '其他成本' && item.item === '其他成本单价'
        ) || {}
      // console.log('====================================')
      // console.log(JSON.parse(JSON.stringify(this.dataSourcePrice)), 'dataSourcePrice')
      // console.log('====================================')
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxAll'], {
        value: this.dataSourceAll[`${this.key_com_rate}`] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxPrice'], {
        value: this.dataSourcePrice[`${this.key_com_rate}`] * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
    },
    initCostDataDay(result) {
      this.dataSourceAll = result.find(item => item.index_name === '其他成本') || {}
      this.dataSourcePrice = result.find(item => item.index_name === '其他成本单价') || {}
      console.log('====================================')
      console.log(JSON.parse(JSON.stringify(this.dataSourceAll)), 'dataSourceAll')
      console.log('====================================')
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxAll'], {
        value: this.dataSourceAll.complete_rate * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
      drawNewGaugeChart({}, this.$refs['gaugeChartBoxPrice'], {
        value: this.dataSourcePrice.complete_rate * 100 || 0,
        showDetail: true,
        name: '完成比'
      })
    },
    initData(result) {
      if (!this.dateIndex) {
        const newList = JSON.parse(JSON.stringify(result))
        this.initCostDataDay(newList.objCard)
        this.initModelChartDay(newList.objLine)
      } else {
        this.initCostData(result)
        this.initModelChart(result)
      }
    },

    initModelChart(result) {
      let xData = []
      const fliterList = JSON.parse(JSON.stringify(result))
      const tem = this.typeNameList.map(item => ({ name: item, data: fliterList.filter(data => data.itype === item) }))
      const filterObj = tem.find(item => item.name === '其他成本') || {}
      const labelList = [
        '其他成本',
        '其他成本单价',
        '外网成本单价',
        '保险成本单价',
        '加盟费类成本单价',
        '其他其他成本单价'
      ]
      const temm = labelList.map((item, index) => {
        return filterObj.data.filter(data => data.item === item)
      })
      console.log(JSON.parse(JSON.stringify(temm)), 'temm-----')
      xData = this.$mergexAxisData(temm, this.key_date)
      const value1 = this.$changeSeriesData(temm[0], xData, this.key_date, 'imoney', 'value1')
      const value2 = this.$changeSeriesData(temm[1], xData, this.key_date, 'imoney', 'value2')
      const value3 = this.$changeSeriesData(temm[2], xData, this.key_date, 'imoney', 'value3')
      const value4 = this.$changeSeriesData(temm[3], xData, this.key_date, 'imoney', 'value4')
      const value5 = this.$changeSeriesData(temm[4], xData, this.key_date, 'imoney', 'value5')
      const value6 = this.$changeSeriesData(temm[5], xData, this.key_date, 'imoney', 'value6')
      const dataList = _.defaultsDeep(value1, value2, value3, value4, value5, value6)
      const sData = [[], [], [], [], [], []]
      if (dataList && dataList.length > 0) {
        dataList.forEach(item => {
          sData[0].push({
            value: item.value1,
            value3: item.value3,
            value4: item.value4,
            value5: item.value5,
            value6: item.value6
          })
          sData[1].push({
            value: item.value2
          })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 10000)
            }
          }
        ],
        series: [
          { type: 'bar', data: sData[0] },
          {
            type: 'line',
            yAxisIndex: 1,

            data: sData[1]
          }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    },
    initModelChartDay(result) {
      const xData = []
      const sData = [[], [], [], [], [], []]
      const fliterList = JSON.parse(JSON.stringify(result))
      console.log('====================================')
      console.log(JSON.parse(JSON.stringify(fliterList)), '其他成本')
      console.log('====================================')
      this.$objectSortUp(fliterList, this.key_date)
      if (fliterList && fliterList.length > 0) {
        fliterList.forEach(item => {
          xData.push(item[`${this.key_date}`])
          sData[0].push({
            value: item.other_cost,
            value3: item.outnet_cost_price,
            value4: item.insurance_cost_price,
            value5: item.join_price,
            value6: item.other_other_cost_price
          })
          sData[1].push({
            value: item.other_cost_price
          })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              formatter: value => this.$dateFormat(value, this.dateIndex)
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: value => this.$intFormat(value, 10000)
            }
          }
        ],
        series: [
          { type: 'bar', data: sData[0] },
          { type: 'line', yAxisIndex: 1, data: sData[1] }
        ]
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.multi_data_list_wrapper {
  background-color: #fff !important;
}
.gauge-chart-box {
  height: 1.76rem;
  width: 96%;
}
.data_list_custom {
  background-color: #fff !important;
}
.text_conent {
  height: 0.81rem;
  width: 3.34rem;
  background: #f8f9fc;
  border-radius: 8px;
}
</style>
