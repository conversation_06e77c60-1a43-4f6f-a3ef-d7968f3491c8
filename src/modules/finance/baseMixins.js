/*
 * @Author: shigl
 * @Date: 2022-07-19 17:32:40
 * @LastEditTime: 2022-07-19 17:33:50
 * @Description:
 */
import { mapGetters } from 'vuex'

export default {
  data() {
    return {}
  },
  computed: {
    zoneLevel() {
      return this.zoneData.zoneLevel
    },
    zoneCode() {
      return this.zoneData.zoneCode
    },
    zoneName() {
      return this.zoneData.zoneName
    },
    ...mapGetters({
      zoneData: 'zoneData'
    })
  },
  methods: {}
}
