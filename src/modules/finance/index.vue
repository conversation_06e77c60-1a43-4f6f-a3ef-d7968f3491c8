<!--
 * @Author: g<PERSON>hi
 * @Date: 2021-01-02 15:39:35
 * @LastEditTime: 2023-07-04 15:53:10
 * @Description:
-->
<template>
  <div>
    <MAlertWarning type="error" v-if="zoneData.zoneLevel > 33"> 财务暂未开发{{ zoneData.levelName }}层级</MAlertWarning>
    <ScrollTabBar color='black' :tabData="tabData" :tabValue="tabValue" @change="change"></ScrollTabBar>
    <component :is="tabValue"></component>
  </div>
</template>
<script>
import profit from './profit'
import revenue from './revenue'
import cost from './cost'
import { mapActions } from 'vuex'
import baseMixins from './baseMixins'
export default {
  mixins: [baseMixins],
  components: {
    profit,
    revenue,
    cost
  },
  data() {
    return {
      tabData: [
        {
          label: '利润',
          value: 'profit'
        },
        {
          label: '收入',
          value: 'revenue'
        },
        {
          label: '成本',
          value: 'cost'
        }
      ],
      tabLabel: '利润',
      tabValue: 'revenue'
    }
  },
  computed: {},

  methods: {
    ...mapActions('finance', ['initDate']),
    change({ label, value }) {
      this.$sensors.pageStay('财务-' + this.tabLabel)
      this.tabLabel = label
      this.tabValue = value
      this.$sensors.pageview('财务-' + this.tabLabel)
    }
  },
  mounted() {
    this.$sensors.pageview('财务-' + this.tabLabel)
  },
  created() {
    // 初始化日期
    this.initDate()
  }
}
</script>
