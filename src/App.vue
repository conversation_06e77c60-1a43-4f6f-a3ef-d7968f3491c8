<!--
 * @Author: shigl
 * @Date: 2022-08-25 18:34:33
 * @LastEditTime: 2024-04-25 10:04:37
 * @Description:
-->
<template>
  <div id="app">
    <keep-alive :exclude="removeRouteCache">
      <router-view v-if="$route.meta.keepAlive"> </router-view>
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" :key="$route.fullPath"> </router-view>
    <FloatBall></FloatBall>
    <FloatBall1 :auth="authList"></FloatBall1>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import FloatBall from 'common/components/FloatBall/index'
import FloatBall1 from 'common/components/FloatBall1/index'

export default {
  name: 'App',
  components: { FloatBall, FloatBall1 },
  data() {
    return {}
  },
  computed: {
    ...mapState(['removeRouteCache', 'authList'])
  },

  methods: {
    // 获取用户基本信息
    getUserBaseMsg() {
      const userMsg = {}
      userMsg.masterId = sessionStorage.getItem('userId') || ''
      this.setUserMsg(userMsg)
    },
    ...mapMutations(['setUserMsg', 'setShowThemeTab'])
  },
  created() {},
  async mounted() {
    // 设置手机主题色
    this.$setWebViewBackgroundColor('#ffffff')
    this.getUserBaseMsg()
    // const remSize = parseInt(document.getElementsByTagName('html')[0].style.fontSize)
    // 节假日主题菜单显示
    // const dateBetween = this.$moment().isBetween('20231031', '20231116', 'day')
    // if (dateBetween) {
    //   this.setShowThemeTab(true)
    // } else {
    //   this.setShowThemeTab(false)
    // }
  }
}
</script>

<style lang="less" scoped></style>
