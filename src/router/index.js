/*
 * @Author: shigl
 * @Date: 2022-07-18 15:48:46
 * @LastEditTime: 2023-12-19 15:25:40
 * @Description:
 */
import Vue from 'vue'
import Router from 'vue-router'
import routerMap from './routerMap.js'

function isArray(param) {
  return Object.prototype.toString.call(param) === '[object Array]'
}
const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}
const originalPush33 = Router.prototype.replace
Router.prototype.replace = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush33.call(this, location, onResolve, onReject)
  return originalPush33.call(this, location).catch(err => err)
}
function genRoutes(routerMap) {
  const routes = []
  for (let i = 0; i < routerMap.length; i++) {
    const { path, name, component, params, children } = routerMap[i]
    const route = {
      path,
      name,
      component: () => import(`modules/${component}.vue`),
      meta: { ...params }
    }
    if (children && isArray(children)) {
      const childRoutes = genRoutes(children)
      route.children = childRoutes
    }
    routes.push(route)
  }
  return routes
}
function checkRoutersNameUniqe(routes) {
  const checkRouters = JSON.parse(JSON.stringify(routes))
  function getAllRouters(routes) {
    return routes.reduce((pre, curr, index) => {
      const { children } = curr
      if (children && isArray(children)) {
        pre.concat(children)
      } else {
        pre.push(curr)
      }
      return pre
    }, [])
  }
  const checkEachRouterName = getAllRouters(checkRouters).reduce((pre, curr) => {
    let { name } = curr
    if (name === undefined) {
      name = 'undefined'
    }
    if (pre[name] === undefined) {
      pre[name] = []
    }
    pre[name].push(curr)
    return pre
  }, {})
  for (const key in checkEachRouterName) {
    if (checkEachRouterName[key].length > 1) {
      throw new Error(`您的路由命名存在重复${key}`)
    }
  }
}

const routes = genRoutes(routerMap)
if (process.env.NODE_ENV === 'development') {
  checkRoutersNameUniqe(routes)
}

Vue.use(Router)

const router = new Router({
  mode: 'hash',
  routes: [{ path: '/', redirect: { path: '/sxMonitor' } }, ...routes]
})

router.beforeEach((to, from, next) => {
  document.title = '顺心战况-' + (to.meta.title || '')
  next()
})

export default router
