/*
 * @Author: shigl
 * @Date: 2022-07-18 15:48:46
 * @LastEditTime: 2022-07-20 18:46:36
 * @Description:
 */
import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)

/**
 * 这些都是全局状态,单独模块开发尽量使用store.registerModule('myModule', {})进行模块注册
 */
import state from './state'
import mutations from './mutations'
import getters from './getters'

import storeMap from './storeMap'
import zoneInfo from './modules/zoneInfo'
let modulesList = {}
storeMap.forEach(item => {
  if (item && item['name']) {
    modulesList = {
      ...modulesList,
      [item['name']]: item
    }
  }
})
export default new Vuex.Store({
  state,
  mutations,
  getters,
  modules: {
    ...modulesList,
    zoneInfo
  }
})
