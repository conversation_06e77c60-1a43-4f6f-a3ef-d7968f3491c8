/*
 * @Author: shigl
 * @Date: 2022-01-13 18:22:35
 * @LastEditTime: 2022-11-28 19:04:37
 * @Description:
 */
const sxMonitorStore = {
  user: {
    zoneCode: '',
    zoneLevel: ''
  },
  zoneParams: {
    zoneCode: '',
    zoneLevel: ''
  },
  isCertain: false,
  timeValue: ''
}
const orgArray = sessionStorage.getItem('orgArray')
const userData = sessionStorage.getItem('userData')
const themeArray = sessionStorage.getItem('themeArray')
const zoneParams = sessionStorage.getItem('zoneParams')
const moduleTitle = sessionStorage.getItem('moduleTitle')
const holidayData = sessionStorage.getItem('holidayData')

export default {
  moduleProblem: [], // 模块跑马灯内容
  isDev: process.env.VUE_APP_SERVE === 'serve-prod' ? false : process.env.NODE_ENV === 'development', // 判断环境
  // 层级选择
  zoneParams: zoneParams ? JSON.parse(zoneParams) : {},
  // 接口返回的主题权限
  themeArray: themeArray ? JSON.parse(themeArray) : [],
  // 主题模块标题
  moduleTitle: moduleTitle || '',
  userData: userData ? JSON.parse(userData) : {}, // 用户层级信息
  requestQueues: [],
  // 顺心的组织层级获取
  orgArray: orgArray ? JSON.parse(orgArray) : [],
  lastComponentPath: '',
  authList: [], // 快运层级映射后多个层级逻辑
  // 当前用户信息
  userMsg: {},
  removeRouteCache: '',
  showThemeTab: false, // 显示节日图片替换底部菜单按钮
  sxMonitorStore: sessionStorage.getItem('SXMonitor') ? JSON.parse(sessionStorage.getItem('SXMonitor')) : sxMonitorStore,
  holidayData: holidayData ? JSON.parse(holidayData) : [] // 节假日
}
