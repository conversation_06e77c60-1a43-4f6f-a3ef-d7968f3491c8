/*
 * @Author: shigl
 * @Date: 2022-07-18 15:18:13
 * @LastEditTime: 2024-03-25 14:02:52
 * @Description:
 */
export default {
  // 添加请求队列
  setRequestQueues(state, payload) {
    const { type } = payload
    if (type === 'push') {
      state.requestQueues.push('req')
    } else if (type === 'delete') {
      state.requestQueues.splice(0, 1)
    } else if (type === 'clear') {
      state.requestQueues = []
    } else {
      state.requestQueues = []
    }
  },
  setRemoveRouteCache(state, val) {
    state.removeRouteCache = val
  },

  setLastComponentPath(state, path) {
    state.lastComponentPath = path
  },
  setUserMsg(state, userMsg) {
    state.userMsg = userMsg
  },

  setThemeArray(state, data) {
    sessionStorage.setItem('themeArray', JSON.stringify(data))
    state.themeArray = data
  },
  setAuthList(state, data) {
    state.authList = data
  },

  setSXMonitor(state, payload = {}) {
    Object.assign(state.sxMonitorStore, payload)
  },

  setOrgArray(state, val) {
    sessionStorage.setItem('orgArray', JSON.stringify(val))
    state.orgArray = val
  },
  setModuleProblem(state, val) {
    state.moduleProblem = val
  },
  setModuleTitle(state, title) {
    sessionStorage.setItem('moduleTitle', title)
    state.moduleTitle = title
  },
  setUserData(state, data) {
    sessionStorage.setItem('userData', JSON.stringify(data))
    state.userData = data
  },
  setZoneParams(state, data) {
    sessionStorage.setItem('zoneParams', JSON.stringify(data))
    state.zoneParams = data
  },
  setHolidayData(state, val) {
    state.holidayData = val
  },
  setModulesConfig(state, val) {
    state.modulesConfig = val
  },
  setShowThemeTab(state, val) {
    state.showThemeTab = val
  }
}
