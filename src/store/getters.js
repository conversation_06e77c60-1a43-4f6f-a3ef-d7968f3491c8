/*
 * @Author: shigl
 * @Date: 2022-06-06 16:49:40
 * @LastEditTime: 2024-03-22 16:23:55
 * @Description:
 */
import { themeList } from '../modules/entrance/config'
export default {
  isAuth(state, getters) {
    return state.themeArray.some(item => item.themeName.includes(state.moduleTitle))
  },
  isLoading(state) {
    return Boolean(state.requestQueues.length)
  },
  // 根据层级判断当前模块是否展示(主菜单)
  levelthemeArray(state, getters) {
    const { zoneLevel, levelName } = getters.zoneData
    // 根据themeList决定模块展示
    const themeArray = []
    // 保证排序,先取有权限的
    state.themeArray.forEach(item => {
      const index = themeList.findIndex(data => data['label'] === item['themeName'])
      if (index !== -1) {
        item['isAuth'] = true
        themeArray.push({
          ...themeList[index],
          ...item
        })
      }
    })
    // 无权限的
    const tmp = []
    themeList.forEach(item => {
      const index = state.themeArray.findIndex(data => data['themeName'] === item['label'])
      if (index === -1) {
        item['isAuth'] = false
        tmp.push(item)
      }
    })
    themeArray.push(...tmp)
    // isLevel 层级展示条件
    const findFun = list => {
      return themeArray.map((item, index) => {
        const isTrue = list.includes(item.themeName)
        item['isLevel'] = isTrue
        return item
      })
    }
    switch (+zoneLevel) {
      case 30:
        return findFun(['市场', '营运', '质控', '末端', '财务', '总体'])
      case 32:
        return findFun(['市场', '营运', '质控', '末端', '财务'])
      case 33:
        return findFun(['市场', '营运', '质控', '末端', '财务'])
      case 34:
        if (levelName === '网管') {
          return findFun(['市场', '末端'])
        }
        if (levelName === '网点') {
          return findFun(['市场', '营运', '末端'])
        }
        return findFun(['市场', '末端'])
      default:
        return findFun([])
    }
  },
  // 当前模块数据 (主菜单)
  levelthemeCurrent(state, getters) {
    const levelthemeArray = getters.levelthemeArray || []
    return levelthemeArray.find(item => item['themeName'] === state.moduleTitle) || {}
  },
  // 市场菜单权限判断
  // levelMarketCurrent(state, getters) {
  //   const themeCodeList = ['sxzk-market-jiameng','sxzk-market-total','sxzk-market-qudao']

  // },

  zoneData(state) {
    const { zoneLevel, zoneName, zoneCode } = state.zoneParams
    let newZoneLevel = ''
    let dZoneLevel = ''
    let dLevelName = ''
    let levelName = ''
    let levelKey = ''
    switch (+zoneLevel) {
      case 0:
        newZoneLevel = '30'
        levelName = '全网'
        levelKey = 'bu'
        dZoneLevel = '32'
        dLevelName = '省区'
        break
      case 2:
        newZoneLevel = '32'
        levelName = '省区'
        levelKey = 'province'
        dZoneLevel = '33'
        dLevelName = '区域'

        break
      case 3:
        newZoneLevel = '33'
        levelName = '区域'
        levelKey = 'area'
        dZoneLevel = '34'
        dLevelName = '网管'
        break
      case 4:
        newZoneLevel = '34'
        dZoneLevel = '34' // 网点无下钻
        const code = Number(zoneCode)
        if (!isNaN(code)) {
          levelName = '网管'
          dLevelName = '网点'
          levelKey = 'manager'
        } else {
          levelName = '网点'
          dLevelName = '网点'
          levelKey = 'dept'
        }

        break
    }
    return {
      zoneLevel: newZoneLevel,
      levelKey,
      dZoneLevel,
      dLevelName,
      level: zoneLevel,
      zoneCode,
      zoneName,
      levelName
    }
  }
}
