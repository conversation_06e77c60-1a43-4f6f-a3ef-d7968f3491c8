/*
 * @Author: shigl
 * @Date: 2022-07-20 18:42:13
 * @LastEditTime: 2022-07-20 18:42:15
 * @Description:
 */
let storeList = []
const storeContext = require.context('../modules/', true, /store\.js$/)
storeContext.keys().forEach(path => {
  const store = storeContext(path)
  if (Object.prototype.toString.call(store.default) === '[object Array]') {
    storeList = storeList.concat(store.default)
  } else {
    storeList.push(store.default)
  }
})
export default storeList
