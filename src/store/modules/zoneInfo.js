/*
 * @Author: AI Assistant
 * @Date: 2025-01-22
 * @Description: 区域信息管理 Vuex 模块
 */

export default {
  namespaced: true,

  state: {
    // 这里可以存储一些缓存数据，如果需要的话
    cachedZoneData: null
  },

  getters: {
    // 当前选中区域的基本信息
    currentZoneInfo: (state, getters, rootState) => {
      const zoneParams = rootState.zoneParams || {}
      const zoneData = rootState.zoneData || {}

      return {
        zoneLevel: zoneData.zoneLevel || zoneParams.zoneLevel,
        zoneCode: zoneParams.zoneCode,
        zoneName: zoneParams.zoneName,
        levelName:
          {
            30: '全网',
            32: '省区',
            33: '区域',
            34: '场站'
          }[zoneData.zoneLevel] || '未知'
      }
    },

    // 获取上级省区code
    parentProvinceAreaCode: (state, getters, rootState) => {
      const zoneParams = rootState.zoneParams || {}
      return zoneParams.province_area_code || null
    },

    // 获取上级省区名称
    parentProvinceAreaName: (state, getters, rootState) => {
      const zoneParams = rootState.zoneParams || {}
      const zoneData = rootState.zoneData || {}
      const zoneLevel = +zoneData.zoneLevel

      switch (zoneLevel) {
        case 32: // 省区级别，没有上级省区
          return null
        case 33: // 区域级别，上级是省区
          return zoneParams.province_area_name || null
        case 34: // 场站级别，上级省区通过区域获取
          return zoneParams.province_area_name || null
        default:
          return null
      }
    },

    // 获取上级区域code
    parentAreaCode: (state, getters, rootState) => {
      const zoneParams = rootState.zoneParams || {}
      const zoneData = rootState.zoneData || {}
      const zoneLevel = +zoneData.zoneLevel

      switch (zoneLevel) {
        case 32: // 省区级别，没有上级区域
        case 33: // 区域级别，没有上级区域
          return null
        case 34: // 场站级别，上级是区域
          return zoneParams.area_code || null
        default:
          return null
      }
    },

    // 获取上级区域名称
    parentAreaName: (state, getters, rootState) => {
      const zoneParams = rootState.zoneParams || {}
      const zoneData = rootState.zoneData || {}
      const zoneLevel = +zoneData.zoneLevel

      switch (zoneLevel) {
        case 32: // 省区级别，没有上级区域
        case 33: // 区域级别，没有上级区域
          return null
        case 34: // 场站级别，上级是区域
          return zoneParams.area_name || null
        default:
          return null
      }
    },

    // 获取完整的上级信息对象
    parentZoneInfo: (state, getters, rootState) => {
      const zoneParams = rootState.zoneParams || {}
      const zoneData = rootState.zoneData || {}
      const zoneLevel = +zoneData.zoneLevel

      return {
        currentLevel: zoneLevel,
        currentCode: zoneParams.zoneCode,
        currentName: zoneParams.zoneName,
        provinceAreaCode: getters.parentProvinceAreaCode,
        provinceAreaName: getters.parentProvinceAreaName,
        areaCode: getters.parentAreaCode,
        areaName: getters.parentAreaName,
        levelName: getters.currentZoneInfo.levelName,
        hasParentProvince: !!getters.parentProvinceAreaCode,
        hasParentArea: !!getters.parentAreaCode
      }
    },

    // 构建API请求参数
    apiRequestParams: (state, getters, rootState) => (baseParams = {}) => {
      const zoneParams = rootState.zoneParams || {}
      const zoneData = rootState.zoneData || {}
      const zoneLevel = +zoneData.zoneLevel
      const params = { ...baseParams }

      switch (zoneLevel) {
        case 32: // 省区级别
          params.provinceAreaCode = zoneParams.zoneCode
          break
        case 33: // 区域级别
          params.provinceAreaCode = getters.parentProvinceAreaCode
          params.areaCode = zoneParams.zoneCode
          break
        case 34: // 场站级别
          params.provinceAreaCode = getters.parentProvinceAreaCode
          params.areaCode = getters.parentAreaCode
          params.deptCode = zoneParams.zoneCode
          break
      }

      return params
    },

    // 获取面包屑导航数据
    breadcrumbData: (state, getters, rootState) => {
      const zoneParams = rootState.zoneParams || {}
      const zoneData = rootState.zoneData || {}
      const zoneLevel = +zoneData.zoneLevel
      const breadcrumbs = []

      // 总是包含全网
      breadcrumbs.push({
        level: 30,
        code: '001',
        name: '顺心捷达',
        levelName: '全网'
      })

      switch (zoneLevel) {
        case 34: // 场站级别 - 显示：全网 > 省区 > 区域 > 场站
          if (getters.parentProvinceAreaCode) {
            breadcrumbs.push({
              level: 32,
              code: getters.parentProvinceAreaCode,
              name: getters.parentProvinceAreaName,
              levelName: '省区'
            })
          }
          if (getters.parentAreaCode) {
            breadcrumbs.push({
              level: 33,
              code: getters.parentAreaCode,
              name: getters.parentAreaName,
              levelName: '区域'
            })
          }
          breadcrumbs.push({
            level: 34,
            code: zoneParams.zoneCode,
            name: zoneParams.zoneName,
            levelName: '场站'
          })
          break

        case 33: // 区域级别 - 显示：全网 > 省区 > 区域
          if (getters.parentProvinceAreaCode) {
            breadcrumbs.push({
              level: 32,
              code: getters.parentProvinceAreaCode,
              name: getters.parentProvinceAreaName,
              levelName: '省区'
            })
          }
          breadcrumbs.push({
            level: 33,
            code: zoneParams.zoneCode,
            name: zoneParams.zoneName,
            levelName: '区域'
          })
          break

        case 32: // 省区级别 - 显示：全网 > 省区
          breadcrumbs.push({
            level: 32,
            code: zoneParams.zoneCode,
            name: zoneParams.zoneName,
            levelName: '省区'
          })
          break
      }

      return breadcrumbs
    },

    // 判断是否为场站级别
    isStationLevel: (state, getters, rootState) => {
      const zoneData = rootState.zoneData || {}
      return +zoneData.zoneLevel === 34
    }
  },

  mutations: {
    // 缓存区域数据（如果需要的话）
    setCachedZoneData(state, data) {
      state.cachedZoneData = data
    }
  },

  actions: {
    // 如果需要异步操作，可以在这里添加
  }
}
